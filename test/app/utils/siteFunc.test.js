const { app, mock, assert } = require('egg-mock/bootstrap');
const {
  siteFunc,
} = require('../../../app/utils');
const manageCates = [
    {
      _id: 'SkFHdYElb',
      comments: '系统管理',
      label: 'systemManage',
      parentId: '0',
      type: '0',
      enable: true,
      icon: 'icon_work_fill',
      routePath: 'systemManage',
      api: '',
    },
    {
      _id: 'H1kjttNg-',
      comments: '用户管理',
      label: 'adminUser',
      parentId: 'SkFHdYElb',
      type: '0',
      api: '',
      routePath: 'adminUser',
      enable: true,
      icon: 'icon_patriarch',
    },
    {
      _id: 'BJ-cqKEe-',
      api: 'adminUser/getList',
      comments: '查询用户列表',
      label: 'getAdminUserList',
      parentId: 'H1kjttNg-',
      type: '1',
      enable: true,
      icon: '',
      routePath: '',
    },
    {
      _id: 'BycritEeW',
      api: 'adminUser/deleteUser',
      comments: '删除用户功能',
      label: 'delAdminUser',
      parentId: 'H1kjttNg-',
      type: '1',
      enable: true,
      icon: '',
      routePath: '',
    },
    {
      _id: 'BywRcKNxW',
      api: 'adminUser/addOne',
      comments: '添加新用户',
      label: 'addNewAdminUser',
      parentId: 'H1kjttNg-',
      type: '1',
      enable: true,
      icon: '',
      routePath: '',
    },
    {
      _id: 'NG4NcVL-B',
      label: 'getOneAdminUser',
      type: '1',
      api: 'adminUser/getOne',
      parentId: 'H1kjttNg-',
      routePath: '',
      icon: '',
      comments: '查询单个用户',
      enable: true,
    },
    {
      _id: 'rJFGsF4g-',
      api: 'adminUser/updateOne',
      comments: '用户更新操作',
      label: 'updateAdminUser',
      parentId: 'H1kjttNg-',
      type: '1',
      enable: true,
      icon: '',
      routePath: '',
    },
  ],
  adminPower = [ 'BywRcKNxW', 'rJFGsF4g-', 'BJ-cqKEe-', 'BycritEeW', 'NG4NcVL-B' ];

before(() => {
  // 等待 app 启动成功，才能执行测试用例
  return app.ready();
});
afterEach(mock.restore);

describe('test/app/utils/siteFunc.test.js', () => {
  it('siteFunc.randomString', function* () {
    const str = siteFunc.randomString(5);
    const str1 = siteFunc.randomString(5, '0123456789');
    assert(str && str1);
  });

  it('siteFunc.renderNoPowerMenus', function* () {
    // 因buildTree、renderTemp、checkNoAllPower三函数与此函数在该文件下联动，故一次执行覆盖，不在另写
    const renderResources = siteFunc.renderNoPowerMenus(manageCates, adminPower, '/hgfvbv', false);
    const renderResources1 = siteFunc.renderNoPowerMenus(manageCates, adminPower, '/hgfvbv');
    assert(renderResources && renderResources1);
  });

  it('siteFunc.getStrLength', function* () {
    const result = siteFunc.getStrLength('柳儿 is Girl!');
    assert(result);
  });

  it('siteFunc.setTempParentId', function* () {
    const result = siteFunc.setTempParentId(manageCates, '0');
    assert(result);
  });

  it('siteFunc.getTempBaseFile', function* () {
    const result = siteFunc.getTempBaseFile('a.html');
    const result1 = siteFunc.getTempBaseFile('a.json');
    const result2 = siteFunc.getTempBaseFile('a.cs');
    assert(result === '' && result1 && result2 === '');
  });

  it('siteFunc.checkExistFile', function* () {
    const result = siteFunc.checkExistFile([{ name: 'authToken.test.js' }], [ 'authToken.test.js' ]);
    assert(result);
  });

  it('siteFunc.getAHref', function* () {
    const result = siteFunc.getAHref('<img src="a.jpg" />');
    const result1 = siteFunc.getAHref('<video src="a.mp4" />', 'video');
    const result2 = siteFunc.getAHref('<audio src="a.mp3" />', 'audio');
    assert(result && result1 && result2);
  });
});
