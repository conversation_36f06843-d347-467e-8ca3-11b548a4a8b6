const { app, mock, assert } = require('egg-mock/bootstrap');
const {
  authToken,
} = require('../../../app/utils');
const jwt = require('jsonwebtoken');

before(() => {
  // 等待 app 启动成功，才能执行测试用例
  return app.ready();
});
afterEach(mock.restore);

describe('test/app/utils/authToken.test.js', () => {
  it('authToken.checkToken', function* () {
    const userToken = jwt.sign({
      userId: 'Hgfvbv',
    }, app.config.encrypt_key);

    const checkToken = yield authToken.checkToken(userToken, app.config.encrypt_key);
    const checkTokenFalse = yield authToken.checkToken('userToken', app.config.encrypt_key);
    assert(checkToken && !checkTokenFalse);
  });
});
