const { app, mock, assert } = require('egg-mock/bootstrap');
const {
  userRule,
} = require('../../../app/validate');

before(() => {
  // 等待 app 启动成功，才能执行测试用例
  return app.ready();
});
afterEach(mock.restore);

describe('test/app/validate/user.test.js', () => {
  it('userRule.form', function* () {
    const ctx = app.mockContext();
    const formObj = {
      userName: 'userName',
      name: 'name',
      email: '<EMAIL>',
      phoneNum: 'phoneNum',
      countryCode: 'countryCode',
      comments: 'comments',
    };

    let result = true;

    try {
      ctx.validate(userRule.form(ctx), formObj);
    } catch (e) {
      result = false;
      console.log(e);
    }

    assert(result);
  });

  it('userRule.login', function* () {
    const ctx = app.mockContext();
    const formObj = {
      userName: 'Hgfvbv',
      password: 'password',
    };

    let result = true;

    try {
      ctx.validate(userRule.login(ctx), formObj);
    } catch (e) {
      result = false;
      console.log(e);
    }

    assert(result);
  });
});
