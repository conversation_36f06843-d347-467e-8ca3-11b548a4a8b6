
const Service = require('egg').Service;

class TestPaperService extends Service {
  // 随机获取考题
  async getTopicsRandom(query) {
    return await this.ctx.model.Topic.find(query);
  }
  // 获取试卷列表
  async getList(data) {
    const adminUserInfo = this.ctx.session.adminUserInfo || {};
    const query = {
      EnterpriseID: adminUserInfo.EnterpriseID,
    };
    if (data.keyWord) {
      query.name = { $regex: data.keyWord };
    }
    if (data.lookMy)query.createPresonId = adminUserInfo._id;
    const res = await this.ctx.model.TestPaper.find(query)
      .sort({ createdAt: -1 })
      .skip((data.pageCurrent - 1) * data.size)
      .limit(data.size)
      .populate('createPresonId', 'name');

    const pageInfo = await this.getPageInfo('TestPaper', data.size, data.pageCurrent, query);
    return {
      res,
      pageInfo,
    };
  }
  async getPageInfo(collection, size, pageCurrent, query = {}) {
    const total = await this.ctx.model[collection].find(query).count();
    const pageInfo = {
      total,
      size,
      pageCurrent,
    };
    return pageInfo;
  }
  // data 存在_id 就更新，不存在就 新建
  async create(data) {
    const { ctx } = this;
    const doc = await ctx.model.TestPaper.findOne({ name: data.name });
    if (!doc) {
      const res = await new ctx.model.TestPaper({
        EnterpriseID: ctx.session.adminUserInfo.EnterpriseID || '',
        createPresonId: ctx.session.adminUserInfo._id || '',
        ...data,
      }).save();
      return res;
    }
  }
  async update(data) {
    return await this.ctx.model.TestPaper.update(
      { _id: data._id },
      data,
      { new: true }
    );
  }
  async del(_id) {
    const { ctx } = this;
    return await ctx.model.TestPaper.findByIdAndRemove(_id);
  }
  async getById(_id) {
    return await this.ctx.model.TestPaper.findOne({ _id })
      .populate('questions');
  }

}

module.exports = TestPaperService;
