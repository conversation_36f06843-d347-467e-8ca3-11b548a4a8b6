const path = require('path');
const fs = require('fs');
const awaitWriteStream = require('await-stream-ready').write;
const sendToWormhole = require('stream-wormhole');
const mkdirp = require('mkdirp');
// const await = require('await-stream-ready/lib/await');

const QuestionBankController = {
  // 获取 题库
  async getQB(ctx) {
    const data = ctx.request.body;
    const res = await ctx.service.questionBank.getQB(data);
    ctx.helper.renderSuccess(ctx, {
      data: res,
      message: 'success',
      status: 200,
    });
  },
  // 创建 题库
  async createQB(ctx) {
    const data = ctx.request.body;
    const res = await ctx.service.questionBank.createQB(data);
    ctx.body = res;
  },
  // 删除题库
  async delQB(ctx) {
    const _id = ctx.request.body._id;
    const res = await ctx.service.questionBank.delQB(_id);
    ctx.helper.renderSuccess(ctx, res);
  },
  // 新建题目
  async createTopic(ctx, app) {
    const EnterpriseID = ctx.session.adminUserInfo ? ctx.session.adminUserInfo.EnterpriseID : '';
    const filePath = path.join(app.config.upload_path, EnterpriseID);
    if (!fs.existsSync(filePath)) {
      await mkdirp(filePath);
    }

    const newData = await this.uploadMultipart(ctx, app, EnterpriseID);
    if (!Array.isArray(newData.answer)) {
      newData.answer = [ newData.answer ];
    }
    const res = await ctx.service.questionBank.createTopic(newData);
    if (res) {
      ctx.helper.renderSuccess(ctx, {
        message: 'success',
        status: 200,
        data: res,
      });
    }
  },
  async addSomeTopic(ctx) {
    const data = ctx.request.body.data;
    const res = await ctx.service.questionBank.addSomeTopic(data);
    if (res.length === data.length) {
      ctx.helper.renderSuccess(ctx, {
        data: res,
        message: '成功导入',
        status: 200,
      });
    } else {
      ctx.helper.renderSuccess(ctx, {
        data: res,
        message: `导入成功${res}条试题`,
        status: 200,
      });
    }
  },
  // 更新题目
  async updateTopic(ctx, app) {
    const EnterpriseID = ctx.session.operateUserInfo ? ctx.session.operateUserInfo._id : '';
    const filePath = path.join(app.config.upload_path, EnterpriseID);
    if (!fs.existsSync(filePath)) {
      await mkdirp(filePath);
    }
    const newData = await this.uploadMultipart(ctx, app, EnterpriseID);
    if (!Array.isArray(newData.answer)) {
      newData.answer = [ newData.answer ];
    }
    // 获取到需要删除的文件
    const delFileList = newData.delFileList ? JSON.parse(newData.delFileList) : [];
    if (delFileList.length > 0) {
      delFileList.forEach(item => {
        fs.unlinkSync(path.resolve(app.config.upload_path, EnterpriseID, item.staticName));
      });
    }

    // 更新的时候，可能会新添加文件，那么之前已经上传过的文件就要特殊处理下
    const fieldArr = [ 'steamPic_', 'answerAnalysisPic_' ];
    const newDataField = Object.keys(newData);
    fieldArr.forEach(item => {
      newDataField.forEach(val => {
        if (val.indexOf(item) > -1) {
          const filetypeField = val.split('_');
          newData[filetypeField[0]].splice(filetypeField[1], 0, JSON.parse(newData[val]));
        }
      });
    });

    const res = await ctx.service.questionBank.updateTopic(newData);
    if (res) {
      ctx.helper.renderSuccess(ctx, {
        data: res,
        message: 'success',
        status: 200,
      });
    }

  },
  // 获取题目
  async getTopic(ctx) {
    const data = ctx.request.body;
    const res = await ctx.service.questionBank.getTopic(data);
    ctx.helper.renderSuccess(ctx, {
      data: res,
      message: 'success',
      status: 200,
    });
  },
  async getRandomTopic(ctx) {
    try {
      const res = await ctx.service.questionBank.getRandomTopic(ctx.query);
      ctx.helper.renderSuccess(ctx, {
        data: res,
        message: 'success',
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err.message || '未知错误',
      });
    }
  },
  // 删除题目
  async delTopic(ctx) {
    const ids = ctx.request.body.ids;
    const res = await ctx.service.questionBank.delTopic(ids);
    ctx.helper.renderSuccess(ctx, res);
  },
  // 处理多文件上传
  async uploadMultipart(ctx, app, EnterpriseID) {
    const parts = ctx.multipart({ autoFields: true });
    const fileList = {
      steamPic: [],
      answerAnalysisPic: [],
    };
    let part;
    while ((part = await parts()) != null) {
      if (!part.filename) {
        return;
      }
      // 文件名
      const filename = 'topic_' + part.fieldname + '_' + Math.random().toString(36).substr(8) + '_' + new Date().getTime() + path.extname(part.filename);
      // 字段名拆开成数组 eg：annex_data_0 ——> ['annex', 'data', 0]
      const fieldname = part.fieldname.split('_');
      // 拼接成新的字段 eg：['annex', 'data', 0] ——> annex_data
      const field = fieldname[0];
      // 将文件的基本信息push到对应的数组下 eg：fileList[annex_data]
      fileList[field].push({
        fileType: path.extname(part.filename),
        name: part.filename,
        staticName: filename,
        staticSrc: '/static' + app.config.upload_http_path + '/' + EnterpriseID + '/' + filename,
      });
      // 处理得到静态资源路径地址
      const filePath = path.join(app.config.upload_path, EnterpriseID);
      const target = path.resolve(filePath, filename);

      // 读流
      const writeStream = fs.createWriteStream(target);
      try {
        // 写流
        await awaitWriteStream(part.pipe(writeStream));
      } catch (error) {
        console.log(error);
        await sendToWormhole(part);
        writeStream.destroy();
        throw error;
      }
    }
    // parts.field.partakeDepartment = parts.field.partakeDepartment.split(',');
    // parts.field.personnel = parts.field.personnel.split(',');

    parts.field.options = JSON.parse(parts.field.options);
    parts.field.labels = JSON.parse(parts.field.labels);
    parts.field.answer = JSON.parse(parts.field.answer);
    return {
      EnterpriseID,
      ...(parts.field), // ctx.multipart({ autoFields: true }) 里面传参了才能得到
      ...fileList,
    };
  },
  async findLabel(ctx) {
    const data = ctx.request.body;
    console.log(444, data);
    const doc = await ctx.service.questionBank.findLabel(data);
    ctx.helper.renderSuccess(ctx, {
      data: doc,
      message: 'success',
      status: 200,
    });
  },
  async addLabel(ctx) {
    const data = ctx.request.body;
    const res = await ctx.service.questionBank.addLabel(data);
    ctx.body = res;
  },
  async delLabel(ctx) {
    const data = ctx.request.body;
    const res = await ctx.service.questionBank.delLabel(data);
    if (res) {
      ctx.helper.renderSuccess(ctx, {
        message: 'success',
        status: 200,
      });
    } else {
      ctx.helper.renderSuccess(ctx, {
        message: 'error',
        status: 500,
      });
    }
  },
  async updateLabel(ctx) {
    const data = ctx.request.body;
    try {
      const res = await ctx.service.questionBank.updateLabel(data);
      ctx.body = res;
    } catch (error) {
      console.log(error);
    }
  },
};

module.exports = QuestionBankController;
