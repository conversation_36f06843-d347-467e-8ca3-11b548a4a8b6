
module.exports = app => {
  const shortid = require('shortid');
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;

  // 试题标签
  const TopicLabelSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    name: String, // 标签名字
    EnterpriseID: { // 企业id, 企业端添加的标签才会有，运营端添加的标签就没有
      type: String,
    },
    createAt: { // 创建日期
      type: Date,
    },
    updateAt: { // 更新时间
      type: Date,
    },
  }, { timestamps: true });


  return mongoose.model('TopicLabel', TopicLabelSchema, 'topicLabel');
};
