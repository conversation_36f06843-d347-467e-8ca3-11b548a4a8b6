module.exports = app => {
  const shortid = require('shortid');
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;

  // 题库
  const QuestionBankSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    // EnterpriseID: String, // 企业id 暂时用不到，后续权限开放给企业可能用得到
    name: String, // 课程id
    count: Number, // 题目总数
    singleTopic: { // 单选题数量
      type: Number,
      default: 0,
    },
    multipleTopic: { // 多选题数量
      type: Number,
      default: 0,
    },
    judgeTopic: { // 判断题数量
      type: Number,
      default: 0,
    },
    // createPreson: String, // 创建人， 暂时用不到
    createAt: { // 创建日期
      type: Date,
    },
    updateAt: { // 更新时间
      type: Date,
    },
  }, { timestamps: true });


  return mongoose.model('QuestionBank', QuestionBankSchema, 'questionBank');
};
