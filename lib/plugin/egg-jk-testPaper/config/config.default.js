exports.jk_testPaper = {
  alias: 'testPaper', // 插件目录，必须为英文
  pkgName: 'egg-jk-testPaper', // 插件包名
  enName: 'jk_testPaper', // 插件名
  name: '试卷管理', // 插件名称
  description: '试卷管理', // 插件描述
  adminApi: [
    {
      url: 'testPaper/getTopicsRandom',
      method: 'post',
      controllerName: 'getTopicsRandom',
      details: '随机获取考题',
    },
    {
      url: 'testPaper/getTopicsRandom2',
      method: 'post',
      controllerName: 'getTopicsRandom',
      details: 'px端随机获取考题',
    },
    {
      url: 'testPaper/getDetail',
      method: 'get',
      controllerName: 'getDetail',
      details: '获取试卷详情',
    },
    {
      url: 'testPaper/getList',
      method: 'post',
      controllerName: 'getList',
      details: '获取试卷列表',
    },
    {
      url: 'testPaper/add',
      method: 'post',
      controllerName: 'add',
      details: '添加试卷',
    },
    {
      url: 'testPaper/del',
      method: 'post',
      controllerName: 'del',
      details: '删除试卷',
    },
    {
      url: 'testPaper/edit',
      method: 'post',
      controllerName: 'edit',
      details: '编辑试卷',
    },
    {
      url: 'questionBank/getQB',
      method: 'post',
      controllerName: 'getQB',
      details: '获取题库信息',
    },
    {
      url: 'questionBank/createQB',
      method: 'post',
      controllerName: 'createQB',
      details: '创建题库信息',
    },
    {
      url: 'questionBank/delQB',
      method: 'post',
      controllerName: 'delQB',
      details: '删除题库信息',
    },
    {
      url: 'questionBank/updateQB',
      method: 'post',
      controllerName: 'updateQB',
      details: '更新题库信息',
    },
    {
      url: 'questionBank/createTopic',
      method: 'post',
      controllerName: 'createTopic',
      details: '新建题目',
    },
    {
      url: 'questionBank/updateTopic',
      method: 'post',
      controllerName: 'updateTopic',
      details: '更新题目',
    },
    {
      url: 'questionBank/getTopic',
      method: 'post',
      controllerName: 'getTopic',
      details: '获取题目',
    },
    {
      url: 'questionBank/getTopic',
      method: 'get',
      controllerName: 'getRandomTopic',
      details: '获取随机题目',
    },
    {
      url: 'questionBank/delTopic',
      method: 'post',
      controllerName: 'delTopic',
      details: '删除题目',
    },
    {
      url: 'questionBank/addSomeTopic',
      method: 'post',
      controllerName: 'addSomeTopic',
      details: '批量导入题目',
    },
    {
      url: 'questionBank/findLabel',
      method: 'post',
      controllerName: 'findLabel',
      details: '获取标签',
    },
    {
      url: 'questionBank/addLabel',
      method: 'post',
      controllerName: 'addLabel',
      details: '新增标签',
    },
    {
      url: 'questionBank/delLabel',
      method: 'post',
      controllerName: 'delLabel',
      details: '删除标签',
    },
    {
      url: 'questionBank/updateLabel',
      method: 'post',
      controllerName: 'updateLabel',
      details: '更新标签',
    },
  ],
  fontApi: [],
  initData: '', // 初始化数据脚本
  pluginsConfig: ` 
    exports.jk_testPaper = {\n
        enable: true,\n        package: 'egg-jk-testPaper',
    };\n
    `, // 插入到 plugins.js 中的配置
  defaultConfig: `
  testPaperRouter:{\n
        match: [ctx => ctx.path.startsWith('/manage/testPaper'), ctx => ctx.path.startsWith('/manage/questionBank')],\n
    },\n
    `, // 插入到 config.default.js 中的配置

  // 列表查询每页显示的数量，默认为10
  // pageSize: 10,
  multipart: {
    fileSize: '50mb',
    fields: '100',
    // mode: 'stream',
    fileExtensions: [ 'docx', 'doc', 'xlsx', 'mp4', 'pdf', 'avi', 'rar' ],
  },
  // 图片上传路径
  propagateUploadPath: './app/public/upload/propagate',
  security: {
    csrf: {
      ignoreJSON: true,
    },
    // 白名单
    // domainWhiteList: ['*'],
  },
};

