exports.jk_examSyllabus = {
  alias: 'examSyllabus', // 插件目录，必须为英文
  pkgName: 'egg-jk-examSyllabus', // 插件包名
  enName: 'jk_examSyllabus', // 插件名
  name: '考试大纲管理', // 插件名称
  description: '考试大纲管理', // 插件描述
  adminApi: [
    {
      url: 'examSyllabus/findOne',
      method: 'get',
      controllerName: 'findOne',
      details: '获取单个详情',
    },
    {
      url: 'examSyllabus/list',
      method: 'get',
      controllerName: 'list',
      details: '获取列表',
    },
    {
      url: 'examSyllabus/create',
      method: 'post',
      controllerName: 'create',
      details: '创建',
    },
    {
      url: 'examSyllabus/del',
      method: 'post',
      controllerName: 'deleteOne',
      details: '删除',
    },
    {
      url: 'examSyllabus/edit',
      method: 'post',
      controllerName: 'updateOne',
      details: '更新',
    },
    {
      url: 'examSyllabus/statistics',
      method: 'get',
      controllerName: 'getStatistics',
      details: '获取统计数据',
    },
    {
      url: 'examSyllabus/subAreaStatistics',
      method: 'get',
      controllerName: 'getSubRegionsTrainData',
      details: '获取子区域的培训统计数据',
    },
    {
      url: 'examSyllabus/trainData',
      method: 'get',
      controllerName: 'getSubRegionsTrainData',
      details: '根据考试大纲获取相关的培训统计数据',
    },
  ],
  fontApi: [],
  initData: '', // 初始化数据脚本
  pluginsConfig: ` 
    exports.jk_examSyllabus = {\n
        enable: true,\n        package: 'egg-jk-examSyllabus',
    };\n
    `, // 插入到 plugins.js 中的配置
  defaultConfig: `
  examSyllabusRouter:{\n
        match: [ctx => ctx.path.startsWith('/manage/examSyllabus')],\n
    },\n
    `, // 插入到 config.default.js 中的配置

  // 列表查询每页显示的数量，默认为10
  // pageSize: 10,
  multipart: {
    fileSize: '50mb',
    fields: '100',
    // mode: 'stream',
    fileExtensions: [ 'docx', 'doc', 'xlsx', 'mp4', 'pdf', 'avi', 'rar' ],
  },
  // 图片上传路径
  propagateUploadPath: './app/public/upload/propagate',
  security: {
    csrf: {
      ignoreJSON: true,
    },
    // 白名单
    // domainWhiteList: ['*'],
  },
};

