// 考试大纲
const Service = require('egg').Service;
const moment = require('moment');
class ExamSyllabusService extends Service {
  async create(data) {
    data.creator = this.ctx.session.superUserInfo._id;
    const res = this.ctx.model.ExamSyllabus.create(data);
    await this.ctx.service.testPaper.updateByExamSyllabus(res._id, 'add');
    return res;
  }
  async updateOne(data) {
    const { ctx } = this;
    const { _id } = data;
    if (!_id) {
      throw new Error('缺少 _id');
    }
    const count = await ctx.model.ExamSyllabus.findOne({ _id }, { creator: 1 });
    if (!count) {
      throw new Error('考试大纲_id不存在');
    }
    const res = await ctx.model.ExamSyllabus.updateOne({ _id }, { $set: data });
    if (res.nModified === 1) {
      await ctx.service.testPaper.updateByExamSyllabus(_id, 'update');
    }
    return res;
  }
  async deleteOne(data) {
    const { ctx } = this;
    const { _id } = data;
    if (!_id) {
      throw new Error('缺少 _id');
    }
    const count = await ctx.model.ExamSyllabus.findOne({ _id }, { creator: 1 });
    if (!count) {
      throw new Error('考试大纲_id不存在');
    }
    if (count.creator !== this.ctx.session.superUserInfo._id) {
      throw new Error('只能删除自己创建的考试大纲');
    }
    const res = await ctx.model.ExamSyllabus.deleteOne({ _id });
    await ctx.service.testPaper.updateByExamSyllabus(_id, 'del');
    return res;
  }
  async findOne(data) {
    const { ctx } = this;
    const { _id } = data;
    if (!_id) {
      throw new Error('缺少 _id');
    }
    let res = await ctx.model.ExamSyllabus.findOne({ _id }, { source: 0, creator: 0 });
    if (!res) {
      throw new Error('考试大纲_id不存在');
    }
    res = JSON.parse(JSON.stringify(res));
    const outlineList = res.outlineList;
    for (let i = 0; i < outlineList.length; i++) {
      const outline = outlineList[i];
      const outlineInfo = await ctx.model.Outline.findOne({ _id: outline.outline[0] });
      if (outlineInfo) {
        const outlineDetail = [{ _id: outline.outline[0], name: outlineInfo.name }];
        const children = outlineInfo.children;
        if (children && children.length > 0) {
          const child = children.find(item => item._id === outline.outline[1]);
          if (child) {
            outlineDetail.push({ _id: outline.outline[1], name: child.name });
          }
        }
        outline.outlineDetail = outlineDetail;
      }
    }
    res.createAt = moment(res.createAt).format('YYYY-MM-DD HH:mm:ss');
    res.updateAt = moment(res.updateAt).format('YYYY-MM-DD HH:mm:ss');
    res.outlineList = outlineList;
    return res;
  }
  async getList(query) {
    const { ctx } = this;
    const { page = 1, pageSize = 10, keyWord = '', certificateType, status } = query;
    const creator = this.ctx.session.superUserInfo._id;
    const queryParam = {
      creator,
    };
    if (certificateType) {
      queryParam.certificateType = +certificateType;
    }
    if (keyWord) {
      queryParam.$or = [
        { name: { $regex: keyWord, $options: 'i' } },
      ];
    }
    if (status === 0 || status === 1) {
      queryParam.status = status;
    }
    const res = await ctx.model.ExamSyllabus.find(queryParam, { source: 0, creator: 0 })
      .sort({ createAt: -1 })
      .skip((Number(page) - 1) * Number(pageSize))
      .limit(Number(pageSize));
    const list = res.map(item => {
      let topicType1 = 0,
        topicType2 = 0,
        topicType3 = 0;
      item.outlineList.forEach(outline => {
        outline.rules.forEach(rule => {
          if (rule.topicType === 1) {
            topicType1 += rule.quantity;
          } else if (rule.topicType === 2) {
            topicType2 += rule.quantity;
          } else if (rule.topicType === 3) {
            topicType3 += rule.quantity;
          }
        });
      });
      return {
        _id: item._id,
        name: item.name,
        createAt: moment(item.createAt).format('YYYY-MM-DD HH:mm:ss'),
        updateAt: moment(item.updateAt).format('YYYY-MM-DD HH:mm:ss'),
        certificateType: [ '企业负责人初次培训', '企业负责人继续教育', '职业健康管理人员初次培训', '职业健康管理人员继续教育', '劳动者上岗前培训', '劳动者在岗培训' ][item.certificateType - 1],
        topicType1,
        topicType2,
        topicType3,
      };
    });
    const total = await ctx.model.ExamSyllabus.countDocuments(queryParam);
    return {
      list,
      total,
    };
  }
  async getAdname(adcode) {
    const { ctx } = this;
    let res = await ctx.model.District.findOne({ area_code: adcode }, { name: 1, parent_code: 1 });
    if (res && res.name) {
      const result = [ res.name ];
      while (res.parent_code !== '0') {
        res = await ctx.model.District.findOne({ area_code: res.parent_code }, { name: 1, parent_code: 1 });
        result.unshift(res.name);
      }
      return result;
    }
    throw new Error('adcode不存在: ' + adcode);
  }
  async getAdcode(params) {
    const { ctx } = this;
    let adcode = params.adcode;
    if (!adcode) {
      const superUserInfo = ctx.session.superUserInfo;
      if (!adcode && superUserInfo) {
        const superUser = await ctx.model.SuperUser.findOne({ _id: superUserInfo._id }, { area_code: 1 });
        if (superUser && superUser.area_code) {
          adcode = superUser.area_code;
        } else {
          throw new Error('缺少adcode');
        }
      }
    }
    if (adcode.length < 12) adcode = adcode.padEnd(12, '0');
    return adcode;
  }
  // 获取子区域
  async getSubRegions(adcode) {
    const { ctx } = this;
    const res = await ctx.model.District.find({ parent_code: adcode }, { area_code: 1, name: 1, lat: 1, lng: 1 });
    return res;
  }
  // 获取子区域的培训统计数据
  async getSubRegionsTrainData(params) { // params: { adcode, year }
    const year = params.year ? +params.year : new Date().getFullYear();
    const adcode = await this.getAdcode(params);
    const adname = await this.getAdname(adcode);
    const subRegions = await this.getSubRegions(adcode);
    const promises = subRegions.map(subRegion => { // 使用map将每个子区域的查询转换为一个promise数组
      const query = {
        adname: [ ...adname, subRegion.name ],
        year,
        adcode: subRegion.area_code,
        point: [ subRegion.lat, subRegion.lng ],
      };
      return this.statisticsForSingleArea(query);
    });
    const subRegionsData = await Promise.all(promises); // 并行执行所有子区域的查询
    return subRegionsData;
  }

  // 获取单个区域的培训统计数据, 即考试大纲管理页面顶部的统计数据
  async statisticsForSingleArea(params) {
    const { ctx } = this;
    const year = +params.year || new Date().getFullYear();
    const adcode = await this.getAdcode(params);
    const adname = params.adname || await this.getAdname(adcode); // eg: [ '广东省', '深圳市', '南山区' ]
    if (!Array.isArray(adname)) throw new Error('adname必须是数组');
    const yearStartTime = new Date(`${year}-01-01`);
    const yearEndTime = new Date(`${year + 1}-01-01`);

    const EnterpriseList = await ctx.model.Adminorg.aggregate([
      {
        $match: {
          isDelete: false,
          workAddress: { $elemMatch: { districts: { $all: adname } } },
        },
      },
      {
        $project: { _id: 1 },
      },
    ]);
    const EnterpriseIds = EnterpriseList.map(item => item._id);

    // 并行执行统计操作
    const [ EnterpriseTrainingStatistics, personalTrainingStatistics, statisticsByMonth ] = await Promise.all([
      this.getEnterpriseTrainingStatistics(EnterpriseIds, yearStartTime, yearEndTime),
      this.getPersonalTrainingStatistics(EnterpriseIds, yearStartTime, yearEndTime),
      this.getStatisticsByMonth(EnterpriseIds, year, params.adname), // 统计子区域时才会传 params.adname
    ]);
    return {
      query: { adname, year, adcode, point: params.point || [] },
      EnterpriseTrainingStatistics,
      personalTrainingStatistics,
      statisticsByMonth,
    };
  }

  // 按月统计培训人数和发证数
  async getStatisticsByMonth(EnterpriseIds = [], year, adname = null) {
    if (adname) return null; // 暂不支持按子区域统计
    const { ctx } = this;
    const months = year === new Date().getFullYear() ? new Date().getMonth() + 1 : 12;
    const statisticsByMonth = await Promise.all(new Array(months).fill(null).map(async (_, index) => {
      const month = index + 1;
      const startDate = new Date(year, index, 1);
      const endDate = month === 12 ? new Date(year + 1, 0, 1) : new Date(year, month, 1);

      const [ certificateNum, learningUserNum ] = await Promise.all([
        ctx.model.Certificate.count({
          EnterpriseID: { $in: EnterpriseIds },
          trainingType: { $ne: 2 },
          issuanceTime: { $exists: true, $gte: startDate, $lt: endDate },
        }),
        ctx.model.PersonalTraining.count({
          EnterpriseID: { $in: EnterpriseIds },
          status: true,
          trainingType: { $ne: 2 },
          updateAt: { $gte: startDate, $lt: endDate },
        }),
      ]);
      return { month: `${month}月`, certificateNum, learningUserNum };
    }));
    return statisticsByMonth;
  }
  // 统计员工数量（应培训人数）、已培训人数、已发放证书数量
  async getPersonalTrainingStatistics(EnterpriseIds = [], yearStartTime, yearEndTime) {
    const { ctx } = this;
    const personalTrainingAggregationResult = await ctx.model.PersonalTraining.aggregate([
      {
        $match: {
          EnterpriseID: { $in: EnterpriseIds },
          status: true,
          trainingType: { $ne: 2 },
          updateAt: { $gte: yearStartTime, $lt: yearEndTime },
        },
      },
      {
        $facet: {
          learnedUser: [
            { $count: 'count' },
          ],
          certificateNum: [
            { $match: { certificateID: { $exists: true } } },
            { $count: 'count' },
          ],
        },
      },
      {
        $project: {
          learnedUser: { $arrayElemAt: [ '$learnedUser.count', 0 ] },
          certificateNum: { $arrayElemAt: [ '$certificateNum.count', 0 ] },
        },
      },
    ]);
    console.log(22222, personalTrainingAggregationResult);
    const { learnedUser = 0, certificateNum = 0 } = personalTrainingAggregationResult.length > 0 ? personalTrainingAggregationResult[0] : { learnedUser: 0, certificateNum: 0 };
    const personalTrainingStatistics = {
      shouldLearnUser: await ctx.model.Employee.count({
        status: 1,
        enable: true,
        EnterpriseID: { $in: EnterpriseIds },
        createAt: { $lt: yearEndTime },
      }),
      learnedUser,
      certificateNum,
      completionRate: '0%',
    };
    personalTrainingStatistics.notStartedUser = personalTrainingStatistics.shouldLearnUser - personalTrainingStatistics.learnedUser;
    if (personalTrainingStatistics.shouldLearnUser && personalTrainingStatistics.certificateNum) {
      personalTrainingStatistics.completionRate = `${(personalTrainingStatistics.certificateNum / personalTrainingStatistics.shouldLearnUser * 100).toFixed(2)}%`;
    }
    return personalTrainingStatistics;
  }
  // 统计总企业数、未开始培训的企业数、进行中的企业数和已完成培训的企业数
  async getEnterpriseTrainingStatistics(EnterpriseIds = [], yearStartTime, yearEndTime) {
    const startTime = new Date();
    const { ctx } = this;

    // 使用聚合查询计算进行中的企业数
    const learningEnterpriseCount = await ctx.model.PersonalTraining.aggregate([
      {
        $match: {
          status: true,
          EnterpriseID: { $in: EnterpriseIds },
          trainingType: { $ne: 2 },
          updateAt: { $gte: yearStartTime, $lt: yearEndTime },
        },
      },
      {
        $group: {
          _id: '$EnterpriseID',
        },
      },
      {
        $count: 'count',
      },
    ]);

    const learningEnterprise = learningEnterpriseCount.length > 0 ? learningEnterpriseCount[0].count : 0;

    // 使用聚合查询计算已完成培训的企业数
    const completedEnterpriseCount = await ctx.model.Employee.aggregate([
      {
        $match: {
          EnterpriseID: { $in: EnterpriseIds },
          status: 1,
          enable: true,
          createAt: { $lt: yearEndTime },
        },
      },
      {
        $lookup: {
          from: 'certificate',
          localField: '_id',
          foreignField: 'employeeId',
          as: 'certificates',
          pipeline: [
            {
              $match: {
                trainingType: { $ne: 2 },
                issuanceTime: { $exists: true, $gte: yearStartTime, $lt: yearEndTime },
              },
            },
          ],
        },
      },
      {
        $match: {
          'certificates.0': { $exists: true },
        },
      },
      {
        $group: {
          _id: '$EnterpriseID',
        },
      },
      {
        $count: 'completedCount',
      },
    ]);

    const completedEnterprise = completedEnterpriseCount.length > 0 ? completedEnterpriseCount[0].completedCount : 0;

    const totalEnterprise = EnterpriseIds.length;
    const EnterpriseTrainingStatistics = {
      total: totalEnterprise,
      notStarted: totalEnterprise - learningEnterprise - completedEnterprise,
      learning: learningEnterprise,
      completed: completedEnterprise,
    };

    const time = new Date() - startTime;
    console.log('企业培训统计耗时=====', time);
    return EnterpriseTrainingStatistics;
  }
  // 根据考试大纲id获取培训详情
  // async getTraningByExamSyllabus(examSyllabusId, trainingStatus) {
  //   const { ctx } = this;
  //   if (!examSyllabusId) {
  //     throw new Error('缺少examSyllabusId');
  //   }
  //   if (trainingStatus && ![ '0', '1', '2' ].includes(trainingStatus)) {
  //     throw new Error('trainingStatus参数错误');
  //   }
  //   const training = await ctx.model.PersonalTraining.findOne({ examSyllabusId, status: 1, trainingType: { $ne: 2 } });
  // }
}

module.exports = ExamSyllabusService;
