const uploadFileAdminController = require('../controller/manage/uploadFile');
const uploadFileApiController = require('../controller/api/uploadFile');

module.exports = (options, app) => {

  return async function uploadFileRouter(ctx, next) {

    Object.assign(app.config.jk_UploadFile, options);
    const pluginConfig = app.config.jk_UploadFile;
    await app.initPluginRouter(ctx, pluginConfig, uploadFileAdminController, uploadFileApiController, next);
    if (ctx.originalUrl.indexOf('/api/upload/ueditor') < 0) {
      await next();
    }

  };

};
