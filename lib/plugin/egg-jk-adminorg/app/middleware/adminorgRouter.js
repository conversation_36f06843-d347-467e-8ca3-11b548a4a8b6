
const adminorgApiController = require('../controller/api/adminorg');
const adminorgAdminController = require('../controller/manage/adminorg');

module.exports = (options, app) => {

  return async function adminorgRouter(ctx, next) {

    const pluginConfig = app.config.jk_adminorg;
    await app.initPluginRouter(ctx, pluginConfig, adminorgAdminController, adminorgApiController);
    await next();

  };

};
