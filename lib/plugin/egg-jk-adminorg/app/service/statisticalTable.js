/*
 * @Author: doramart
 * @Date: 2019-06-24 13:20:49
 * @Last Modified by: doramart
 * @Last Modified time: 2019-09-23 15:35:57
 */


const Service = require('egg').Service;
const path = require('path');
const { siteFunc } = require('@utils');

// general是一个公共库，可用可不用
const {
  _list,
  _item,
  _count,
  _create,
  _update,
  _removes,
  _removeAll,
} = require(path.join(process.cwd(), 'app/service/general'));


class StatisticaltablesService extends Service {

  async find(payload, {
    query = {},
    searchKeys = [],
    populate = [],
    files = null,
    sort = {},
  } = {}) {

    const listdata = _list(this.ctx.model.StatisticalTable, payload, {
      query,
      searchKeys,
      populate,
      files,
      sort,
    });
    return listdata;

  }


  async count(params = {}) {
    return _count(this.ctx.model.StatisticalTable, params);
  }

  async create(payload) {
    return _create(this.ctx.model.StatisticalTable, payload);
  }

  async removes(res, values, key = '_id') {
    return _removes(res, this.ctx.model.StatisticalTable, values, key);
  }

  async removeAll() {
    return _removeAll(this.ctx.model.StatisticalTable);
  }

  async update(res, _id, payload) {
    return _update(res, this.ctx.model.StatisticalTable, _id, payload);
  }

  async item(res, params = {}) {
    // console.log('===============amdinorg');
    // console.log(res);
    // console.log(1234567890);
    // console.log(this.ctx.model)
    return _item(res, this.ctx.model.StatisticalTable, params);
  }
  async item2(res, params = {}) {
    return _item(res, this.ctx.model.StatisticalTable, params);
  }

  async sendVerifyMassage(ctx, beforupdate, updateRes) {
    try {
      console.log('%s进入发送审核短信方法', beforupdate.adminUserId.userName);
      beforupdate.isactive = parseInt(beforupdate.isactive);
      updateRes.isactive = parseInt(updateRes.isactive);
      const apiName = 'SendSms';
      const requestOption = { method: 'POST' };
      let TemplateCode = '';
      const TemplateParam = { userName: beforupdate.adminUserId.userName, company: updateRes.cname };
      if (updateRes.isactive === 1) {
        // 发送过通短信息
        TemplateCode = 'SMS_198931224';
      } else if (updateRes.isactive === 2) {
        // 发送未通过短信息
        TemplateCode = 'SMS_198917869';
        TemplateParam.message = updateRes.message;
      }

      const params = {
        TemplateCode,
        TemplateParam: JSON.stringify(TemplateParam),
        PhoneNumbers: beforupdate.adminUserId.phoneNum,
        SignName: '职业健康信息化平台',
      };

      const alires = await siteFunc.aliSMS(apiName, params, requestOption);
      console.log('阿里短信返回', alires);

    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  }

}

module.exports = StatisticaltablesService;
