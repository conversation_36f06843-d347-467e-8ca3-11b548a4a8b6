/*
 * @Author: xxn
 * @Date: 2024-4-9
 * @LastEditors: xxn
 * @LastEditTime: 2024-4-9
 * @Description: 企业车间工种点位信息表(新)
 */


module.exports = app => {
  const mongoose = app.mongoose;
  const shortid = require('shortid');
  const Schema = mongoose.Schema;
  const WorkspaceSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    EnterpriseID: { // 企业id
      type: String,
      required: true,
      ref: 'Adminorg',
    },
    serviceOrgId: { // 检测机构导入
      type: String,
      ref: 'ServiceOrg',
    },
    physicalExamOrgId: { // 体检机构导入
      type: String,
      ref: 'PhysicalExamOrg',
    },
    status: { // 是否经过企业确认
      type: String,
      enum: [ '0', '1' ], // '0'表示未确认，'1'表示已确认
      default: '0',
    },
    workshopName: { // 厂房名称
      type: String,
      default: '',
      trim: true,
    },
    workspaceName: { // 车间名称
      type: String,
      required: true,
      trim: true,
    },
    workTypeName: { // 工种名称
      type: String,
      required: true,
      trim: true,
    },
    exposedPeopleNumber: Number, // 作业(暴露)人数（手填）
    dailyProduce: String, // 生产班制
    workDays: Number, // 工种每周接触天数
    exposureHours: Number, // 工种每天接触时间/h
    employees: [{ // 员工信息
      _id: {
        type: String,
        default: shortid.generate,
      },
      employeeId: {
        type: String,
        ref: 'Employees',
      },
      confirmTransfer: { // 转岗是否经过本人确认
        type: Boolean,
        default: false,
      },
    }],
    stations: [ // 点位信息
      {
        _id: {
          type: String,
          default: shortid.generate,
        },
        stationName: String, // 点位名称
        shiftContactTime: Number, // 每班接触时间(h) 工作日写实记录
        workDayWeek: String, // 每周接触几天
        weeklyExposureHours: Number, // 每周接触时间/h
        harmFactors: [ String ], // 危害因素名称 不含总呼
        workWay: String, // 作业方式
        protectiveEquipment: String, // 个人防护用品
        protectiveFacilities: String, // 职业病防护设施
        equipCount: Number, // 设备总数
      },
    ],
  }, { timestamps: true });

  return mongoose.model('Workspace', WorkspaceSchema, 'workspace');

};

