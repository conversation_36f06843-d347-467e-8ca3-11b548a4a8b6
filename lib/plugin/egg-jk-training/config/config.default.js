exports.jk_training = {
  alias: 'training', // 插件目录，必须为英文
  pkgName: 'egg-jk-training', // 插件包名
  enName: 'jk_training', // 插件名
  name: '培训管理', // 插件名称
  description: '培训管理', // 插件描述
  adminApi: [
    {
      url: 'adminTraining/list',
      method: 'post',
      controllerName: 'list',
      details: '获取企业的管理员培训列表',
    },
    {
      url: 'adminTraining/getDetail',
      method: 'post',
      controllerName: 'getDetail',
      details: '获取培训的详细信息',
    },
    {
      url: 'adminTraining/createPersonalTraining',
      method: 'post',
      controllerName: 'createPersonalTraining',
      details: '创建个人培训记录一条',
    },
    {
      url: 'adminTraining/updatePersonalTraining',
      method: 'post',
      controllerName: 'updatePersonalTraining',
      details: '更新个人培训信息',
    },
    {
      url: 'adminTraining/delPersonalTraining',
      method: 'post',
      controllerName: 'delPersonalTraining',
      details: '删除个人培训信息',
    },
    {
      url: 'adminTraining/personalTrainingList',
      method: 'post',
      controllerName: 'personalTrainingList',
      details: '个人培训列表',
    },
    {
      url: 'adminTraining/getPersonalTraining',
      method: 'post',
      controllerName: 'getPersonalTraining',
      details: '获取个人培训记录详情',
    },
    {
      url: 'adminTraining/addTestRecord',
      method: 'post',
      controllerName: 'addTestRecord',
      details: '记录一次培训考试详情',
    },
    {
      url: 'adminTraining/getCertificateDetail',
      method: 'post',
      controllerName: 'getCertificateDetail',
      details: '获取证书详情',
    },
    {
      url: 'adminTraining/getMyInfo',
      method: 'post',
      controllerName: 'getMyInfo',
      details: '查询当前用户的基本信息',
    },
    {
      url: 'adminTraining/getCertificateCount',
      method: 'get',
      controllerName: 'getCertificateCount',
      details: '获取培训证书的批次和编号',
    },
    {
      url: 'adminTraining/addCertificate',
      method: 'post',
      controllerName: 'addCertificate',
      details: '添加证书',
    },
    {
      url: 'adminTraining/purchaseCertificate',
      method: 'post',
      controllerName: 'purchaseCertificate',
      details: '通过付费添加证书',
    },
    {
      url: 'adminTraining/payForTrainGroup',
      method: 'post',
      controllerName: 'payForTrainGroup',
      details: '团购',
    },
    {
      url: 'adminTraining/getCertificate',
      method: 'post',
      controllerName: 'getCertificate',
      details: '获取证书详情',
    },
    {
      url: 'adminTraining/myCertificateList',
      method: 'post',
      controllerName: 'myCertificateList',
      details: '获取证书列表',
    },
    {
      url: 'adminTraining/myOrderList',
      method: 'post',
      controllerName: 'myOrderList',
      details: '获取订单列表',
    },
    {
      url: 'adminTraining/obtainInvoice',
      method: 'post',
      controllerName: 'obtainInvoice',
      details: '申请发票',
    },
    {
      url: 'adminTraining/useCdk',
      method: 'post',
      controllerName: 'useCdk',
      details: '使用团购cdk',
    },
    {
      url: 'adminTraining/activateCdk',
      method: 'post',
      controllerName: 'activateCdk',
      details: '激活团购cdk',
    }, {
      url: 'adminTraining/myCdkList',
      method: 'post',
      controllerName: 'myCdkList',
      details: '获取我的团购cdk列表',
    },
    {
      url: 'adminTraining/updateCertificate',
      method: 'post',
      controllerName: 'updateCertificate',
      details: '更新培训证书图片',
    },
    // 课程学习相关api
    {
      url: 'courses/getCourseAllContent',
      method: 'get',
      controllerName: 'getCourseAllContent',
      details: '获取课程详细信息',
    },
    {
      url: 'courses/getVideoPlayAuth',
      method: 'get',
      controllerName: 'getVideoPlayAuth',
      details: '获取播放凭证',
    },
    {
      url: 'courses/getPersonalTraining',
      method: 'get',
      controllerName: 'getPersonalTraining',
      details: '获取个人进度',
    },
    {
      url: 'courses/updateCourseProgress',
      method: 'post',
      controllerName: 'updateCourseProgress',
      details: '更新学习进度',
    },
    {
      url: 'courses/updateCompleteState',
      method: 'post',
      controllerName: 'updateCompleteState',
      details: '更新所有状态',
    },
    {
      url: 'employeeTraining/list',
      method: 'post',
      controllerName: 'list',
      details: '获取员工培训列表',
    },
    {
      url: 'training/expertTrainList',
      method: 'post',
      controllerName: 'expertTrainList',
      details: '专家课程列表',
    },
    {
      url: 'training/expertCreateCourse',
      method: 'post',
      controllerName: 'expertCreateCourse',
      details: '专家创建课程',
    },
    {
      url: 'training/expertUpdateCourse',
      method: 'post',
      controllerName: 'expertUpdateCourse',
      details: '专家更新课程',
    },
    {
      url: 'training/getMyVideos',
      method: 'post',
      controllerName: 'getMyVideos',
      details: '专家获取我的所有视频',
    },
    {
      url: 'training/insertContent',
      method: 'post',
      controllerName: 'insertContent',
      details: '添加视频到course中',
    },
    {
      url: 'training/resortContent',
      method: 'post',
      controllerName: 'resortContent',
      details: '给视频排序（上移/下移）',
    },
    {
      url: 'training/deleteContent',
      method: 'post',
      controllerName: 'deleteContent',
      details: '专家删除培训视频',
    },
    {
      url: 'training/getVideoInfo',
      method: 'post',
      controllerName: 'getVideoInfo',
      details: '获取阿里+数据库视频信息',
    },
    {
      url: 'training/updateVideoInfo',
      method: 'post',
      controllerName: 'updateVideoInfo',
      details: '修改阿里+数据库视频信息',
    },
    {
      url: 'training/pullALi',
      method: 'post',
      controllerName: 'pullALi',
      details: '上传完视频同步到数据库信息',
    },
    {
      url: 'training/submitForReview',
      method: 'post',
      controllerName: 'submitForReview',
      details: '培训添加好了提交审核',
    },
    {
      url: 'training/editExpertQalification',
      method: 'post',
      controllerName: 'editExpertQalification',
      details: '上传/更新专家资质信息',
    },
    {
      url: 'training/expertQalificationList',
      method: 'get',
      controllerName: 'expertQalificationList',
      details: '资质证书列表',
    },
    {
      url: 'training/delExpertQalification',
      method: 'post',
      controllerName: 'delExpertQalification',
      details: '删除资质证书',
    },
    {
      url: 'training/expertDataStatistics',
      method: 'get',
      controllerName: 'expertDataStatistics',
      details: '获取专家管理后台主页数据概览',
    },
    {
      url: 'training/getExpertList',
      method: 'post',
      controllerName: 'getExpertList',
      details: '获取所有的专家列表',
    },
    {
      url: 'training/getUploadAuthWithOutID',
      method: 'post',
      controllerName: 'getUploadAuthWithOutID',
      details: '用于视频上传至阿里云，获取上传凭证或者超时刷新凭证账号',
    },
    {
      url: 'training/getUploadAuthWithID',
      method: 'post',
      controllerName: 'getUploadAuthWithID',
      details: '用于视频上传至阿里云，获取上传凭证或者超时刷新凭证账号ID',
    },
    {
      url: 'training/expertTrainDel',
      method: 'post',
      controllerName: 'expertTrainDel',
      details: '删除草稿课程',
    },
    // 代理相关
    {
      url: 'agent/createShareRecord',
      method: 'post',
      controllerName: 'createShareRecord',
      details: '创建分享记录',
    },
    {
      url: 'agent/getShareDetail',
      method: 'get',
      controllerName: 'getShareDetail',
      details: '获取分享课程',
    },
    {
      url: 'agent/getShareRecordList',
      method: 'get',
      controllerName: 'getShareRecordList',
      details: '获取分享课程列表',
    },
    {
      url: 'agent/getParent',
      method: 'get',
      controllerName: 'getParent',
      details: '获取上级',
    },
    {
      url: 'agent/getAgentInfo',
      method: 'get',
      controllerName: 'getAgentInfo',
      details: '获取代理中心详情',
    },
    {
      url: 'agent/getRanking',
      method: 'get',
      controllerName: 'getRanking',
      details: '获取代理中心详情',
    },
    {
      url: 'agent/addAgentRelation',
      method: 'post',
      controllerName: 'addAgentRelation',
      details: '添加代理关系',
    },
    // {
    //   url: 'agent/test',
    //   method: 'post',
    //   controllerName: 'test',
    //   details: '测试收益',
    // },
    {
      url: 'agent/getTrainSiteConfig',
      method: 'get',
      controllerName: 'getTrainSiteConfig',
      details: '获取培训站配置',
    },
  ],
  fontApi: [],
  initData: '', // 初始化数据脚本
  pluginsConfig: ` 
    exports.jk_training = {\n
        enable: true,\n        package: 'egg-jk-training',
    };\n
    `, // 插入到 plugins.js 中的配置
  defaultConfig: `
  trainingRouter:{\n
        match: [ctx => ctx.path.startsWith('/manage/adminTraining'), ctx => ctx.path.startsWith('/manage/employeeTraining'), ctx => ctx.path.startsWith('/manage/training'), ctx => ctx.path.startsWith('/manage/agent')],\n
    },\n
    `, // 插入到 config.default.js 中的配置

  // 列表查询每页显示的数量，默认为10
  // pageSize: 10,
  multipart: {
    fileSize: '50mb',
    fields: '100',
    // mode: 'stream',
    fileExtensions: [ 'docx', 'doc', 'xlsx', 'mp4', 'pdf', 'avi', 'rar' ],
  },
  // 图片上传路径
  propagateUploadPath: './app/public/upload/propagate',
  security: {
    csrf: {
      ignoreJSON: true,
    },
    // 白名单
    // domainWhiteList: ['*'],
  },
};

