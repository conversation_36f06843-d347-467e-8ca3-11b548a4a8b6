
const adminTrainingController = require('../controller/manage/adminTraining');
const agentController = require('../controller/manage/agent');
const employeeTrainingController = require('../controller/manage/employeeTraining');
const coursesController = require('../controller/manage/courses');
const expertController = require('../controller/manage/expert');

module.exports = (options, app) => {
  return async function trainingRouter(ctx, next) {
    const pluginConfig = app.config.jk_training;
    if (ctx.request.url.startsWith('/manage/adminTraining/')) {
      await app.initPluginRouter(ctx, pluginConfig, adminTrainingController);
    } else if (ctx.request.url.startsWith('/manage/employeeTraining/')) {
      await app.initPluginRouter(ctx, pluginConfig, employeeTrainingController);
    } else if (ctx.request.url.startsWith('/manage/courses/')) {
      await app.initPluginRouter(ctx, pluginConfig, coursesController);
    } else if (ctx.request.url.startsWith('/manage/training/')) {
      await app.initPluginRouter(ctx, pluginConfig, expertController);
    } else if (ctx.request.url.startsWith('/manage/agent/')) {
      await app.initPluginRouter(ctx, pluginConfig, agentController);
    }
    await next();

  };

};
