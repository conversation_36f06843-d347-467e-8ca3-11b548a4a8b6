module.exports = app => {
  const mongoose = app.mongoose;
  const shortid = require('shortid');
  const Schema = mongoose.Schema;
  // 如果值存在并且为文件路径，就转化为文件名
  const setFileUrl = v => {
    if (v && v.indexOf('/') > -1) {
      return v.split('/').pop();
    } else if (v) {
      return v;
    }
  };

  const trainSiteConfigSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    siteName: { // 网站名称
      type: String,
      default: '',
    },
    ogTitle: { // 标题
      type: String,
      default: '',
    },
    siteDiscription: { // 网站描述
      type: String,
      default: '',
    },
    siteHelpFile: { // 帮助中心地址
      type: String,
      default: '',
      set: setFileUrl,
    },
    miniLogo: { // 小logo
      type: String,
      default: '',
      set: setFileUrl,
    },
    longLogo: { // logo
      type: String,
      default: '',
      set: setFileUrl,
    },
    linkList: [{
      title: String, // 标题
      _id: {
        type: String,
        default: shortid.generate,
      },
      type: {
        type: String, // 类型
        default: 'url',
        enum: [ 'url', 'tel' ],
      },
      content: [ // 内容
        {
          _id: {
            type: String,
            default: shortid.generate,
          },
          name: String, // 名称
          url: String, // 链接
          phoneNum: String, // 电话号码
          codeImg: {
            type: String,
            default: '',
            set: setFileUrl,
          }, // 二维码
        },
      ],
    }],
    officialAccount: { // 公众号二维码
      type: String,
      default: '',
      set: setFileUrl,
    },
    wxMiniProgram: { // 小程序二维码
      type: String,
      default: '',
      set: setFileUrl,
    },
    siteKeywords: String, // 关键字
    siteAltKeywords: String, // 标签内的alt关键字
    registrationNo: { // 网站登记号
      type: String,
      default: '',
    },
    // 是否开启代理功能
    isAgent: {
      type: Boolean,
      default: false,
    },
    // 默认代理方案
    defaultAgent: {
      type: String,
      ref: 'AgentCommissionRate',
    },
    // 推广中心提示内容
    promotionCenterTips: {
      type: String,
      default: '',
    },
  });

  return mongoose.model('TrainSiteConfig', trainSiteConfigSchema, 'trainSiteConfig');
};
