

module.exports = app => {
  const shortid = require('shortid');
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;

  const roleSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    // dept: String,
    formData: [{
      _id: {
        type: String,
        default: shortid.generate,
      },
      role: { type: String },
      userId: [{ type: Array }],
      alisa: { type: String },
    }],
    EnterpriseID: { type: String },
  });
  return mongoose.model('Role', roleSchema, 'roles');
};
