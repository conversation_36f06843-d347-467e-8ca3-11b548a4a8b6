/**
 * 专家-资质信息表
 */

module.exports = app => {
  const mongoose = app.mongoose;
  const shortid = require('shortid');
  const Schema = mongoose.Schema;

  const ExpertQalificationsSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    userID: {
      type: String,
      ref: 'User',
      default: '',
    },
    name: { // 资质证书名称
      type: String,
      default: '',
    },
    NO: { // 资质证书编号
      type: String,
      trim: true,
      require: true,
      unique: true,
    },
    img: { // 资质图线上路径
      type: String,
      default: '',
      trim: true,
    },
    level: { // 资质等级
      type: String,
      default: '',
    },
    validTime: { // 有效期
      type: Date,
      require: true,
    },
    status: { // 状态 暂时没啥用 备用的
      type: Number,
      default: 1,
      emun: [ 0, 1 ], // 0已删除 1正常
      set: val => Number(val),
    },
  }, {
    timestamps: true,
  });


  return mongoose.model('ExpertQalifications', ExpertQalificationsSchema, 'expertQalifications');

};

