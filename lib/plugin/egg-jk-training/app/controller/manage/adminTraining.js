// 管理员培训
const fs = require('fs'); // 引入fs模块
const AdminTrainingController = {
  // adminTraining列表
  async list(ctx) {
    const data = ctx.request.body;
    const res = await ctx.service.adminTraining.getList(data);
    ctx.helper.renderSuccess(ctx, {
      data: res,
      message: '数据获取成功',
      status: 200,
    });
  },
  // adminTraning详情
  async getDetail(ctx) {
    const data = ctx.request.body;
    const res = await ctx.service.adminTraining.getDetail(data);
    ctx.helper.renderSuccess(ctx, {
      data: res,
      message: '数据获取成功',
      status: 200,
    });
  },
  // 获取个人某个培训记录详情
  async getPersonalTraining(ctx, app) {
    try {
      const data = ctx.request.body;
      let res;
      if (app.config.branch === 'ah') {
        res = await ctx.service.personalTraining.getOne_ah(data);
      } else {
        res = await ctx.service.personalTraining.getOne(data);
      }
      // await this.updateCompletedEnterprise_new(ctx, res.detail._id);
      ctx.helper.renderSuccess(ctx, {
        data: res,
        message: '数据获取成功',
      });
    } catch (e) {
      ctx.helper.renderFail(ctx, {
        message: e.message || '数据获取失败',
      });
    }
  },
  // 个人培训列表，带分页
  async personalTrainingList(ctx) {
    const data = ctx.request.body;
    const res = await ctx.service.personalTraining.getList(data);
    ctx.helper.renderSuccess(ctx, {
      data: res,
      message: '数据获取成功',
      status: 200,
    });
  },
  // 创建个人培训记录
  async createPersonalTraining(ctx) {
    const data = ctx.request.body;
    data.trainingType = +data.trainingType;
    if (typeof data.courses === 'string') {
      data.courses = JSON.parse(data.courses);
    }
    let res;
    if (data.trainingType === 1) {
      res = await ctx.service.personalTraining.create1(data);
    } else if (data.trainingType === 2) {
      res = await ctx.service.personalTraining.create2(data);
    } else if (data.trainingType === 3) {
      res = await ctx.service.personalTraining.create3(data);
    }
    if (res && res._id) {
      // 返回数据
      ctx.helper.renderSuccess(ctx, {
        data: res,
        message: '数据添加成功',
        status: 200,
      });
    } else {
      ctx.body = {
        data: res,
        message: '数据添加失败',
        status: 400,
      };
    }
  },
  // 更新个人培训记录
  async updatePersonalTraining(ctx) {
    const data = ctx.request.body;
    const res = await ctx.service.personalTraining.update(data);
    ctx.helper.renderSuccess(ctx, {
      data: res,
      message: '更新成功',
      status: 200,
    });
  },
  // 删除个人培训记录一条
  async delPersonalTraining(ctx) {
    const _id = ctx.request.body._id;
    const res = await ctx.service.personalTraining.delById(_id);
    ctx.helper.renderSuccess(ctx, {
      data: res,
      message: '操作成功',
      status: 200,
    });
  },
  // 记录一次培训考试详情
  async addTestRecord(ctx, app) {
    const data = ctx.request.body;
    const resMsg = await ctx.service.personalTraining.addTestRecord(data);
    if (resMsg === '培训完成' && app.config.branch !== 'ah') {
      if (data.personalTrainingId) {
        // await this.updateCompletedEnterprise(ctx, data.personalTrainingId);
        // await this.updateCompletedEnterprise_new(ctx, data.personalTrainingId);
        // 增加一条培训记录到企业培训列表中
        ctx.service.personalTraining.addPropagate(data.personalTrainingId);
      }
    }
    ctx.helper.renderSuccess(ctx, {
      message: resMsg,
      status: 200,
    });
  },
  // 查询当前用户的基本信息
  async getMyInfo(ctx) {
    const data = ctx.request.body;
    const myInfo = await ctx.service.personalTraining.findMyInfo(data);
    // if (myInfo && myInfo.headImg) myInfo.headImg = `/static${app.config.upload_http_path}/${data.EnterpriseID}/${myInfo.headImg}`;
    ctx.helper.renderSuccess(ctx, {
      data: myInfo,
      message: '数据获取成功',
      status: 200,
    });
  },
  // 获取培训证书的批次和编号
  async getCertificateCount(ctx) {
    const params = ctx.request.query;
    const res = await ctx.service.certificate.getCount(params);
    ctx.helper.renderSuccess(ctx, {
      data: res,
      message: '数据获取成功',
      status: 200,
    });
  },
  // 获取证书详情
  async getCertificate(ctx, app) {
    const params = ctx.request.body;
    const res = await ctx.service.certificate.findOne(params);
    let data;
    if (res && res.img && res.img.length < 30) {
      data = JSON.parse(JSON.stringify(res));
      data.img = `/static${app.config.certificate_http_path}/${res.img}`;
    }
    if (res.type === 3) { // 员工培训 获取最后一次的考试成绩
      const testRecord = await ctx.model.TestRecord.find(
        { personalTrainingId: res.personalTrainingId },
        { resultStatistics: 1 }
      ).sort({ createdAt: -1 });
      if (testRecord.length) {
        if (!data) data = JSON.parse(JSON.stringify(res));
        data.testRecord = testRecord[0];
      }
    }
    const resData = JSON.parse(JSON.stringify(data || res));
    resData.branch = app.config.branch;
    ctx.helper.renderSuccess(ctx, {
      data: resData,
      message: '数据获取成功',
      status: 200,
    });
  },
  // 添加证书,申请证书
  async addCertificate(ctx) {
    const data = ctx.request.body;
    console.log('创建证书的', data);
    if (typeof data.trainingDetail === 'string') {
      data.trainingDetail = JSON.parse(data.trainingDetail);
      data.winner = JSON.parse(data.winner);
    }
    // 1. 创建证书
    const res = await ctx.service.certificate.create(data);
    if (res && res._id) {
      // 2. 更新个人培训记录
      await ctx.service.personalTraining.update({
        _id: res.personalTrainingId,
        certificateID: res._id,
      });
      // 返回数据
      ctx.helper.renderSuccess(ctx, {
        data: res,
        message: '操作成功，培训完成。',
        status: 200,
      });
    } else {
      ctx.helper.renderFail(ctx, {
        message: res,
        status: 400,
      });
    }
  },
  // 付费获得证书接口
  async purchaseCertificate(ctx) {
    const data = ctx.request.body;
    try {
      if (typeof data.trainingDetail === 'string') {
        data.trainingDetail = JSON.parse(data.trainingDetail);
        data.winner = JSON.parse(data.winner);
      }
      const payStatus = await ctx.service.certificate.checkPayStatus(data.personalTrainingId);
      if (!payStatus) {
        ctx.auditLog('购买成功取证时', '非法操作，支付暂未成功', 'warn');
        return ctx.helper.renderFail(ctx, {
          message: '非法操作，支付暂未成功',
          data: null,
        });
      }
      // 1. 创建证书
      const res = await ctx.service.certificate.create(data);
      if (res && res._id) {
        // 2. 更新个人培训记录
        await ctx.service.personalTraining.update({
          _id: res.personalTrainingId,
          certificateID: res._id,
        });
        // 返回数据
        ctx.helper.renderSuccess(ctx, {
          data: res,
          message: '操作成功，培训完成。',
          status: 200,
        });
      } else {
        throw res;
      }
    } catch (error) {
      ctx.auditLog('购买成功取证时', `${error}`, 'error');
      return ctx.helper.renderFail(ctx, {
        message: error,
        data: null,
      });
    }

  },
  // 获取我的证书列表 1
  async myCertificateList(ctx) {
    const data = ctx.request.body;
    const res = await ctx.service.certificate.myList(data);
    if (res && res.list) {
      ctx.helper.renderSuccess(ctx, {
        data: res,
        message: '数据获取成功',
        status: 200,
      });
    } else {
      ctx.helper.renderFail(ctx, {
        message: '数据获取失败',
        status: 400,
        data: res,
      });
    }
  },
  // 获取我的订单列表
  async myOrderList(ctx) {
    const data = ctx.request.body;
    const res = await ctx.service.certificate.myOrderList(data);
    if (res && res.list) {
      ctx.helper.renderSuccess(ctx, {
        data: res,
        message: '数据获取成功',
        status: 200,
      });
    } else {
      ctx.helper.renderFail(ctx, {
        message: '数据获取失败',
        status: 400,
        data: res,
      });
    }
  },
  // px获取发票
  async obtainInvoice(ctx) {
    const data = ctx.request.body;
    const res = await ctx.service.certificate.obtainInvoice(data);
    if (res) {
      ctx.helper.renderSuccess(ctx, {
        data: res,
        message: '发票申请成功',
        status: 200,
      });
    } else {
      ctx.helper.renderFail(ctx, {
        message: '发票获取失败',
        data: res,
      });
    }
  },

  // 更新培训证书图片 1
  async updateCertificate(ctx, app) {
    const data = ctx.request.body;
    if (data._id) {
      const img = data.img;
      const imgName = data.number || new Date().getTime() + '';
      if (img) data.img = `${imgName}.png`;
      const res = await ctx.service.certificate.update({
        _id: data._id,
        img: data.img,
      });
      if (res.ok === 1) {
        if (data.img) {
          this.handlePic(app.config.certificate_path, imgName, img, ctx);
        }
        ctx.helper.renderSuccess(ctx, {
          message: '数据跟新成功',
          data: res,
          status: 200,
        });
      } else {
        ctx.helper.renderFail(ctx, {
          message: '数据跟新失败',
          status: 400,
        });
      }
    }
  },
  // 把base64转成图片存放 1
  async handlePic(basePath, pic_name, base_64_url, ctx) {
    if (!fs.existsSync(basePath)) fs.mkdirSync(basePath);
    // 保存路径 upload_path + /EnterpriseID/ + 文件名
    const _path = `${basePath}/${pic_name}.png`;
    const base64 = base_64_url.replace(/^data:image\/\w+;base64,/, ''); // 去掉图片base64码前面部分data:image/png;base64
    const dataBuffer = new Buffer(base64, 'base64'); // 把base64码转成buffer对象，
    // console.log('dataBuffer是否是Buffer对象：' + Buffer.isBuffer(dataBuffer)); // 输出是否是buffer对象
    fs.writeFile(_path, dataBuffer, function(err) { // 用fs写入文件
      if (err) {
        console.log(err);
      } else {
        ctx.auditLog('培训证书写入', `培训证书 - ${pic_name}写入成功！`, 'info');
      }
    });
  },

  // 根据personalTraining判断其所在企业是否要更新adminTraining中的completedEnterprise字段
  async updateCompletedEnterprise(ctx, personalTrainingId) {
    const personalTrainingInfo = await ctx.service.personalTraining.findOne({ _id: personalTrainingId });
    if (personalTrainingInfo.trainingType !== 1 || !personalTrainingInfo.completeState) return; // 个人培训没有完成或者不是管理员培训的不做此判断
    // 满足条件，继续
    const EnterpriseID = personalTrainingInfo.EnterpriseID;
    const adminTrainingInfo = await ctx.service.adminTraining.findOne(
      { _id: personalTrainingInfo.adminTrainingId },
      { completedEnterprise: 1, EnterpriseID: 1 }
    );

    if (personalTrainingInfo.roles.length && // 是管理人员
      adminTrainingInfo && adminTrainingInfo.EnterpriseID.includes(EnterpriseID) &&
      !adminTrainingInfo.completedEnterprise.includes(EnterpriseID)) {
      const leader = [ 'GroupLeader', 'VicGroupLeader' ], // roles中的组长和副组长
        manager = [ 'MajorManeger', 'PartTimeManeger' ]; // roles中的管理人员
      const intersection1 = personalTrainingInfo.roles.filter(v => leader.includes(v));
      const intersection2 = personalTrainingInfo.roles.filter(v => manager.includes(v));

      let updateFlag = false;
      if (intersection1.length && intersection2.length) {
        // 1 该用户既是（副）组长也是(兼职)管理人员
        updateFlag = true;
      } else if (intersection1.length || intersection2.length) {
        // 2 该用户是（副）组长或者是(兼职)管理人员
        // 查找有没有其他人完成了培训，其他人又是什么身份
        const others = await ctx.service.personalTraining.findMany(
          {
            EnterpriseID,
            adminTrainingId: personalTrainingInfo.adminTrainingId,
            _id: { $ne: personalTrainingInfo._id },
            completeState: true,
          },
          { roles: 1 }
        );
        console.log('查找到同个培训已完成的人：', others);
        if (others && others.length) { // 找到了已完成的人
          let hasSome = false;
          const targetArr = intersection1.length ? manager : leader;
          others.forEach(ele => {
            if (ele.roles.filter(v => targetArr.includes(v)).length) hasSome = true;
          });
          if (hasSome) updateFlag = true;
        }
      }
      if (updateFlag) {
        // 更新adminTraining, 添加完成的企业
        const newCompletedEnterprise = [ ...adminTrainingInfo.completedEnterprise, EnterpriseID ];
        await ctx.service.adminTraining.update({
          _id: adminTrainingInfo._id,
          completedEnterprise: newCompletedEnterprise,
        });
        ctx.auditLog('管理员培训完成', `名为 ${adminTrainingInfo.name} 的管理员培训完成。`, 'info');
      }
    } else {
      console.log('不是管理人员，或者该企业已经有人完成了培训。');
    }
  },


  // 修改后的updateCompletedEnterprise，判断企业是否完成某个培训
  async updateCompletedEnterprise_new(ctx, personalTrainingId) {
    const personalTrainingInfo = await ctx.service.personalTraining.findOne({
      _id: personalTrainingId,
    });
    if (personalTrainingInfo.trainingType !== 1 || !personalTrainingInfo.completeState) return;
    // console.log(personalTrainingInfo);
    // const EnterpriseID = ctx.session.adminUserInfo.EnterpriseID;
    // 查询这个人所涉及的所有企业
    const train = await ctx.model.AdminTraining.findOne({
      _id: personalTrainingInfo.adminTrainingId,
    }, {
      EnterpriseID: 1,
    });
    const companys = await ctx.model.Adminorg.find({
      adminArray: {
        $in: [ personalTrainingInfo.adminUserId ],
      },
      _id: {
        $in: train.EnterpriseID,
      },
    }, {
      adminArray: 1,
      _id: 1,
    });
    if (!companys || !companys.length) {
      ctx.logger.error(new Error('adminuserid查询无企业' + personalTrainingInfo.adminUserId));
      return;
    }
    for (let i = 0; i < companys.length; i++) {
      const company = companys[i];
      if (!company.adminArray || !company.adminArray.length) continue;
      // 查询这个企业所有没作废的完成的培训记录
      const personTrains = await ctx.model.PersonalTraining.find({
        adminUserId: {
          $in: company.adminArray,
        },
        adminTrainingId: personalTrainingInfo.adminTrainingId,
        completeState: true,
        status: true,
      }, {
        EnterpriseID: 1,
        completeState: 1,
        userId: 1,
        adminTrainingId: 1,
      });
      if (!personTrains || !personTrains.length) continue;

      // 查询这个企业所有培训的管理员负责人
      const roles = await ctx.model.Roles.findOne({
        EnterpriseID: company._id,
      });
      const userIDSet = new Set(); // 去重
      for (let i = 0; i < roles.formData.length; i++) {
        const formData = roles.formData[i];
        for (let k = 0; k < formData.userId.length; k++) {
          const employeeId = formData.userId[k].slice(-1)[0];
          const user = await ctx.model.Employee.findOne({
            _id: employeeId,
          }, {
            _id: 1,
            userId: 1,
          });
          userIDSet.add(user.userId);
        }
      }

      // 比对培训记录和管理员负责人的userId
      let allCompleteMark = true;
      for (const userId of userIDSet) {
        let hasId = false;
        for (let j = 0; j < personTrains.length; j++) {
          if (personTrains[j].userId === userId) {
            hasId = true;
            if (!personTrains[j].completeState) {
              allCompleteMark = false;
              break;
            }
          }
        }
        if (!allCompleteMark || !hasId) {
          allCompleteMark = false;
          break;
        }
      }

      if (allCompleteMark) {
        // 完成了
        console.log('完成了');
        await ctx.model.AdminTraining.updateOne({
          _id: personalTrainingInfo.adminTrainingId,
        }, {
          $addToSet: {
            completedEnterprise: company._id,
          },
        });
      } else {
        // 没完成，给他删了
        console.log('没完成，给他删了');
        await ctx.model.AdminTraining.updateOne({
          _id: personalTrainingInfo.adminTrainingId,
        }, {
          $pull: {
            completedEnterprise: company._id,
          },
        }, {
          multi: true,
        });
      }
    }

    return;
  },
  // 使用团购码
  async useCdk(ctx) {
    const data = ctx.request.body;
    try {
      const res = await ctx.service.trainingGroupPurchase.useCdk(data);
      if (res === 'success') {
        ctx.helper.renderSuccess(ctx, {
          data: res,
          message: '兑换成功',
          status: 200,
        });
      } else {
        ctx.helper.renderFail(ctx, {
          message: res,
          status: 400,
        });
      }
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
        status: 400,
      });
    }

  },
  // 激活团购码
  async activateCdk(ctx) {
    const data = ctx.request.body;
    try {
      const res = await ctx.service.trainingGroupPurchase.activateCdk(data);
      if (res === 'success') {
        ctx.helper.renderSuccess(ctx, {
          data: res,
          message: '激活成功',
          status: 200,
        });
      } else {
        ctx.helper.renderFail(ctx, {
          message: res,
          status: 400,
        });
      }
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
        status: 400,
      });
    }

  },
  // 获取我的团购码列表
  async myCdkList(ctx) {
    const data = ctx.request.body;
    const res = await ctx.service.trainingGroupPurchase.myCdkList(data);
    if (res && res.list) {
      ctx.helper.renderSuccess(ctx, {
        data: res,
        message: '数据获取成功',
        status: 200,
      });
    } else {
      ctx.helper.renderFail(ctx, {
        message: '数据获取失败',
        status: 400,
        data: res,
      });
    }
  },
};

module.exports = AdminTrainingController;
