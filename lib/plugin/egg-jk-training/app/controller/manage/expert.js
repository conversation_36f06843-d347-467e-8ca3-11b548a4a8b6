// 管理员培训
const fs = require('fs'); // 引入fs模块
const path = require('path');
const sendToWormhole = require('stream-wormhole');
const { tools } = require('@utils');
const awaitWriteStream = require('await-stream-ready').write;
const shortid = require('shortid');
const AdminTrainingController = {

  // 专家培训列表
  async expertTrainList(ctx) {
    const { auditStatus, keyWords, price, salesStatus, size, pageCurrent, userID } = ctx.request.body;
    const query = {
      size: size ? +size : 10,
      pageCurrent: pageCurrent ? +pageCurrent : 1,
      keyWords,
      trainingType: 2,
      userID: userID || ctx.session.user._id,
    };
    if (price) query.price = price === '0' ? 0 : { $gt: 0 };
    if (salesStatus) query.salesStatus = salesStatus !== '0';
    if (auditStatus) query.auditStatus = auditStatus;
    // console.log(11111111, query);
    const data = await ctx.service.expert.myList(query);
    ctx.helper.renderSuccess(ctx, {
      data,
      message: '课程获取成功',
      status: 200,
    });
  },
  // 专家 创建课程+培训
  async expertCreateCourse(ctx, app) {
    const { config } = app;
    try {
      const parts = ctx.multipart({ autoFields: true });
      const stream = await parts();
      const id = parts.field.courseID || shortid.generate();
      const updateFlag = !!parts.field.courseID;
      if (updateFlag) {
        const count = ctx.service.course.count({ _id: id });
        if (count === 0) {
          return ctx.helper.renderFail(ctx, {
            message: '参数courseID错误',
          });
        }
      }
      const newCourse = {
        _id: id,
        name: parts.field.name.trim(),
        explain: parts.field.explain || '',
        authorID: ctx.session.user._id,
        credit: parts.field.credit,
        classHours: parts.field.classHours,
        classification: parts.field.classification ? parts.field.classification.split(',') : [],
        labels: parts.field.labels ? parts.field.labels.split(',') : [],
        allowToOpen: parts.field.allowToOpen === 'true',
        complete: parts.field.complete === 'true',
        allowComment: parts.field.allowComment === 'true',
        questionBank: parts.field.questionBank,
        price: parts.field.price,
        source: 'user',
      };
      if (parts.field.complete === 'true') newCourse.openTime = new Date();
      if (stream && stream.filename) {
        const suffixArray = stream.filename.split('.');
        const suffix = suffixArray[suffixArray.length - 1];
        const fileName = Math.random().toString(36).substr(2) + new Date().getTime() + '.' + suffix;
        const savePath = path.join(config.upload_courses_path, id);
        tools.makeEnterpriseDir(savePath);
        const writeStream = fs.createWriteStream(path.resolve(savePath, fileName));
        await awaitWriteStream(stream.pipe(writeStream));
        if (!Object.keys(parts.field).length) {
          await sendToWormhole(stream);
        }
        newCourse.cover = `/static${config.upload_courses_http_path}/${id}/${fileName}`;
      }
      if (updateFlag) { // 更新课程数据
        const res = await ctx.service.course.updateCourse(id, newCourse);
        if (res && res.ok === 1) {
          // 更新培训数据
          let adminTrainingId = await ctx.model.AdminTraining.find({ coursesID: id }, { _id: 1 });
          if (adminTrainingId && adminTrainingId.length === 1) {
            adminTrainingId = adminTrainingId[0]._id;
            const updateTrain = {
              name: newCourse.name,
              Introduction: newCourse.explain,
              completeTime: parts.field.completeTime || new Date('2050'),
              price: newCourse.price || 0,
            };
            await ctx.service.expert.updateTrain(adminTrainingId, updateTrain);
          }
          // 返回数据
          return ctx.helper.renderSuccess(ctx, {
            data: {
              result: res,
              updateData: newCourse,
            },
            message: res.nModified === 1 ? '课程更新成功' : '视频管理',
          });
        }
        return ctx.helper.renderFail(ctx, {
          message: '课程更新失败',
          data: res,
        });
      }
      const course = await ctx.service.course.createCourse(newCourse);
      if (course && course._id) {
        // 创建培训
        const adminTraining = await ctx.service.expert.createTrain({
          name: newCourse.name,
          Introduction: newCourse.explain,
          coursesID: [ course._id ],
          allowTestOnly: false,
          completeTime: parts.field.completeTime || new Date('2050'),
          needExam: false,
          requiredCoursesHours: 0, // 因为还没上传视频
          price: newCourse.price || 0,
        });
        ctx.helper.renderSuccess(ctx, {
          data: {
            adminTraining,
            course,
          },
          message: '课程创建成功',
        });
      } else {
        ctx.helper.renderFail(ctx, {
          message: '课程创建失败',
          data: course,
        });
      }
    } catch (error) {
      ctx.logger.error(error);
      console.error(error);
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  },
  // 专家判断 培训添加好了提交审核
  async submitForReview(ctx) {
    const { adminTrainingId } = ctx.request.body;
    const adminTraining = await ctx.model.AdminTraining.findOne({ _id: adminTrainingId }, { auditStatus: 1 }).populate('coursesID', 'classHours price name explain');
    if (!adminTraining) {
      return ctx.helper.renderCustom(ctx, {
        ststus: 404,
        message: '参数错误：adminTrainingId找不到',
      });
    }
    if (adminTraining.auditStatus === '1') {
      return ctx.helper.renderCustom(ctx, {
        ststus: 400,
        message: '课程正在审核中，请勿重复提交',
      });
    }
    if (adminTraining.coursesID && adminTraining.coursesID[0]) {
      const newCourse = adminTraining.coursesID[0];
      const res = await ctx.service.expert.updateTrain(adminTrainingId, {
        name: newCourse.name,
        Introduction: newCourse.explain,
        requiredCoursesHours: newCourse.classHours,
        price: newCourse.price || 0,
        auditStatus: '1',
      });
      return ctx.helper.renderSuccess(ctx, {
        data: res,
        message: '审核申请提交成功',
      });
    }
    return ctx.helper.renderCustom(ctx, {
      ststus: 400,
      message: '参数错误：course详情找不到',
    });
  },
  // 专家编辑更新培训内容
  async expertUpdateCourse(ctx) {
    const { salesStatus, _id, auditStatus } = ctx.request.body;
    const adminTrain = await ctx.model.AdminTraining.findOne({ _id }, { salesStatus: 1 });
    if (!adminTrain) {
      return ctx.helper.renderFail(ctx, {
        message: '培训找不到',
      });
    }
    const query = {};
    if (salesStatus) {
      query.salesStatus = String(salesStatus) !== '0';
      if (query.salesStatus !== adminTrain.salesStatus) { // 变更了上下架状态
        query.$push = {
          salesStatusRecord: {
            salesStatus: query.salesStatus,
            operator: await ctx.model.User.findOne({ _id: ctx.session.user._id }, { name: 1 }),
            reason: '',
            operatingPlatform: 'px',
          },
        };
      }
    }
    if (auditStatus) query.auditStatus = query.auditStatus === '1' ? '1' : '0';
    const res = await ctx.service.expert.updateTrain(_id, query);
    if (res && res.ok) {
      ctx.helper.renderSuccess(ctx, {
        data: res,
        message: '课程更新成功',
      });
    } else {
      ctx.helper.renderFail(ctx, {
        data: res,
        message: '课程更新失败',
      });
    }
  },
  // 专家删除草稿培训课程
  async expertTrainDel(ctx, app) {
    const { adminTrainingId } = ctx.request.body;
    const adminTraining = await ctx.model.AdminTraining.findOne({ _id: adminTrainingId }, { auditStatus: 1, coursesID: 1 });
    if (!adminTraining) {
      return ctx.helper.renderFail(ctx, {
        message: '找不相关培训课程',
      });
    }
    if (adminTraining.auditStatus !== '0') {
      return ctx.helper.renderFail(ctx, {
        message: '非草稿不能删除！',
      });
    }
    // 删除培训和课程
    if (adminTraining.coursesID && adminTraining.coursesID.length) {
      const courses = await ctx.model.Courses.find({ _id: { $in: adminTraining.coursesID } }, { cover: 1 });
      try {
        // 删除课程封面
        for (let i = 0; i < courses.length; i++) {
          const savePath = path.join(app.config.upload_courses_path, courses[i].cover.split('courses/')[1]);
          if (savePath) fs.unlinkSync(savePath);
        }
      } catch (err) {
        console.log(444444444, err);
      }
      await ctx.model.Courses.deleteOne({ _id: { $in: adminTraining.coursesID } });
    }
    const data = await ctx.model.AdminTraining.deleteOne({ _id: adminTrainingId });
    ctx.helper.renderSuccess(ctx, {
      data,
      message: '课程草稿删除成功',
      status: 200,
    });
  },
  // 获取我的所有视频
  async getMyVideos(ctx) {
    try {
      const { auditStatus, pageCurrent, size, keyWords } = ctx.request.body;
      const query = { uploader: ctx.session.user._id };
      if (auditStatus) query.auditStatus = auditStatus;
      if (keyWords) query.name = { $regex: new RegExp(keyWords, 'i') };
      const limit = size ? +size : 10;
      const skip = ((pageCurrent || 1) - 1) * limit;
      const list = await ctx.model.VideoInfos
        .find(query)
        .sort({ date: -1 })
        .skip(skip)
        .limit(limit);
      const totalCount = await ctx.model.VideoInfos.count(query);
      ctx.helper.renderSuccess(ctx, {
        data: {
          list,
          totalCount,
          message: '视频数据获取成功',
        },
      });
    } catch (error) {
      ctx.logger.error(error);
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  },
  // 在course末尾插入内容
  async insertContent(ctx) {
    try {
      const { _id } = ctx.request.body;
      const type = ctx.request.body.type || 'videoInfos';
      const userId = ctx.session.user._id || '';
      let backData;
      if (type === 'videoInfos') {
        const { name, VideoId, Size, author, cover, video_id, classHours } = ctx.request.body;
        ctx.auditLog('创建新的章节视频', `视频VideoId：${VideoId}\n课程id：${_id}\zn作者：${author}`, 'info');
        if (video_id && video_id.length) {
          const course = await ctx.model.Courses.findOne({ _id }, { sort: 1 });
          const video = await ctx.model.VideoInfos.findOne({ _id: video_id });
          backData = await ctx.model.Courses.updateOne({ _id }, {
            $push: {
              [type]: video_id,
              sort: {
                contentType: type,
                ID: video_id,
                sequence: course.sort.length + 1,
              },
            },
            $inc: {
              classHours: Number(video.classHours),
            },
          });
        } else {
          backData = await ctx.service.expert.insertContent(_id, type, {
            VideoId,
            Size,
            name,
            author,
            cover,
            classHours,
            uploader: userId,
          });
          ctx.auditLog(`${userId}创建新的视频`, `视频VideoId：${VideoId}\n课程id：${_id}\n`, 'info');
        }

      } else if (type === 'documents') {
        const { Description, htmlContent, name, cover, documentsUrl } = ctx.request.body;
        backData = await ctx.service.expert.insertContent(_id, type, {
          Description,
          htmlContent,
          cover,
          name,
          documentsUrl,
        });

      } else {
        ctx.helper.renderFail(ctx, {
          message: '类型不支持',
        });
      }
      ctx.helper.renderSuccess(ctx, {
        data: {
          backData,
          message: '视频上传成功',
        },
      });
    } catch (error) {
      ctx.auditLog('创建新章节失败', JSON.stringify(error), 'info');
      ctx.logger.error(error);
      console.error(error);
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  },
  // 给视频排序  上移/下移
  async resortContent(ctx) {
    try {
      const { _id, id1, id2 } = ctx.request.body;
      const back = await ctx.service.expert.resortContent(_id, id1, id2);
      ctx.helper.renderSuccess(ctx, {
        data: {
          message: 'OK',
          back,
        },
      });
    } catch (error) {
      ctx.logger.error(error);
      console.error(error);
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  },
  // 获取某视频 阿里+数据库视频信息
  async getVideoInfo(ctx) {
    const { VideoId } = ctx.request.body;
    const res = await ctx.service.videoLib.getInfo(VideoId);
    if (res) {
      ctx.helper.renderSuccess(ctx, {
        data: res,
        message: '数据获取成功',
        status: 200,
      });
    } else {
      ctx.helper.renderFail(ctx, {
        message: '数据获取失败',
        status: 400,
        data: res,
      });
    }
  },
  // 修改阿里+数据库视频信息 参数：参考运营端
  async updateVideoInfo(ctx) {
    try {
      const { data } = ctx.request.body;
      if (typeof data !== 'object') {
        ctx.helper.renderFail(ctx, {
          message: '参数错误',
        });
      }
      const res = await ctx.service.videoLib.updateALi(data);
      ctx.helper.renderSuccess(ctx, {
        data: res,
        message: '更新成功',
      });
    } catch (error) {
      ctx.logger.error(error);
      console.error(error);
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  },
  // 从阿里同步
  async pullALi(ctx) {
    try {
      const { VideoId } = ctx.request.body;
      const res = await ctx.service.videoLib.pullALi(VideoId);
      ctx.helper.renderSuccess(ctx, {
        data: res,
        message: '视频上传成功',
      });
    } catch (error) {
      ctx.logger.error(error);
      console.error(error);
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  },
  // 删除内容，先判断章节类型，不删除对应资源
  async deleteContent(ctx) {
    try {
      const { id, courseID } = ctx.request.body;
      if (!courseID || !id) {
        return ctx.helper.renderFail(ctx, {
          message: '参数不全，id, courseID为比传参。',
        });
      }
      let course = await ctx.model.Courses.findOne({ _id: courseID }, { sort: 1, videoInfos: 1 });
      if (!course) {
        return ctx.helper.renderFail(ctx, {
          message: '课程找不到。',
        });
      }
      course = JSON.parse(JSON.stringify(course));
      let sortIndex = -1;
      for (let i = 0; i < course.sort.length; i++) {
        if (course.sort[i]._id === id) {
          sortIndex = i;
          break;
        }
      }
      if (sortIndex === -1) {
        return ctx.helper.renderFail(ctx, {
          message: '该视频章节不存在',
        });
      }
      const videoID = course.sort[sortIndex].ID;
      let newSort = course.sort.filter(ele => ele._id !== id);
      // sort重新排序
      newSort = newSort.sort((a, b) => a.sequence - b.sequence);
      newSort = newSort.map((ele, i) => {
        const newEle = JSON.parse(JSON.stringify(ele));
        newEle.sequence = i + 1;
        return newEle;
      });
      // 计算学时
      const videoList = await ctx.model.VideoInfos.find({ _id: { $in: newSort.map(ele => ele.ID) } }, { classHours: 1 });
      let classHours = 0;
      videoList.forEach(ele => (classHours += ele.classHours));
      // 更新课程
      const res = await ctx.model.Courses.updateOne({ _id: courseID }, {
        $pull: { videoInfos: videoID || '' },
        $set: { sort: newSort, classHours },
      });
      ctx.helper.renderSuccess(ctx, {
        message: res && res.nModified === 1 ? '视频删除成功' : '视频删除失败',
      });
    } catch (error) {
      ctx.logger.error(error);
      console.error(error);
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  },

  // 上传、更新资质信息
  async editExpertQalification(ctx, app) {
    const { config } = app;
    try {
      const userID = ctx.session.user._id;
      if (!userID) {
        return ctx.helper.renderCustom(ctx, {
          ststus: 400,
          message: '此处上传资质照片必须要有userID',
        });
      }
      const parts = ctx.multipart({ autoFields: true });
      const stream = await parts();
      const id = parts.field._id ? parts.field._id.trim() : shortid.generate();
      const updateFlag = !!parts.field._id;
      const NO = parts.field.NO ? parts.field.NO.trim() : '';
      const level = parts.field.level ? parts.field.level.trim() : '';
      if (!updateFlag && !NO) {
        return ctx.helper.renderCustom(ctx, {
          ststus: 400,
          message: '此处上传资质照片必须要有NO',
        });
      }
      if (NO) {
        const count = await ctx.model.ExpertQalifications.findOne({ NO, status: 1 });
        if (count) {
          if (updateFlag) {
            if (count._id !== id) {
              return ctx.helper.renderCustom(ctx, {
                ststus: 400,
                message: '编号已被占用，请重新输入',
              });
            }
          } else {
            return ctx.helper.renderCustom(ctx, {
              ststus: 400,
              message: '编号已被占用，请重新输入',
            });
          }
        }
      }
      let imgPath;
      if (stream && stream.filename) {
        const suffixArray = stream.filename.split('.');
        const suffix = suffixArray[suffixArray.length - 1];
        const fileName = Math.random().toString(36).substr(2) + new Date().getTime() + '.' + suffix;
        const savePath = path.join(config.upload_path, userID);
        tools.makeEnterpriseDir(savePath);
        const writeStream = fs.createWriteStream(path.resolve(savePath, fileName));
        await awaitWriteStream(stream.pipe(writeStream));
        if (!Object.keys(parts.field).length) {
          await sendToWormhole(stream);
        }
        imgPath = `${config.static.prefix}${config.upload_http_path}/${userID}/${fileName}`;
      }
      const params = {
        userID,
        name: parts.field.name ? parts.field.name.trim() : '',
      };
      if (imgPath) params.img = imgPath;
      if (NO) params.NO = NO;
      if (level) params.level = level;
      if (parts.field.validTime)params.validTime = new Date(parts.field.validTime);
      if (updateFlag) {
        const res = await ctx.model.ExpertQalifications.updateOne({ _id: id }, params);
        return ctx.helper.renderSuccess(ctx, {
          data: res,
          message: '数据更新成功',
        });
      }
      const expertQalification = await ctx.model.ExpertQalifications.create(params);
      if (expertQalification && expertQalification._id) {
        ctx.helper.renderSuccess(ctx, {
          data: expertQalification,
          message: '数据保存成功',
        });
      } else {
        ctx.helper.renderFail(ctx, {
          message: '数据保存失败',
          data: expertQalification,
        });
      }
    } catch (error) {
      ctx.logger.error(error);
      console.error(error);
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  },
  // 资质证书列表
  async expertQalificationList(ctx) {
    const userID = ctx.session.user._id;
    const data = await ctx.model.ExpertQalifications.find({ userID, status: 1 });
    ctx.helper.renderSuccess(ctx, {
      data,
      message: '数据获取成功',
    });
  },
  // 删除资质证书
  async delExpertQalification(ctx) {
    const _id = ctx.request.body._id;
    const data = await ctx.model.ExpertQalifications.updateOne({ _id }, { status: 0 });
    ctx.helper.renderSuccess(ctx, {
      data,
      message: '删除成功',
    });
  },
  // 获取管理后台主页数据概览
  async expertDataStatistics(ctx) {
    const userID = ctx.session.user._id;
    const list = await ctx.model.ExpertDataStatistics.find({ userID }).sort({ date: -1 }).limit(2);
    const resData = {
      income: 0,
      views: 0,
      likes: 0,
      fans: 0,
      incomeContrast: '0%',
      viewsContrast: '0%',
      likesContrast: '0%',
      fansContrast: '0%',
    };
    if (list.length) {
      resData.income = list[0].income;
      resData.views = list[0].views;
      resData.likes = list[0].likes;
      resData.fans = list[0].fans;
    }
    if (list.length === 2) {
      resData.incomeContrast = list[1].income === 0 ? (list[0].income === 0 ? '0%' : list[0].income + '%') : parseInt((list[0].income - list[1].income) * 100 / list[1].income) + '%';
      resData.viewsContrast = list[1].views === 0 ? (list[0].views === 0 ? '0%' : list[0].views + '%') : parseInt((list[0].views - list[1].views) * 100 / list[1].views) + '%';
      resData.likesContrast = list[1].likes === 0 ? (list[0].likes === 0 ? '0%' : list[0].likes + '%') : parseInt((list[0].likes - list[1].likes) * 100 / list[1].likes) + '%';
      resData.fansContrast = list[1].fans === 0 ? (list[0].fans === 0 ? '0%' : list[0].fans + '%') : parseInt((list[0].fans - list[1].fans) * 100 / list[1].fans) + '%';
    }
    ctx.helper.renderSuccess(ctx, {
      data: resData,
      message: 'OK',
    });
  },
  // 获取所有的专家
  async getExpertList(ctx) {
    const query = ctx.request.body;
    const resData = await ctx.service.expert.getExpertList(query);
    ctx.helper.renderSuccess(ctx, {
      data: resData,
      message: 'OK',
    });
  },

  // 刷新上传凭证
  async getUploadAuthWithID(ctx) {
    const {
      videoId,
    } = ctx.request.body;
    try {
      const response = await ctx.helper.request_alivod('RefreshUploadVideo', {
        videoId,
      }, {});
      // console.log(response);
      ctx.body = {
        info: 200,
        VideoId: response.VideoId,
        UploadAddress: response.UploadAddress,
        UploadAuth: response.UploadAuth,
      };
    } catch (response) {
      console.error('ErrorCode = ' + response.code);
      console.error('ErrorMessage = ' + response.Message);
      console.error('RequestId = ' + response.RequestId);
      ctx.body = {
        info: 500,
      };
    }

  },
  // 获取上传凭证
  async getUploadAuthWithOutID(ctx) {
    const {
      Title,
      FileName,
    } = ctx.request.body;
    // console.log('Title, FileName, region, CateId, Tags', Title, FileName, region, CateId, Tags, Description);
    try {
      const params = {
        Title,
        FileName,
      };
      const response = await ctx.helper.request_alivod('CreateUploadVideo', params, {});
      // console.log(response)
      ctx.body = {
        info: 200,
        VideoId: response.VideoId,
        UploadAddress: response.UploadAddress,
        UploadAuth: response.UploadAuth,
        RequestId: response.RequestId,
      };
    } catch (response) {
      console.error('请求上传凭证出错啦');
      console.error('ErrorCode = ' + response.code);
      console.error('ErrorMessage = ' + response.Message);
      console.error('RequestId = ' + response.RequestId);
      ctx.body = {
        info: 500,
      };
    }
  },
  // 课程/视频名称去重校验
  async nameDuplication(ctx) {
    const { name, type } = ctx.request.body;
    let count = 0;
    const userID = ctx.session.user._id || '';
    if (type && type === 'video') {
      count = await ctx.model.VideoInfos.count({ uploader: userID, name });
    } else {
      count = await ctx.model.AdminTraining.count({ userID, name });
    }
    const typeName = type && type === 'video' ? '视频名称' : '课程名称';
    if (count) {
      ctx.helper.renderFail(ctx, {
        message: typeName + '已被使用',
      });
    } else {
      ctx.helper.renderSuccess(ctx, {
        message: typeName + '去重校验成功',
      });
    }
  },

};

module.exports = AdminTrainingController;
