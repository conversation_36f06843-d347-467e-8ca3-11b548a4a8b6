const Service = require('egg').Service;
const moment = require('moment');
const path = require('path');
const fs = require('fs');
const PizZip = require('pizzip');
const qrcode = require('qrcode');
const Docxtemplater = require('docxtemplater');
// const mkdirp = require('mkdirp');
const ImageModule = require('docxtemplater-image-module-free-norepeat');
class CertificateService extends Service {

  // 生成培训证书pdf文件
  async createCertificatePdf(data) { // data就是证书详情
    try {
      const { ctx, app, config } = this;
      let sex = ''; // 性别
      if (data.winner && data.winner.IDNum && data.winner.IDNum.length === 18) {
        sex = data.winner.IDNum.charAt(16) % 2 === 0 ? '女' : '男'; // 获取身份证号码的第17位，并根据其奇偶性判断性别
      }
      // 获取培训开始时间
      let startTime = ''; // 培训开始时间
      if (data.personalTrainingId) {
        startTime = await ctx.model.PersonalTraining.findOne({ _id: data.personalTrainingId }, { createdAt: 1 });
      }
      let trainingDura = 0;
      if (data.trainingDetail && data.trainingDetail.time) {
        for (let i = 0; i < data.trainingDetail.coursesList.length; i++) {
          trainingDura = trainingDura + data.trainingDetail.coursesList[i].classHours;
        }
      }
      if (!data.trainingType) data.trainingType = 1;
      let wordData = { // 公开课培训
        number: data.number || (data.EnterpriseID + '-' + new Date().getTime()),
        name: data.winner ? data.winner.name : '',
        trainingName: data.trainingDetail ? data.trainingDetail.name : '',
        trainingDura: data.trainingDetail ? trainingDura : '',
        trainingTime: data.trainingDetail && data.trainingDetail.time ? moment(data.trainingDetail.time).format('YYYY-MM-DD') : '',
        coursesList: data.trainingDetail ? data.trainingDetail.coursesList : [],
        sex: sex || '',
        companyName: data.winner ? data.winner.companyName : '',
        startTime: startTime ? moment(startTime.createdAt).format('YYYY-MM-DD') : '',
        issuanceTime: moment(data.issuanceTime || new Date()).format('YYYY-MM-DD'),
      };
      if (+data.trainingType === 2) { // 培训云课堂要加身份证号 头像和视频列表 简陋版
        wordData.IDNum = data.winner.IDNum;
        wordData.videoList = [];
        for (const item of wordData.coursesList) {
          const arr = await ctx.model.Courses.findOne({ _id: item.courseId }).populate([{
            path: 'videoInfos',
            select: 'name classHours',
          }]);
          wordData.videoList = wordData.videoList.concat(arr.videoInfos);
          wordData.videoList = JSON.parse(JSON.stringify(wordData.videoList));
          wordData.totalClasshours = 0; // 总学时
          for (let i = 0; i < wordData.videoList.length; i++) {
            if (i % 2 === 0) {
              wordData.videoList[i].isLeft = true;
            } else {
              wordData.videoList[i].isRight = true;
            }
            wordData.totalClasshours += wordData.videoList[i].classHours;
          }
        }
        // wordData.headImg = 'app/public/images/Worker.png';//测试用
        wordData.headImg = data.winner.headImg ? app.config.upload_path + data.winner.headImg.split('/enterprise')[1] : '';
      }
      // if (wordData.coursesList) {
      //   for (let i = 0; i < wordData.coursesList.length; i++) {
      //     wordData.coursesList[i].courseNo = i + 1;
      //   }
      //   if (wordData.coursesList.length < 6) {
      //     const len = wordData.coursesList.length;
      //     for (let i = 0; i < 6 - len; i++) {
      //       wordData.coursesList.push({
      //         courseNo: ' ',
      //         name: ' ',
      //         classHours: ' ',
      //       });
      //     }
      //   }
      // }
      if (data.trainingType === 1) { // 管理员培训
        let headImg = '';
        if (data.winner.headImg) {
          if (data.winner.headImg.split('/enterprise')[1]) {
            headImg = app.config.upload_path + data.winner.headImg.split('/enterprise')[1];
          } else {
            headImg = path.resolve(app.config.upload_path, data.EnterpriseID, data.winner.headImg);
          }
        }
        ctx.auditLog('培训证书生成PDF === 头像：', headImg, 'info');
        wordData = {
          ...wordData,
          headImg,
          // headImg: app.config.certificate_path + '/1657250637605.png',
          IDNum: data.winner.IDNum,
          companyName: data.winner.companyName || '',
          trainingType: '职业卫生管理人员',
          unit: data.unit,
          effectiveTime: moment(data.effectiveTime || new Date(new Date(wordData.issuanceTime).getTime() + 1000 * 60 * 60 * 24 * 365 * 3)).format('YYYY-MM-DD'), // 有效期
        };
        if (app.config.branch === 'ah' && data.certificateType) {
          wordData.trainingType = data.certificateType;
        }
      } else if (data.trainingType === 3) { // 员工培训
        let testRecord = await ctx.model.TestRecord.find(
          { personalTrainingId: data.personalTrainingId },
          { resultStatistics: 1 }
        ).sort({ createdAt: -1 });
        testRecord = testRecord.length ? testRecord[0].resultStatistics.actualScore : '/'; // 获取最后一次的考试成绩
        let totalHours = 0;
        for (let i = 0; i < data.trainingDetail.coursesList.length; i++) {
          const item = data.trainingDetail.coursesList[i];
          totalHours += item.classHours;
        }
        wordData = {
          ...wordData,
          companyName: data.winner.companyName || '',
          year: new Date(wordData.issuanceTime).getFullYear(),
          // totleClassHours: data.trainingDetail.coursesList.reduce((pre, next) => pre.classHours + next.classHours, { classHours: 0 }),
          totleClassHours: totalHours,
          testRecord,
        };
        delete wordData.coursesList;
      }
      if (data.forTrainrole) {
        if (data.forTrainrole === 'responsible') wordData.forTrainrole = '主要负责人';
        if (data.forTrainrole === 'management') wordData.forTrainrole = '职业卫生管理员';
        if (data.forTrainrole === 'laborer') wordData.forTrainrole = '劳动者';
      }
      let templateFile = [ '管理员培训证书.docx', '公开课培训证书.docx', '员工培训证书.docx' ][data.trainingType - 1]; // 模板
      if (config.branch === 'gx' && data.trainingType === 2) {
        wordData.companyName = data.winner.companyName || '';
        templateFile = '广西公开课培训证书.docx';
      }
      // if (config.branch === 'hf') {
      //   wordData.companyName = data.winner.companyName || '';
      //   templateFile = '合肥公开课培训证书.docx';
      // }
      if (config.branch === 'xhl') {
        if (data.trainingType === 2) {
          wordData.companyName = data.winner.companyName || '';
          templateFile = '杏花岭公开课培训证书.docx';
        } else if (data.trainingType === 1) {
          wordData.unit = '太原市杏花岭区卫生监督所';
          templateFile = '杏花岭管理员培训证书.docx';
        }
      }
      const res = await this.fillCertificateWord(templateFile, wordData); // 填充模板
      // word生成成功了
      if (res.code === 200 && res.data.staticName) {
        // 生成pdf
        const wordFile = res.data.staticName;
        const fileName = wordFile.split('.docx')[0];
        const configFilePath = app.config.certificate_path;
        const data = await ctx.curl(app.config.coolHost + '/cool/convert-to/pdf', {
          files: path.resolve(configFilePath, wordFile),
          data: {
            Format: 'pdf',
          // PDFVer:'PDF/A-1b'
          },
          timeout: 300000,
          method: 'POST',
        });
        if (data.data) {
          await fs.writeFileSync(path.resolve(configFilePath, fileName + '.pdf'), data.data);
          fs.unlink(path.resolve(configFilePath, wordFile), err => {
            if (err) {
              console.log(err);
              return '';
            }
            // console.log('删除docx原文件成功');
          });
          return fileName + '.pdf';
        }
      }
      return '';
    } catch (error) {
      this.ctx.auditLog('培训证书生成失败PDF', JSON.stringify(error), 'error');
      console.log('培训证书生成失败PDF', error);
      return '';
    }
  }

  // 填充证书word模板文件通用方法
  async fillCertificateWord(templateFile, wordData) {
    let res = {};
    try {
      const fileName = Math.random().toString(36).substr(2) + new Date().getTime(); // 采用随机名
      const { app } = this;
      // await mkdirp(path.resolve(app.config.report_template_path));
      const content = fs.readFileSync(path.resolve(app.config.report_template_path, templateFile), 'binary');
      const zip = new PizZip(content);
      const doc = new Docxtemplater();
      if (wordData.forTrainrole) { // 培训网站过来的的时候才加二维码
        // 直接调用qrcode生成data._id的二维码，用于填充到word中,证书防伪二维码部分
        const qrCodeData = this.ctx.app.config.domainNames.px + '/certificate?number=' + wordData.number;
        const qrCodeOptions = {
          errorCorrectionLevel: 'M',
          type: 'image/png',
          margin: 1,
          rendererOpts: {
            quality: 0.3,
          },
        };
        const qrCodeImage = await qrcode.toDataURL(qrCodeData, qrCodeOptions);
        const qrCodeBase64 = qrCodeImage.split(',')[1];
        wordData.qrcode = qrCodeBase64;
      }
      if (wordData.headImg) { // 处理图片
        const imageModule = new ImageModule({
          centered: true,
          fileType: 'docx',
          getImage(tagValue, tagName) {
            try {
              if (tagName === 'headImg') {
                return fs.readFileSync(tagValue);
              } else if (tagName === 'qrcode') {
                return Buffer.from(tagValue, 'base64');
              }
            } catch (error) {
              console.log(44444444, error);
              // this.ctx.auditLog('培训证书头像获取不到', JSON.stringify(error), 'error');
              return '';
            }
          },
          getSize(imgBuffer, tagValue, tagName) {
            console.log(88888888, tagName, wordData.forTrainrole);
            if (tagName === 'qrcode') { return [ 80, 80 ]; }
            if (tagName === 'headImg' && wordData.forTrainrole) { return [ 150, 206 ]; }
            return [ 63, 84 ];
          },
        });
        doc.attachModule(imageModule);
      }
      doc.loadZip(zip);
      doc.setData(wordData);
      await doc.render();
      const buf = doc.getZip().generate({ type: 'nodebuffer' });
      const filePath = path.resolve(app.config.certificate_path, fileName + '.docx');
      const fileDir = path.dirname(filePath);

      // 创建目录
      fs.mkdirSync(fileDir, { recursive: true });
      // 将文件写入文件系统
      fs.writeFileSync(filePath, buf);
      // fs.writeFileSync(path.resolve(app.config.certificate_path, fileName + '.docx'), buf);
      res = {
        code: 200,
        message: '模板文件生成成功',
        data: {
          sort: '系统生成',
          fileType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
          originName: templateFile,
          staticName: fileName + '.docx',
        },
      };
    } catch (error) {
      console.log(error);
      const e = {
        message: error.message,
        name: error.name,
        stack: error.stack,
        properties: error.properties,
      };
      res = { code: 500, e, message: '请关闭正在操作的word文件' };
    }
    return res;
  }

}

module.exports = CertificateService;
