
const Service = require('egg').Service;
const moment = require('moment');
// 个人培训记录
class PersonTrainingService extends Service {
  async getList(data) {
    const { ctx } = this;
    // 获取个人培训记录列表
    const query = {
      status: true,
    };
    // if (data.keyWord) query.name = { $regex: new RegExp(data.keyWord, 'i') };
    if (data.trainingType) query.trainingType = +data.trainingType;
    if (data.EnterpriseID && query.trainingType === 3) query.EnterpriseID = data.EnterpriseID;
    if (data.employeesId) query.employeesId = data.employeesId;
    if (data.userId) {
      query.userId = data.userId;
    } else if (ctx.session.user) {
      query.userId = ctx.session.user._id;
    }
    if (data.completeState === 'false') query.completeState = false;
    if (data.completeState === 'true') query.completeState = true;
    if (typeof data.curStatus === 'number') query.completeState = !!data.curStatus;
    data.pageCurrent = data.pageCurrent ? +data.pageCurrent : 1;
    data.size = data.size ? +data.size : 10;
    console.log(88888888888, query);
    let res;
    if (data.keyWord) {
      if (query.trainingType === 3) {
        res = await ctx.model.PersonalTraining.aggregate([
          { $match: query },
          { $lookup: {
            from: 'employeesTrainingPlan',
            localField: 'employeesTrainingPlanId',
            foreignField: '_id',
            as: 'oldEmployeesTrainingPlanId',
          } },
          { $addFields: { employeesTrainingPlanId: { $arrayElemAt: [ '$oldEmployeesTrainingPlanId', 0 ] } } },
          { $match: { 'employeesTrainingPlanId.name': { $regex: data.keyWord || '' } } },
          { $sort: { createdAt: -1 } },
          { $skip: (data.pageCurrent - 1) * data.size },
          { $limit: data.size },
        ]);
      } else {
        res = await ctx.model.PersonalTraining.aggregate([
          { $match: query },
          { $lookup: {
            from: 'adminTraining',
            localField: 'adminTrainingId',
            foreignField: '_id',
            as: 'oldAdminTrainingId',
          } },
          { $addFields: { adminTrainingId: { $arrayElemAt: [ '$oldAdminTrainingId', 0 ] } } },
          { $match: { 'adminTrainingId.name': { $regex: data.keyWord || '' } } },
          { $sort: { createdAt: -1 } },
          { $skip: (data.pageCurrent - 1) * data.size },
          { $limit: data.size },
        ]);
      }
    } else {
      res = await ctx.model.PersonalTraining.find(query)
        .sort({ createdAt: -1 })
        .skip((data.pageCurrent - 1) * data.size)
        .limit(data.size)
        .populate('adminTrainingId')
        .populate('employeesTrainingPlanId');
    }

    // 获取分页信息
    const pageInfo = await this.getPageInfo('PersonalTraining', data.size, data.pageCurrent, query);
    const statistics = await this.statistics('PersonalTraining', query);


    // 查询培训中首个课程的详情
    // let newRes;
    // if (data.size <= 4) { // 只在培训首页展示的时候查询
    const newRes = JSON.parse(JSON.stringify(res));
    const len = res.length;
    for (let i = 0; i < len; i++) {
      if (res[i].courses && res[i].courses[0]) {
        newRes[i].firstCourseDetail = await this.ctx.model.Courses.findOne(
          { _id: res[i].courses[0].coursesId },
          { cover: 1, labels: 1, views: 1, likes: 1, commentLength: 1 }
        );
      }
    }
    // }
    if (newRes) {
      // 获取课程学习进度
      newRes.forEach(ele => {
        let total = 0,
          hasCompleted = 0;
        let needStudyHours = 0,
          hasStudyHours = 0;
        ele.courses.forEach(course => {
          if (course.coursePlayProgress) {
            total += course.coursePlayProgress.length;
            hasCompleted += course.coursePlayProgress.filter(prog => {
              needStudyHours += prog.classHours;
              if (prog.completeState) {
                hasStudyHours += prog.classHours;
              }
              return prog.completeState;
            }).length;
          }
        });
        ele.learningProgress = { total, hasCompleted };
        ele.studyHoursProgress = { hasStudyHours, needStudyHours };
      });
    }

    return {
      res: newRes || res,
      pageInfo,
      statistics,
    };
  }
  // 分页统计
  async getPageInfo(collection, size, pageCurrent, query = {}) {
    const total = await this.ctx.model[collection].find(query).count();
    const pageInfo = {
      total,
      size,
      pageCurrent,
    };
    return pageInfo;
  }
  // 总数统计
  async statistics(collection, query = {}) {
    const total = await this.ctx.model[collection].find(query).count();
    const completed = await this.ctx.model[collection].find({ ...query, completeState: true }).count();
    const statistics = {
      total,
      completed,
    };
    return statistics;
  }
  // 查询当前用户的基本信息
  async findMyInfo(data) {
    const { ctx, app } = this;
    const personalTrainingId = data.personalTrainingId || '';
    if (personalTrainingId) {
      const personalTrainingDetail = await ctx.model.PersonalTraining.findOne(
        { _id: personalTrainingId },
        { certificateID: 1 }
      );
      if (personalTrainingDetail && personalTrainingDetail.certificateID) {
        return '您的培训证书已申领，请勿重复提交';
      }
    }
    const EnterpriseID = data.EnterpriseID || '';
    const employeesId = data.employeesId || '';
    const res = await ctx.model.Employee.findOne(
      { _id: employeesId },
      {
        name: 1,
        IDNum: 1,
        status: 1,
        headImg: 1,
      }
    ) || {};
    if (!res) {
      ctx.auditLog('培训获取个人信息', '员工信息找不到了：employeesId', 'error');
      // return '员工信息找不到了：employeesId';
    }
    const company = await ctx.model.Adminorg.findOne(
      { _id: EnterpriseID },
      { cname: 1, regAdd: 1, districtRegAdd: 1 }
    ) || {};
    const user = await ctx.model.User.findOne({ _id: ctx.session.user._id }, { logo: 1, idNo: 1, name: 1 });
    const logo = user.logo.includes('defaultlogo') ? '' : user.logo;

    return {
      employeeId: employeesId,
      name: res.name || user.name || '',
      IDNum: res.IDNum || user.idNo || '',
      headImg: res.headImg ? `/static${app.config.upload_http_path}/${EnterpriseID}/${res.headImg}` : logo,
      companyName: company.cname,
      companyRegAdd: company.regAdd,
      districtRegAdd: company.districtRegAdd,
    };
  }
  async count(query) {
    return await this.ctx.model.PersonalTraining.count(query);
  }
  // 管理员培训 trainingType === 1
  async create1(data) {
    const { ctx } = this;
    const userId = ctx.session.user ? ctx.session.user._id : '';
    const adminUserId = data.adminUserId;
    const EnterpriseID = data.EnterpriseID || '';
    if (!adminUserId) return '找不到adminUserId';
    const docNum = await ctx.model.PersonalTraining.find({
      adminTrainingId: data.adminTrainingId,
      adminUserId,
      // EnterpriseID,
      trainingType: 1,
      status: true,
      userId,
    });
    if (docNum.length === 1) {
      return docNum[0];
    } else if (docNum.length > 1) {
      return '系统错误：23，请联系客服解决。';
    }

    // 查询当前用户的 adminUserId
    const adminUserCount = await ctx.model.AdminUser.count(
      { _id: data.adminUserId, enable: true }
    );
    if (!adminUserCount) return `userId为${userId}的用户添加管理员培训时没有找到对应的adminUser${data.adminUserId}`;
    const param = {
      employeesId: data.employeesId || '',
      EnterpriseID,
      adminUserId: data.adminUserId,
      roles: data.roles,
      adminTrainingId: data.adminTrainingId || '',
      courses: data.courses,
      trainingType: 1,
      userId,
    };
    const res = await new ctx.model.PersonalTraining(param).save();
    this.ctx.auditLog('添加个人培训计划', '当前用户进行了添加培训计划操作。', 'info');

    if (res && res._id) {
      // 查询并更新该企业在adminTraining的培训状态
      const adminTrainingInfo = await ctx.service.adminTraining.findOne(
        { _id: res.adminTrainingId },
        { incompleteEnterprise: 1, EnterpriseID: 1 }
      );
      if (!adminTrainingInfo.incompleteEnterprise.includes(EnterpriseID)) {
        await ctx.service.adminTraining.update({
          _id: adminTrainingInfo._id,
          incompleteEnterprise: [ ...adminTrainingInfo.incompleteEnterprise, EnterpriseID ],
        });
      }
    }
    return res;
  }
  // 公开课培训 trainingType === 2 时
  async create2(data) {
    const { ctx } = this;
    const userId = ctx.session.user ? ctx.session.user._id : '';
    if (!userId) return '请先登录';

    const docNum = await ctx.model.PersonalTraining.find({
      adminTrainingId: data.adminTrainingId || '',
      userId,
      trainingType: 2,
      status: true,
    });
    if (docNum.length === 1) {
      return docNum[0];
    } else if (docNum.length > 1) {
      return '系统错误：24，请联系客服解决。';
    }
    // if (docNum) return `用户${userId}的培训${data.adminTrainingId || ''}已经创建过PersonalTraining`;
    const param = {
      adminTrainingId: data.adminTrainingId,
      courses: data.courses,
      trainingType: 2,
      userId,
      // adminUserId: data.adminUserId || '',
    };
    param.courses.forEach(course => {
      if (course.coursePlayProgress && !Array.isArray(course.coursePlayProgress)) {
        course.coursePlayProgress = Object.values(course.coursePlayProgress);
      }
    });

    const res = await new ctx.model.PersonalTraining(param).save();
    ctx.auditLog('添加个人培训计划', `用户${userId}进行了添加公开课培训计划操作。`, 'info');
    return res;
  }
  // 员工培训 trainingType === 3 时
  async create3(data) {
    const { ctx } = this;
    const userId = ctx.session.user ? ctx.session.user._id : '';
    const employeesId = data.employeesId;
    const EnterpriseID = data.EnterpriseID || '';
    if (!employeesId || !EnterpriseID) return '找不到employeesId和EnterpriseID';
    const docNum = await this.count({
      employeesTrainingPlanId: data.employeesTrainingPlanId,
      employeesId,
      EnterpriseID,
      trainingType: 3,
      status: true,
    });
    if (docNum) return `用户${userId}的培训${data.employeesTrainingPlanId || ''}已经创建过PersonalTraining`;
    const param = {
      employeesId,
      EnterpriseID,
      employeesTrainingPlanId: data.employeesTrainingPlanId,
      courses: data.courses,
      trainingType: 3,
      userId,
    };
    const res = await new ctx.model.PersonalTraining(param).save();
    this.ctx.auditLog('添加个人培训计划', `用户${userId}进行了添加培训计划操作。`, 'info');
    return res;
  }

  // 根据管理员培训id获取当前用户该培训的详细信息
  async getByAdminTrainingId(adminTrainingId) {
    const detail = await this.ctx.model.PersonalTraining.findOne({
      adminTrainingId,
      adminUserId: this.ctx.session.adminUserInfo._id,
      // EnterpriseID: this.ctx.session.adminUserInfo.EnterpriseID,
      status: true,
    }).populate('adminTrainingId');
    const courses = detail ? detail.courses.map(ele => ele.coursesId) : [];
    const coursesList = detail ? await this.ctx.model.Courses.find({ _id: { $in: courses } }) : [];
    // 返回数据
    return detail ? {
      detail,
      coursesList,
    } : null;
  }
  // 根据传入的条件findOne个人培训记录 - 培训详情页
  async getOne(data) {
    const { ctx } = this;
    let detail = await this.ctx.model.PersonalTraining.findOne({
      ...data,
      status: true,
    }).populate('adminTrainingId')
      .populate('employeesTrainingPlanId')
      .populate('certificateID', 'certificateStatus img');
    if (!detail && data._id) {
      const userId = ctx.session.user ? ctx.session.user._id : '';
      if (!userId) return null;
      detail = await this.ctx.model.PersonalTraining.findOne({
        adminTrainingId: data._id,
        userId,
        status: true,
      }).populate('adminTrainingId')
        .populate('employeesTrainingPlanId')
        .populate('certificateID', 'certificateStatus img');
    }
    if (!detail) return null;
    detail = JSON.parse(JSON.stringify(detail));
    const originTrain = detail.adminTrainingId ? detail.adminTrainingId : detail.employeesTrainingPlanId;
    if (originTrain) {
      delete originTrain.EnterpriseID;
      delete originTrain.completedEnterprise;
      delete originTrain.incompleteEnterprise;
    }

    const courses = detail ? detail.courses.map(ele => ele.coursesId) : [];
    // const coursesList = detail ? await this.ctx.model.Courses.find({ _id: { $in: courses } }).populate('videoInfos') : [];
    // 获取课程及视频想请
    const coursesList = [];
    for (let i = 0; i < courses.length; i++) {
      const courseDetail = await this.ctx.service.course.getOneCourseDetail(courses[i]);
      if (courseDetail) coursesList.push(courseDetail);
    }
    // 给coursesList排序，让它跟detail中的courses一一对应
    const newCoursesList = [];
    courses.forEach((ele, index) => {
      newCoursesList[index] = coursesList.filter(course => course._id === ele)[0];
    });

    // 如果是管理员培训，获取监管端名称
    let superUser = null;
    if (detail && detail.adminTrainingId && detail.adminTrainingId.superID) {
      const userId = ctx.session.user ? ctx.session.user._id : '';
      const user = await ctx.model.User.findOne({ _id: userId }, { employeeId: 1, companyId: 1 });
      const EnterpriseID = user.companyId[user.companyId.length - 1];

      superUser = await this.ctx.model.SuperUser.findOne(
        { _id: detail.adminTrainingId.superID },
        { cname: 1, area_code: 1, regAdd: 1 }
      );
      // 查看是否角色有变化
      if (superUser && !detail.certificateID) {
        const roles = await this.findRoles(EnterpriseID, user.employeeId);
        if (roles) {
          console.log(333333, roles);
          if (roles.join('') !== detail.roles.join('')) {
            // 更新角色
            await this.update({
              _id: detail._id,
              roles,
            });
            detail.roles = roles;
          }
        }
      }
      // 更新企业在监管端的培训状态
      if (detail.completeState) this.updateCompletedEnterprise2(detail._id, EnterpriseID);
    }
    // 更新员工培训完成状态
    if (detail.employeesTrainingPlanId && detail.completeState && detail.employeesId) {
      await ctx.model.EmployeesTrainingPlan.updateOne(
        { _id: detail.employeesTrainingPlanId._id },
        { $addToSet: { completedEmployees: detail.employeesId } }
      );
    }
    // 判断培训(大考的不算)的完成状态是否正确，不正确的话修复一下
    if (!detail.completeState && detail.adminTrainingId && detail.adminTrainingId.examinationType === 1 && detail.courses.length === newCoursesList.length) {
      if ((!detail.adminTrainingId.needExam && detail.courses.every(ele => ele.completeState))
        || detail.courses.every(ele => ele.testStatus)) {
        this.update({ _id: detail._id, completeState: true });
      }
    }
    if (detail.payInfoID) {
      const payInfo = await this.ctx.model.PayInfo.findOne({ _id: detail.payInfoID });
      detail.payStatus = payInfo.payStatus;
    }
    let needStudyHours = 0,
      hasStudyHours = 0;
    detail.courses.forEach(course => {
      if (course.coursePlayProgress) {
        course.coursePlayProgress.forEach(prog => {
          needStudyHours += prog.classHours;
          if (prog.completeState) {
            hasStudyHours += prog.classHours;
          }
        });
      }
    });
    detail.studyHoursProgress = { hasStudyHours, needStudyHours };
    // 返回数据
    return detail ? {
      detail,
      coursesList: newCoursesList,
      superName: superUser ? superUser.cname : '',
      superUser,
    } : null;
  }
  async getOne_ah(data) {
    let detail = await this.ctx.model.PersonalTraining.findOne({
      ...data,
      status: true,
    }, { courses: 0 })
      .populate('examSyllabusId', 'name certificateType examDuration')
      .populate('certificateID', 'certificateStatus img');
    if (!detail) return null;
    detail = JSON.parse(JSON.stringify(detail));
    const examSyllabus = detail.examSyllabusId;
    if (!examSyllabus) return '找不到考试大纲';
    if (typeof examSyllabus !== 'object') return '考试大纲数据异常';
    delete detail.examSyllabusId;
    const testPapers = await this.ctx.model.TestPaper.find({ examSyllabusId: examSyllabus._id, status: true })
      .populate('questions');
    if (testPapers.length === 0) return '找不到试卷，请先更新考试大纲';
    const testPaper = testPapers[Math.floor(Math.random() * testPapers.length)];
    // 返回数据
    return {
      detail,
      testPaper,
      examSyllabus,
    };
  }


  async update(data = {}) {
    return await this.ctx.model.PersonalTraining.updateOne(
      { _id: data._id, status: true },
      data,
      { new: true }
    );
  }
  async delById(_id) {
    this.ctx.auditLog('取消培训计划', '当前用户进行了取消公开课培训计划操作。', 'info');
    return await this.ctx.model.PersonalTraining.findByIdAndRemove(_id);
  }
  async findOne(data = {}) {
    data.status = true;
    return await this.ctx.model.PersonalTraining.findOne(
      data,
      {
        roles: 1,
        adminTrainingId: 1,
        employeesTrainingPlanId: 1,
        EnterpriseID: 1,
        trainingType: 1,
        completeState: 1,
        adminUserId: 1,
        userId: 1,
        employeesId: 1,
      }
    );
  }
  async findMany(data = {}, options = {}) {
    data.status = true;
    return await this.ctx.model.PersonalTraining.find(
      data,
      options
    );
  }
  // 添加一次考试记录
  async addTestRecord(data) {
    const { ctx } = this;
    if (!data.resultStatistics || !data.resultStatistics.scoreLine) return '找不到考试结果';
    const addRes = await new ctx.model.TestRecord({
      personalTrainingId: data.personalTrainingId || '',
      courseId: data.courseId || '',
      testResult: data.testResult,
      resultStatistics: data.resultStatistics,
    }).save();
    // 更新个人培训记录
    const personalTraining = await ctx.model.PersonalTraining.findOne({
      _id: data.personalTrainingId,
      status: true,
    });
    if (!personalTraining) return '找不到个人培训记录';

    let completeState = false;
    if (data.courseId) { // 小考
      const newData = personalTraining.courses.map(ele => {
        if (ele.coursesId === data.courseId) {
          ele.testList.push(addRes._id);
          ele.testStatus = +data.resultStatistics.actualScore >= +data.resultStatistics.scoreLine; // 计算考试是否通过
          if (ele.testStatus) ele.completeTime = Date.now();
        }
        return ele;
      });
      // 判断是否全部课程的考试都通过了，若都通过了，则培训完成
      completeState = newData.every(ele => ele.testStatus);
      await ctx.model.PersonalTraining.update(
        { _id: data.personalTrainingId },
        { courses: newData, completeState },
        { new: true }
      );
    } else { // 大考
      completeState = +data.resultStatistics.actualScore >= +data.resultStatistics.scoreLine;
      await ctx.model.PersonalTraining.update({
        _id: data.personalTrainingId,
        status: true,
      }, {
        $push: {
          bigTestList: addRes._id,
        },
        completeState,
      }, {
        new: true,
      });
      if (this.config.branch === 'ah' && completeState) {
        this.ahCreateCertificate(personalTraining);
      }
    }
    // 返回数据
    return completeState ? '培训完成' : '考试记录添加成功';
  }

  // 安徽培训生成证书
  async ahCreateCertificate(personalTraining = {}) {
    const { ctx } = this;
    try {
      ctx.auditLog('安徽培训生成证书开始==', personalTraining, 'info');
      const employeeId = personalTraining.employeesId;
      const options = {
        returnOptions: {
          phoneNum: {
            returnPlaintext: true, // 返回明文密码
          },
          IDNum: {
            returnPlaintext: true, // 返回明文密码
          },
        },
      };
      const employee = await ctx.model.Employee.findOne({ _id: employeeId }).populate('EnterpriseID', 'cname districtRegAdd regAdd').setOptions(options);
      if (!employee) {
        ctx.auditLog('安徽培训生成证书', '找不到员工信息: ' + employeeId, 'error');
        return;
      }
      const examSyllabus = await ctx.model.ExamSyllabus.findOne({ _id: personalTraining.examSyllabusId });
      if (!examSyllabus) {
        ctx.auditLog('安徽培训生成证书', '找不到考试大纲: ' + personalTraining.examSyllabusId, 'error');
        return;
      }
      const certificateType = [ '企业负责人初次培训', '企业负责人继续教育', '职业健康管理人员初次培训', '职业健康管理人员继续教育', '劳动者上岗前培训', '劳动者在岗培训' ][examSyllabus.certificateType - 1];
      let coursesList = [];
      const trainingClass = await ctx.model.TrainingClass.findOne({ _id: personalTraining.trainingClassId }, { name: 1, coursesList: 1 }).populate('coursesList', 'name classHours');
      if (trainingClass && trainingClass.coursesList) {
        coursesList = trainingClass.coursesList.map(ele => ({
          courseId: ele._id,
          classHours: ele.classHours,
          name: ele.name,
        }));
      } else {
        ctx.auditLog('安徽培训生成证书', '找不到课程信息: ' + personalTraining.trainingClassId, 'error');
      }
      const params = {
        number: 'AH' + moment().format('YYYYMMDDHH') + Math.floor(Math.random() * 1000),
        name: examSyllabus.name,
        trainingType: 1,
        type: examSyllabus.certificateType < 3 ? 2 : (examSyllabus.certificateType < 5 ? 1 : 3),
        certificateType,
        personalTrainingId: personalTraining._id,
        unit: '安徽省卫生健康委员会',
        superID: examSyllabus.creator || '',
        certificateStatus: 2, // 审核通过
        winner: {
          employeeId,
          headImg: employee.headImg || '',
          IDNum: employee.IDNum,
          companyName: employee.EnterpriseID.cname,
          name: employee.name,

          companyRegAdd: employee.EnterpriseID.regAdd || '',
          districtRegAdd: employee.EnterpriseID.districtRegAdd || [],
        },
        trainingDetail: {
          coursesList,
          name: trainingClass ? trainingClass.name : examSyllabus.name,
          time: personalTraining.createdAt,
        },
        EnterpriseID: personalTraining.EnterpriseID,
        employeeId,
        adminUserId: personalTraining.adminUserId,
      };
      const newData = await ctx.service.certificate.create(params);
      if (newData && newData._id) {
        ctx.auditLog('安徽培训生成证书成功', newData, 'info');
        await ctx.model.PersonalTraining.updateOne({ _id: personalTraining._id }, { certificateID: newData._id });
      }
    } catch (e) {
      ctx.auditLog('安徽培训生成证书失败', e, 'error');
    }
  }

  // 增加一条记录到企业端的培训记录表
  async addPropagate(personalTrainingId) {
    const { ctx } = this;
    if (!personalTrainingId) return;
    const count = await ctx.model.Propagate.count({ personalTrainingId });
    if (count) return; // 已经创建过了
    const personalTraining = await ctx.model.PersonalTraining
      .findOne({ _id: personalTrainingId, status: true })
      .populate('adminTrainingId', 'name date createdAt coursesID requiredCoursesHours electivesCoursesHours')
      .populate('employeesTrainingPlanId', 'name date createdAt coursesID requiredCoursesHours electivesCoursesHours')
      .populate('employeesId', 'name departs');
    if (!personalTraining) return;
    let type = '劳动者培训'; // 培训类型
    const organizeDepartment = [ '监管单位', '职业卫生管理人员', '企业管理员' ]; // 组织部门
    const originTraining = personalTraining.adminTrainingId || personalTraining.employeesTrainingPlanId;
    if (personalTraining.roles && personalTraining.roles.length) {
      if (personalTraining.roles.includes('GroupLeader') || personalTraining.roles.includes('VicGroupLeader')) {
        type = '负责人培训';
      } else {
        type = '管理人员培训';
      }
    }

    let EnterpriseID = personalTraining.EnterpriseID;
    let user = {};
    if (!EnterpriseID && personalTraining.trainingType === 1) {
      const userId = ctx.session.user ? ctx.session.user._id : '';
      user = await ctx.model.User.findOne({ _id: userId }, { companyId: 1, name: 1 });
      EnterpriseID = user.companyId[user.companyId.length - 1];
    }
    const res = await new ctx.model.Propagate({
      EnterpriseID,
      createTime: Date.now(),
      type,
      hours: String(originTraining.requiredCoursesHours + originTraining.electivesCoursesHours) || '0',
      partakeDepartment: [],
      personnel: [[ personalTraining.employeesId ? personalTraining.employeesId.name : user.name || '' ]],
      content: originTraining.name,
      organizeDepartment: organizeDepartment[personalTraining.trainingType - 1],
      lecturer: '',
      implementData: originTraining.date || originTraining.createdAt,
      annex: originTraining.coursesID.length, // 也就是课程数量
      year: new Date().getFullYear() + '',
      allPeopleNumber: '1',
      personalTrainingId,
      source: 'oapi',
    }).save();
    if (res) {
      ctx.auditLog('添加企业培训记录', `personalTrainingId为 ${personalTrainingId}的培训完成。`, 'info');
      // this.createTrainingTestPaper(personalTrainingId, personalTraining);// 生成考试试卷及证明到企业端培训记录表lht+
    }
  }

  // async createTrainingTestPaper(personalTrainingId, personalTraining) { // 生成考试试卷及证明到企业端培训记录表lht+
  //   const { ctx } = this;
  //   const info = await ctx.model.PersonalTraining.findOne({ _id: personalTrainingId })
  //     .populate('adminTrainingId', 'name date createdAt coursesID requiredCoursesHours electivesCoursesHours')
  //     .populate('employeesTrainingPlanId', 'name date createdAt coursesID requiredCoursesHours electivesCoursesHours')
  //     .populate('employeesId', 'name departs');
  //   console.log('222222222222222');
  //   console.log(personalTrainingId, personalTraining, '个人记录的idddddddddddd');
  // }

  // 查询当前用户的角色
  async findRoles(EnterpriseID, employeeId) {
    const { ctx } = this;
    // const userId = ctx.session.user ? ctx.session.user._id : '';
    // const user = await ctx.model.User.findOne({ _id: userId }, { employeeId: 1, companyId: 1 });
    // const EnterpriseID = user.companyId[user.companyId.length - 1];
    // const employeeId = user.employeeId;
    if (!EnterpriseID || !employeeId) return;
    // 查询当事人的角色
    const rolesDate = await ctx.model.Roles.findOne({
      EnterpriseID,
    });
    // if (!rolesDate) return '请先设置组织架构';
    const roles = [];
    if (rolesDate && rolesDate._id && employeeId) {
      rolesDate.formData.forEach(ele => {
        ele.userId.forEach(ele2 => {
          if (ele2.includes(employeeId)) roles.push(ele.alisa);
        });
      });
    }
    return roles;
  }

  // xxn add 2021/8/16 只计算当前企业
  async updateCompletedEnterprise2(personalTrainingId, EnterpriseID) {
    if (!EnterpriseID || !personalTrainingId) return;
    const { ctx } = this;
    const personalTrainingInfo = await ctx.model.PersonalTraining.findOne(
      {
        _id: personalTrainingId,
        status: true,
      }, {
        roles: 1,
        adminTrainingId: 1,
        trainingType: 1,
        completeState: 1,
        adminUserId: 1,
      }
    );
    // 没有完成或者不是管理员培训的不做此判断
    if (personalTrainingInfo.trainingType !== 1 || !personalTrainingInfo.completeState) return;
    // 满足条件，继续
    const adminTrainingInfo = await ctx.service.adminTraining.findOne(
      { _id: personalTrainingInfo.adminTrainingId },
      { completedEnterprise: 1, EnterpriseID: 1, name: 1 }
    );

    if (adminTrainingInfo && personalTrainingInfo.roles.length && // 是管理人员
      !adminTrainingInfo.completedEnterprise.includes(EnterpriseID) && // 还没完成
      adminTrainingInfo.EnterpriseID.includes(EnterpriseID)) {

      let updateFlag = false;
      const adminOrgDetail = await ctx.model.Adminorg.findOne({ _id: EnterpriseID }, { adminArray: 1 });
      const adminArray = adminOrgDetail.adminArray || [];
      if (adminArray.length) {
        if (adminArray.length === 1) {
          updateFlag = true;
        } else {
          const allTrainCount = await ctx.model.PersonalTraining.count({
            adminTrainingId: personalTrainingInfo.adminTrainingId,
            status: true,
            adminUserId: { $in: adminArray },
          });
          if (allTrainCount >= adminArray.length) updateFlag = true;
        }
      } else {
        ctx.auditLog('培训数据异常', `公司id为 ${EnterpriseID} 的adminArray没有值。`, 'error');
      }
      if (updateFlag) {
        // 更新adminTraining, 添加完成的企业
        await ctx.model.AdminTraining.updateOne(
          { _id: adminTrainingInfo._id },
          { $push: { completedEnterprise: EnterpriseID } }
        ).then(res => {
          if (res.nModified === 1) {
            ctx.auditLog('管理员培训完成', `名为 ${adminTrainingInfo.name} 的管理员培训完成。`, 'info');
          }
        });
      }
    }
  }

}

module.exports = PersonTrainingService;
