const Service = require('egg').Service;
class AgentService extends Service {
  // 创建分享记录
  async createShareRecord(data) {
    // 查询有无代理关系
    const agentRelation = await this.ctx.model.AgentRelation.findOne({ userID: data.shareBy });
    // 没有的话就需要创建了以供分配收益用 这里不需要上级，因为上级的话注册时就添加了
    if (!agentRelation) {
      // 查培训站点配置有无开启代理功能，获取默认代理方案（以后可能根据上线配置先不管又复杂了）
      const trainSiteConfig = await this.getTrainSiteConfig();
      if (!trainSiteConfig.isAgent) {
        return {
          code: 400,
          message: '未开启代理功能',
        };
      }
      // 创建代理关系
      const agentRelationData = {
        userID: data.shareBy,
        agentScheme: trainSiteConfig.defaultAgent,
      };
      await this.ctx.model.AgentRelation.create(agentRelationData);
    }
    return await this.ctx.model.AgentShareRecord.create(data);
  }
  // 获取分享记录详情 首次点击注册代理关系
  async getShareDetail(params) {
    try {
      const { shareRecordId, pageCurrent, size, userID } = params;
      // 聚合管道
      const pipeline = [
        { $match: { _id: shareRecordId } },
        {
          $lookup: {
            from: 'adminTraining',
            localField: 'shareTrain.adminTrainingId',
            foreignField: '_id',
            as: 'adminTrainings',
          },
        },
        { $unwind: '$adminTrainings' },
        {
          $lookup: {
            from: 'Courses',
            localField: 'adminTrainings.coursesID',
            foreignField: '_id',
            as: 'courses',
          },
        },
        {
          $group: {
            _id: '$_id',
            shareBy: { $first: '$shareBy' },
            shareTrain: {
              $push: {
                adminTrainingID: '$adminTrainings._id',
                views: { $first: '$courses.views' },
                cover: { $first: '$courses.cover' },
                price: '$adminTrainings.price',
                name: '$adminTrainings.name',
                updatedAt: '$adminTrainings.updatedAt',
              },
            },
            clickCount: { $first: '$clickCount' },
            deadlineDate: { $first: '$deadlineDate' },
          },
        },
        {
          $project: {
            _id: 0,
            shareBy: 1,
            clickCount: 1,
            deadlineDate: 1,
            shareTrain: {
              $slice: [ '$shareTrain', (pageCurrent - 1) * size, size ],
            },
            total: { $size: '$shareTrain' },
          },
        },
      ];

      let [ result ] = await this.ctx.model.AgentShareRecord.aggregate(pipeline);
      result = JSON.parse(JSON.stringify(result));
      if (result) {
        // 自增 clickCount 并保存
        await this.ctx.model.AgentShareRecord.updateOne({ _id: shareRecordId }, { $inc: { clickCount: 1 } });
        if (userID) {
          const ismylist = await this.ctx.model.PersonalTraining.find({
            userId: userID,
          }, { adminTrainingId: 1 });
          let myTrain = '';
          for (const item of ismylist) {
            myTrain += (',' + item.adminTrainingId);
          }
          const lastData = result.shareTrain.map(data => {
            if (myTrain.includes(data.adminTrainingID)) {
              data.isStudy = true;
            }
            return data;
          });
          result.shareTrain = lastData;
        }

        return { ...result };
      }

      return null;
    } catch (error) {
      console.error(error);
      this.ctx.auditLog('代理相关', `${error}`, 'info');
    }
  }
  // 获取分享记录列表
  async getShareRecordList(params) {
    try {
      const { pageCurrent, size, userID } = params;
      const skip = parseInt((pageCurrent - 1) * size);
      const pipeline = [
        { $match: { shareBy: userID } },
        { $unwind: '$shareTrain' },
        {
          $lookup: {
            from: 'adminTraining',
            localField: 'shareTrain.adminTrainingId',
            foreignField: '_id',
            as: 'adminTrainings',
          },
        },
        { $unwind: '$adminTrainings' },
        {
          $lookup: {
            from: 'Courses',
            localField: 'adminTrainings.coursesID',
            foreignField: '_id',
            as: 'courses',
          },
        },
        {
          $group: {
            _id: '$_id',
            shareBy: { $first: '$shareBy' },
            shareTrain: {
              $push: {
                adminTrainingID: '$adminTrainings._id',
                cover: { $first: '$courses.cover' },
                price: '$adminTrainings.price',
                name: '$adminTrainings.name',
                _id: '$shareTrain._id',
              },
            },
            clickCount: { $first: '$clickCount' },
            deadlineDate: { $first: '$deadlineDate' },
          },
        },
        {
          $project: {
            _id: 1,
            shareBy: 1,
            clickCount: 1,
            deadlineDate: 1,
            shareTrain: {
              $slice: [ '$shareTrain', skip, size ],
            },
            total: { $size: '$shareTrain' },
          },
        },
        {
          $unwind: '$shareTrain',
        },
      ];
      const result = await this.ctx.model.AgentShareRecord.aggregate(pipeline);
      return result;
    } catch (error) {
      console.error(error);
      this.ctx.auditLog('代理相关', `${error}`, 'info');
    }
  }

  // 查询有无上级
  async getParent(params) {
    try {
      // 查找代理关系
      const agentRelation = await this.ctx.model.AgentRelation.findOne({ userID: params.userID }, { parentUserID: 1 });
      return agentRelation;
    } catch (err) {
      console.error(err);
    }
  }
  // 获取代理中心详细信息
  async getAgentInfo(params) {
    try {
      const skip = parseInt((params.page - 1) * params.limit);
      const now = new Date();
      const result = await this.ctx.model.AgentRelation.aggregate([
        { $match: { userID: params.userID } },
        { $unwind: { path: '$profitRecords', preserveNullAndEmptyArrays: true } },
        {
          $group: {
            _id: '$_id',
            userID: { $first: '$userID' },
            parentUserID: { $first: '$parentUserID' },
            profit: { $first: '$profit' },
            agentScheme: { $first: '$agentScheme' },
            childUserIDs: { $first: '$childUserIDs' },
            totalIncome: { $sum: '$profitRecords.amount' },
            monthlyIncome: {
              $sum: {
                $cond: {
                  if: {
                    $and: [
                      { $eq: [{ $year: '$profitRecords.date' }, now.getFullYear() ] },
                      { $eq: [{ $month: '$profitRecords.date' }, now.getMonth() + 1 ] },
                    ],
                  },
                  then: '$profitRecords.amount',
                  else: 0,
                },
              },
            },
            profitRecords: { $push: '$profitRecords' },
          },
        },
        {
          $project: {
            _id: 0,
            userID: 1,
            parentUserID: 1,
            profit: 1,
            agentScheme: 1,
            effectiveChildCount:
              {
                $size: {
                  $filter: {
                    input: '$childUserIDs',
                    as: 'childUserID',
                    cond: {
                      $eq: [ '$$childUserID.effectivePayed', true ],
                    },
                  },
                },
              },
            totalIncome: { $round: [ '$totalIncome', 2 ] },
            monthlyIncome: { $round: [ '$monthlyIncome', 2 ] },
            profitRecords: { $slice: [ '$profitRecords', skip, params.limit ] },
            profitRecordsCount: { $size: '$profitRecords' },
          },
        },
      ]);
      if (result.length > 0) {
        return {
          data: result[0],
          count: result[0].profitRecordsCount,
          totalPages: Math.ceil(result[0].profitRecordsCount / params.limit),
          currentPage: params.page,
        };
      }
      return null;
    } catch (err) {
      console.error(err);
      this.ctx.auditLog('代理相关', `${err}`, 'info');
    }
  }
  // 获取代理排行榜
  async getRanking() {
    try {
      const result = await this.ctx.model.AgentRelation.aggregate([
        {
          $lookup: {
            from: 'users',
            localField: 'userID',
            foreignField: '_id',
            as: 'user',
          },
        },
        {
          $unwind: {
            path: '$user',
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $addFields: {
            phone: {
              $concat: [
                {
                  $substrCP: [ '$user.phoneNum', 0, 3 ],
                },
                '****',
                {
                  $substrCP: [ '$user.phoneNum', 7, 11 ],
                },
              ],
            },
            name: {
              $concat: [
                {
                  $cond: [
                    {
                      $eq: [ '$user.name', null ],
                    },
                    '',
                    {
                      $substrCP: [ '$user.name', 0, 1 ],
                    },
                  ],
                },
                {
                  $cond: [
                    {
                      $eq: [ '$user.name', null ],
                    },
                    '**',
                    '**',
                  ],
                },
              ],
            },
          },
        },
        {
          $project: {
            _id: 1,
            name: 1,
            totalIncome: {
              $round: [
                { $sum: '$profitRecords.amount' },
                2,
              ],
            },
            userID: 1,
            effectiveChild: {
              $size: {
                $filter: {
                  input: '$childUserIDs',
                  as: 'child',
                  cond: {
                    $eq: [
                      '$$child.effectivePayed',
                      true,
                    ],
                  },
                },
              },
            },
            phone: 1,
          },
        },
        {
          $sort: {
            totalIncome: -1,
          },
        },
        {
          $limit: 10,
        },
        {
          $group: {
            _id: null,
            rankingList: { $push: '$$ROOT' },
          },
        },
        {
          $addFields: {
            rankingList: {
              $map: {
                input: { $range: [ 0, { $size: '$rankingList' }] },
                in: {
                  $mergeObjects: [
                    { $arrayElemAt: [ '$rankingList', '$$this' ] },
                    { rank: {
                      $add: [ '$$this', 1 ],
                    } },
                  ],
                },
              },
            },
          },
        },
        {
          $unwind: '$rankingList',
        },
        {
          $replaceRoot: {
            newRoot: '$rankingList',
          },
        },
      ]);
      return result;
    } catch (error) {
      console.error(error);
      this.ctx.auditLog('代理相关', `${error}`, 'info');
    }
  }

  // 添加代理关系
  async addAgentRelation(data) {
    try {
      // 首先查这个userId的有没有文档 刚注册的理论上没有
      const trainSiteConfig = await this.getTrainSiteConfig();
      const userRelation = await this.ctx.model.AgentRelation.findOne({ userID: data.userID });
      const count = await this.ctx.model.AgentShareRecord.countDocuments({ shareBy: data.userID });
      if (userRelation && userRelation.parentUserID || count > 0) {
        // 更新上级代理关系
        return {
          code: 400,
          message: '代理关系已存在或已经分享过',
        };
      } else if (userRelation && !userRelation.parentUserID) {
        // 有代理关系没有上级
        await this.ctx.model.AgentRelation.updateOne({ userID: data.userID }, { parentUserID: data.parentUserID });
      } else {
        // 没有代理关系
        await this.ctx.model.AgentRelation.create({ userID: data.userID, parentUserID: data.parentUserID, agentScheme: trainSiteConfig.defaultAgent });
      }
      // 其次userId的有没有文档
      const parentAgentRelation = await this.ctx.model.AgentRelation.findOne({ userID: data.parentUserID });
      if (parentAgentRelation) {
        // 更新上级代理关系
        await this.ctx.model.AgentRelation.updateOne({ userID: data.parentUserID }, { $push: { childUserIDs: { childUserID: data.userID, source: data.source } } });
      } else {
        // 创建代理关系
        await this.ctx.model.AgentRelation.create({
          userID: data.parentUserID,
          childUserIDs: [{
            childUserID: data.userID,
            source: data.source,
          }],
          agentScheme: trainSiteConfig.defaultAgent,
        });
      }
      return {
        code: 200,
        message: '代理关系添加成功',
      };
    } catch (err) {
      console.error(err);
      this.ctx.auditLog('代理相关', `${err}`, 'info');
      return {
        code: 500,
        message: '代理关系添加失败',
      };
    }
  }
  // 触发分配收益
  async triggerProfit(out_trade_no) {
    try {
      // 首先根据这个订单号查询一下支付状态 还要是培训证书和团购的订单
      const payInfo = await this.ctx.model.PayInfo.findOne({ out_trade_no, payStatus: 1, productCategory: { $in: [ 1, 2 ] } });
      if (!payInfo) throw new Error('订单状态或类型错误');
      if (payInfo.isCommission !== 0) throw new Error('已经分配过收益,或正在分配');
      // 判断网站是否开启代理功能并获取代理配置方案
      const trainSiteConfig = await this.ctx.model.TrainSiteConfig.findOne({ isAgent: true });
      if (!trainSiteConfig) throw new Error('未开启代理收益功能');
      // 查找上级代理 userId
      const nowUserParentAgentRelation = await this.ctx.model.AgentRelation.findOne({ userID: payInfo.userID, parentUserID: { $nin: [ null, '' ] } }, { parentUserID: 1 });
      const firstParentUserID = nowUserParentAgentRelation ? nowUserParentAgentRelation.parentUserID : null;
      if (!firstParentUserID) throw new Error('没有上级代理');
      // 到此为止，支付成功，订单类型正确，代理功能开启，获取代理关系,有上级-->> 要上级的啊

      // 将payInfo的isCommission字段改为1分配中
      await this.ctx.model.PayInfo.findOneAndUpdate({ out_trade_no }, { $set: { isCommission: 1 } });
      // 判断上级的代理关系是否正常
      const agentRelation = await this.ctx.model.AgentRelation.findOne({ userID: firstParentUserID, agentScheme: { $nin: [ null, '' ] } });
      if (!agentRelation) throw new Error(`userID->${firstParentUserID}代理关系错误`);
      // 到此为止，支付者上级的代理关系正确，获取分配方案
      const agentCommissionRate = await this.ctx.model.AgentCommissionRate.findOne({ _id: agentRelation.agentScheme });
      if (!agentCommissionRate) throw new Error('代理方案错误');
      // 拿到分配方案里的分配比例
      const { takeCommissionRates } = agentCommissionRate;
      // 支付人的上级代理人
      let nowAllocationUser = agentRelation.userID; // 当前支付者上级userID
      let parentUserID = agentRelation.parentUserID;// 当前支付者的上级的上级userID
      // 上级的代理方案是否开启下级代理收益
      if (parentUserID) {
        const parentAgentRelation = await this.ctx.model.AgentRelation.findOne({ userID: parentUserID, agentScheme: { $ne: null } });
        if (parentAgentRelation) { // 如果有上级代理关系
          const parentAgentCommissionRate = await this.ctx.model.AgentCommissionRate.findOne({ _id: parentAgentRelation.agentScheme });
          if (parentAgentCommissionRate) { // 如果有上级代理方案
            const { subordinatesUseable } = parentAgentCommissionRate;
            if (!subordinatesUseable) { // 如果不开启下级代理收益
              throw new Error('上级代理方案未开启代理收益功能');
            }
          }
        }
      }
      console.log(88888, '进来了');
      // 子代理人数
      let childAgentCount = await this.countEffectivePayedChildUsers(agentRelation.childUserIDs || []);
      const priceTotal = [];
      let isSuccess = false;


      for (const [ index, layer ] of takeCommissionRates.entries()) { // 循环层级  需要userID 进行查询
        if (parentUserID) { // 如果有上级代理关系
          const nowAgentRelation = await this.ctx.model.AgentRelation.findOne({ userID: nowAllocationUser });
          // 如果没查到就跳出循环
          if (!nowAgentRelation) break;
          parentUserID = nowAgentRelation.parentUserID;
          // 这一步应该判断有效人数
          childAgentCount = await this.countEffectivePayedChildUsers(nowAgentRelation.childUserIDs || []);
        }
        for (const level of layer) { // 层级下的分配方案
          if (childAgentCount >= level.condition.min && (level.condition.max === null || childAgentCount <= level.condition.max)) {
            const price = +(payInfo.price * level.rate * 100 / 100).toFixed(2);
            await this.ctx.model.AgentRelation.findOneAndUpdate(
              { userID: nowAllocationUser },
              {
                $inc: { profit: price },
                $push: { profitRecords: { payInfoID: payInfo._id, amount: price, type: 0, remark: `第${index + 1}级代理消费产生收益`, triggerUserID: payInfo.userID } },
              },
              { new: true });
            priceTotal.push(price);
            if (index === 0) { // 只给支付者的上级标注此支付者为有效支付
              await this.ctx.model.AgentRelation.updateOne({ userID: nowAllocationUser, 'childUserIDs.childUserID': payInfo.userID }, { $set: { 'childUserIDs.$.effectivePayed': true } });
            }
            if (parentUserID) { // 如果有就赋值给下一次循环
              // 给上级标记有效支付
              nowAllocationUser = parentUserID;
            } else {
              isSuccess = true;
            }
            break;
          }
        }
        if (isSuccess) {
          break;
        }
      }
      await this.ctx.model.PayInfo.findOneAndUpdate({ out_trade_no }, { $set: { isCommission: isSuccess ? 2 : 3 } });
      return {
        priceTotal,
      };
    } catch (error) {
      this.ctx.auditLog('代理相关', error.message, 'error');
      if (!JSON.stringify(error).indexOf('已经分配过收益,或正在分配') === -1) { // 如果不是分配中就改为分配失败
        await this.ctx.model.PayInfo.findOneAndUpdate({ out_trade_no }, { $set: { isCommission: 3 } });
      }
      // return error;
    }
  }

  // 触发退款的时候减掉收益
  async triggerRefund(out_trade_no) {
    try {
    // 首先根据这个订单号查询一下支付状态 还要是培训证书和团购的订单
      const payInfo = await this.ctx.model.PayInfo.findOne({ out_trade_no, payStatus: 4, productCategory: { $in: [ 1, 2 ] } });
      if (!payInfo) throw new Error('订单状态或类型错误');
      if (payInfo.isCommission !== 2) throw new Error('没有分配成功');
      // 判断网站是否开启代理功能并获取代理配置方案
      const trainSiteConfig = await this.ctx.model.TrainSiteConfig.findOne({ isAgent: true });
      if (!trainSiteConfig) throw new Error('未开启代理收益功能');
      // 查找上级代理 userId
      const nowUserParentAgentRelation = await this.ctx.model.AgentRelation.findOne({ userID: payInfo.userID, parentUserID: { $nin: [ null, '' ] } }, { parentUserID: 1 });
      const firstParentUserID = nowUserParentAgentRelation ? nowUserParentAgentRelation.parentUserID : null;
      if (!firstParentUserID) throw new Error('没有上级代理');
      // 到此为止，支付成功，订单类型正确，代理功能开启，获取代理关系,有上级-->> 要上级的啊

      // 将payInfo的isCommission字段改为1分配中
      await this.ctx.model.PayInfo.findOneAndUpdate({ out_trade_no }, { $set: { isCommission: 1 } });
      const priceTotal = [];
      let isSuccess = false;
      // 根据payInfo的_id 查询AgentRelation表中  profitRecords.payInfoID = payInfo._id 的记录
      const neddRefundAgentRelations = await this.ctx.model.AgentRelation.aggregate([
        {
          $match: {
            'profitRecords.payInfoID': payInfo._id,
          },
        },
        {
          $project: {
            _id: 1,
            profit: 1,
            userID: 1,
            profitRecords: {
              $filter: {
                input: '$profitRecords',
                as: 'record',
                cond: { $eq: [ '$$record.payInfoID', payInfo._id ] },
              },
            },
          },
        },
      ]);
      if (!neddRefundAgentRelations) throw new Error('没有分配记录');
      const nowUserPaySuccessCount = await this.ctx.model.PayInfo.countDocuments({ userID: payInfo.userID, payStatus: 1, productCategory: { $in: [ 1, 2 ] } });
      // 循环这些neddRefundAgentRelations，在profitRecords中 添加一条记录，type为1，remark为退款，amount为profitRecords[0].amount，payInfoID为payInfo._id
      for (const neddRefundAgentRelation of neddRefundAgentRelations) {
        await this.ctx.model.AgentRelation.findOneAndUpdate(
          { _id: neddRefundAgentRelation._id },
          {
            $inc: { profit: -neddRefundAgentRelation.profitRecords[0].amount },
            $push: { profitRecords: { payInfoID: payInfo._id, amount: -neddRefundAgentRelation.profitRecords[0].amount, type: 1, remark: '下级产生退款', triggerUserID: payInfo.userID } },
          },
          { new: true }
        );
        priceTotal.push(-neddRefundAgentRelation.profitRecords[0].amount);
        // 判断payInfo.userID是否为第一次支付，如果是，那么就要把他的上级代理表里的有效支付 改为false
        if (nowUserPaySuccessCount === 0 && neddRefundAgentRelation.userID === firstParentUserID) {
          await this.ctx.model.AgentRelation.updateOne({ userID: firstParentUserID, 'childUserIDs.childUserID': payInfo.userID }, { $set: { 'childUserIDs.$.effectivePayed': false } });
        }
      }

      isSuccess = priceTotal.length === neddRefundAgentRelations.length;
      await this.ctx.model.PayInfo.findOneAndUpdate({ out_trade_no }, { $set: { isCommission: isSuccess ? 4 : 2 } });
      // console.log('退款分配成功', priceTotal);
      return {
        priceTotal,
      };
    } catch (error) {
      console.log(error);
      if (!JSON.stringify(error).indexOf('没有分配成功') === -1) { // 如果不是分配中就改为分配失败
        await this.ctx.model.PayInfo.findOneAndUpdate({ out_trade_no }, { $set: { isCommission: 3 } });
      }
      return error;
    }
  }


  // 获取培训网站配置
  async getTrainSiteConfig() {
    const { ctx } = this;
    const userID = ctx.session.user ? ctx.session.user._id : '';
    let res = await ctx.model.TrainSiteConfig.findOne({});
    res = JSON.parse(JSON.stringify(res));
    const host = this.ctx.app.config.domainNames.px.replace(/^https?:\/\//i, '');
    const envRes = await ctx.curl(`${this.config.iServiceHost}/api/envConfig`, {
      method: 'get',
      dataType: 'json', // 返回的数据类型
      data: {
        host,
      },
    });
    if (envRes.status === 200) {
      res.information = envRes.data.data.footerContent;
      res.recordNumber = envRes.data.data.recordNumber;
    }
    if (userID) {
      const isFlag = await ctx.model.AgentRelation.aggregate([
        {
          $match: {
            userID,
          },
        },
        {
          $lookup: {
            from: 'agentCommissionRate',
            localField: 'agentScheme',
            foreignField: '_id',
            as: 'agentScheme',
          },
        },
        {
          $unwind: {
            path: '$agentScheme',
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $project: {
            _id: 0,
            'agentScheme.subordinatesUseable': 1,
          },
        },
      ]);
      if (isFlag.length > 0) {
        // 如果有上级并且代理方案没有开启下级代理收益
        if (isFlag[0].agentScheme && !isFlag[0].agentScheme.subordinatesUseable) {
          res.isAgent = false;
        }
      }
    }
    return res;
  }
  // 判断有效人数
  async countEffectivePayedChildUsers(childUserIDs) {
    const effectivePayedChildUsers = childUserIDs.filter(childUserIdObj => childUserIdObj.effectivePayed);
    return effectivePayedChildUsers.length;
  }

}

module.exports = AgentService;
