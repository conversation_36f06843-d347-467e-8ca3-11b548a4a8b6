const Service = require('egg').Service;
class TrainingGroupPurchaseService extends Service {
  // 使用cdk
  async useCdk(data) {
    const { ctx } = this;
    try {
      // 需要卡密，培训id
      // params  conversionCode adminTrainingId
      const userId = ctx.session.user ? ctx.session.user._id : '';
      let personalTrainingId = data.personalTrainingId || '';
      if (!userId) return '请先登录';
      const groupInfo = await ctx.model.TrainingGroupPurchase.findOne({ conversionCode: data.conversionCode }).populate('payInfoID', 'payStatus price');
      if (groupInfo && groupInfo.payInfoID && groupInfo.payInfoID.payStatus === 1 && (groupInfo.useInfo.length < groupInfo.quota)) { // 如果查得到并且支付成功并且还有名额
        // 添加到我的培训
        if (!personalTrainingId) {
          const trainInfo = await ctx.service.adminTraining.getDetail({ _id: data.adminTrainingId });
          const courses = trainInfo.detail.coursesID.map(ele => {
            // 必修课
            const coursesDetail = trainInfo.coursesList.filter(course => course._id === ele)[0];
            const coursePlayProgress = coursesDetail.contentList.map(video => {
              return {
                classHours: video.classHours || 0,
                duration: video.duration, // 视频的总时长
                chapterID: video._id,
              };
            });
            return { coursesId: ele, courseType: 1, coursePlayProgress };
          });
          // 添加成功后
          const addRes = await ctx.service.personalTraining.create2({
            adminTrainingId: data.adminTrainingId,
            trainingType: 2,
            courses,
          });
          personalTrainingId = addRes._id;
        }
        // 判断价格 --获取购买时单价和要绑定的培训价格比较,如果不同不让兑换
        // 查培训价格
        let trainPrice = 0;
        if (!data.adminTrainingId) {
          const personalTrainingInfo = await ctx.model.PersonalTraining.findOne({ _id: personalTrainingId }).populate('adminTrainingId', 'price');
          trainPrice = personalTrainingInfo.adminTrainingId.price;
        } else {
          const adminTrainingInfo = await ctx.model.PersonalTraining.findOne({ _id: data.adminTrainingId });
          trainPrice = adminTrainingInfo.price;
        }
        const payedPrice = +groupInfo.payInfoID.price / groupInfo.quota;
        if (payedPrice !== trainPrice) {
          return '兑换码价格不匹配';
        }
        // 绑定支付信息
        const bindPayInfo = await ctx.model.PersonalTraining.updateOne({ _id: personalTrainingId }, { payInfoID: groupInfo.payInfoID._id });
        if (bindPayInfo.nModified === 1) {
          const useInfoItem = {
            userID: userId,
            personalTrainingId,
            usedTime: Date.now(),
          };
          await ctx.model.TrainingGroupPurchase.updateOne({ _id: groupInfo._id }, { $push: { useInfo: useInfoItem } });
          if (groupInfo.useInfo.length + 1 >= groupInfo.quota) {
            await ctx.model.TrainingGroupPurchase.updateOne({ _id: groupInfo._id }, { status: 2 });
          }
        } else {
          return '出错啦，请联系客服';
        }
        return 'success';
      }
      if (groupInfo.length === 0) {
        return '兑换码错误';
      }
      if (groupInfo.payInfoID.payStatus !== 1) {
        return '兑换码暂未激活';
      }
      if (groupInfo.useInfo.length >= groupInfo.quota) {
        return '兑换码名额已经用完';
      }

      // 首先判断卡密是否正确 在判断是否还有名额
      // 如果正确就算使用了 插入到卡密表中的useinfo里
      // 然后添加个人培训记录
      // 绑定支付信息

    } catch (error) {
      ctx.logger.error(error, data);
      console.error(error);
    }
  }
  // 付费成功激活cdk
  async activateCdk(data) {
    const { ctx } = this;
    try {
      // 查询是否支付成功并且cdk有无
      const oldData = await ctx.model.TrainingGroupPurchase.findOne({ _id: data.cdkeyId }).populate('payInfoID', 'payStatus');
      console.log(1111, data.cdkeyId, oldData);
      if (oldData && oldData.payInfoID && oldData.payInfoID.payStatus === 1) {
        const res = await ctx.model.TrainingGroupPurchase.updateOne({ _id: data.cdkeyId }, { status: 1 });
        if (res.nModified === 1) {
          return 'success';
        }
      } else {
        return '非法操作';
      }
      return 'error';
    } catch (error) {
      ctx.logger.error(error, data);
      console.error(error);
    }
  }
  // 团购list
  async myCdkList(data) {
    const { ctx } = this;
    const userID = ctx.session.user ? ctx.session.user._id : '';
    if (data.keyWord) data.keyWord = data.keyWord.trim();
    // const reg = new RegExp(data.keyWord); // 不区分大小写
    const query = {
      buyerUserID: userID,
      status: { $ne: 0 },
    };
    data.pageCurrent = data.pageCurrent ? +data.pageCurrent : 1;
    data.size = data.size ? +data.size : 10;
    const res = await ctx.model.TrainingGroupPurchase.find(query)
      .populate('useInfo.userID', 'name')
      .sort({ createdAt: -1 })
      .skip((data.pageCurrent - 1) * data.size)
      .limit(data.size);
    const resData = JSON.parse(JSON.stringify(res));
    console.log('res', res, userID);
    const pageInfo = await this.getPageInfo('TrainingGroupPurchase', data.size, data.pageCurrent, query);
    return {
      list: resData,
      pageInfo,
    };
  }
  async getPageInfo(collection, size, pageCurrent, query = {}) {
    const total = await this.ctx.model[collection].find(query).count();
    const pageInfo = {
      total,
      size,
      pageCurrent,
    };
    return pageInfo;
  }
}
module.exports = TrainingGroupPurchaseService;
