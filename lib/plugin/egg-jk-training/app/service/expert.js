
const Service = require('egg').Service;
// 专家入驻
class ExpertService extends Service {
  // 创建培训
  async createTrain(params = {}) {
    const { ctx } = this;
    params.userID = ctx.session.user._id;
    return await ctx.model.AdminTraining.create(params);
  }
  // 获取我的培训
  async myList(query = {}) {
    const { ctx } = this;
    if (query.keyWords) {
      const reg = { $regex: new RegExp(query.keyWords, 'i') };
      query.$or = [
        { name: reg },
        { Introduction: reg },
      ];
    }
    delete query.keyWords;
    const skip = (query.pageCurrent - 1) * query.size;
    const limit = query.size;
    const pageCurrent = query.pageCurrent;
    delete query.pageCurrent;
    delete query.size;
    let list = await ctx.model.AdminTraining.find(query, { name: 1, coursesID: 1, requiredCoursesHours: 1, auditStatus: 1, auditFailedReason: 1, salesStatus: 1, price: 1, updatedAt: 1, salesStatusRecord: 1 })
      .sort({ updatedAt: -1 })
      .skip(skip)
      .limit(limit)
      .populate('coursesID', 'price views complete classHours cover labels name sort');
    const total = await ctx.model.AdminTraining.count(query);
    // 计算报名人数
    list = JSON.parse(JSON.stringify(list));
    for (let i = 0; i < list.length; i++) {
      const curItem = list[i];
      curItem.trainNum = await ctx.model.PersonalTraining.count({ adminTrainingId: curItem._id, status: true });
    }
    return { list, pageInfo: { total, size: limit, pageCurrent } };
  }
  // 更新培训信息
  async updateTrain(_id, updateData = {}) {
    if (!_id) return { message: '_id为必传参' };
    return await this.ctx.model.AdminTraining.updateOne({ _id }, updateData);
  }
  // 上传新的视频 在course末尾插入内容
  async insertContent(_id, contentType, project) {
    let contentID = '';
    if (contentType === 'videoInfos') {
      let response = {};
      try {
        response = await this.ctx.helper.request_alivod('GetPlayInfo', {
          VideoId: project.VideoId,
          Formats: 'mp4',
        }, {});
      } catch (error) {
        this.ctx.logger.error(error);
      }
      project.duration = response.PlayInfoList.PlayInfo[0].Duration || 0;
      const newVideoInfos = await this.ctx.model.VideoInfos.create(project);
      contentID = newVideoInfos._id;
    } else if (contentType === 'documents') {
      const newTrainingDocument = await this.ctx.model.TrainingDocument.create(project);
      contentID = newTrainingDocument._id;
    }
    await this.ctx.model.Courses.updateOne({ _id }, {
      $push: {
        [contentType]: contentID,
      },
      $inc: {
        classHours: Number(project.classHours),
      },
    });
    const course = await this.ctx.model.Courses.findOne({ _id }, { sort: 1 });
    this.ctx.auditLog('追加章节', `${contentID} ** ${_id} ** ${contentID} ** ${JSON.stringify(course)}`, 'info');
    const backData = await this.ctx.model.Courses.updateOne({ _id }, {
      $push: {
        sort: {
          contentType,
          ID: contentID,
          sequence: course.sort.length + 1,
        },
      },
    });
    return backData;
  }
  // 给视频排序  上移/下移
  async resortContent(_id, id1, id2) {
    const course = await this.ctx.model.Courses.findOne({ _id }, { sort: 1 });
    let index1,
      index2;
    for (let index = 0; index < course.sort.length; index++) {
      if (course.sort[index]._id === id1) index1 = index;
      if (course.sort[index]._id === id2) index2 = index;
    }
    // console.log(2222222222, course.sort, index1, index2);
    const temp = course.sort[index1].sequence;
    course.sort[index1].sequence = course.sort[index2].sequence;
    course.sort[index2].sequence = temp;
    const back = await this.ctx.model.Courses.updateOne({ _id }, {
      $set: {
        sort: course.sort,
      },
    });
    return back;
  }
  // 获取所有的专家列表
  async getExpertList(params = {}) {
    const { ctx } = this;
    const query = { role: 'expert', state: '1', expertStatus: 1 };// expertStatus:上下架
    if (params.keyWords) {
      const reg = { $regex: new RegExp(params.keyWords, 'i') };
      query.$or = [
        { name: reg },
        { introduction: reg },
      ];
    }
    const limit = +params.size || 10;
    const skip = ((params.pageCurrent || 1) - 1) * limit;
    let list = await ctx.model.User.find(query, { name: 1, logo: 1, introduction: 1 }).skip(skip).limit(limit);
    if (limit === 4) {
      // 获取专家资质证书
      list = JSON.parse(JSON.stringify(list));
      for (let i = 0; i < list.length; i++) {
        list[i].expertQalifications = await ctx.model.ExpertQalifications.find({
          status: 1,
          userID: list[i]._id,
          validTime: { $exists: true, $gt: new Date() },
        }, { name: 1, NO: 1, validTime: 1 });
      }
    }
    const total = await ctx.model.User.count(query);
    return { list, total };
  }

}

module.exports = ExpertService;
