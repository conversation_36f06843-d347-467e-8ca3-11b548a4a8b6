
const pkgInfo = require('../package.json');
exports.jk_physicalexamorg = {
  alias: 'physicalexamorg', // 插件目录，必须为英文
  pkgName: 'egg-jk-physicalexamorg', // 插件包名
  enName: 'jk_physicalexamorg', // 插件名
  name: '体检机构插件', // 插件名称
  description: '体检机构插件', // 插件描述
  isadm: 0, // 是否有后台管理，1：有，0：没有，入口地址:'/ext/devteam/admin/index'
  isindex: 0, // 是否需要前台访问，1：需要，0：不需要,入口地址:'/ext/devteam/index/index'
  version: pkgInfo.version, // 版本号
  iconName: 'icon_service', // 主菜单图标名称
  adminUrl: '/adminuser/js/app.js',
  adminApi: [],
  fontApi: [{
    url: 'physical/report',
    method: 'post',
    controllerName: 'createPhyReport',
    details: '创建体检项目(外)',
  }, {
    url: 'hzwy/physical/report',
    method: 'post',
    controllerName: 'createHZWYPhyReport',
    details: '创建体检项目(内)', // 中间件加白名单，禁止通过资源管理增加
  }, {
    url: 'physical/reportFile',
    method: 'post',
    controllerName: 'uploadPhyReportFile',
    details: '上传体检报告',
  }, {
    url: 'hzwy/physical/reportFile',
    method: 'post',
    controllerName: 'uploadHZWYPhyReportFile',
    details: '上传体检报告(内)', // 中间件加白名单，禁止通过资源管理增加
  }, {
    url: 'physical/reportList',
    method: 'get',
    controllerName: 'getSuperPhyReportList',
    details: '获取体检报告列表(行政用)',
  }, {
    url: 'physical/phyReportList',
    method: 'get',
    controllerName: 'getPhyReportList',
    details: '获取体检报告列表',
  }],

  initData: '', // 初始化数据脚本
  pluginsConfig: ` 
    exports.jk_physicalexamorg = {\n
        enable: true,\n
         \n
    };\n
    `, // 插入到 plugins.js 中的配置
  defaultConfig: `
  physicalexamorgRouter:{\n
        match: [ctx => ctx.path.startsWith('/manage/adminuser'), ctx => ctx.path.startsWith('/api/adminuser')],\n
    },\n
    `, // 插入到 config.default.js 中的配置
};
