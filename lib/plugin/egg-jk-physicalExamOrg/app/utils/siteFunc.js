const fs = require('fs');
const _ = require('lodash');
const siteFunc = {
// 删除存在本地的文件
  deleteFile(ctx, filePath) {
    fs.unlink(filePath, err => {
      if (err) {
        ctx.auditLog('错误', `体检报告模块删除文件错误：${err.stack} 。`, 'error');
      }
    });
  },
  /**
   * 同步企业工作场所
   * @param {any} ctx 上下文context
   * @param {Object} query 查询条件
   * @param {[Object]} newWorkAddr 新的工作场所地址
   */
  async synchronization_adminorg_workaddress(ctx, query, newWorkAddr) {
    const { service } = ctx;
    const adminorg = await service.adminorg.item(ctx, { query, files: 'workAddress -_id' });
    const workAddress = adminorg.workAddress || [],
      workAddressCount = workAddress.length;
    let is_has = false;
    for (let i = 0; i < workAddressCount; i++) {
      const item = workAddress[i];
      if (item.districts.toString() === newWorkAddr.districts.toString() && item.address === newWorkAddr.address) {
        is_has = true;
        break;
      }
    }
    !is_has && workAddress.push(newWorkAddr);
    await service.adminorg.update(ctx, query._id, { workAddress });
  },
  /**
   * 创建和绑定企业管理账户
   * @param {any} ctx 上下文context
   * @param {any} app 系统application
   * @param {Object} params { _id: 企业ID, phoneNum: 联系电话, name: 联系人 }
   */
  async create_adminorg_manage_user(ctx, app, params) {
    const { service } = ctx;
    const { _id, phoneNum } = params;
    const name = params.name || phoneNum;

    const adminuser = await service.adminuser.item(ctx, { query: { phoneNum }, files: '_id userId' });
    const user = await service.user.item(ctx, { query: { phoneNum }, files: '_id' });
    const adminorg = await service.adminorg.item(ctx, { query: { _id }, files: 'adminUserId adminArray -_id' });

    const adminorg_update_params = { isactive: '1' },
      adminuser_update_params = {},
      user_update_params = {};
    const employee_create_params = { EnterpriseID: _id };

    // const session = await (app.mongoose.startSession());
    // (await session).startTransaction();
    try {
      let nAdminuser = adminuser;
      const adminuser_create_params = { phoneNum, name, userName: phoneNum, newAddEnterpriseID: _id };
      if (!adminuser) {
        // create adminuser
        // nAdminuser = await service.adminuser.create(adminuser_create_params, { session });
        nAdminuser = await service.adminuser.create(adminuser_create_params);
      }
      if (adminorg.adminUserId) {
        const orgAUserCount = await service.adminuser.count({ _id: adminorg.adminUserId });
        if (orgAUserCount < 1) {
          // set adminorg update params
          adminorg_update_params.adminUserId = nAdminuser._id;
        } else {
          // set adminorg update params
          if (adminorg.adminUserId !== nAdminuser._id && !adminorg.adminArray.includes(nAdminuser._id)) {
            adminorg_update_params.adminArray = [ nAdminuser._id ];
          }
          const adminArray = adminorg.adminArray,
            adminArrayCount = adminArray.length;
          for (let i = 0; i < adminArrayCount; i++) {
            const item_id = adminArray[i];
            const adminUserCount = await service.adminuser.count({ _id: item_id });
            if (adminUserCount > 0) {
              if (!adminorg_update_params.adminArray || adminorg_update_params.adminArray.length === 0) {
                adminorg_update_params.adminArray = [];
              }
              adminorg_update_params.adminArray.push(item_id);
            }
          }
        }
      } else {
        // set adminorg update params
        adminorg_update_params.adminUserId = nAdminuser._id;
      }

      // let nUser = await service.user.item(ctx, { query: { _id: this.get_value(nAdminuser, 'userId') }, files: '_id' }, { session });
      let nUser = await service.user.item(ctx, { query: { _id: this.get_value(nAdminuser, 'userId') }, files: '_id' });
      if (!nUser) {
        if (!user) {
          // create user
          // nUser = await service.user.create({ phoneNum, name, userName: phoneNum }, { session });
          nUser = await service.user.create({ phoneNum, name, userName: phoneNum });
        } else {
          nUser = { _id: user._id };
        }
        // set adminuser update params
        adminuser_update_params.userId = nUser._id;
        // set employee create params
        employee_create_params.userId = nUser._id;
      }
      // set user update params
      if (!nUser.companyId || nUser.companyId.length < 1) {
        user_update_params.companyId = [ _id ];
      } else if (nUser.companyId) {
        user_update_params.companyId = nUser.companyId;
        user_update_params.companyId.push(_id);
      }

      // let nEmployee = await service.employee.item(ctx, { query: { _id: this.get_value(user, 'employeeId') }, files: '_id' }, { session });
      let nEmployee = await service.employee.item(ctx, { query: { _id: this.get_value(user, 'employeeId') }, files: '_id' });
      if (!nEmployee) {
        const employee = await service.employee.item(ctx, { query: { phoneNum }, files: '_id' });
        if (!employee) {
          // create employee
          // nEmployee = await service.employee.create({ phoneNum, name, userName: phoneNum, ...employee_create_params }, { session });
          nEmployee = await service.employee.create({ phoneNum, name, userName: phoneNum, ...employee_create_params });
        } else {
          nEmployee = { _id: employee._id };
        }
        // set adminuser update params
        adminuser_update_params.targetEditor = nEmployee._id;
        adminuser_update_params.employees = [ nEmployee._id ];
        // set user update params
        user_update_params.employeeId = nEmployee._id;
      }

      if (!_.isEmpty(adminorg_update_params)) {
        // update adminorg
        // await service.adminorg.update(ctx, _id, adminorg_update_params, {}, { session });
        await service.adminorg.update(ctx, _id, adminorg_update_params, {});
      }
      if (!_.isEmpty(adminuser_update_params)) {
        // update adminuser
        // await service.adminuser.update(ctx, nAdminuser._id, adminuser_update_params, {}, { session });
        await service.adminuser.update(ctx, nAdminuser._id, adminuser_update_params);
      }
      if (!_.isEmpty(user_update_params)) {
        // update user
        // await service.user.update(ctx, nUser._id, user_update_params, {}, { session });
        await service.user.update(ctx, nUser._id, user_update_params, {});
      }
      // (await session).commitTransaction();
    } catch (error) {
      // (await session).abortTransaction();
      ctx.auditLog('错误', `创建修改体检项目同步企业用户异常：${error.stack} 。`, 'error');
    } finally {
      // (await session).endSession(); // 官网写了是要加的，但是加了后报错，也不知道为什么
      // https://mongoosejs.com/docs/transactions.html
      // 只有副本集可以使用事务，参考：https://blog.csdn.net/liuty66/article/details/126610914
    }
  },
  /**
   * 获取对象属性值
   * @param {any} that 属性的对象主体
   * @param {String} key 属性名
   * @return 属性值,默认返回空字符串
   */
  get_value(that, key) {
    if (that) {
      return that[key] ? that[key] : '';
    }
    return '';
  },
};
module.exports = siteFunc;
