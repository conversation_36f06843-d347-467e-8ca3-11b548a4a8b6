const path = require('path');
const encryptionPlugin = require(path.join(process.cwd(), 'app/utils/encryptionPlugin'));
module.exports = app => {
  const shortid = require('shortid');
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;
  const ctx = app.createAnonymousContext();
  const { dbEncryption = false } = app.config;
  const suspectSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    unitCode: {
      // 编码（对外）
      type: String,
    },
    reportCode: {
      // 个人体检档案编号
      type: String,
    },
    name: {
      // 姓名
      type: String,
      require: true,
    },
    nameForStore: {
      // 加密姓名
      type: String,
    },
    nameSplitEncrypted: {
      // 分段加密的姓名
      type: String,
    },
    age: {
      // 年龄
      type: String,
      require: true,
    },
    gender: {
      // 员工性别
      type: String,
      enum: [ '0', '1' ], // 0男 1女
    },
    workType: {
      // 工种
      type: String,
      require: true,
    },
    harmFactors: {
      // 危害因素
      type: String,
      require: true,
    },
    otherHarmFactors: {
      // 其他危害因素
      type: String,
      default: '',
    },
    opinion: {
      // 意见
      type: String,
      require: true,
    },
    CwithO: {
      // 结论
      type: String,
      require: true,
    },
    dedicalAdvice: {
      type: String,
      require: true,
    }, // 医学建议

    batch: {
      // 体检项目id
      type: String,
      ref: 'Healthcheck',
    },
    employeeId: {
      // 人员id
      type: String,
      ref: 'Employees',
    },
    IDCard: {
      // 身份证号
      type: String,
      default: '',
    },
    IDCardForStore: {
      // 用于加密存储的身份证号
      type: String,
    },
    IDCardSplitEncrypted: {
      // 分段加密的身份证号
      type: String,
    },
    EnterpriseID: {
      type: String,
      ref: 'adminorg',
    },

    checkType: {
      // 体检类型 0上岗前 1在岗 2离岗时 3复查 4应急 5 离岗后 6 普通体检
      type: String,
      enum: [ '0', '1', '2', '3', '4', '5', '6' ],
      require: true,
    },
    checkDate: {
      type: Date,
      require: true,
    },
    recheck: {
      type: String,
      default: '否',
    },
    lastBhkCode: {
      // 复检对应上次体检编号
      type: String,
      default: '',
    },
    // (非必填)
    year: {
      type: String,
    }, // 弃用
    illness: {
      type: String,
    }, // 可能导致的职业病/ 意见

    checkConclusion: {
      type: String,
    },
    status: {
      type: String,
    }, // 落实情况
    workspace: {
      type: String,
    },
    organization: {
      type: String,
      require: true,
    },
    abnormalIndex: String, // 异常指标
    diseaseName: Array,
    workYears: String, // 工龄

    createTime: {
      type: Date,
      default: new Date(),
    },
    manageYear: {
      // 上传历年个案卡年月
      type: String,
    },
    source: {
      // 来源
      type: String,
      default: 'oapi',
    },
    abnormalDes: {
      // 异常描述，没有的话就表示是正常的体检数据
      type: String,
      default: '',
    },
    files: [
      {
        // 处理的附件
        originName: String, // 用户上传文件名称
        staticName: String, // 后台处理后存储到public静态资源的文件名
        // 种类：体检签字图片
        fileType: String,
      },
    ],
    riskFactorsOfPhysicalExaminations: [
      // 体检危害因素
      {
        _id: {
          type: String,
          default: shortid.generate,
        },
        harmFactor: {
          // 危害因素
          type: String,
        },
        examConclusion: {
          // 主检查结论
          type: String,
        },
        suspectedOccupationalDisease: {
          // 疑似职业病
          type: String,
        },
        occupationalContraindications: {
          // 职业禁忌证
          type: String,
        },
        otherOrDes: {
          // 其他疾病或异常描述
          type: String,
        },
      },
    ],
    bhkSubList: [
      // 体检结果 fz对接时候的
      {
        _id: {
          type: String,
          default: shortid.generate,
        },
        itmcod: {
          // 体检项目编号，时间来不及做处理了，先这样暂时也用不到，可以找到福州的编码对应3.10的编码
          type: String,
        },
        name: {
          // 体检项目名称
          type: String,
        },
        classify: {
          // 体检项目分类
          type: String,
        },
        msrunt: {
          type: String,
        }, // 计量单位
        itemStdValue: {
          // 参考值
          type: String,
        },
        result: {
          // 结果
          type: String,
        },
        rgltag: {
          // 合格标记 1：合格0：不合格
          type: Number,
        },
        rstDesc: {
          // 结果偏高偏低标记0：正常1：偏高2：偏低	N1
          type: Number,
        },
        ifLack: {
          // 是否缺项 0：未缺1：缺项	N1
          type: Number,
        },
        chkdat: {
          // 检查日期	D10
          type: Date,
        },
        chkdoct: {
          // 检查医生	S..25
          type: String,
        },
        jdgptn: {
          // jdgptn	判断模式 1：定性2：定量
          // 判断模式
          type: Number,
        },
        minVal: {
          // 最小值
          type: String,
        },
        maxVal: {
          // 最大值
          type: String,
        },
        diagRest: {
          // 诊断结论
          type: String,
        },
        rstFlag: {
          //
          // 结果判定标记(目前只有胸片有) 0：未见异常 1：尘肺样改变 2：其他异常 3：未检查
          type: String,
        },
      },
    ],
    mhkRstList: [
      {
        _id: {
          type: String,
          default: shortid.generate,
        },
        examConclusion: {
          // 主检查结论
          type: String,
        },
      },
    ],
    exmdDataList: [
      // 一般问诊项目
      {
        mns: {
          // 月经史-经期
          type: String,
        },
        cys: {
          // 月经史-周期
          type: String,
        },
        mnlage: {
          //	月经史-停经年龄
          type: Number,
        },
        isxmns: {
          // 月经史-是否经期
          type: Number,
        },
        mnrage: {
          // 月经史-初潮年龄
          type: Number,
        },
        chldqty: {
          // 生育史- 子女人数
          type: Number,
        },
        abrqty: {
          // 生育史-流产次数
          type: Number,
        },
        slnkqty: {
          // 生育史-早产次数
          type: Number,
        },
        stlqty: {
          // 生育史-死产次数
          type: Number,
        },
        trsqty: {
          //	生育史-畸胎次数
          type: Number,
        },
        chldhthCnd: {
          // 生育史-子女健康状况
          type: String,
        },
        mrydat: {
          // 婚姻史-结婚日期
          type: Date,
        },
        cplrdtcnd: {
          // 配偶接触放射线情况
          type: String,
        },
        cplprfhthcnd: {
          // 配偶职业及健康状况
          type: String,
        },

        smksta: {
          // 吸烟酒史-吸烟情况 0：不吸烟1：偶尔吸2：经常吸；
          type: Number,
        },
        smkdayble: {
          // 每天吸烟包数
          type: String,
        },
        smkyerqty: {
          // 吸烟酒史-吸烟年数
          type: String,
        },
        winsta: {
          // 烟酒史-饮酒情况
          type: Number,
        },
        windaymlx: {
          // 饮酒每天毫升数 0：不饮酒1：偶尔饮2：经常饮
          type: String,
        },
        winyerqty: {
          // 饮酒年数
          type: String,
        },

        jzs: {
          //	家族史
          type: String,
        },
        grs: {
          //	个人史
          type: String,
        },
        oth: {
          // 其他情况
          type: String,
        },
      },
    ],
    symptomList: [
      // 症状信息
      {
        _id: {
          type: String,
          default: shortid.generate,
        },
        symptom: {
          // 症状编码 --需要转码
          type: String,
        },
        othsym: {
          //	其他症状
          type: String,
        },
        chkdat: {
          //	检查日期	D10	必填
          type: Date,
        },
        chkdoct: {
          //	检查医生	S..10	必填
          type: String,
        },
      },
    ],
    bhkAnamnesisList: [
      // 既往病史
      {
        othsym: {
          //	其他症状
          type: String,
        },
        chkdat: {
          //	检查日期	D10	必填
          type: Date,
        },
        chkdoct: {
          //	检查医生	S..10	必填
          type: String,
        },
        hstnam: {
          // 疾病名称
          type: String,
        },
        hstcruprc: {
          // 治疗经过
          type: String,
        },
        hstlps: {
          // 转归
          type: String,
        },
        hstunt: {
          // 诊断单位
          type: String,
        },
      },
    ],
    supoccdiseList: [
      // 疑似职业病
      {
        _id: {
          type: String,
          default: shortid.generate,
        },
        badrsn: {
          // 危害因素
          type: String,
        },
        occdise: {
          // 疑似职业病
          type: String,
        },
      },
    ],
    contraindList: [
      {
        _id: {
          type: String,
          default: shortid.generate,
        },
        // 职业禁忌证
        badrsn: {
          // 危害因素
          type: String,
        },
        contraind: {
          // 禁忌证
          type: String,
        },
      },
    ],
    caseCard: {
      // 个案卡
      _id: {
        type: String,
        default: shortid.generate,
      },
      originName: {
        // 用户上传文件名称
        type: String,
        default: '',
      },
      status: {
        // 用户上传状态
        type: String,
        default: '1',
      },
      staticName: {
        // 后台处理后存储到public静态资源的文件名
        type: String,
        default: '',
      },
    },
    confirmStatus: {
      // 确认状态 0未确认 1已确认
      type: Boolean,
      default: false,
    },
    encryptionAlgorithm: {
      type: String,
    },
    tjPlanId: {
      // 体检计划id
      type: String,
      ref: 'TjPlan',
    },
  });
  dbEncryption && suspectSchema.plugin(encryptionPlugin, {
    fields: {
      IDCard: 18,
      name: 3,
    },
    model: 'Suspect',
    ctx,
  });
  return mongoose.model('suspect', suspectSchema);
};
