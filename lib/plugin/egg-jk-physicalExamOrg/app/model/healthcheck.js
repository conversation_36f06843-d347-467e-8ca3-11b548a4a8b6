module.exports = app => {
  const shortid = require('shortid');
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;
  const HealthcheckSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    // ===================体检机构相关信息
    organization: {
      type: String,
    }, // 检测机构名，老数据中有，妥协，
    physicalExaminationOrgID: {
      type: String,
      ref: 'PhysicalExamOrg',
    },

    // ===================委托单位相关信息
    enterpriseName: {
      type: String,
    },
    EnterpriseID: {
      type: String,
      ref: 'Adminorg',
    },
    enterpriseContactsName: {
      type: String,
      default: '',
    },
    enterpriseContactsPhonNumber: {
      type: String,
      default: '',
    },
    enterpriseAddr: {
      type: Array,
      default: [],
    },
    specificAddress: {
      type: String,
      default: '',
    },
    workAddress: [{
      districts: {
        type: Array,
        default: [],
      },
      address: {
        type: String,
        default: '',
      },
      point: Array, // 经纬度
      _id: {
        type: String,
        default: shortid.generate,
      },
    }], // 工作场所

    // ====================项目信息
    year: String, // 年度
    projectNumber: String, // 报告编号
    checkDate: { // 项目开始时间
      type: Date,
      require: true,
    },
    checkEndDate: { // 项目结束时间
      type: Date,
    },
    checkPlace: { // 体检机构地点
      type: String,
    },
    approvalDate: { // 批准日期
      type: Date,
    },
    applyTime: {
      type: Date,
    }, // 上报日期
    checkType: { // 检查类型 0是上岗前 1是在岗 2是离岗 3复查 4应急  警告：用于过渡，3复查 废除 5 离岗后 6 普通体检
      type: String,
      require: true,
      enum: [ '0', '1', '2', '3', '4', '5', '6' ],
    },
    // 这玩意不要变，可以加，否则前端也要改
    shouldCheckNum: {
      type: Number,
      default: 0,
    }, // 应该检查人数
    actuallNum: {
      type: Number,
      default: 0,
    }, // 实际人数
    normal: {
      type: Number,
      default: 0,
    }, // 未见异常
    re_examination: {
      type: Number,
      default: 0,
    }, // 复查
    suspected: {
      type: Number,
      default: 0,
    }, // 疑似职业病
    forbid: {
      type: Number,
      default: 0,
    }, // 禁忌症
    otherDisease: {
      type: Number,
      default: 0,
    }, // 其他疾病
    comment: {
      type: String,
    }, // 评价

    medicalExaminationReport: {
      fileName: { // 原文件名
        type: String,
        default: '',
      },
      url: { // 文件地址
        type: String,
        default: '',
      },
      source: {
        type: String,
        default: 'oapi',
      }, // 文件存储来源端
    }, // 体检报告文件

    recheck: { // 是否复查
      type: Boolean,
      require: true,
      default: false,
    },

    createTime: { // 创建时间
      type: Date,
      default: Date.now,
    },

    source: { // 来源
      type: String,
      default: 'oapi',
    },

    // ===================上报信息
    superUserID: [{
      type: String,
      ref: 'SuperUser',
    }], // 上报的区县的ID
    reportStatus: {
      type: Boolean,
      default: true,
    }, // 上报状态， true上报

    // 退回状态
    giveBackStatus: {
      type: Boolean,
      default: false,
    },
    giveBackTime: Date, // 退回时间
    // 退回备注
    giveBackRemark: {
      type: String,
      default: '',
    },
    // 体检计划id
    tjPlanId: {
      type: String,
      ref: 'tjPlan',
    },
  });
  return mongoose.model('Healthcheck', HealthcheckSchema, 'healthchecks');
};
