module.exports = app => {
  const shortid = require('shortid');
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;
  const InterfaceLogSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    orgId: { // 绑定的机构id
      type: String,
      ref: 'PhysicalExamOrg',
    },
    soucre: { // 来源
      type: String,
      default: 'tj',
    },
    requestUrl: { // 请求的接口
      type: String,
    },
    requestBody: { // 请求进来的数据
      type: Object,
    },
    requestResult: { // 队列处理的结果
      type: Object,
    },
    handledTime: { // 处理结束时间
      type: Date,
    },
  }, { timestamps: true });
  return mongoose.model('interfaceLog', InterfaceLogSchema, 'interfaceLog');
};
