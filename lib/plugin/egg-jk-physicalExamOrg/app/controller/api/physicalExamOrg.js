const reportValidate = require('../../validate/tjreport');
const { siteFunc } = require('../../utils/index');
const path = require('path');
const _ = require('lodash');
const amqplib = require('amqplib');
const { promisify } = require('util');
const sleep = promisify(setTimeout);
const PhysicalExamOrgController = {
  async uploadPhyReportFile(ctx, app) {
    try {
      if (app.config.branch === 'wkzwy') {
        try {
          const result = await ctx.service.healthcheckPartial.uploadPhyReport(ctx);
          ctx.helper.renderCustom(ctx, { data: result, message: '文件上传成功', status: 201 });
        } catch (e) {
          ctx.auditLog('错误', `上传体检报告错误：${e.stack} 。`, 'error');
          ctx.helper.renderCustom(ctx, { message: e.message, status: 400 });
        }
        return;
      }
      const params = await ctx.service.healthcheckPartial.formatPhyReportData(ctx);
      const result = {
        status: 201,
        message: 'success',
      };
      if (!params.status) {
        const conn = await amqplib.connect(app.config.rabbitmq_url);
        // Sender
        const ch = await conn.createChannel();
        // console.log('\x1b[42m发送\x1b[0m');
        ch.sendToQueue(`${app.config.branch ? app.config.branch + '_' : ''}healthcheck_file_list`, Buffer.from(JSON.stringify({ token: ctx.header.token, accept: ctx.header.accept, ...params })), { persistent: true });
        ch.close();
      } else {
        result.status = params.status;
        result.message = params.message;
      }
      ctx.helper.renderCustom(ctx, result);
    } catch (error) {
      ctx.auditLog('错误', `上传体检报告发送队列错误：${error.stack} 。`, 'error');
      ctx.helper.renderCustom(ctx, {
        status: 400,
        message: error.message || '服务器错误',
      });
    }
  },

  async uploadHZWYPhyReportFile(ctx, app) {
    try {
      const accept = ctx.header.accept;
      const version = ctx.helper.getAPIVersion(accept, 'application/oapi.zyws.v', '+json');
      const result = await this['uploadPhyReportFileV' + version](ctx, app, ctx.request.body);
      ctx.helper.renderCustom(ctx, result);
    } catch (error) {
      ctx.auditLog('错误', `上传体检报告整体错误：${error.stack} 。`, 'error');
      ctx.helper.renderCustom(ctx, {
        status: 500,
        message: '服务器错误',
      });
    }
  },

  // info:{"creditCode":"91330108676781732W","projectNumber":"测试接口1"}
  async uploadPhyReportFileV1_0(ctx, app, params) {
    const { config } = app;
    const { newFile, physicalExamOrgId, projectNumber } = params;
    let status = 201,
      message = 'success';
    try {
      if (!projectNumber || projectNumber === '') {
        status = 202;
        message = '未找到对应体检编号的体检项目';
        siteFunc.deleteFile(ctx, path.join(config.upload_phy_report_path, physicalExamOrgId, newFile.staticFileName));
      } else {
        // 轮询体检项目1分钟，2s一次查询，共30次
        let healthcheckInfo = null;
        for (let i = 0; i < 30; i++) {
          healthcheckInfo = await ctx.service.healthcheck.item(ctx, { query: { physicalExaminationOrgID: physicalExamOrgId, projectNumber }, files: 'medicalExaminationReport' });
          if (healthcheckInfo) {
            break;
          }
          await sleep(2000);
        }
        if (healthcheckInfo) {
          if (healthcheckInfo.medicalExaminationReport) {
            siteFunc.deleteFile(ctx, path.join(config.upload_phy_report_path, healthcheckInfo.medicalExaminationReport.url || ''));
          }
          await ctx.service.healthcheck.update(ctx, healthcheckInfo._id, { medicalExaminationReport: {
            fileName: newFile.fileName,
            url: `${physicalExamOrgId}/${newFile.staticFileName}`,
            source: 'oapi',
          } });
        } else {
          status = 202;
          message = '未找到对应体检编号的体检项目';
          siteFunc.deleteFile(ctx, path.join(config.upload_phy_report_path, physicalExamOrgId, newFile.staticFileName));
        }
      }
    } catch (error) {
      status = 202;
      message = '保存时出现了错误';
      ctx.auditLog('错误', `上传体检报告文件处理错误：${error.stack} 。`, 'error');
      siteFunc.deleteFile(ctx, path.join(config.upload_phy_report_path, physicalExamOrgId, newFile.staticFileName));
    }

    return {
      status,
      message,
    };
  },

  // info:{"creditCode":"91330108676781732W","fileList":[{"projectNumber":"测试接口1","name":"1.docx"},{"projectNumber":"123131231","name":"2.docx"},{"projectNumber":"手动新建测试","name":"3.pdf"}]}
  async uploadPhyReportFileV1_0Old(ctx, app, params) {
    const { config } = app;
    const { newFiles, physicalExamOrgId, fileList } = params;
    const newFilesCount = newFiles.length;
    const fileListCount = fileList.length;
    const errorIndexArr = [];
    const errorArr = [];
    let errcount = 0,
      successCount = 0,
      status = 202,
      message = '',
      data = {};
    try {
      for (let i = 0; i < newFilesCount; i++) {
        try {
          let isEffective = false;
          let isSameMsg = false;
          const item = newFiles[i];
          for (let j = 0; j < fileListCount; j++) {
            try {
              const file = fileList[j];
              if (item.fileName === file.name) {
                isSameMsg = true;
                isEffective = true;
                const healthcheckInfo = await ctx.service.healthcheck.item(ctx, { query: { physicalExaminationOrgID: physicalExamOrgId, projectNumber: file.projectNumber }, files: 'medicalExaminationReport' });
                if (healthcheckInfo) {
                  if (healthcheckInfo.medicalExaminationReport) {
                    siteFunc.deleteFile(ctx, path.join(config.upload_phy_report_path, healthcheckInfo.medicalExaminationReport.url || ''));
                  }
                  await ctx.service.healthcheck.update(ctx, healthcheckInfo._id, { medicalExaminationReport: {
                    fileName: file.name,
                    url: `${physicalExamOrgId}/${item.staticFileName}`,
                    source: 'oapi',
                  } });
                  successCount++;
                } else {
                  isEffective = false;
                  errorIndexArr.push(i);
                  errorArr.push(`第${i + 1}个文件未找到对应体检编号的体检项目`);
                }
                break;
              }
            } catch (error) {
              ctx.auditLog('错误', `上传体检报告文件对比处理错误：${error.stack} 。`, 'error');
              isSameMsg = true;
              isEffective = false;
              errorIndexArr.push(i);
              errorArr.push(`第${i + 1}个文件保存处理时出现了错误`);
              break;
            }
          }
          if (!isEffective) {
            if (!isSameMsg) {
              errorIndexArr.push(i);
              errorArr.push(`第${i + 1}个文件保存处理时文件名不一致`);
            }
            errcount++;
            siteFunc.deleteFile(ctx, path.join(config.upload_phy_report_path, physicalExamOrgId, item.staticFileName));
          }
        } catch (error) {
          ctx.auditLog('错误', `上传体检报告文件处理错误：${error.stack} 。`, 'error');
          errcount++;
          errorIndexArr.push(i);
          errorArr.push(`第${i + 1}个文件保存时出现了错误`);
          continue;
        }
      }
      if (errcount > 0) {
        message += errorArr.join('      ');
      } else {
        status = 201;
        message = 'OK';
      }
      data = {
        total: newFilesCount,
        success: successCount,
        err: errcount,
        index: errorIndexArr.join(','),
      };
    } catch (error) {
      data = {};
      message = '参数错误';
      ctx.auditLog('错误', `上传体检报告整体错误：${error.stack} 。`, 'error');
    }

    return {
      status,
      message,
      data,
    };
  },

  async createPhyReport(ctx, app) {
    ctx.auditLog('createPhyReport: 提交体检报告', JSON.parse(JSON.stringify(ctx)), 'info');
    // console.log(ctx);
    // 进入队列
    try {
      const conn = await amqplib.connect(app.config.rabbitmq_url);
      const body = ctx.request.body;
      if (app.config.branch === 'hz' && body.report && body.report.workAddress) {
        // const checkArea = body.report.workAddress.includes('杭州市');
        const checkArea = [ '杭州', '建德', '淳安', '桐庐' ].some(e => body.report.workAddress.includes(e));
        if (!checkArea) {
          ctx.auditLog('导入体检项目', body.report.enterpriseName + '体检项目不隶属于杭州地区', 'error');
        }
      }
      const correlationId = await ctx.service.physicalExamOrg.generateInterFaceLog();
      // Sender
      const ch2 = await conn.createChannel();
      // console.log('\x1b[42m发送\x1b[0m', `${app.config.branch ? app.config.branch + '_' : ''}healthcheck_list`);
      ch2.sendToQueue(`${app.config.branch ? app.config.branch + '_' : ''}healthcheck_list`, Buffer.from(JSON.stringify({ token: ctx.header.token, accept: ctx.header.accept, body, correlationId })), { persistent: true });

      // const ch1 = await conn.createChannel();
      // await ch1.assertQueue(queue,{durable:true});
      // ch1.prefetch(1)
      // // Listener
      // ch1.consume(queue, async msg => {
      //   if (msg !== null) {
      //     quit = true;
      //     console.log('Recieved:', msg.content.toString());
      //     // const accept = ctx.header.accept;
      //     // const version = ctx.helper.getAPIVersion(accept, 'application/oapi.zyws.v', '+json');
      //     // result = await this[`createPhyReportV${version}`](ctx);
      //     // console.log(result,'\x1b[44mxassdsdsdddddd\x1b[0m')
      //     await sleep(5000);
      //     ch1.ack(msg);
      //   } else {
      //     console.log('Consumer cancelled by server');
      //   }
      // },{noAck: false});

      // while (!quit) {
      //   console.log('\x1b[41m等待...\x1b[0m');
      //   await sleep(900);
      // }
      ch2.close();
      await sleep(900); // 给用户调用上传文件接口提供时间

      ctx.helper.renderCustom(ctx, {
        status: 201,
        message: 'success',
      });
    } catch (error) {
      console.log(error);
      ctx.auditLog('错误', `创建体检项目发送队列错误：${error.stack} 。`, 'error');
      ctx.helper.renderCustom(ctx, {
        status: 500,
        message: '参数错误',
      });
    }
  },

  async createHZWYPhyReport(ctx) {
    try {
      const accept = ctx.header.accept;
      const version = ctx.helper.getAPIVersion(accept, 'application/oapi.zyws.v', '+json');
      const result = await this[`createPhyReportV${version}`](ctx);
      ctx.auditLog('Result--------------', result);

      await ctx.service.physicalExamOrg.updateInterfaceLog(result.correlationId, {
        handledTime: new Date(),
        requestResult: result,
      });
      ctx.helper.renderCustom(ctx, {
        status: result.status,
        message: result.message,
      });
    } catch (error) {
      ctx.auditLog('错误', `创建体检项目整体错误：${error.stack} 。`, 'error');
      ctx.helper.renderCustom(ctx, {
        status: 500,
        message: '参数错误',
      });
    }
  },

  async createPhyReportV1_0(ctx) {
    const { service } = ctx;
    const body = ctx.request.body;
    const correlationId = ctx.header.correlationid; // 接口日志id
    let data = body.data || null;
    let creditCode = null,
      isSubmit = null,
      report = {}, // 受检单位信息
      peopleInfoList = []; // 体检人员信息
    if (typeof data === 'string') {
      // x-www传输形式
      try {
        data = JSON.parse(data);
        creditCode = data.creditCode || null;
        isSubmit = data.isSubmit;
        report = data.report || {};
        peopleInfoList = data.peopleInfoList || [];
      } catch (e) {
        return {
          status: 202,
          message: '参数异常',
        };
      }
    } else {
      creditCode = body.creditCode || null;
      isSubmit = body.isSubmit;
      report = body.report || {};
      peopleInfoList = body.peopleInfoList || [];
    }

    ctx.auditLog('First ReportInfo----------', `${JSON.stringify(report)}`, 'info');

    if (typeof isSubmit !== 'boolean') {
      return {
        status: 202,
        message: '是否上报的类型必须为Boolean',
        correlationId,
      };
    }

    const {
      project: projectValidate,
      info: infoValidate,
      peopleInfoList: peopleInfoListValidate,
      peopleInfoList_notVaildIDCard: peopleInfoList_notVaildIDCardValidate,
    } = reportValidate;

    const info = { creditCode };
    const infoValidArr = infoValidate.validate(info);
    if (infoValidArr.length > 0) {
      const error = [];
      infoValidArr.forEach(i => error.push(i.message));
      return {
        status: 202,
        message: error.join(','),
        correlationId,
      };
    }

    const physicalExamOrg = await service.physicalExamOrg.item(ctx, { query: { organization: creditCode }, files: 'name' });
    if (_.isEmpty(physicalExamOrg)) {
      ctx.auditLog('体检机构无数据', `保存体检数据接口营业执照为${creditCode}的体检机构未注册！`, 'info');
      return {
        status: 202,
        message: '该体检机构还未注册, 请注册后再使用',
        correlationId,
      };
    }

    const physicalExamOrgId = physicalExamOrg._id;
    try {
      let isCreateEnterprise = false; // 是否创建企业，用于出现错误时数据还原判断是否删除企业
      const error = [];

      const projectValidArr = projectValidate.validate(report);
      if (projectValidArr.length > 0) {
        projectValidArr.forEach(o => error.push(o.message));
        return {
          status: 202,
          message: `项目信息错误详情：${error.join(' ')}`,
          correlationId,
        };
      }

      const needValidIDCardPeopleInfoList = peopleInfoList.filter(e => {
        // IDType: 1 身份证 2 户口簿 3 护照 4 军官证
        // IDType = 1 时做校验
        if (e.IDType) {
          if ((e.IDType + '') === '1') {
            return true;
          }
          return false;
        }
        return true;
      });

      const notNeedValidIDCardPeopleInfoList = peopleInfoList.filter(e => {
        if (e.IDType) {
          if ((e.IDType + '') !== '1') {
            return true;
          }
        }
        return false;
      });
      const peopleInfoListValidArr_needValidIDCard = peopleInfoListValidate.validate({ peopleInfoList: needValidIDCardPeopleInfoList });
      const peopleInfoListValidArr_notNeedValid = peopleInfoList_notVaildIDCardValidate.validate({ peopleInfoList: notNeedValidIDCardPeopleInfoList });
      const peopleInfoListValidArr = [ ...peopleInfoListValidArr_needValidIDCard, ...peopleInfoListValidArr_notNeedValid ];

      if (peopleInfoListValidArr.length > 0) {
        peopleInfoListValidArr.forEach(i => error.push(i.message));
        return {
          status: 202,
          message: `体检人员信息错误详情：${error.join(' ')}`,
          correlationId,
        };
      }

      const count = await service.healthcheck.count({
        projectNumber: report.projectNumber,
        physicalExaminationOrgID: physicalExamOrgId,
      });
      if (count > 0) {
        return {
          status: 202,
          message: '此报告已经创建，跳过',
          correlationId,
        };
      }

      let EnterpriseID = '';
      const checkDate = new Date(parseInt(report.checkDate));
      const enterpriseName = report.enterpriseName.replace(/（/g, '(').replace(/）/g, ')');
      const enterpriseCode = (report.enterpriseCode || '').trim();
      let regenterpriseName = '';
      let adminorg_query_params = { code: enterpriseCode };
      if (!enterpriseCode) {
        // 企业查询正则 /浙江华采[\(（]*杭州[\)）]*有限公司/
        regenterpriseName = new RegExp(report.enterpriseName
          .replace(/(（|\()/g, '[\(（]*')
          .replace(/(）|\))/g, '[\)）]*'));
        adminorg_query_params = { cname: { $regex: regenterpriseName } };
      }
      const Adminorg = await service.adminorg.item(ctx, { query: adminorg_query_params, files: 'workAddress cname' });
      if (!_.isEmpty(Adminorg)) {
        EnterpriseID = Adminorg._id;
        const workAddressStr = report.workAddress;
        const places = await service.healthcheckPartial.getPoint(workAddressStr);
        let info = { workAddName: '' };
        if (places && places.status === '1' && places.geocodes[0]) {
          const addrDetail = places.geocodes[0];
          const streetDetail = addrDetail.street + addrDetail.number;
          info = {
            workAdd: [ addrDetail.adcode.slice(0, 2) + '**********', addrDetail.adcode.slice(0, 4) + '00000000', addrDetail.adcode.slice(0, 6) + '000000', '' ],
            location: addrDetail.location,
            workAddName: addrDetail.province + '/' + addrDetail.city + '/' + addrDetail.district,
            name: streetDetail || addrDetail.formatted_address || '',
          };
        }
        report.workAddress = info.workAddName !== '' ? {
          districts: info.workAddName.split('/'),
          address: workAddressStr,
          point: info.location ? info.location.split(',') : [],
        } : { districts: [],
          point: [],
          address: '' };
        // report.workAddress = (Adminorg.workAddress && Adminorg.workAddress.length > 0) ? Adminorg.workAddress[0] : { districts: [],
        //   point: [],
        //   address: '' };
      } else {
        if (report.checkDate) {
          isCreateEnterprise = true;
          try {
            const result = await service.healthcheckPartial.createNewCompany({
              enterpriseName,
              regenterpriseName,
              workAddress: report.workAddress,
              code: enterpriseCode,
            }, physicalExamOrgId, checkDate); // physicalExamOrgId, checkDate 处理企业曾用名
            EnterpriseID = result.EnterpriseID;
            report.workAddress = (result.workAddress && result.workAddress.length > 0) ? result.workAddress[0]
              : { districts: [],
                point: [],
                address: '' };
          } catch (error) {
            ctx.auditLog('错误', `创建企业功能错误：${error.stack} 。`, 'error');
            return {
              status: 202,
              message: '企业信息创建异常',
              correlationId,
            };
          }
        }

      }
      report.EnterpriseID = EnterpriseID;

      const peopleInfoListCount = peopleInfoList.length;
      for (let i = 0; i < peopleInfoListCount; i++) {
        const item = peopleInfoList[i];
        const { CwithO } = item;
        if (/疾病.*异常/.test(CwithO)) {
          item.CwithO = '其他疾病或异常';
        } else if (!/未见明显职业禁忌/.test(CwithO) &&
        /(禁忌|职业禁)/.test(CwithO)) {
          item.CwithO = '禁忌证';
        } else if (/(复查|复检)/.test(CwithO)) {
          item.CwithO = '复查';
        } else if (/(职业病|疑似)/.test(CwithO)) {
          item.CwithO = '疑似职业病';
        } else if (/(未见异常|未见明显异常|无异常|目前未见|未见明显|-)/.test(CwithO)) {
          item.CwithO = '目前未见异常';
        }
      }

      peopleInfoList = await service.healthcheckPartial.FormatTable(peopleInfoList);
      const checkEndDate = report.checkEndDate ? new Date(parseInt(report.checkEndDate)) : checkDate;
      const createProject = {
        projectNumber: report.projectNumber,
        enterpriseContactsPhonNumber: report.enterpriseContactsPhoneNumber,
        enterpriseContactsName: report.enterpriseContactsName || '',
        checkType: report.checkType,
        recheck: report.recheck,
        checkDate,
        checkEndDate,
        approvalDate: new Date(parseInt(report.approvalDate)),
        year: checkEndDate.getFullYear(),
        checkPlace: report.checkPlace,
        enterpriseName,
        EnterpriseID: report.EnterpriseID,
        physicalExaminationOrgID: physicalExamOrgId,
        shouldCheckNum: report.shouldCheckNum,
        actuallNum: report.actuallNum,
        normal: 0,
        re_examination: 0,
        suspected: 0,
        forbid: 0,
        otherDisease: 0,
        notCwithO: 0,
        enterpriseAddr: report.workAddress.districts,
        specificAddress: report.workAddress.address,
        workAddress: [ report.workAddress ],
      };
      if (isSubmit) {
        createProject.reportStatus = true;
        createProject.applyTime = new Date();
      }

      const res = await service.healthcheckPartial.createPhysicalData(createProject, peopleInfoList, physicalExamOrgId, physicalExamOrg.name || '', isSubmit);
      if (!res) {
        if (isCreateEnterprise) {
          service.adminorg.removes(ctx, EnterpriseID);
        }
        return {
          status: 202,
          message: '体检人员信息创建异常',
          correlationId,
        };
      }
    } catch (error) {
      ctx.auditLog('错误', `创建体检数据循环过程错误：${error.stack} 。`, 'error');
      return {
        status: 202,
        message: '数据创建异常',
        correlationId,
      };
    }

    return {
      status: 201,
      message: 'success',
      correlationId,
    };
  },

  async createPhyReportV1_0Old(ctx) {
    const { service } = ctx;
    // const params = test.testScama;
    const body = ctx.request.body;
    let data = body.data || null;
    let creditCode = null,
      listArray = [];
    if (typeof data === 'string') {
      // x-www传输形式
      try {
        data = JSON.parse(data);
        creditCode = data.creditCode;
        listArray = data.list;
      } catch (e) {
        return {
          status: 202,
          message: '参数异常',
          data: {
            total: 0,
            success: 0,
            skip: 0,
            err: 0,
          },
        };
      }
    } else {
      creditCode = body.creditCode || null;
      listArray = body.list || [];
    }

    const {
      project: projectValidate,
      info: infoValidate,
      peopleInfoList: peopleInfoListValidate,
    } = reportValidate;

    const info = { creditCode };
    const infoValidArr = infoValidate.validate(info);
    if (infoValidArr.length > 0) {
      const error = [];
      infoValidArr.forEach(i => error.push(i.message));
      return {
        status: 202,
        message: error.join(','),
        data: {
          total: 0,
          success: 0,
          skip: 0,
          err: 0,
        },
      };
    }

    const physicalExamOrg = await service.physicalExamOrg.item(ctx, { query: { organization: creditCode }, files: 'name' });
    if (_.isEmpty(physicalExamOrg)) {
      ctx.auditLog('体检机构无数据', `保存体检数据接口营业执照为${creditCode}的体检机构未注册！`, 'info');
      return {
        status: 202,
        message: '该体检机构还未注册, 请注册后再使用',
        data: {
          total: 0,
          success: 0,
          skip: 0,
          err: 0,
        },
      };
    }

    let successCount = 0,
      skipCount = 0,
      skipMsg = '',
      errorCount = 0;
    const skipIndexArr = [];
    const totalCount = listArray.length;

    if (!totalCount) {
      return {
        status: 202,
        message: '参数异常，list必须是一个JSON数组',
        data: {
          total: 0,
          success: 0,
          skip: 0,
          err: 0,
        },
      };
    }

    const physicalExamOrgId = physicalExamOrg._id;
    const resError = [];
    const resErrIndexArr = [];
    for (let i = 0; i < totalCount; i++) {
      try {
        let {
          isSubmit,
          report, // 受检单位信息
          peopleInfoList, // 体检人员信息
        } = listArray[i];
        isSubmit = isSubmit || false; // 是否上报
        let isCreateEnterprise = false; // 是否创建企业，用于出现错误时数据还原判断是否删除企业
        let error = [];

        const projectValidArr = projectValidate.validate(report);
        if (projectValidArr.length > 0) {
          error.push(`第${i + 1}个项目信息错误详情：`);
          projectValidArr.forEach(o => error.push(o.message));
          resError.push(error.join(' '));
          resErrIndexArr.push(i);
          errorCount++;
          continue;
        }

        const peopleInfoListValidArr = peopleInfoListValidate.validate({ peopleInfoList });
        if (peopleInfoListValidArr.length > 0) {
          error = [];
          error.push(`第${i + 1}个体检人员信息错误详情：`);
          peopleInfoListValidArr.forEach(i => error.push(i.message));
          resError.push(error.join(' '));
          resErrIndexArr.push(i);
          errorCount++;
          continue;
        }

        const count = await service.healthcheck.count({
          projectNumber: report.projectNumber,
          physicalExaminationOrgID: physicalExamOrgId,
        });
        if (count > 0) {
          skipMsg += ` [${report.projectNumber}] `;
          skipIndexArr.push(i);
          skipCount++;
          continue;
        }

        let EnterpriseID = '';
        const enterpriseName = report.enterpriseName.replace(/（/g, '(').replace(/）/g, ')');
        // 企业查询正则 /浙江华采[\(（]*杭州[\)）]*有限公司/
        const regenterpriseName = new RegExp(report.enterpriseName
          .replace(/(（|\()/g, '[\(（]*')
          .replace(/(）|\))/g, '[\)）]*'));
        const Adminorg = await service.adminorg.item(ctx, { query: { cname: { $regex: regenterpriseName } }, files: 'workAddress cname' });
        if (!_.isEmpty(Adminorg)) {
          EnterpriseID = Adminorg._id;
          report.workAddress = (Adminorg.workAddress && Adminorg.workAddress.length > 0) ? Adminorg.workAddress[0] : { districts: [],
            point: [],
            address: '' };
        } else {
          isCreateEnterprise = true;
          try {
            const result = await service.healthcheckPartial.createNewCompany({
              enterpriseName,
              regenterpriseName,
              workAddress: report.workAddress,
            });
            EnterpriseID = result.EnterpriseID;
            report.workAddress = (result.workAddress && result.workAddress.length > 0) ? result.workAddress[0]
              : { districts: [],
                point: [],
                address: '' };
          } catch (error) {
            ctx.auditLog('错误', `创建企业功能错误：${error.stack} 。`, 'error');
            resError.push(`第${i + 1}个企业信息创建异常`);
            resErrIndexArr.push(i);
            errorCount++;
            continue;
          }
        }
        report.EnterpriseID = EnterpriseID;

        const peopleInfoListCount = peopleInfoList.length;
        for (let i = 0; i < peopleInfoListCount; i++) {
          const item = peopleInfoList[i];
          const { CwithO } = item;
          if (/(疾病.*异常|异常)/.test(CwithO)) {
            item.CwithO = '其他疾病或异常';
          } else if (!/未见明显职业禁忌/.test(CwithO) &&
        /(禁忌|职业禁)/.test(CwithO)) {
            item.CwithO = '禁忌证';
          } else if (/(复查|复检)/.test(CwithO)) {
            item.CwithO = '复查';
          } else if (/(职业病|疑似)/.test(CwithO)) {
            item.CwithO = '疑似职业病';
          } else if (/(未见异常|未见明显异常|无异常|目前未见|未见明显|-)/.test(CwithO)) {
            item.CwithO = '目前未见异常';
          }
        }

        peopleInfoList = await service.healthcheckPartial.FormatTable(peopleInfoList);
        const checkDate = new Date(parseInt(report.checkDate));
        const checkEndDate = report.checkEndDate ? new Date(parseInt(report.checkEndDate)) : checkDate;
        const createProject = {
          projectNumber: report.projectNumber,
          enterpriseContactsPhonNumber: report.enterpriseContactsPhoneNumber,
          enterpriseContactsName: report.enterpriseContactsName || '',
          checkType: report.checkType,
          recheck: report.recheck,
          checkDate,
          checkEndDate,
          year: checkEndDate.getFullYear(),
          approvalDate: new Date(parseInt(report.approvalDate)),
          checkPlace: report.checkPlace,
          enterpriseName,
          EnterpriseID: report.EnterpriseID,
          physicalExaminationOrgID: physicalExamOrgId,
          shouldCheckNum: report.shouldCheckNum,
          actuallNum: report.actuallNum,
          normal: 0,
          re_examination: 0,
          suspected: 0,
          forbid: 0,
          otherDisease: 0,
          notCwithO: 0,
          enterpriseAddr: report.workAddress.districts,
          specificAddress: report.workAddress.address,
          workAddress: [ report.workAddress ],
        };
        if (isSubmit) {
          createProject.reportStatus = true;
          createProject.applyTime = new Date();
        }

        const res = await service.healthcheckPartial.createPhysicalData(createProject, peopleInfoList, physicalExamOrgId, physicalExamOrg.name);
        if (!res) {
          if (isCreateEnterprise) {
            service.adminorg.removes(ctx, EnterpriseID);
          }
          resError.push(`第${i + 1}个体检人员信息创建异常`);
          resErrIndexArr.push(i);
          errorCount++;
          continue;
        } else {
          successCount++;
        }
      } catch (error) {
        ctx.auditLog('错误', `创建体检数据循环过程错误：${error.stack} 。`, 'error');
        resError.push(`第${i + 1}个数据创建异常`);
        resErrIndexArr.push(i);
        errorCount++;
        continue;
      }
    }

    let message = '';
    let status = 201;
    if (skipCount > 0) {
      message += `体检编号为${skipMsg}的体检报告已上传，请勿重复上传      `;
      status = 202;
    }
    if (errorCount > 0) {
      message += resError.join('      ');
      status = 202;
    }
    if (skipCount === 0 && errorCount === 0) {
      message = 'OK';
    }
    return {
      status,
      message,
      data: {
        total: totalCount,
        success: successCount,
        skip: skipCount,
        skipindex: skipIndexArr.join(','),
        err: errorCount,
        index: resErrIndexArr.join(','),
      },
    };
  },

  async getSuperPhyReportList(ctx) {
    ctx.auditLog('getSuperPhyReportList: 行政用户获取体检报告数据', JSON.parse(JSON.stringify(ctx)), 'info');
    console.log(ctx);
    try {
      const accept = ctx.header.accept;
      const version = ctx.helper.getAPIVersion(accept, 'application/oapi.zyws.v', '+json');
      const result = await this[`getPhyReportListV${version}`](ctx, true);
      ctx.helper.renderCustom(ctx, {
        status: result.status,
        message: result.message,
        data: result.data,
      });
    } catch (error) {
      ctx.auditLog('错误', `获取体检报告列表整体错误：${error.stack} 。`, 'error');
      ctx.helper.renderCustom(ctx, {
        status: 500,
        message: '参数错误',
      });
    }
  },

  async getPhyReportList(ctx) {
    ctx.auditLog('getPhyReportList: 体检机构获取体检报告数据', JSON.parse(JSON.stringify(ctx)), 'warn');
    console.log(ctx);
    try {
      const accept = ctx.header.accept;
      const version = ctx.helper.getAPIVersion(accept, 'application/oapi.zyws.v', '+json');
      const result = await this[`getPhyReportListV${version}`](ctx, false);
      ctx.helper.renderCustom(ctx, {
        status: result.status,
        message: result.message,
        data: result.data,
      });
    } catch (error) {
      ctx.auditLog('错误', `获取体检报告列表整体错误：${error.stack} 。`, 'error');
      ctx.helper.renderCustom(ctx, {
        status: 500,
        message: '参数错误',
      });
    }
  },

  async getPhyReportListV1_0(ctx, isSuper) {
    const { service } = ctx;
    const query = ctx.query;
    let queryObj = {};
    let result = [];
    try {
      const id = _.isEmpty(query.id) ? '' : query.id.toString().trim(), // string
        enterpriseName = _.isEmpty(query.name) ? '' : query.name.toString().trim(), // string
        projectNumber = _.isEmpty(query.projectNo) ? '' : query.projectNo.toString().trim(), // string
        checkDate = _.isEmpty(query.checkDate) ? '' : !isNaN(parseInt(query.checkDate.toString().trim())) ? parseInt(query.checkDate.toString().trim()) : '', // string 体检开始日期  For example: 2022.05.09 => '1652025600000'
        checkEndDate = _.isEmpty(query.checkEndDate) ? '' : !isNaN(parseInt(query.checkEndDate.toString().trim())) ? parseInt(query.checkEndDate.toString().trim()) : '', // string 体检结束日期  For example: 2022.05.09 => '1652025600000'
        applyTime = _.isEmpty(query.applyTime) ? '' : !isNaN(parseInt(query.applyTime.toString().trim())) ? parseInt(query.applyTime.toString().trim()) : '', // string 上报日期  For example: 2022.05.09 => '1652025600000'
        createTime = _.isEmpty(query.created) ? '' : !isNaN(parseInt(query.created.toString().trim())) ? parseInt(query.created.toString().trim()) : '', // string  For example: 2022.05.09 => '1652025600000'
        purPage = _.isEmpty(query.purPage) ? 1 : !isNaN(parseInt(query.purPage.toString().trim())) ? parseInt(query.purPage.toString().trim()) : 1, // int
        pageSize = _.isEmpty(query.pageSize) ? 20 : !isNaN(parseInt(query.pageSize.toString().trim())) ? parseInt(query.pageSize.toString().trim()) : 20, // int
        customData = _.isEmpty(query.customData) ? !isSuper ? '_id enterpriseName projectNumber checkDate applyTime createTime' : '_id enterpriseName projectNumber checkDate applyTime' : query.customData.toString().trim(); // 指定获取自定义字段数据(完全重置,非追加) string  For example: 'field1,field2'

      const payload = { current: purPage, pageSize };
      const files = customData === 'ALL' ? '' : customData.replace(/(,|，)/g, ' ');

      !_.isEmpty(id) ? queryObj._id = id : null;
      !_.isEmpty(enterpriseName) ? queryObj.enterpriseName = enterpriseName : null;
      !_.isEmpty(projectNumber) ? queryObj.projectNumber = projectNumber : null;

      const orginToken = ctx.header.token;
      const token = typeof orginToken === 'string' ? JSON.parse(orginToken) : orginToken;
      const agentId = token.agentId || '';
      const apiUser = await service.apiUser.item(ctx, {
        files: { _id: 1, orgId: 1 },
        query: { _id: agentId, status: 1, enable: true },
      });
      if (!_.isEmpty(apiUser)) {
        if (!isSuper) {
          // 体检机构使用的无指定地址区域查询参数，限制在当前体检机构下查询
          queryObj = await this.get_params(ctx, service, apiUser, queryObj);
        } else {
          // 行政单位有地区限制  文档：address: 示例值：'福建,福州'  说明：查询指定区域的数据
          queryObj = await this.get_super_params(ctx, service, query, apiUser, queryObj);
        }
      }

      !isNaN(parseInt(checkDate)) ? queryObj.checkDate = { $gte: new Date(checkDate) } : null;
      !isNaN(parseInt(checkEndDate)) ? queryObj.checkEndDate = { $lte: new Date(checkEndDate) } : null;
      !isNaN(parseInt(applyTime)) ? queryObj.applyTime = { $gte: new Date(applyTime) } : null;
      !isNaN(parseInt(createTime)) ? queryObj.createTime = { $gte: new Date(createTime) } : null;

      const healthCheckList = await service.healthcheck.find(payload, {
        query: queryObj,
        files,
      });
      if (healthCheckList) {
        result = healthCheckList.docs;
      }
    } catch (error) {
      ctx.auditLog('错误', `获取体检报告数据错误：${error.stack} 。`, 'error');
      return {
        status: 202,
        message: '数据获取异常',
        data: result,
      };
    }

    return {
      status: 200,
      message: 'success',
      data: result,
    };
  },

  /**
   * 查询体检项目获取体检机构参数
   * @param {*} ctx ctx
   * @param {*} service service
   * @param {*} apiUser OAPI用户信息
   * @param {*} queryObj 整体查询参数
   * @return 返回整体查询参数
   */
  async get_params(ctx, service, apiUser, queryObj) {
    const physicalExamOrg = await service.physicalExamOrg.item(ctx, { query: { _id: apiUser.orgId }, files: '_id' });
    if (!_.isEmpty(physicalExamOrg)) {
      queryObj.physicalExaminationOrgID = physicalExamOrg._id || '';
    } else {
      // 若无体检机构则查不到任何数据
      queryObj._id = 'AZ90T2...';
    }
    return queryObj;
  },

  /**
   * 查询体检项目获取福州行政单位参数
   * @param {*} ctx ctx
   * @param {*} service service
   * @param {*} query 用户传的完整筛选参数
   * @param {*} apiUser OAPI用户信息
   * @param {*} queryObj 整体查询参数
   * @return 返回整体查询参数
   */
  async get_super_params(ctx, service, query, apiUser, queryObj) {
    let workAddress = _.isEmpty(query.address) ? '' : query.address.toString().trim(); // string  For example: '福建,福州'
    const superUser = await service.superUser.item(ctx, { query: { _id: apiUser.orgId }, files: '-_id regAdd' });
    const superUserRegAddr = superUser ? superUser.regAdd : [];
    const maxAddr = await service.district.item(ctx, { query: { name: new RegExp(superUserRegAddr[superUserRegAddr.length - 1]) }, files: '-_id area_code name' });
    const maxCode = maxAddr ? maxAddr.area_code : '';
    let minLevel = superUserRegAddr[superUserRegAddr.length - 1];
    let workAddressArr = superUserRegAddr;
    let workAddressArrCount = workAddressArr.length;
    if (!_.isEmpty(workAddress)) {
      workAddress = workAddress.replace(/，/g, ',');
      workAddressArr = workAddress.split(',');
      workAddressArrCount = workAddressArr.length;
      minLevel = workAddressArr[workAddressArrCount - 1];
    } else {
      workAddress = workAddressArr.join(',');
    }
    const addrItem = await service.district.item(ctx, { query: { name: new RegExp(minLevel), merger_name: new RegExp(workAddress.replace(/(省|市|街道|区|县|镇)/g, '')) }, files: '-_id area_code parent_code name merger_name' });

    if (addrItem) {
      const isCheck = await this.check_loop(ctx, addrItem.parent_code, addrItem.area_code, maxCode);
      if (isCheck) {
        workAddressArr = JSON.parse(JSON.stringify(workAddressArr));
        for (let i = 0; i < workAddressArrCount; i++) {
          workAddressArr[i] = new RegExp(workAddressArr[i]);
        }
        queryObj['workAddress.districts'] = { $all: workAddressArr };
      }
    }
    return queryObj;
  },

  /**
   * 检查地址区域许可查询范围
   * @param {object} ctx ctx
   * @param {string} parent_code 父级区域Code
   * @param {string} area_code 当前区域Code
   * @param {string} maxCode 最高许可范围Code
   * @return 允许获取 true 拒绝获取 false
   */
  async check_loop(ctx, parent_code, area_code, maxCode) {
    parent_code = parent_code || '';
    if (parent_code === maxCode || area_code === maxCode) {
      return true;
    }
    const parent = await ctx.service.district.item(ctx, { query: { area_code: parent_code }, files: '-_id parent_code area_code' });
    if (parent) {
      return await this.check_loop(ctx, parent.parent_code, parent.area_code, maxCode);
    }
    return false;
  },
};
module.exports = PhysicalExamOrgController;
