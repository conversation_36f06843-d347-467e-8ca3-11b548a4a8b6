const Service = require('egg').Service;
const path = require('path');
const {
  _unionQuery,
  _list,
  _item,
  _count,
  _create,
  _update,
  _removes,
  _removeAll,
} = require(path.join(process.cwd(), 'app/service/general'));

class PhysicalExamOrgService extends Service {
  async unionQuery(payload, {
    collections = [],
    unwindArray = [],
    query = {},
    searchKeys = [],
    files = null,
    sort = {},
    statisticsFiles = [],
  } = {}) {
    const listdata = _unionQuery(this.ctx.model.PhysicalExamOrg, payload, {
      collections,
      unwindArray,
      query,
      searchKeys,
      files,
      sort,
      statisticsFiles,
    });
    return listdata;
  }

  async find(payload, {
    query = {},
    searchKeys = [],
    populate = [],
    files = null,
    sort = {},
  } = {}) {
    const listdata = _list(this.ctx.model.PhysicalExamOrg, payload, {
      query,
      searchKeys,
      populate,
      files,
      sort,
    });
    return listdata;
  }

  async count(params = {}) {
    return _count(this.ctx.model.PhysicalExamOrg, params);
  }

  async create(payload) {
    return _create(this.ctx.model.PhysicalExamOrg, payload);
  }

  async item(res, params = {}) {
    return _item(res, this.ctx.model.PhysicalExamOrg, params);
  }

  async update(res, _id, payload) {
    return _update(res, this.ctx.model.PhysicalExamOrg, _id, payload);
  }

  async removes(res, values, key = '_id') {
    return _removes(res, this.ctx.model.PhysicalExamOrg, values, key);
  }

  async removeAll() {
    return _removeAll(this.ctx.model.PhysicalExamOrg);
  }
  async generateInterFaceLog() {
    const { ctx } = this;
    // 生成接口日志
    try {
      let token = ctx.header.token;
      if (typeof token === 'string') {
        token = JSON.parse(token);
      }

      const body = ctx.request.body;
      let data = body.data || null;

      if (typeof data === 'string') {
        // x-www传输形式
        try {
          data = JSON.parse(data);
        } catch (e) {
          return {
            status: 400,
            message: '参数异常',
          };
        }
      } else {
        data = body;
      }

      const apiUser = await ctx.model.ApiUser.findOne({ _id: token.agentId });
      const logData = {
        orgId: apiUser.orgId,
        requestUrl: ctx.request.url,
        requestBody: data,
      };
      const res = await new ctx.model.InterfaceLog(logData).save();
      return res && res._id;
    } catch (err) {
      console.log(err);
    }
    return '';
  }
  async updateInterfaceLog(correlationId, daat) {
    if (correlationId) {
      await this.ctx.model.InterfaceLog.update({ _id: correlationId }, {
        $set: daat,
      });
    }
  }
}

module.exports = PhysicalExamOrgService;
