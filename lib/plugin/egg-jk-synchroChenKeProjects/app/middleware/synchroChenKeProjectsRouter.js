const synchroChenKeProjectsApiController = require('../controller/api/synchroChenKeProjects');
const synchroChenKeProjectsAdminController = require('../controller/manage/synchroChenKeProjects');

module.exports = (options, app) => {

  return async function synchroChenKeProjectsRouter(ctx, next) {

    const pluginConfig = app.config.jk_synchroChenKeProjects;
    await app.initPluginRouter(ctx, pluginConfig, synchroChenKeProjectsAdminController, synchroChenKeProjectsApiController);
    await next();

  };

};

