
const Service = require('egg').Service;
const moment = require('moment');
const Axios = require('axios');
const shortid = require('shortid');
class TestPaperService extends Service {

  // 更新项目实际金额
  async projectFeeV1_0(params) {
    const { ctx } = this;
    // 校验
    const feeRule = { required: true, type: 'Number' };
    const rules = [
      {
        label: '实际合作费',
        field: 'salesFee',
        rules: feeRule,
        realField: 'cooperationFee',
      },
      {
        label: '实际评审费',
        field: 'reviewFee',
        rules: feeRule,
      },
      {
        label: '实际其他费用',
        field: 'otherFee',
        rules: feeRule,
      },
      {
        label: '实际分包费用',
        field: 'outsourceFee',
        rules: feeRule,
      },
      {
        label: '实际净合同值',
        field: 'netValue',
        rules: feeRule,
        realField: 'netEarnings',
      },
    ];
    const postparams = {};
    const errMsgs = [];
    rules.forEach(item => {
      params[item.field] = params[item.field].trim();
      if (!params[item.field]) {
        errMsgs.push(item.label + '不能为空');
      } else {
        params[item.field] += '';
        if (!/^[0-9]*$/.test(params[item.field])) {
          errMsgs.push(item.label + '需输入数字字符');
        } else {
          if (item.realField) {
            postparams[item.realField] = params[item.field];
          } else {
            postparams[item.field] = params[item.field];
          }
        }
      }
    });

    if (errMsgs.length) {
      return { code: -1, errMsg: errMsgs.join('；') };
    }

    // 更新数据
    const jcqlcInfo = await ctx.model.JcqlcProject.findOne({ projectSN: params.projectSN }, { _id: 1 });
    if (!jcqlcInfo) {
      return { code: -1, errMsg: `项目${params.projectSN}暂未提交到全流程，请先将该项目推送至全流程` };
    }
    await ctx.model.JcqlcProject.updateOne({ projectSN: params.projectSN }, { $set: postparams });

  }
  /**
   * <AUTHOR>
   * @description 根据区域名称匹配地址
   * @param {Array<String>} areaNames required:true 地址名称
   * createAt 2023-04-12
   * @return {Object}
   *    {
   *       areaNames: Array<String>,匹配到的地址名称
   *       detailedAddress:String, 未匹配的地址 放在详细地址中
   *       codes: Array<String>, 匹配到的地址编码
   *    }
   */
  async getAreaCode(areaNames) {
    const res = {
      areaNames: [],
      detailedAddress: '',
      codes: [],
    };
    let index = 0;
    let parent_code = '0';
    while (index < areaNames.length) {
      if (!areaNames[index]) areaNames[index] = ' ';
      areaNames[index] = areaNames[index].replace(/\(/g, '（');
      areaNames[index] = areaNames[index].replace(/\)/g, '）');
      const data = await this.ctx.model.District.findOne({
        parent_code,
        name: { $regex: areaNames[index] },
      });
      if (data) {
        parent_code = data.area_code;
        res.areaNames.push(data.name);
        res.codes.push(data.area_code);
        index++;
      } else { // 结束循环
        res.detailedAddress = areaNames.slice(index).join('');
        index = areaNames.length;
      }
    }
    return res;
  }

  async findRegAdd(regAdd) {
    // 更改地址处理方式，晨科以"/"划分;不用信用代码匹配
    // 处理以/区分区域的地址 浙江省/杭州市/西湖区
    if (regAdd.indexOf('/') !== -1) {
      const arr = regAdd.split('/');
      return await this.getAreaCode(arr);
    }

    // 处理没有其他分隔符的地址形式 浙江省杭州市西湖区
    let streetName = '';// 街道名称
    // const queryCode = code.substr(2, 6); // 区域代码
    let names = regAdd.split(/省|市|区|县/);
    const remainderName = names[names.length - 1];
    names = names.slice(0, -1);
    const streetIndex = remainderName.search(/街道|镇|乡/);
    let streetEndIndex = -1;
    if (streetIndex !== -1) {
      if (remainderName.indexOf('街道') !== -1) {
        streetEndIndex = streetIndex + 2;
      } else {
        streetEndIndex = streetIndex + 1;
      }

      streetName = remainderName.slice(streetIndex - 2, streetIndex + 1);
    }
    const streetIndex2 = remainderName.search(/办事/);
    if (streetIndex2 !== -1) {
      streetEndIndex = streetIndex2 + 2;
      streetName = remainderName.slice(streetIndex2 - 2, streetIndex2);
    }
    if (streetName) {
      names.push(streetName);
      // 处理街道后面的详细地址
      if (streetEndIndex !== -1 && streetEndIndex < remainderName.length) {
        names.push(remainderName.slice(streetEndIndex));
      }
    }

    return await this.getAreaCode(names);
  }

  // 以下是lht原来逻辑 先保留
  // async findRegAdd(code, regAdd) {
  //   // 更改地址处理方式，晨科以"/"划分;不用信用代码匹配
  //   if (regAdd.indexOf('/') !== -1) {
  //     const arr = regAdd.split('/');
  //     // const address = [];
  //     const area = [];
  //     let value = '';
  //     for (let i = arr.length - 1; i > 0; i--) {
  //       const ele = arr[i];
  //       value = await this.ctx.model.District.findOne({ $or: [{ $and: [{ area_code: { $regex: '^' + code.substr(2, 6) } }, { name: { $regex: ele } }] }, { $and: [{ parent_code: { $regex: '^' + code.substr(2, 6) } }, { name: { $regex: ele } }] }] });
  //       if (value) {
  //         area.push(value.name);
  //         let parent_code = value.parent_code;
  //         for (let index = 0; index < 5; index++) {
  //           if (parent_code !== '0') {
  //             const data = await this.ctx.model.District.find({ area_code: parent_code });
  //             area.push(data[0].name);
  //             parent_code = data[0].parent_code;
  //           } else {
  //             break;
  //           }
  //         }
  //         area.reverse();
  //         return area;
  //       }
  //     }
  //     return area;
  //   }
  //   const area = []; // districtRegAdd
  //   let parent_code = '';
  //   const queryCode = code.substr(2, 6); // 区域代码
  //   let sumQu = 0;
  //   let sumXian = 0;
  //   let sumShi = 0;
  //   let indexXian = '';
  //   let queryName = '';
  //   const len = regAdd ? regAdd.length : 0;
  //   let street;
  //   let shi;
  //   if (len) {
  //     for (let i = 0; i < len; i++) {
  //       if (regAdd[i] === '区') {
  //         sumQu += 1;
  //       }
  //       if (regAdd[i] === '县') {
  //         sumXian += 1;
  //       }
  //       if (sumXian === 1 && regAdd[i] === '县') {
  //         indexXian = i;
  //       }
  //       if (regAdd[i] === '市') {
  //         sumShi += 1;
  //       }
  //       let queryName = '';
  //       if ((regAdd[i] + regAdd[i + 1] === '街道') || (regAdd[i] === '镇') || (regAdd[i] === '乡')) {
  //         queryName = regAdd.slice(i - 2, i + 1);
  //       } else if (regAdd[i] + regAdd[i + 1] === '办事') {
  //         queryName = regAdd.slice(i - 2, i);
  //       }
  //       if (queryName === '' || code === '') {
  //         street = '';
  //       } else {
  //         const areas = await this.ctx.model.District.findOne({ name: { $regex: queryName }, area_code: { $regex: queryCode.substr(2, 6) } });
  //         street = areas;
  //       }
  //       if (!(street === '')) {
  //         break;
  //       }
  //     }
  //     if (regAdd.indexOf('县') !== -1) {
  //       if (sumShi > 0) {
  //         if (sumQu === 1 && sumXian === 1) {
  //           queryName = regAdd.substring(regAdd.indexOf('市') + 1, indexXian + 1);
  //         } else {
  //           queryName = regAdd.substring(regAdd.indexOf('市') + 1, regAdd.indexOf('县') + 1);
  //         }
  //       } else {
  //         if (sumQu === 1 && sumXian === 1) {
  //           queryName = regAdd.substring(0, indexXian + 1);
  //         } else {
  //           queryName = regAdd.substring(regAdd.indexOf('县') - 2, regAdd.indexOf('县') + 1);
  //           //   console.log(queryName, '555555555555555');
  //         }
  //       }
  //     } else {
  //       if (sumShi > 0) {
  //         shi = regAdd.substring(regAdd.indexOf('市') - 2, regAdd.indexOf('市') + 1);
  //         if (regAdd.indexOf('区') === -1) {
  //           queryName = regAdd.substring(regAdd.indexOf('市') + 1, regAdd.indexOf('市') + 3);
  //         } else {
  //           queryName = regAdd.substring(regAdd.indexOf('市') + 1, regAdd.indexOf('区') + 1);
  //         }
  //       } else {
  //         queryName = regAdd.substring(0, regAdd.indexOf('区') + 1);
  //       }
  //     }
  //   }

  //   let areas = await this.ctx.model.District.find({ $and: [{ area_code: { $regex: '^' + queryCode } }, { name: { $regex: queryName } }] });
  //   if (areas.length === 0 && queryName) {
  //     areas = await this.ctx.model.District.find({ $or: [{ $and: [{ area_code: { $regex: '^' + queryCode } }, { name: { $regex: queryName } }] }, { $and: [{ parent_code: { $regex: '^' + queryCode } }, { name: { $regex: queryName } }] }, { name: { $regex: queryName } }] });
  //     if (areas.length === 0 && shi) {
  //       areas = await this.ctx.model.District.find({ $and: [{ parent_code: { $regex: '^' + queryCode } }, { name: { $regex: shi } }] });
  //     }
  //   } else if (areas.length === 0) {
  //     areas = await this.ctx.model.District.find({ area_code: { $regex: '^' + queryCode } });
  //   }
  //   if (!areas[0] || queryName && areas[0].name !== queryName && areas[0].name !== shi) { // 这里的name拿不到；
  //     // areas = await this.ctx.model.District.find({ name: { $regex: queryName } });
  //     areas = await this.ctx.model.District.find({ area_code: { $regex: '^' + queryCode } });
  //   }
  //   if (areas[0]) {
  //     area.push(areas[0].name);
  //     parent_code = areas[0].parent_code;
  //     for (let index = 0; index < 5; index++) {
  //       if (parent_code !== '0') {
  //         const data = await this.ctx.model.District.find({ area_code: parent_code });
  //         area.push(data[0].name);
  //         parent_code = data[0].parent_code;
  //       } else {
  //         break;
  //       }
  //     }
  //   }
  //   if (area.length === 0 && street) {
  //     parent_code = street.parent_code;
  //     for (let index = 0; index < 5; index++) {
  //       if (parent_code !== '0') {
  //         const data = await this.ctx.model.District.find({ area_code: parent_code }).toArray();
  //         area.push(data[0].name);
  //         parent_code = data[0].parent_code;
  //       } else {
  //         break;
  //       }
  //     }
  //   }
  //   area.reverse();
  //   if (street) {
  //     area.push(street.name);
  //   }
  //   return area;
  // }


  // update adminOrg时批量处理工作场所的经纬度point问题
  async dealWithWorkAddress(workAddressArr) {
    if (workAddressArr instanceof Array && workAddressArr.length) {
      const newWorkAddressArr = JSON.parse(JSON.stringify(workAddressArr));

      const len = workAddressArr.length;
      for (let i = 0; i < len; i++) {
        const curAddr = workAddressArr[i];
        if (curAddr.districts && curAddr.districts instanceof Array) {
          const point = await this.getPoint(curAddr.districts.join('') + curAddr.address || '');
          if (point) newWorkAddressArr[i].point = point;
        }
      }

      return newWorkAddressArr;
    }
  }
  // 根据中文地址查找它的经纬度
  async getPoint(address, bmap = this.app.config.bmap) {
    const getOptions = {
      data: {
        output: 'json',
        ak: bmap.ak,
        address,
      },
      timeout: 8000,
      dataType: 'json',
      method: 'GET',
    };
    const resBack = await this.ctx.curl(bmap.url, getOptions);
    if (resBack.status === 200 && resBack.data.result) {
      return [ resBack.data.result.location.lng, resBack.data.result.location.lat ];
    }
  }
  /**
  * @param {String*} code 信用代码
  * @param {String*} serviceID 机构id
  * @param {String*} contract 联系人姓名
  * @param {String*} phoneNum 联系人电话
  * updateBy htt
  * updateTime 2023-02-23
  * updateDesc 一个机构的企业联系人可添加多个，添加主要联系人的设置
  * author: linht copy from jhw
  */
  async handleContract(code, serviceID, contract, phoneNum) {
    const { ctx } = this;
    const serviceIDs = [{ _id: serviceID, EnterpriseContractName: contract, EnterprisePhoneNum: phoneNum }];
    let adminOrgInfo = await ctx.model.Adminorg.aggregate([
      { $match: { code } },
      { $project: {
        serviceID: {
          $filter: {
            input: '$serviceID',
            as: 'item',
            cond: { $eq: [ '$$item._id', serviceID ] },
          },
        },
        serviceIDOtherOrg: { // 绑定的其他机构
          $filter: {
            input: '$serviceID',
            as: 'item',
            cond: { $ne: [ '$$item._id', serviceID ] },
          },
        },
      } },
    ]);
    adminOrgInfo = adminOrgInfo[0];
    adminOrgInfo.serviceID = adminOrgInfo.serviceID || [];

    if (serviceIDs.length === 1 && (!adminOrgInfo.serviceID || !adminOrgInfo.serviceID.filter(item => item.EnterprisePhoneNum !== serviceIDs[0].EnterprisePhoneNum).length)) { // 如果只有一个联系人，那么默认为主要联系人
      serviceIDs[0].mainContract = true;
    }
    // if (!adminOrgInfo.serviceID.length && serviceIDs.length === 1) { // 如果只有一个联系人，那么默认为主要联系人
    //   serviceIDs[0].mainContract = true;
    // }
    if (serviceIDs.length) {
      await ctx.model.Adminorg.updateOne({ code }, {
        $set: { serviceID: [ ...serviceIDs, ...adminOrgInfo.serviceIDOtherOrg ] },
      });
    }
  }
  // async handleContract(code, serviceID, contract, phoneNum) {
  //   const { ctx } = this;
  //   const hasContract = await ctx.model.Adminorg.findOne({ code, 'serviceID._id': { $in: [ serviceID ] } });
  //   let res;
  //   if (hasContract) {
  //     console.log('更新');
  //     // 有更新
  //     res = await ctx.model.Adminorg.updateOne(
  //       { code },
  //       {
  //         $set: {
  //           'serviceID.$[i]._id': serviceID,
  //           'serviceID.$[i].EnterpriseContractName': contract,
  //           'serviceID.$[i].EnterprisePhoneNum': phoneNum,
  //         },
  //       },
  //       { multi: true, arrayFilters: [{ 'i._id': serviceID }] }
  //     );
  //   } else {
  //     console.log(phoneNum, contract, serviceID, '新增');
  //     // 没有新建
  //     res = await ctx.model.Adminorg.updateOne(
  //       { code, 'serviceID._id': { $ne: serviceID } },
  //       {
  //         $addToSet: {
  //           serviceID: {
  //             _id: serviceID,
  //             EnterpriseContractName: contract,
  //             EnterprisePhoneNum: phoneNum,
  //           },
  //         },
  //       }
  //     );
  //   }
  //   console.log(res);
  // }

  // 发起检测合同审批
  async projectCreateApproval(params = {}, salesman, _id) {
    const { ctx } = this;
    const employeeInfo = await ctx.model.ServiceEmployee.findOne({ name: salesman }, { phoneNum: 1 });
    const phoneNumber = employeeInfo.phoneNum;
    try {
      const { dingInfo } = await ctx.model.ServiceOrg.findOne({ _id: params.serviceOrgId });
      const dingTalkBasicInfo = await ctx.service.dingTalkWorkFlow.dingTalkApiGettoken(dingInfo.AppKey, dingInfo.AppSecret);
      const dingTalkUserInfo = await ctx.service.dingTalkWorkFlow.dingTalkGteUserInfoByPhone(phoneNumber, dingTalkBasicInfo.access_token);
      // const getProcessCodeUrl = 'https://oapi.dingtalk.com/topapi/process/get_by_name';
      const createUrl = 'https://oapi.dingtalk.com/topapi/processinstance/create?access_token=' + dingTalkBasicInfo.access_token;
      const process_code = (ctx.app.config.qlcProcessCodes.filter(item => item._idField === 'process_instance_id'))[0].code;
      // const process_code = 'PROC-7469F564-0226-4FC8-A84A-33DBC2DD6C7A';
      const formObj = {
        form_component_values: [
          { name: '业务员', value: salesman },
          {
            name: '企业名称',
            value: params.EnterpriseName,
          },
          {
            name: '项目名称',
            value: params.projectName,
          }, {
            name: '项目编号',
            value: params.projectSN,
          }, {
            name: '项目地址',
            value: params.companyAddress && params.companyAddress[0] ? (params.companyAddress[0].districts.join('') + params.companyAddress[0].address) : '',
          },
          {
            name: '行业类型',
            value: params.industryCategoryInfo || '',
          }, {
            name: '是否VIP',
            value: (params.isVIP === 'svip' || params.isVIP === 'vip') ? '是' : '否',
          }, {
            name: '联系人',
            value: params.companyContact,
          }, {
            name: '联系电话',
            value: params.companyContactPhoneNumber,
          }, {
            name: '项目价格',
            value: params.projectPrice,
          }, {
            name: '合作费',
            value: params.expectedCooperationFee || '',
          }, {
            name: '评审费',
            value: params.expectedReviewFee || '',
          }, {
            name: '其他费用',
            value: params.expectedOtherFee || '',
          }, {
            name: '净项目额（元）',
            value: params.expectedNetEarnings || '',
          },
          {
            name: '项目简介',
            value: '',
          }, {
            name: 'VIP需求',
            value: '',
          },
          {
            name: '是否符合法律法规要求',
            value: '符合',
          },
          {
            name: '本公司和服务人员资质是否满足要求',
            value: '满足',
          },
          {
            name: '检测检验设备是否满足要求',
            value: '满足',
          },
          {
            name: '检测检验方法、标准适用',
            value: '适用',
          },
          {
            name: '是否分包',
            value: '无分包',
          },
          {
            name: '分包具体项目：',
            value: '',
          },
          { name: '拟选分包机构：', value: '' },
          {
            name: '合同或协议是否已明确分包内容并告知客户',
            value: '是',
          }, {
            name: '技术风险和商业风险是否可控',
            value: '可控制、风险小',
          }, {
            name: '项目收费是否能满足工作要求',
            value: '收费合理',
          }, {
            name: '能否按合同要求进度完成项目',
            value: '能完成',
          }, {
            name: '后勤保障能否满足要求',
            value: '可保障',
          }, {
            name: '是否满足项目和合同保密要求',
            value: '能满足',
          },
          {
            name: '备注',
            value: '',
          },
        ],
        agent_id: ctx.app.config.dingTalk.AgentId,
        process_code,
        cc_position: 'FINISH',
        dept_id: dingTalkUserInfo.department && dingTalkUserInfo.department[0],
        originator_user_id: dingTalkUserInfo.userid,
      };

      if (params.entrustDate) {
        formObj.form_component_values.push({
          name: '拟采用时间',
          value: moment(params.entrustDate).format('YYYY-MM-DD'),
        });
      }
      if (params.requiredTime) {
        formObj.form_component_values.push({
          name: '拟完成时间',
          value: moment(params.requiredTime).format('YYYY-MM-DD'),
        });
      }
      const createBack = await Axios.post(createUrl, formObj);
      if (createBack.data.errcode === 0) {
        // 更新状态字段
        await ctx.model.JcqlcProject.updateOne({ _id }, { $set: {
          'progress.approved.status': 1,
          'progress.approved.completedTime': new Date(),
          process_instance_id: createBack.data.process_instance_id,
        } });
        return { process_instance_id: createBack.data.process_instance_id };
      }
      return { errMsg: createBack.data.errmsg };
    } catch (error) {
      return { errMsg: JSON.stringify(error.stack) };
    }
  }
  // 发送放射钉钉审批
  async projectCreateApprovalRad(params = {}, salesman, _id) {
    const { ctx } = this;
    const employeeInfo = await ctx.model.ServiceEmployee.findOne({ name: salesman }, { phoneNum: 1 });
    const phoneNumber = employeeInfo.phoneNum;
    const { dingInfo } = await ctx.model.ServiceOrg.findOne({ _id: params.serviceOrgId });
    try {
      const dingTalkBasicInfo = await ctx.service.dingTalkWorkFlow.dingTalkApiGettoken(dingInfo, dingInfo.AppKey, dingInfo.AppSecret);
      const dingTalkUserInfo = await ctx.service.dingTalkWorkFlow.dingTalkGteUserInfoByPhone(phoneNumber, dingTalkBasicInfo.access_token);
      // const getProcessCodeUrl = 'https://oapi.dingtalk.com/topapi/process/get_by_name';
      const createUrl = 'https://oapi.dingtalk.com/topapi/processinstance/create?access_token=' + dingTalkBasicInfo.access_token;
      const process_code = (ctx.app.config.radProcessCodes.filter(item => item._idField === 'process_instance_id'))[0].code;
      console.log(333, params.companyAddress);
      // const process_code = 'PROC-7469F564-0226-4FC8-A84A-33DBC2DD6C7A';
      // 项目信息
      const formObj = {
        form_component_values: [
          { name: '业务员', value: salesman },
          {
            name: '企业名称',
            value: params.EnterpriseName,
          },
          {
            name: '项目名称',
            value: params.projectName,
          }, {
            name: '项目编号',
            value: params.projectSN,
          }, {
            name: '项目地址',
            value: params.companyAddress && params.companyAddress[0] ? (params.companyAddress[0].districts.join('') + params.companyAddress[0].address) : '',
          },
          {
            name: '行业类型',
            value: params.industryCategoryInfo || '',
          }, {
            name: '是否VIP',
            value: (params.isVIP === 'svip' || params.isVIP === 'vip') ? '是' : '否',
          }, {
            name: '联系人',
            value: params.companyContact,
          }, {
            name: '联系电话',
            value: params.companyContactPhoneNumber,
          }, {
            name: '项目价格',
            value: params.projectPrice,
          }, {
            name: '合作费',
            value: params.expectedCooperationFee || '',
          }, {
            name: '评审费',
            value: params.expectedReviewFee || '',
          }, {
            name: '其他费用',
            value: params.expectedOtherFee || '',
          }, {
            name: '净项目额（元）',
            value: params.expectedNetEarnings || '',
          },
          {
            name: '项目简介',
            value: '',
          }, {
            name: 'VIP需求',
            value: '',
          },
          {
            name: '是否符合法律法规要求',
            value: '符合',
          },
          {
            name: '本公司和服务人员资质是否满足要求',
            value: '满足',
          },
          {
            name: '检测检验设备是否满足要求',
            value: '满足',
          },
          {
            name: '检测检验方法、标准适用',
            value: '适用',
          },
          {
            name: '是否分包',
            value: '无分包',
          },
          {
            name: '分包具体项目：',
            value: '',
          },
          { name: '拟选分包机构：', value: '' },
          {
            name: '合同或协议是否已明确分包内容并告知客户',
            value: '是',
          }, {
            name: '技术风险和商业风险是否可控',
            value: '可控制、风险小',
          }, {
            name: '项目收费是否能满足工作要求',
            value: '收费合理',
          }, {
            name: '能否按合同要求进度完成项目',
            value: '能完成',
          }, {
            name: '后勤保障能否满足要求',
            value: '可保障',
          }, {
            name: '是否满足项目和合同保密要求',
            value: '能满足',
          },
          {
            name: '备注',
            value: '',
          },
        ],
        agent_id: ctx.app.config.dingTalk.AgentId,
        process_code,
        cc_position: 'FINISH',
        dept_id: dingTalkUserInfo.department && dingTalkUserInfo.department[0],
        originator_user_id: dingTalkUserInfo.userid,
      };
      if (params.entrustDate) {
        formObj.form_component_values.push({
          name: '拟采用时间',
          value: moment(params.entrustDate).format('YYYY-MM-DD'),
        });
      }
      if (params.requiredTime) {
        formObj.form_component_values.push({
          name: '拟完成时间',
          value: moment(params.requiredTime).format('YYYY-MM-DD'),
        });
      }
      const createBack = await Axios.post(createUrl, formObj);
      if (createBack.data.errcode === 0) {
        // 更新状态字段
        await ctx.model.RadiateqlcProject.updateOne({ _id }, { $set: {
          'progress.approved.status': 1,
          'progress.approved.completedTime': new Date(),
          process_instance_id: createBack.data.process_instance_id,
        } });
        return { process_instance_id: createBack.data.process_instance_id };
      }
      return { errMsg: createBack.data.errmsg };
    } catch (error) {
      return { errMsg: JSON.stringify(error.stack) };
    }
  }
  // 根据八骏返回的修改内容更新项目信息
  async updateJcqlcProject(query, setFieldObj) {
    // 查询对应机构的信息
    const anthem = await await this.ctx.model.Adminorg.aggregate([
      { $match: { cname: setFieldObj.anthemCompanyName } },
      { $project: { _id: 1, code: 1, regAdd: 1, districtRegAdd: 1 } },
      { $addFields: { anthemEnterpriseID: '$_id', anthemCompanyID: '$code' } },
    ]);
    if (!anthem[0]) {
      this.ctx.auditLog('更新全流程项目信息', `未查询到用人单位名称为:${setFieldObj.anthemCompanyName}的企业`, 'info');
      return { nModified: 0 };
    }
    setFieldObj.anthemEnterpriseID = anthem[0].anthemEnterpriseID;
    setFieldObj.anthemCompanyID = anthem[0].anthemCompanyID;
    setFieldObj.anthemCompanyAddress = [{ districts: anthem[0].districtRegAdd, address: anthem[0].regAdd, _id: shortid.generate() }];
    const enterprise = await await this.ctx.model.Adminorg.aggregate([
      { $match: { cname: setFieldObj.EnterpriseName } },
      { $project: { _id: 1, code: 1, regAdd: 1, districtRegAdd: 1 } },
      { $addFields: { EnterpriseID: '$_id', companyID: '$code' } },
    ]);
    if (!enterprise[0]) {
      this.ctx.auditLog('更新全流程项目信息', `未查询到受检单位名称为:${setFieldObj.anthemCompanyName}的企业`, 'info');
      return { nModified: 0 };
    }
    setFieldObj.EnterpriseID = enterprise[0].EnterpriseID;
    setFieldObj.companyID = enterprise[0].companyID;
    setFieldObj.companyAddress = [{ districts: enterprise[0].districtRegAdd, address: enterprise[0].regAdd, _id: shortid.generate() }];
    let res = await this.ctx.model.JcqlcProject.updateOne(query, { $set: setFieldObj });
    // 更新放射项目
    if (res.n !== 1) {
      const serviceEmployee = await this.ctx.model.ServiceEmployee.find({ name: setFieldObj.salesman }, { _id: 1 });
      if (!serviceEmployee[0]) {
        this.ctx.auditLog('更新全流程项目信息', `未查询到人员信息名称为:${setFieldObj.salesman}`, 'info');
        return { nModified: 0 };
      }
      setFieldObj.salesman = serviceEmployee[0]._id;
      res = await this.ctx.model.RadiateqlcProject.updateOne(query, { $set: setFieldObj });
    }
    return res;
  }
  // 根据八骏返回的修改内容更新机构信息
  async updateAdminorg(query, setFieldObj) {
    const res = await this.ctx.model.Adminorg.updateOne(query, { $set: setFieldObj });
    return res;
  }
}
module.exports = TestPaperService;
