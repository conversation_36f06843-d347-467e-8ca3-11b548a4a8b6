/*
 * @Author: tangzg <EMAIL>
 * @Date: 2024-03-12 13:32:34
 * @LastEditors: tangzg <EMAIL>
 * @LastEditTime: 2024-03-12 14:51:39
 * @FilePath: \oapi\lib\plugin\egg-jk-synchroChenKeProjects\config\config.default.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
exports.jk_synchroChenKeProjects = {
  alias: 'synchroChenKeProjects', // 插件目录，必须为英文
  pkgName: 'egg-jk-synchroChenKeProjects', // 插件包名
  enName: 'jk_synchroChenKeProjects', // 插件名
  name: '培训管理', // 插件名称
  description: '培训管理', // 插件描述
  adminApi: [],
  fontApi: [{
    url: 'message',
    method: 'post',
    controllerName: 'message',
    details: '消息推送',
  }, {
    url: 'customer',
    method: 'post',
    controllerName: 'customer',
    details: '创建新客户',
  }, {
    url: 'customer',
    method: 'put',
    controllerName: 'customer',
    details: '创建新客户',
  }, {
    url: 'projectFee',
    method: 'post',
    controllerName: 'projectFee',
    details: '更新项目金额',
  }, {
    url: 'project',
    method: 'post',
    controllerName: 'project',
    details: '创建新项目',
  }, {
    url: 'project',
    method: 'put',
    controllerName: 'project',
    details: '创建新项目',
  }, {
    url: 'customer',
    method: 'get',
    controllerName: 'customer',
    details: '验证是否有当前客户',
  }, {
    url: 'projectStatus',
    method: 'post',
    controllerName: 'projectStatus',
    details: '同步项目状态',
  }],
  initData: '', // 初始化数据脚本
  pluginsConfig: ` 
    exports.jk_synchroChenKeProjects = {\n
        enable: true,\n        package: 'egg-jk-synchroChenKeProjects',
    };\n
    `, // 插入到 plugins.js 中的配置
  defaultConfig: `
  synchroChenKeProjectsRouter:{\n
        match: [ctx => ctx.path.startsWith('/manage/synchroChenKeProjects'), ctx => ctx.path.startsWith('/api/synchroChenKeProjects')],\n
    },\n
    `, // 插入到 config.default.js 中的配置

  // 列表查询每页显示的数量，默认为10
  // pageSize: 10,
  multipart: {
    fileSize: '50mb',
    fields: '100',
    // mode: 'stream',
    fileExtensions: [ 'docx', 'doc', 'xlsx', 'mp4', 'pdf', 'avi', 'rar' ],
  },
  // 图片上传路径
  propagateUploadPath: './app/public/upload/synchroChenKeProjects',
  security: {
    csrf: {
      ignoreJSON: true,
    },
    // 白名单
    // domainWhiteList: ['*'],
  },
};

