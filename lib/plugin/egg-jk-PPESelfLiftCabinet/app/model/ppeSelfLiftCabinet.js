/*
 * @Author: 林卉桐
 * @Date: 2022-05-17
 * @LastEditors: 林卉桐
 * @LastEditTime: 2022-05-19
 * @Description: 防护用品自提柜库存及仪器信息
 *
 */

module.exports = app => {
  const shortid = require('shortid');
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;

  const ppeSelfLiftCabinetSchema = new Schema({
    _id: { // 机器id
      type: String,
      default: shortid.generate,
    },
    cabinetSN: String, // 机器代码
    EnterpriseId: String,
    specifications: [{
      totality: Number, // 本货道的总件数
      goods: String, // 货物
      freightLaneSN: Number, // 货道号
      surplus: Number, // 剩余
    }],
    // 最后一次补货的时间、人员、（后面可加）
    finalOrderTime: Date, // 最后一次订单的时间
  });
  return mongoose.model('ppeSelfLiftCabinet', ppeSelfLiftCabinetSchema, 'ppeSelfLiftCabinets');
};
