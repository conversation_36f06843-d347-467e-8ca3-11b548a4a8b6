module.exports = app => {
  const shortid = require('shortid');
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;

  // 视频库
  const PPEvideoSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    name: String,
    VideoId: String, // 对应aliyun的videoID
    Size: String, // 视频文件大小 单位字节B
    description: String,
    // author: {
    //   type: String,
    //   default: '',
    // }, // 视频主角，就是老师，可以多个，可以么有
    times: { // 播放次数
      type: Number,
      default: 0,
    },
    date: {
      type: Date,
      default: Date.now,
    }, // 上传时间
    duration: {
      type: Number,
      default: 0,
    }, // 时长s
    harmFactors: { // 危害因素
      type: Array,
      default: [],
    },
    type: { // 类型
      type: String,
      default: '',
    },
    modelNumber: { // 型号
      type: String,
      default: '',
    },
    // classHours: {
    //   type: Number,
    //   default: 1,
    // }, // 视频学时
    // selfOpen: {
    //   type: Boolean,
    //   default: false,
    // }, // 是否单独开放（用户不用必须在课程/培训中才能看到）
    source: {
      type: String,
      default: 'operate', // 也有可能是User,看source
      emun: [ 'user', 'operate', 'org' ],
    }, // 来源，指哪个端上传，方便日后用户上传，不同端默认值需修改
    uploader: {
      type: String,
      ref: 'OperateUser',
    }, // 上传人账号，日后不同端ref需修改
    auditStatus: { // 审核状态
      type: String,
      default: '2',
      emun: [ '0', '1', '2', '3' ], // 0没上传完,暂存 1待审核 2审核通过 3审核不通过
    },
  });
  return mongoose.model('PPEvideo', PPEvideoSchema, 'PPEvideos');
};
