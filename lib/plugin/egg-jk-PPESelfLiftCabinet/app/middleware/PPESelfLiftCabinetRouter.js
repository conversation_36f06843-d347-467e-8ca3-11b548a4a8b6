const PPESelfLiftCabinetApiController = require('../controller/api/PPESelfLiftCabinet');
const PPESelfLiftCabinetAdminController = require('../controller/manage/PPESelfLiftCabinet');

module.exports = (options, app) => {

  return async function PPESelfLiftCabinetRouter(ctx, next) {

    const pluginConfig = app.config.jk_PPESelfLiftCabinet;
    await app.initPluginRouter(ctx, pluginConfig, PPESelfLiftCabinetAdminController, PPESelfLiftCabinetApiController);
    await next();

  };

};

