{"name": "egg-jk-PPESelfLiftCabinet", "version": "1.0.0", "description": "PPESelfLiftCabinet", "private": true, "eggPlugin": {"name": "jk_PPESelfLiftCabinet"}, "keywords": ["egg", "eggPlugin", "egg-plugin"], "dependencies": {"await-stream-ready": "^1.0.1", "dayjs": "^1.8.24", "egg": "^2.15.1", "egg-cors": "^2.2.3", "egg-mongoose": "^3.2.0", "egg-scripts": "^2.11.0", "egg-validate": "^2.0.2", "md5": "^2.2.1", "stream-wormhole": "^1.1.0"}, "devDependencies": {"autod": "^3.0.1", "autod-egg": "^1.1.0", "egg-bin": "^4.11.0", "egg-ci": "^1.11.0", "egg-mock": "^3.21.0", "eslint": "^5.13.0", "eslint-config-egg": "^7.1.0"}, "engines": {"node": ">=10.0.0"}, "scripts": {"start": "egg-scripts start --daemon --title=egg-server-webServer", "stop": "egg-scripts stop --title=egg-server-webServer", "dev": "egg-bin dev", "debug": "egg-bin debug", "test": "npm run lint -- --fix && npm run test-local", "test-local": "egg-bin test", "cov": "egg-bin cov", "lint": "eslint .", "ci": "npm run lint && npm run cov", "autod": "autod"}, "ci": {"version": "10"}, "repository": {"type": "git", "url": ""}, "author": "lht", "license": "MIT"}