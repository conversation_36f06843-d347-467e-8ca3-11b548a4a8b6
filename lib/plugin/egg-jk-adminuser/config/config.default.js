
const pkgInfo = require('../package.json');
exports.jk_adminuser = {
  alias: 'adminuser', // 插件目录，必须为英文
  pkgName: 'egg-jk-adminuser', // 插件包名
  enName: 'jk_adminuser', // 插件名
  name: '企业管理', // 插件名称
  description: '企业管理', // 插件描述
  isadm: 1, // 是否有后台管理，1：有，0：没有，入口地址:'/ext/devteam/admin/index'
  isindex: 0, // 是否需要前台访问，1：需要，0：不需要,入口地址:'/ext/devteam/index/index'
  version: pkgInfo.version, // 版本号
  iconName: 'icon_service', // 主菜单图标名称
  adminUrl: '/adminuser/js/app.js',
  adminApi: [{
    url: 'adminuser/stationInfo',
    method: 'post',
    controllerName: 'stationInfo',
    details: '获取岗位信息',
  },
  {
    url: 'adminuser/getCheckResult',
    method: 'post',
    controllerName: 'getCheckResult',
    details: '获取危害因素检测结果',
  },
  {
    url: 'adminuser/getDefendproducts',
    method: 'post',
    controllerName: 'getDefendproducts',
    details: '获取防护用具',
  }, {
    url: 'adminuser/receiveProducts',
    method: 'post',
    controllerName: 'receiveProducts',
    details: '领取防护用具',
  }, {
    url: 'adminuser/getStationChange',
    method: 'get',
    controllerName: 'getStationChange',
    details: '获取转岗记录',
  }, {
    url: 'adminuser/getMessageNotice',
    method: 'get',
    controllerName: 'getMessageNotice',
    details: '获取消息通知',
  }, {
    url: 'adminuser/confirmMessageNotice',
    method: 'post',
    controllerName: 'confirmMessageNotice',
    details: '确认消息通知',
  }, {
    url: 'adminuser/getViolationNotice',
    method: 'get',
    controllerName: 'getViolationNotice',
    details: '获取违章通知',
  }, {
    url: 'adminuser/getLaborIsEdit',
    method: 'get',
    controllerName: 'getLaborIsEdit',
    details: '获取劳动者是否可以在劳动者端编辑个人职业史',
  }, {
    url: 'adminuser/getHistoryList',
    method: 'get',
    controllerName: 'getHistoryList',
    details: '获取劳动者个人职业史',
  }, {
    url: 'adminuser/addHistoryList',
    method: 'post',
    controllerName: 'addHistoryList',
    details: '新增劳动者个人职业史',
  }, {
    url: 'adminuser/deleteHistoryList',
    method: 'post',
    controllerName: 'deleteHistoryList',
    details: '删除劳动者个人职业史',
  }, {
    url: 'adminuser/editHistoryList',
    method: 'post',
    controllerName: 'editHistoryList',
    details: '编辑劳动者个人职业史',
  }, {
    url: 'adminuser/getDefendproductsAuditList',
    method: 'post',
    controllerName: 'getDefendproductsAuditList',
    details: '获取防护用品审核列表',
  }, {
    url: 'adminuser/selectApplication',
    method: 'post',
    controllerName: 'selectApplication',
    details: '防护用品审批',
  },
  ],
  fontApi: [],

  initData: '', // 初始化数据脚本
  pluginsConfig: ` 
    exports.jk_adminuser = {\n
        enable: true,\n
         \n
    };\n
    `, // 插入到 plugins.js 中的配置
  defaultConfig: `
    adminuserRouter:{\n
        match: [ctx => ctx.path.startsWith('/manage/adminuser'), ctx => ctx.path.startsWith('/api/adminuser')],\n
    },\n
    `, // 插入到 config.default.js 中的配置
};
