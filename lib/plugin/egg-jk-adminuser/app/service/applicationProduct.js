/*
 * @Author: AI Assistant
 * @Date: 2024-12-27
 * @Description: 防护用品申请服务 - 管理申请列表查询和审批流程
 */

'use strict';

const Service = require('egg').Service;
const moment = require('moment');

/**
 * @service 防护用品申请服务
 * @description 负责防护用品申请的查询、审批和后续处理
 */
class ApplicationProductService extends Service {

  /**
   * @summary 获取申请列表
   * @description 分页查询防护用品申请列表，支持搜索和筛选
   * @param {Object} params - 查询参数
   * @param {Object} params.query - 查询条件
   * @param {string} params.query.searchKey - 搜索关键字
   * @param {number} params.current - 当前页码
   * @param {number} params.pageSize - 每页大小
   * @param {number} params.year - 年份
   * @param {number} params.isSelected - 月份
   * @return {Promise<Object>} 分页查询结果
   */
  async getApplicationProducts(params) {
    const { ctx, app } = this;
    const EnterpriseID = ctx.session.adminUserInfo ? ctx.session.adminUserInfo.EnterpriseID : '';

    try {
      // 获取企业及子公司ID列表
      const enterpriseIds = await this.ctx.service.employee.findSubCompany(EnterpriseID, 'branch');
      const enterpriseData = [ EnterpriseID, ...enterpriseIds ];

      const { query = {}, current, pageSize, year, isSelected } = params;

      // 构建搜索条件
      const regexMatch = {};
      if (query.searchKey) {
        regexMatch.$or = [
          { workshop: { $regex: query.workplace || query.searchKey } },
          { workspaces: { $regex: query.workplace || query.searchKey } },
          { workstation: { $regex: query.workplace || query.searchKey } },
          { 'products.product': { $regex: query.product || query.searchKey } },
        ];
      }

      // 构建聚合管道
      const pipeline = [
        { $match: { EnterpriseID: { $in: enterpriseData } } },
        { $match: regexMatch },
        { $match: { yearNumber: year } },
        { $match: { mouthNumber: isSelected } },
        {
          $sort: {
            auditStatus: 1,
            applicationTime: -1,
          },
        },
        {
          $facet: {
            data: [
              {
                $skip: (current - 1) * Number(pageSize),
              },
              {
                $limit: Number(pageSize),
              },
              {
                $lookup: {
                  from: 'employees',
                  localField: 'employee',
                  foreignField: '_id',
                  as: 'employeeInfo',
                },
              },
              {
                $project: {
                  workspaces: 1, // 车间
                  workshop: 1, // 厂房
                  workshopId: 1, // 厂房Id
                  workspace: 1, // 车间
                  workspaceId: 1, // 车间id
                  workstation: 1, // 岗位
                  workstationId: 1, // 岗位id
                  products: 1, // 产品列表
                  employeeInfo: 1, // 操作人
                  applicationTime: 1, // 申请时间
                  yearNumber: 1, // 年份
                  mouthNumber: 1, // 月份
                  updateAt: 1, // 更新时间
                  auditStatus: 1, // 审核状态 0 未审核 1 一级通过 2 被驳回 3 二级通过
                  sign: 1, // 签名图片
                  notes: 1, // 备注
                  claimType: 1, // 申请类型
                  employee: 1, // 员工id
                  auditLevel: 1, // 审核级别
                  auditRecords: 1, // 审核记录
                },
              },
            ],
            totalLength: [{ $count: 'length' }],
          },
        },
      ];

      // 执行查询
      let res = await ctx.service.db.aggregate('ApplicationProduct', pipeline);

      // 获取防护用品列表（如果是特定分支）
      let subProducts = [];
      if (app.config.branch === 'wh') {
        subProducts = await this.findProducts();
      }

      // 处理查询结果
      if (res[0]) {
        res = res[0];
        if (res.data.length > 0) {
          for (const item of res.data) {
            // 处理最大数量信息（WH分支）
            if (app.config.branch === 'wh') {
              await this.processMaxNumber(item, subProducts, EnterpriseID);
            }

            // 格式化申请时间
            if (item.applicationTime) {
              item.applicationTime = moment(item.applicationTime).format('YYYY-MM-DD HH:mm');
            }

            // 处理签名图片路径
            if (item.sign) {
              item.sign = `/static${app.config.upload_http_path}/${item.sign}`;
            }
          }
        }
        res.totalLength = res.totalLength[0] ? res.totalLength[0].length : 0;
      }

      ctx.auditLog(
        '查询防护用品申请列表',
        `企业: ${EnterpriseID}, 年份: ${year}, 月份: ${isSelected}, 返回${res?.data?.length || 0}条记录`,
        'info'
      );

      return res;

    } catch (error) {
      ctx.logger.error('获取申请列表失败', error);
      throw new Error(`获取申请列表失败: ${error.message}`);
    }
  }
  /**
   * @summary 处理最大数量信息
   * @description 为WH分支处理产品的最大申请数量信息
   * @param {Object} item - 申请项目
   * @param {Array} subProducts - 产品列表
   * @param {string} EnterpriseID - 企业ID
   * @return {Promise<void>}
   */
  async processMaxNumber(item, subProducts, EnterpriseID) {
    const { ctx } = this;

    try {
      if (!item.products || item.products.length === 0) {
        item.maxNumber = '-';
        return;
      }

      // 根据employee查询部门ID
      const employee = await ctx.service.db.findOne('Employee', { _id: item.employee, EnterpriseID }, 'departs');
      if (!employee || !employee.departs || employee.departs.length === 0) {
        item.maxNumber = '-';
        return;
      }

      // 查找对应的产品规格
      const subProduct = item.products[0].productSpecId &&
        subProducts.find(sub => sub._id === item.products[0].productSpecId);

      if (!subProduct) {
        item.maxNumber = '-';
        return;
      }

      // 查询防护计划中的最大数量
      const maxNumber = await ctx.service.db.findOne('ProtectionPlan', {
        EnterpriseID,
        subRegion: {
          $elemMatch: {
            $elemMatch: {
              $eq: employee.departs[0],
            },
          },
        },
        'products.product': subProduct.product,
      }, {
        'products.$': 1,
      });

      item.maxNumber = maxNumber ? maxNumber.products[0].maxNumber : '-';

    } catch (error) {
      ctx.logger.error('处理最大数量信息失败', error);
      item.maxNumber = '-';
    }
  }

  /**
   * @summary 查找防护用品列表
   * @description 获取企业的防护用品产品列表
   * @return {Promise<Array>} 产品列表
   */
  async findProducts() {
    const { ctx } = this;
    const EnterpriseID = ctx.session.adminUserInfo ? ctx.session.adminUserInfo.EnterpriseID : '';

    try {
      const result = await ctx.model.ProtectiveSuppliesList.aggregate([
        { $match: { EnterpriseID } },
        { $unwind: '$list' },
        { $unwind: '$list.data' },
        {
          $project: {
            _id: '$list.data._id',
            product: '$list.data.product',
          },
        },
      ]);

      return result;
    } catch (error) {
      ctx.logger.error('查找防护用品列表失败', error);
      return [];
    }
  }

  /**
   * @summary 按月导出申请列表
   * @description 导出指定年月的申请数据为Word文档
   * @param {Object} params - 导出参数
   * @param {number} params.mouthNumber - 月份
   * @param {number} params.yearNumber - 年份
   * @return {Promise<string>} 导出文件路径
   */
  async exportByMonth(params) {
    const { ctx, app } = this;
    const EnterpriseID = ctx.session.adminUserInfo ? ctx.session.adminUserInfo.EnterpriseID : '';
    const { mouthNumber, yearNumber } = params;

    try {
      // 查询指定年月的申请数据
      const pipeline = [
        {
          $match: {
            EnterpriseID,
            mouthNumber,
            yearNumber,
            auditStatus: { $in: [ 1, 3 ] }, // 已审核通过的申请
          },
        },
        {
          $lookup: {
            from: 'employees',
            localField: 'employee',
            foreignField: '_id',
            as: 'employeeInfo',
          },
        },
        {
          $project: {
            products: 1, // 产品信息
            workspaces: 1, // 车间
            workspace: 1, // 车间名称
            workstation: 1, // 岗位
            employeeInfo: 1, // 员工信息
            applicationTime: 1, // 申请时间
            sign: 1, // 签名
            auditStatus: 1, // 审核状态
          },
        },
      ];

      const tableData = await ctx.service.db.aggregate('ApplicationProduct', pipeline);

      if (tableData.length === 0) {
        return '暂无申请数据或数据未审核通过！';
      }

      // 处理导出数据
      for (let i = 0; i < tableData.length; i++) {
        const temp = tableData[i];
        temp.index = i + 1;
        temp.applicationTime = moment(temp.applicationTime).format('YYYY-MM-DD HH:mm');
        temp.employeeName = temp.employeeInfo?.[0]?.name || '未知';
        temp.productList = temp.products?.map(p => `${p.product}(${p.number}件)`).join('、') || '';

        if (temp.sign) {
          temp.sign = `${app.config.upload_path}/${temp.sign}`;
        }
      }

      // 生成Word文档
      const templateWordName = '防护用品申请登记表';
      const word = await ctx.helper.fillWord(ctx, templateWordName, {
        tableData,
        yearNumber,
        mouthNumber,
        exportTime: moment().format('YYYY-MM-DD HH:mm:ss'),
      });

      ctx.auditLog(
        '导出申请列表',
        `企业: ${EnterpriseID}, 年月: ${yearNumber}-${mouthNumber}, 导出${tableData.length}条记录`,
        'info'
      );

      return word.path;

    } catch (error) {
      ctx.logger.error('导出申请列表失败', error);
      throw new Error(`导出失败: ${error.message}`);
    }
  }
}

module.exports = ApplicationProductService;
