/*
 * @Author: AI Assistant
 * @Date: 2024-12-27
 * @Description: 审批工作流服务 - 管理多级审批流程的状态转换、任务推送和取消
 */

'use strict';

const Service = require('egg').Service;
const moment = require('moment');
const whWechatMessageUtil = require('@utils').whWechatMessageUtil;

/**
 * @service 审批工作流服务
 * @description 负责管理两级审批流程，包括HSE管理员、部门经理的审批状态转换
 */
class ApprovalWorkflowService extends Service {

  constructor(ctx) {
    super(ctx);
    // 初始化微信消息推送工具
    this.wechatMessageUtil = new whWechatMessageUtil(ctx);
    // 审批状态常量
    this.APPROVAL_STATUS = {
      HSE_PENDING: 'hse_pending', // HSE管理员审批中
      MANAGER_PENDING: 'manager_pending', // 部门经理审批中
      COMPLETED: 'completed', // 审批完成
      REJECTED: 'rejected', // 已驳回
    };

    // 审批级别常量 - 两级审批
    this.APPROVAL_LEVELS = {
      HSE: 1, // HSE管理员审批
      MANAGER: 2, // 部门经理审批
    };

    // Redis 键前缀
    this.REDIS_KEYS = {
      STATUS: 'approval:status:', // 审批状态
      TASKS: 'approval:tasks:', // 推送任务
      META: 'approval:meta:', // 申请元信息
      DAILY_SEQUENCE: 'ohs:daily_sequence:', // 每日序列号
    };
  }

  /**
   * @summary 初始化审批流程
   * @description 启动新的审批流程，推送第一级HSE管理员审批任务
   * @param {Array} applications - 申请记录数组
   * @param {string} employeeId - 申请人ID
   @param {string} employeeName - 申请人姓名
   * @return {Promise<Object>} 初始化结果
   */
  async initializeApproval(applications, employeeId, employeeName) {
    const { ctx } = this;

    try {
      const results = [];

      for (const application of applications) {
        const applicationId = application._id;

        // 为整个审批流程生成唯一的订单编码
        const orderCode = await this.generateOrderCode();

        ctx.logger.info(`生成订单编码: ${orderCode} for applicationId: ${applicationId}`);

        // 1. 存储申请基础信息到Redis
        const metaData = {
          applicationId,
          employeeId,
          employeeName,
          createTime: moment().format('YYYY-MM-DD HH:mm:ss'),
          auditRecords: application.auditRecords,
          orderCode, // 存储订单编码，整个流程使用同一个
        };

        await ctx.helper.setRedis(
          `${this.REDIS_KEYS.META}${applicationId}`,
          JSON.stringify(metaData),
          24 * 60 * 60 // 24小时过期
        );

        ctx.logger.info(`META数据已保存，包含orderCode: ${orderCode}`);

        // 验证是否正确保存
        const savedMeta = await ctx.helper.getRedis(`${this.REDIS_KEYS.META}${applicationId}`);
        ctx.logger.info(`验证META保存结果: ${savedMeta}`);

        // 2. 设置初始审批状态
        const statusData = {
          currentStatus: this.APPROVAL_STATUS.HSE_PENDING,
          currentLevel: this.APPROVAL_LEVELS.HSE,
          updateTime: moment().format('YYYY-MM-DD HH:mm:ss'),
        };

        await ctx.helper.setRedis(
          `${this.REDIS_KEYS.STATUS}${applicationId}`,
          JSON.stringify(statusData),
          24 * 60 * 60
        );

        // 3. 推送HSE管理员审批任务
        const pushResult = await this.pushLevelTasks(
          applicationId,
          this.APPROVAL_LEVELS.HSE,
          employeeId,
          employeeName
        );

        results.push({
          applicationId,
          status: 'initialized',
          pushedTasks: pushResult.taskCount,
          message: '审批流程已启动，HSE管理员审批任务已推送',
        });

        ctx.auditLog(
          '审批流程初始化',
          `申请ID: ${applicationId}, 已推送${pushResult.taskCount}个HSE管理员审批任务`,
          'info'
        );
      }

      return {
        success: true,
        results,
      };

    } catch (error) {
      ctx.logger.error('审批流程初始化失败', error);
      throw new Error(`审批流程初始化失败: ${error.message}`);
    }
  }

  /**
   * @summary 处理审批结果
   * @description 当某个审批完成时，处理状态转换和下一级任务推送
   * @param {string} applicationId - 申请ID
   * @param {number} auditLevel - 当前审批级别
   * @param {number} auditResult - 审批结果 (1-通过, 2-驳回)
   * @param {string} auditorId - 审批人ID
   * @return {Promise<Object>} 处理结果
   */
  async handleApprovalResult(applicationId, auditLevel, auditResult, auditorId) {
    const { ctx } = this;

    try {
      // 1. 获取当前审批状态
      const currentStatusData = await this.getApprovalStatus(applicationId);
      if (!currentStatusData) {
        ctx.logger.warn(`申请${applicationId}未找到Redis状态信息，可能是老数据`);
        return {
          success: true,
          applicationId,
          currentStatus: auditResult === 1 ? 'approved' : 'rejected',
          message: auditResult === 1 ? '审批通过' : '审批驳回',
        };
      }

      // 2. 获取申请元信息
      const metaDataStr = await ctx.helper.getRedis(`${this.REDIS_KEYS.META}${applicationId}`);
      console.log('metaDataStr', metaDataStr);
      if (!metaDataStr) {
        throw new Error('未找到申请元信息');
      }

      // 处理不同的数据类型，避免JSON解析错误
      let metaData;
      if (typeof metaDataStr === 'object' && metaDataStr !== null) {
        // 如果已经是对象，直接使用
        metaData = metaDataStr;
        console.log('metaData已经是对象，直接使用');
      } else if (typeof metaDataStr === 'string') {
        // 如果是字符串，尝试解析JSON
        try {
          metaData = JSON.parse(metaDataStr);
          console.log('metaData JSON解析成功');
        } catch (parseError) {
          console.error('metaData JSON解析失败:', parseError);
          throw new Error(`申请元信息JSON解析失败: ${parseError.message}`);
        }
      } else {
        throw new Error(`申请元信息数据类型异常: ${typeof metaDataStr}`);
      }

      // 3. 如果是驳回，直接结束流程
      if (auditResult === 2) {
        return await this.handleRejection(applicationId, auditLevel);
      }

      // 4. 处理当前审批人的已办状态和取消其他人待办任务，并推送下一级任务（批量操作）
      let batchResult = null;
      if (auditLevel < this.APPROVAL_LEVELS.MANAGER) {
        // 有下一级审批，批量处理当前级别任务和下一级任务
        batchResult = await this.batchProcessApprovalTasks(
          applicationId,
          auditLevel,
          auditorId,
          auditLevel + 1,
          metaData.employeeId,
          metaData.employeeName
        );
      } else {
        // 最终审批，只处理当前级别任务
        batchResult = await this.batchProcessApprovalTasks(
          applicationId,
          auditLevel,
          auditorId,
          null,
          metaData.employeeId,
          metaData.employeeName
        );
      }

      // 5. 更新审批状态
      const statusData = {
        currentStatus: auditLevel < this.APPROVAL_LEVELS.MANAGER ? this.APPROVAL_STATUS.MANAGER_PENDING : this.APPROVAL_STATUS.COMPLETED,
        currentLevel: auditLevel < this.APPROVAL_LEVELS.MANAGER ? this.APPROVAL_LEVELS.MANAGER : null,
        updateTime: moment().format('YYYY-MM-DD HH:mm:ss'),
        lastApprover: auditorId,
      };

      await ctx.helper.setRedis(
        `${this.REDIS_KEYS.STATUS}${applicationId}`,
        JSON.stringify(statusData),
        24 * 60 * 60
      );

      // 6. 如果是最终完成，清理Redis数据
      if (statusData.currentStatus === this.APPROVAL_STATUS.COMPLETED) {
        await this.cleanupApprovalData(applicationId);
      }

      const resultMessage = statusData.currentLevel
        ? `已转到${this.getLevelName(statusData.currentLevel)}审批阶段`
        : '审批流程已完成';

      ctx.auditLog(
        '审批状态转换',
        `申请ID: ${applicationId}, 级别${auditLevel}审批通过, ${resultMessage}`,
        'info'
      );

      return {
        success: true,
        applicationId,
        previousLevel: auditLevel,
        currentStatus: statusData.currentStatus,
        currentLevel: statusData.currentLevel,
        nextTaskCount: batchResult ? batchResult.totalOperations : 0,
        message: resultMessage,
      };

    } catch (error) {
      ctx.logger.error('处理审批结果失败', error);
      throw new Error(`处理审批结果失败: ${error.message}`);
    }
  }
  /**
   * @summary 批量处理审批任务
   * @description 将审批人同意、取消其他待办、推送下一级任务合并成一个批量操作
   * @param {string} applicationId - 申请ID
   * @param {number} currentLevel - 当前审批级别
   * @param {string} auditorId - 当前审批人ID
   * @param {number} nextLevel - 下一级审批级别（如果有）
   * @param {string} employeeId - 申请人ID
   * @param {string} employeeName - 申请人姓名
   * @return {Promise<Object>} 批量处理结果
   */
  async batchProcessApprovalTasks(applicationId, currentLevel, auditorId, nextLevel, employeeId, employeeName) {
    const { ctx } = this;

    try {
      console.log('=== 开始批量处理审批任务 ===');
      console.log('applicationId:', applicationId);
      console.log('currentLevel:', currentLevel);
      console.log('auditorId:', auditorId);
      console.log('nextLevel:', nextLevel);
      console.log('employeeId:', employeeId);
      console.log('employeeName:', employeeName);

      const allTaskRows = [];
      let completedTaskCount = 0;
      let cancelledTaskCount = 0;
      let newTaskCount = 0;

      // 1. 处理当前级别的任务：标记当前审批人为已办，取消其他人的待办
      const isLastLevel = !nextLevel; // 如果没有下一级，说明是最后一级
      const currentLevelTaskResult = await this.getCurrentLevelTaskDataRows(applicationId, currentLevel, auditorId, isLastLevel);
      if (currentLevelTaskResult.dataRows.length > 0) {
        allTaskRows.push(...currentLevelTaskResult.dataRows);
        completedTaskCount = currentLevelTaskResult.completedCount;
        cancelledTaskCount = currentLevelTaskResult.cancelledCount;
      }

      // 2. 如果有下一级，添加下一级审批任务
      if (nextLevel) {
        const nextLevelTaskResult = await this.getNextLevelTaskDataRows(applicationId, nextLevel, employeeId, employeeName);
        if (nextLevelTaskResult.dataRows.length > 0) {
          allTaskRows.push(...nextLevelTaskResult.dataRows);
          newTaskCount = nextLevelTaskResult.newTaskCount;
        }
      }

      // 3. 批量推送所有任务操作（一次性发送）
      let batchResult = null;
      if (allTaskRows.length > 0) {
        console.log('=== 准备批量推送任务（一次性发送）===');
        console.log('总任务数量:', allTaskRows.length);
        console.log('已办任务数:', completedTaskCount);
        console.log('取消任务数:', cancelledTaskCount);
        console.log('新建任务数:', newTaskCount);

        ctx.logger.info(`批量推送任务: 已办${completedTaskCount}个, 取消${cancelledTaskCount}个, 新建${newTaskCount}个`);
        ctx.logger.info('所有任务数据:', allTaskRows);

        try {
        // 构建统一的批量请求payload
          const payload = ctx.service.whPortal.whPortalTaskUtil.buildTaskPayload(allTaskRows, 'OHS', allTaskRows.length);

          console.log('=== 批量推送payload ===');
          console.log(JSON.stringify(payload, null, 2));

          // 一次性发送所有任务
          const result = await ctx.service.whPortal.whPortalTaskUtil.sendTaskRequest(payload);

          console.log('=== 批量推送结果 ===');
          console.log(JSON.stringify(result, null, 2));

          batchResult = {
            success: result.success,
            data: result.data,
            error: result.error,
            totalTasks: allTaskRows.length,
            completedTasks: completedTaskCount,
            cancelledTasks: cancelledTaskCount,
            newTasks: newTaskCount,
          };
          if (!result.success) {
            throw new Error(batchResult.error);
          }
          // 如果成功且有新任务，存储新任务信息到Redis并发送微信消息
          if (result.success && nextLevel && newTaskCount > 0) {
            const newTasks = allTaskRows.filter(row => row.OrderTaskStatus === '1');
            const taskCacheData = {
              level: nextLevel,
              tasks: newTasks.map(row => ({
                orderId: row.OrderId,
                orderCode: row.OrderCode,
                orderTaskId: row.OrderTaskId,
                orderTypeNameZh: row.OrderTypeName && row.OrderTypeName.split('ZH@!')[1] ? row.OrderTypeName.split('ZH@!')[1].split('_@!@_')[0] : '',
                orderTypeNameEn: row.OrderTypeName && row.OrderTypeName.split('EN@!')[1] ? row.OrderTypeName.split('EN@!')[1].split('_@!@_')[0] : '',
                orderTypeNameHu: row.OrderTypeName && row.OrderTypeName.split('HU@!')[1] ? row.OrderTypeName.split('HU@!')[1] : '',
                orderUserId: row.OrderUserId,
                orderTime: row.OrderTime,
                orderTaskUrl: row.OrderTaskUrl,
                colum2: row.Colum2,
                currentUserId: row.OrderTaskCurrentUserId,
                taskNameZh: row.OrderTaskName && row.OrderTaskName.split('ZH@!')[1] ? row.OrderTaskName.split('ZH@!')[1].split('_@!@_')[0] : '',
                taskNameEn: row.OrderTaskName && row.OrderTaskName.split('EN@!')[1] ? row.OrderTaskName.split('EN@!')[1].split('_@!@_')[0] : '',
                taskNameHu: row.OrderTaskName && row.OrderTaskName.split('HU@!')[1] ? row.OrderTaskName.split('HU@!')[1] : '',
                orderUserName: employeeName,
              })),
              pushTime: moment().format('YYYY-MM-DD HH:mm:ss'),
              pushResult: result,
            };

            // 发送微信消息给新任务的审批人（无论Portal推送是否成功）
            let wechatResult = null;
            try {
              wechatResult = await this.sendWechatNotification(
                applicationId,
                nextLevel,
                newTasks,
                employeeId,
                employeeName
              );
              taskCacheData.wechatResult = wechatResult;
              ctx.logger.info(`批量任务微信消息推送完成，Portal状态: ${result.success}, 微信状态: ${wechatResult.success}`);
            } catch (wechatError) {
              ctx.logger.error('批量任务处理中微信消息推送异常，但不影响主流程', wechatError);
              taskCacheData.wechatResult = { success: false, error: wechatError.message, messageCount: 0 };
            }

            await ctx.helper.setRedis(
              `${this.REDIS_KEYS.TASKS}${applicationId}:${nextLevel}`,
              JSON.stringify(taskCacheData),
              24 * 60 * 60
            );
          }

        } catch (error) {
          ctx.logger.error('批量推送任务失败', error);
          batchResult = {
            success: false,
            error: error.message,
            totalTasks: allTaskRows.length,
            completedTasks: completedTaskCount,
            cancelledTasks: cancelledTaskCount,
            newTasks: newTaskCount,
          };

          // 即使Portal推送失败，也要发送微信消息给新任务的审批人
          console.log('=== Portal推送失败，检查是否需要发送微信消息 ===');
          console.log('nextLevel:', nextLevel);
          console.log('newTaskCount:', newTaskCount);
          console.log('allTaskRows长度:', allTaskRows.length);
          if (nextLevel && newTaskCount > 0) {
            const newTasks = allTaskRows.filter(row => row.OrderTaskStatus === '1');
            console.log('=== 开始Portal失败后的微信推送 ===');
            console.log('过滤后的新任务数量:', newTasks.length);
            console.log('新任务详情:', newTasks);
            try {
              const wechatResult = await this.sendWechatNotification(
                applicationId,
                nextLevel,
                newTasks,
                employeeId,
                employeeName
              );
              ctx.logger.info(`Portal推送失败但微信消息推送完成，Portal状态: false, 微信状态: ${wechatResult.success}`);

              // 构建任务缓存数据
              const taskCacheData = {
                level: nextLevel,
                tasks: newTasks.map(row => ({
                  orderId: row.OrderId,
                  orderCode: row.OrderCode,
                  orderTaskId: row.OrderTaskId,
                  orderTypeNameZh: row.OrderTypeName && row.OrderTypeName.split('ZH@!')[1] ? row.OrderTypeName.split('ZH@!')[1].split('_@!@_')[0] : '',
                  orderTypeNameEn: row.OrderTypeName && row.OrderTypeName.split('EN@!')[1] ? row.OrderTypeName.split('EN@!')[1].split('_@!@_')[0] : '',
                  orderTypeNameHu: row.OrderTypeName && row.OrderTypeName.split('HU@!')[1] ? row.OrderTypeName.split('HU@!')[1] : '',
                  orderUserId: row.OrderUserId,
                  orderTime: row.OrderTime,
                  orderTaskUrl: row.OrderTaskUrl,
                  colum2: row.Colum2,
                  currentUserId: row.OrderTaskCurrentUserId,
                  taskNameZh: row.OrderTaskName && row.OrderTaskName.split('ZH@!')[1] ? row.OrderTaskName.split('ZH@!')[1].split('_@!@_')[0] : '',
                  taskNameEn: row.OrderTaskName && row.OrderTaskName.split('EN@!')[1] ? row.OrderTaskName.split('EN@!')[1].split('_@!@_')[0] : '',
                  taskNameHu: row.OrderTaskName && row.OrderTaskName.split('HU@!')[1] ? row.OrderTaskName.split('HU@!')[1] : '',
                  orderUserName: employeeName,
                })),
                pushTime: moment().format('YYYY-MM-DD HH:mm:ss'),
                pushResult: { success: false, error: error.message },
                wechatResult,
              };

              await ctx.helper.setRedis(
                `${this.REDIS_KEYS.TASKS}${applicationId}:${nextLevel}`,
                JSON.stringify(taskCacheData),
                24 * 60 * 60
              );
            } catch (wechatError) {
              ctx.logger.error('Portal推送失败后的微信消息推送也异常，但不影响主流程', wechatError);
            }
          }
        }
      }

      console.log('=== 批量任务处理完成 ===');
      console.log('处理结果:', JSON.stringify(batchResult, null, 2));

      ctx.auditLog(
        '批量审批任务处理',
        `申请ID: ${applicationId}, 已办${completedTaskCount}个, 取消${cancelledTaskCount}个, 新建${newTaskCount}个`,
        'info'
      );

      return {
        success: batchResult ? batchResult.success : true,
        applicationId,
        completedTaskCount,
        cancelledTaskCount,
        newTaskCount,
        totalOperations: allTaskRows.length,
        batchResult,
      };

    } catch (error) {
      ctx.logger.error('批量处理审批任务失败', error);
      throw new Error(`批量处理审批任务失败: ${error.message}`);
    }
  }
  /**
   * @summary 获取当前级别的任务数据行
   * @description 获取当前级别需要标记已办和取消的任务，转换为DATAROW格式
   * @param {string} applicationId - 申请ID
   * @param {number} level - 审批级别
   * @param {string} auditorId - 当前审批人ID
   * @param {boolean} isLastLevel - 是否是最后一级审批
   * @return {Promise<Object>} 任务数据行结果
   */
  async getCurrentLevelTaskDataRows(applicationId, level, auditorId, isLastLevel = false) {
    const { ctx } = this;

    try {
      // 获取该级别的推送任务信息
      const taskDataStr = await ctx.helper.getRedis(`${this.REDIS_KEYS.TASKS}${applicationId}:${level}`);
      if (!taskDataStr) {
        // 改进错误处理：尝试从申请元信息重建任务数据
        ctx.logger.warn(`Redis中未找到级别${level}的任务信息，尝试从申请元信息重建任务数据`);
        return await this.reconstructTaskDataFromMeta(applicationId, level, auditorId, isLastLevel);
      }

      // 处理不同的数据类型
      let taskData;
      if (typeof taskDataStr === 'object' && taskDataStr !== null) {
        taskData = taskDataStr;
      } else if (typeof taskDataStr === 'string') {
        try {
          taskData = JSON.parse(taskDataStr);
        } catch (parseError) {
          ctx.logger.error(`任务数据JSON解析失败: ${parseError.message}`);
          return { dataRows: [], completedCount: 0, cancelledCount: 0 };
        }
      } else {
        ctx.logger.error(`任务数据类型异常: ${typeof taskDataStr}`);
        return { dataRows: [], completedCount: 0, cancelledCount: 0 };
      }

      // 获取申请元信息以获取申请人信息
      const metaDataStr = await ctx.helper.getRedis(`${this.REDIS_KEYS.META}${applicationId}`);
      let metaData = null;
      if (metaDataStr) {
        if (typeof metaDataStr === 'object' && metaDataStr !== null) {
          metaData = metaDataStr;
        } else if (typeof metaDataStr === 'string') {
          try {
            metaData = JSON.parse(metaDataStr);
          } catch (parseError) {
            ctx.logger.error(`申请元信息JSON解析失败: ${parseError.message}`);
          }
        }
      }

      const tasks = taskData.tasks;
      const dataRows = [];
      let completedCount = 0;
      let cancelledCount = 0;
      const currentTime = moment().format('YYYY-MM-DD HH:mm:ss');

      for (const task of tasks) {
        // 构建完整的任务数据行基础信息
        const baseTaskRow = {
          OrderSource: 'OHS',
          OrderId: task.orderId,
          OrderCode: task.orderCode || task.orderId,
          OrderTypeName: ctx.service.whPortal.whPortalTaskUtil.buildMultiLangField(
            '防护用品领用申请',
            'Protective Equipment Application',
            'Fegyverek hozzáadás'
          ),
          OrderUserId: metaData ? metaData.employeeId.toLowerCase() : task.orderUserId || '',
          OrderTime: task.orderTime || currentTime,
          OrderTaskId: task.orderTaskId,
          OrderTaskUrl: task.orderTaskUrl,
          Colum2: task.colum2,
          OrderTaskCurrentUserId: task.currentUserId,
          OrderTaskName: ctx.service.whPortal.whPortalTaskUtil.buildMultiLangField(
            this.getTaskNameZh(level),
            this.getTaskNameEn(level),
            this.getTaskNameZh(level)
          ),
          OrderTaskDesc: ctx.service.whPortal.whPortalTaskUtil.buildMultiLangField(
            `${metaData ? metaData.employeeName : '用户'}提交了防护用品领用申请,请您尽快审批`,
            `${metaData ? metaData.employeeName : 'User'} submitted Protective Equipment Application, please handle it ASAP`,
            ''
          ),
          OrderTaskTime: task.orderTime || currentTime,
        };

        if (task.currentUserId === auditorId.toLowerCase()) {
          // 当前审批人的任务设置为已办（状态2，如果是最后一级则为3）
          dataRows.push({
            ...baseTaskRow,
            OrderTaskStatus: isLastLevel ? '3' : '2', // 最后一级为3，其他为2
            OrderAction: 'submit', // 正常完成已办
            OrderDoneTime: currentTime, // 处理时间
          });
          completedCount++;
        } else {
          // 其他审批人的任务设置为取消（状态6-待办删除，这是特殊情况）
          dataRows.push({
            ...baseTaskRow,
            OrderTaskStatus: '6', // 6-待办删除（特殊情况，用于取消其他人的待办）
          });
          cancelledCount++;
        }
      }

      return {
        dataRows,
        completedCount,
        cancelledCount,
      };

    } catch (error) {
      ctx.logger.error('获取当前级别任务数据行失败', error);
      return { dataRows: [], completedCount: 0, cancelledCount: 0 };
    }
  }
  /**
   * @summary 获取下一级别的任务数据行
   * @description 获取下一级别需要创建的新任务，转换为DATAROW格式
   * @param {string} applicationId - 申请ID
   * @param {number} level - 下一级审批级别
   * @param {string} employeeId - 申请人ID
   * @param {string} employeeName - 申请人姓名
   * @return {Promise<Object>} 任务数据行结果
   */
  async getNextLevelTaskDataRows(applicationId, level, employeeId, employeeName) {
    const { ctx } = this;

    try {
      // 获取申请元信息
      const metaDataStr = await ctx.helper.getRedis(`${this.REDIS_KEYS.META}${applicationId}`);
      if (!metaDataStr) {
        throw new Error('未找到申请元信息');
      }

      // 处理不同的数据类型
      let metaData;
      if (typeof metaDataStr === 'object' && metaDataStr !== null) {
        metaData = metaDataStr;
      } else if (typeof metaDataStr === 'string') {
        try {
          metaData = JSON.parse(metaDataStr);
        } catch (parseError) {
          throw new Error(`申请元信息JSON解析失败: ${parseError.message}`);
        }
      } else {
        throw new Error(`申请元信息数据类型异常: ${typeof metaDataStr}`);
      }

      // 从审批记录中获取指定级别的审批人
      const auditRecord = metaData.auditRecords.find(record => record.auditLevel === level);
      if (!auditRecord || !auditRecord.needToAuditIds.length) {
        ctx.logger.warn(`未找到级别${level}的审批人信息`);
        return { dataRows: [], newTaskCount: 0 };
      }

      // 构建新任务数据行
      const dataRows = [];
      const time = moment().format('YYYY-MM-DD HH:mm:ss');

      // 从META数据获取已存在的订单编码
      let orderCode = metaData.orderCode;

      // 如果META中没有orderCode，尝试从第一级任务中获取
      if (!orderCode) {
        ctx.logger.warn('META数据中未找到订单编码，尝试从第一级任务中获取');
        try {
          const firstLevelTaskStr = await ctx.helper.getRedis(`${this.REDIS_KEYS.TASKS}${applicationId}:1`);
          if (firstLevelTaskStr) {
            let firstLevelTask;
            if (typeof firstLevelTaskStr === 'string') {
              firstLevelTask = JSON.parse(firstLevelTaskStr);
            } else {
              firstLevelTask = firstLevelTaskStr;
            }

            if (firstLevelTask.tasks && firstLevelTask.tasks.length > 0) {
              orderCode = firstLevelTask.tasks[0].orderCode || firstLevelTask.tasks[0].orderId;
              ctx.logger.info(`从第一级任务中获取到订单编码: ${orderCode}`);
            }
          }
        } catch (fallbackError) {
          ctx.logger.error('从第一级任务获取订单编码失败', fallbackError);
        }
      }

      // 最终fallback：如果还是没有orderCode，生成新的
      if (!orderCode) {
        ctx.logger.warn('所有方式都无法获取到订单编码，生成新的订单编码');
        orderCode = await this.generateOrderCode();

        // 更新META数据，保存新生成的orderCode
        try {
          metaData.orderCode = orderCode;
          await ctx.helper.setRedis(
            `${this.REDIS_KEYS.META}${applicationId}`,
            JSON.stringify(metaData),
            24 * 60 * 60
          );
          ctx.logger.info(`已将新生成的订单编码${orderCode}保存到META数据`);
        } catch (saveError) {
          ctx.logger.error('保存新订单编码到META失败', saveError);
        }
      }

      for (const userId of auditRecord.needToAuditIds) {
        const taskDataRow = {
          OrderSource: 'OHS',
          OrderId: orderCode,
          OrderCode: orderCode,
          OrderTypeName: ctx.service.whPortal.whPortalTaskUtil.buildMultiLangField(
            '防护用品领用申请',
            'Protective Equipment Application',
            'Fegyverek hozzáadás'
          ),
          OrderUserId: employeeId.toLowerCase(),
          OrderTime: time,
          OrderTaskId: orderCode + userId,
          OrderTaskUrl: this.config.whRequest.qy,
          Colum2: this.config.whRequest.h5sp,
          OrderTaskCurrentUserId: userId.toLowerCase(),
          OrderTaskStatus: '1', // 1-待办
          status: '1', // 兼容字段
          OrderTaskName: ctx.service.whPortal.whPortalTaskUtil.buildMultiLangField(
            this.getTaskNameZh(level),
            this.getTaskNameEn(level),
            this.getTaskNameZh(level)
          ),
          OrderTaskDesc: ctx.service.whPortal.whPortalTaskUtil.buildMultiLangField(
            `${employeeName}提交了防护用品领用申请,请您尽快审批`,
            `${employeeName} submitted Protective Equipment Application, please handle it ASAP`,
            ''
          ),
          OrderTaskTime: time,
        };

        dataRows.push(taskDataRow);
      }

      return {
        dataRows,
        newTaskCount: dataRows.length,
      };

    } catch (error) {
      ctx.logger.error('获取下一级别任务数据行失败', error);
      return { dataRows: [], newTaskCount: 0 };
    }
  }
  /**
   * @summary 推送指定级别的审批任务
   * @description 向指定级别的审批人推送待办任务
   * @param {string} applicationId - 申请ID
   * @param {number} level - 审批级别
   * @param {string} employeeId - 申请人ID
   * @param {string} employeeName - 申请人姓名
   * @return {Promise<Object>} 推送结果
   */
  async pushLevelTasks(applicationId, level, employeeId, employeeName) {
    const { ctx } = this;

    try {
      // 1. 获取申请元信息
      const metaDataStr = await ctx.helper.getRedis(`${this.REDIS_KEYS.META}${applicationId}`);
      if (!metaDataStr) {
        throw new Error('未找到申请元信息');
      }
      const metaData = JSON.parse(metaDataStr);

      // 2. 从审批记录中获取指定级别的审批人
      const auditRecord = metaData.auditRecords.find(record => record.auditLevel === level);
      if (!auditRecord || !auditRecord.needToAuditIds.length) {
        throw new Error(`未找到级别${level}的审批人信息`);
      }

      // 3. 构建推送任务
      const tasks = [];
      const time = moment().format('YYYY-MM-DD HH:mm:ss');

      // 从META数据获取已存在的订单编码
      let orderCode = metaData.orderCode;

      // 如果META中没有orderCode，尝试从第一级任务中获取
      if (!orderCode) {
        ctx.logger.warn('META数据中未找到订单编码，尝试从第一级任务中获取');
        try {
          const firstLevelTaskStr = await ctx.helper.getRedis(`${this.REDIS_KEYS.TASKS}${applicationId}:1`);
          if (firstLevelTaskStr) {
            let firstLevelTask;
            if (typeof firstLevelTaskStr === 'string') {
              firstLevelTask = JSON.parse(firstLevelTaskStr);
            } else {
              firstLevelTask = firstLevelTaskStr;
            }

            if (firstLevelTask.tasks && firstLevelTask.tasks.length > 0) {
              orderCode = firstLevelTask.tasks[0].orderCode || firstLevelTask.tasks[0].orderId;
              ctx.logger.info(`从第一级任务中获取到订单编码: ${orderCode}`);
            }
          }
        } catch (fallbackError) {
          ctx.logger.error('从第一级任务获取订单编码失败', fallbackError);
        }
      }

      // 最终fallback：如果还是没有orderCode，生成新的
      if (!orderCode) {
        ctx.logger.warn('所有方式都无法获取到订单编码，生成新的订单编码');
        orderCode = await this.generateOrderCode();

        // 更新META数据，保存新生成的orderCode
        try {
          metaData.orderCode = orderCode;
          await ctx.helper.setRedis(
            `${this.REDIS_KEYS.META}${applicationId}`,
            JSON.stringify(metaData),
            24 * 60 * 60
          );
          ctx.logger.info(`已将新生成的订单编码${orderCode}保存到META数据`);
        } catch (saveError) {
          ctx.logger.error('保存新订单编码到META失败', saveError);
        }
      }

      for (const userId of auditRecord.needToAuditIds) {
        const taskData = {
          orderId: orderCode,
          orderCode,
          orderTaskId: orderCode + userId,
          orderTypeNameZh: '防护用品领用申请',
          orderTypeNameEn: 'Protective Equipment Application',
          orderTypeNameHu: 'Fegyverek hozzáadás',
          orderUserId: employeeId.toLowerCase(),
          orderTime: time,
          orderTaskUrl: this.config.whRequest.qy,
          colum2: this.config.whRequest.h5sp,
          currentUserId: userId.toLowerCase(),
          taskNameZh: this.getTaskNameZh(level),
          taskNameEn: this.getTaskNameEn(level),
          taskNameHu: this.getTaskNameZh(level),
          orderUserName: employeeName,
          // 添加任务状态字段，新创建的都是待办状态
          OrderTaskStatus: '1', // 1-待办
          status: '1', // 兼容字段
        };

        tasks.push(taskData);
      }

      // 4. 调用whPortal服务推送任务
      const pushResult = await ctx.service.whPortal.pushPendingTask(tasks);

      // 5. 无论Portal推送是否成功，都尝试发送微信消息
      let wechatResult = null;
      try {
        wechatResult = await this.sendWechatNotification(
          applicationId,
          level,
          tasks,
          employeeId,
          employeeName
        );
        ctx.logger.info(`微信消息推送完成，Portal状态: ${pushResult.success}, 微信状态: ${wechatResult.success}`);
      } catch (wechatError) {
        ctx.logger.error('微信消息推送异常，但不影响主流程', wechatError);
        wechatResult = { success: false, error: wechatError.message, messageCount: 0 };
      }

      // 6. 将推送的任务信息存储到Redis
      const taskCacheData = {
        level,
        tasks,
        pushTime: time,
        pushResult,
        wechatResult,
      };

      await ctx.helper.setRedis(
        `${this.REDIS_KEYS.TASKS}${applicationId}:${level}`,
        JSON.stringify(taskCacheData),
        24 * 60 * 60
      );

      ctx.auditLog(
        '审批任务推送',
        `申请ID: ${applicationId}, 级别${level}, 推送${tasks.length}个任务, 微信消息: ${wechatResult ? wechatResult.messageCount : 0}条`,
        'info'
      );

      return {
        success: true,
        level,
        taskCount: tasks.length,
        pushResult,
        wechatResult,
      };

    } catch (error) {
      ctx.logger.error('推送审批任务失败', error);
      throw new Error(`推送审批任务失败: ${error.message}`);
    }
  }

  /**
   * @summary 取消指定级别的待办任务
   * @description 取消指定级别的所有待办任务
   * @param {string} applicationId - 申请ID
   * @param {number} level - 审批级别
   * @return {Promise<Object>} 取消结果
   */
  async cancelLevelTasks(applicationId, level) {
    const { ctx } = this;

    try {
      // 1. 获取该级别的推送任务信息
      const taskDataStr = await ctx.helper.getRedis(`${this.REDIS_KEYS.TASKS}${applicationId}:${level}`);
      if (!taskDataStr) {
        ctx.logger.warn(`未找到级别${level}的任务信息，可能已被清理`);
        return { success: true, cancelCount: 0 };
      }

      const taskData = JSON.parse(taskDataStr);
      const tasks = taskData.tasks;

      // 2. 构建取消任务的请求
      const cancelTasks = [];
      for (const task of tasks) {
        cancelTasks.push({
          orderId: task.orderId,
          orderTaskId: task.orderTaskId,
          orderSource: 'OHS',
        });
      }

      // 3. 调用whPortal服务取消任务（使用特殊状态6-待办删除）
      const cancelResults = [];
      for (const cancelTask of cancelTasks) {
        try {
          const result = await ctx.service.whPortal.pushSpecialTask(cancelTask, '6');
          cancelResults.push(result);
        } catch (error) {
          ctx.logger.error(`取消任务失败: ${cancelTask.orderTaskId}`, error);
          cancelResults.push({ success: false, error: error.message });
        }
      }

      // 4. 删除Redis中的任务缓存
      await ctx.helper.delRedis(`${this.REDIS_KEYS.TASKS}${applicationId}:${level}`);

      ctx.auditLog(
        '审批任务取消',
        `申请ID: ${applicationId}, 级别${level}, 取消${tasks.length}个任务`,
        'info'
      );

      return {
        success: true,
        level,
        cancelCount: tasks.length,
        cancelResults,
      };

    } catch (error) {
      ctx.logger.error('取消审批任务失败', error);
      throw new Error(`取消审批任务失败: ${error.message}`);
    }
  }

  /**
   * @summary 获取审批状态
   * @description 获取指定申请的当前审批状态
   * @param {string} applicationId - 申请ID
   * @return {Promise<Object|null>} 审批状态信息
   */
  async getApprovalStatus(applicationId) {
    const { ctx } = this;

    try {
      const statusDataStr = await ctx.helper.getRedis(`${this.REDIS_KEYS.STATUS}${applicationId}`);
      return statusDataStr ? JSON.parse(statusDataStr) : null;
    } catch (error) {
      ctx.logger.error('获取审批状态失败', error);
      return null;
    }
  }
  /**
   * @summary 处理审批操作
   * @description 处理审批通过或驳回操作，包括状态转换和任务推送
   * @param {string} applicationId - 申请ID
   * @param {number} auditLevel - 当前审批级别
   * @param {boolean} approved - 是否审批通过
   * @param {string} auditorId - 审批人ID
   * @param {string} auditorName - 审批人姓名
   * @param {string} reason - 审批意见
   * @return {Promise<Object>} 处理结果
   */
  async processApproval(applicationId, auditLevel, approved, auditorId, auditorName, reason) {
    const { ctx } = this;

    try {
      if (!auditorName) {
        const result = await ctx.model.AdminUser.findOne({ _id: auditorId.toUpperCase() }).select('name');
        auditorName = result.name;
      }
      // 1. 更新数据库中的审批记录
      await this.updateDatabaseAuditRecord(applicationId, auditLevel, approved, auditorId, auditorName, reason);

      // 2. 检查Redis中是否有状态信息，如果没有则说明是老数据，只更新数据库
      const currentStatus = await this.getApprovalStatus(applicationId);
      if (!currentStatus) {
        ctx.logger.info(`申请${applicationId}未找到Redis状态信息，可能是老数据，仅更新数据库`);

        // 根据分支配置决定审批完成条件
        let finalStatus;
        if (!approved) {
          finalStatus = 'rejected';
        } else {
          // by分支一级审批即可完成，wh分支需要二级审批
          if (ctx.app.config.branch === 'by') {
            finalStatus = 'completed'; // by分支一级审批通过即完成
          } else {
            finalStatus = auditLevel === this.APPROVAL_LEVELS.MANAGER ? 'completed' : 'pending';
          }
        }

        return {
          success: true,
          applicationId,
          approved,
          auditLevel,
          auditorId,
          auditorName,
          reason,
          finalStatus,
          message: approved ? '审批通过' : '审批驳回',
        };
      }

      // 3. 处理审批结果和状态转换（有Redis状态的情况）
      const auditResult = approved ? 1 : 2; // 1-通过, 2-驳回
      const workflowResult = await this.handleApprovalResult(applicationId, auditLevel, auditResult, auditorId);

      // 4. 构造返回结果
      const result = {
        success: true,
        applicationId,
        approved,
        auditLevel,
        auditorId,
        auditorName,
        reason,
        ...workflowResult,
      };

      // 5. 设置最终状态用于判断是否添加领用记录
      if (workflowResult.currentStatus === this.APPROVAL_STATUS.COMPLETED) {
        result.finalStatus = 'completed';
        result.message = '审批流程已完成，申请通过';
      } else if (workflowResult.currentStatus === this.APPROVAL_STATUS.REJECTED) {
        result.finalStatus = 'rejected';
        result.message = '申请已被驳回';
      } else {
        result.finalStatus = 'pending';
        result.message = workflowResult.message || '已转到下一级审批';
      }

      ctx.auditLog(
        '审批操作处理',
        `申请ID: ${applicationId}, 级别: ${auditLevel}, 结果: ${approved ? '通过' : '驳回'}, 审批人: ${auditorName}`,
        'info'
      );

      return result;

    } catch (error) {
      ctx.logger.error('处理审批操作失败', error);
      return {
        success: false,
        applicationId,
        error: error.message,
        message: '审批处理失败，请重试',
      };
    }
  }
  /**
   * @summary 更新数据库中的审批记录
   * @description 更新ApplicationProduct中的auditRecords和auditStatus
   * @param {string} applicationId - 申请ID
   * @param {number} auditLevel - 审批级别
   * @param {boolean} approved - 是否通过
   * @param {string} auditorId - 审批人ID
   * @param {string} auditorName - 审批人姓名
   * @param {string} reason - 审批意见
   * @return {Promise<void>}
   */
  async updateDatabaseAuditRecord(applicationId, auditLevel, approved, auditorId, auditorName, reason) {
    const { ctx } = this;

    try {
      // 1. 获取当前申请记录
      const application = await ctx.model.ApplicationProduct.findOne({ _id: applicationId });
      if (!application) {
        throw new Error('未找到申请记录');
      }
      if (!auditorName) {
        const result = await ctx.model.AdminUser.findOne({ _id: auditorId }).select('name');
        auditorName = result.name;
      }
      // 2. 更新对应级别的审批记录
      const auditRecords = application.auditRecords || [];

      const targetRecordIndex = auditRecords.findIndex(record => record.auditLevel === auditLevel);

      if (targetRecordIndex === -1) {
        throw new Error(`未找到级别${auditLevel}的审批记录`);
      }

      // 更新审批记录
      auditRecords[targetRecordIndex].auditStatus = approved ? 1 : 2; // 1-通过, 2-驳回
      auditRecords[targetRecordIndex].auditTime = new Date();
      auditRecords[targetRecordIndex].operator = {
        _id: auditorId,
        name: auditorName,
      };
      auditRecords[targetRecordIndex].reason = reason;

      // 3. 确定总体审批状态
      let overallStatus;
      if (!approved) {
        // 驳回
        overallStatus = 2;
      } else if (auditLevel === this.APPROVAL_LEVELS.HSE) {
        // HSE管理员通过，如果还有二级审批则为一级通过，否则为最终通过
        const hasManagerLevel = auditRecords.some(record => record.auditLevel === this.APPROVAL_LEVELS.MANAGER);
        overallStatus = hasManagerLevel ? 1 : 3; // 1-一级通过, 3-最终通过
      } else if (auditLevel === this.APPROVAL_LEVELS.MANAGER) {
        // 部门经理通过，为最终通过
        overallStatus = 3;
      }
      console.log('更新参数', {
        _id: applicationId,
        auditRecords,
        auditStatus: overallStatus,
        updateAt: new Date(),
      });
      // 4. 更新数据库
      await ctx.model.ApplicationProduct.updateOne(
        { _id: applicationId },
        {
          $set: {
            auditRecords,
            auditStatus: overallStatus,
            updateAt: new Date(),
          },
        }
      );

      ctx.auditLog(
        '审批记录更新',
        `申请ID: ${applicationId}, 更新级别${auditLevel}审批记录，状态: ${overallStatus}`,
        'info'
      );

    } catch (error) {
      ctx.logger.error('更新数据库审批记录失败', error);
      throw new Error(`更新审批记录失败: ${error.message}`);
    }
  }
  /**
   * @summary 处理审批驳回
   * @description 处理审批被驳回的情况
   * @param {string} applicationId - 申请ID
   * @param {number} level - 当前审批级别
   * @return {Promise<Object>} 处理结果
   */
  async handleRejection(applicationId, level) {
    const { ctx } = this;

    try {
      // 1. 取消当前级别的所有待办任务
      await this.cancelLevelTasks(applicationId, level);

      // 2. 更新状态为已驳回
      const statusData = {
        currentStatus: this.APPROVAL_STATUS.REJECTED,
        currentLevel: null,
        updateTime: moment().format('YYYY-MM-DD HH:mm:ss'),
        rejectedAt: level,
      };

      await ctx.helper.setRedis(
        `${this.REDIS_KEYS.STATUS}${applicationId}`,
        JSON.stringify(statusData),
        24 * 60 * 60
      );

      // 3. 清理审批数据
      await this.cleanupApprovalData(applicationId);

      ctx.auditLog(
        '审批被驳回',
        `申请ID: ${applicationId}, 在级别${level}被驳回`,
        'info'
      );

      return {
        success: true,
        applicationId,
        status: this.APPROVAL_STATUS.REJECTED,
        message: '审批已驳回，流程结束',
      };

    } catch (error) {
      ctx.logger.error('处理审批驳回失败', error);
      throw new Error(`处理审批驳回失败: ${error.message}`);
    }
  }

  /**
   * @summary 清理审批数据
   * @description 清理Redis中的审批相关数据
   * @param {string} applicationId - 申请ID
   * @return {Promise<void>}
   */
  async cleanupApprovalData(applicationId) {
    const { ctx } = this;

    try {
      // 删除所有相关的Redis键
      const keysToDelete = [
        `${this.REDIS_KEYS.STATUS}${applicationId}`,
        `${this.REDIS_KEYS.META}${applicationId}`,
        `${this.REDIS_KEYS.TASKS}${applicationId}:${this.APPROVAL_LEVELS.HSE}`,
        `${this.REDIS_KEYS.TASKS}${applicationId}:${this.APPROVAL_LEVELS.MANAGER}`,
      ];

      for (const key of keysToDelete) {
        await ctx.helper.delRedis(key);
      }

      ctx.auditLog(
        '审批数据清理',
        `申请ID: ${applicationId}, 已清理相关Redis数据`,
        'info'
      );

    } catch (error) {
      ctx.logger.error('清理审批数据失败', error);
    }
  }

  /**
   * @summary 获取级别对应的中文任务名称
   * @param {number} level - 审批级别
   * @return {string} 中文任务名称
   */
  getTaskNameZh(level) {
    switch (level) {
      case this.APPROVAL_LEVELS.HSE:
        return 'HSE管理员审批';
      case this.APPROVAL_LEVELS.MANAGER:
        return '部门经理审批';
      default:
        return '审批';
    }
  }

  /**
   * @summary 获取级别对应的英文任务名称
   * @param {number} level - 审批级别
   * @return {string} 英文任务名称
   */
  getTaskNameEn(level) {
    switch (level) {
      case this.APPROVAL_LEVELS.HSE:
        return 'HSE Management Admin Approval';
      case this.APPROVAL_LEVELS.MANAGER:
        return 'Department Manager Approval';
      default:
        return 'Approval';
    }
  }

  /**
   * @summary 获取级别名称
   * @param {number} level - 审批级别
   * @return {string} 级别名称
   */
  getLevelName(level) {
    switch (level) {
      case this.APPROVAL_LEVELS.HSE:
        return 'HSE管理员';
      case this.APPROVAL_LEVELS.MANAGER:
        return '部门经理';
      default:
        return '未知级别';
    }
  }

  /**
   * @summary 生成订单编码
   * @description 生成OHS+年份+月份+日期+小时+分钟+秒+序列号格式的订单编码
   * @return {Promise<string>} 订单编码，格式：OHS+YYYY+MM+DD+HH+mm+ss+序列号（2位数补零）
   */
  async generateOrderCode() {
    const { ctx } = this;

    try {
      // 获取当前日期时间
      const now = moment();
      const dateTimeStr = now.format('YYYYMMDDHHmmss');

      // 构建Redis键，按日期管理序列号（保持递增特性）
      const dateKey = now.format('YYYY-MM-DD');
      const sequenceKey = `${this.REDIS_KEYS.DAILY_SEQUENCE}${dateKey}`;

      // 使用Redis INCR命令获取当日递增序列号（原子操作）
      let sequence;
      try {
        // 获取并递增序列号
        sequence = await ctx.app.redis.incr(sequenceKey);

        // 设置过期时间为48小时（防止跨日问题）
        await ctx.app.redis.expire(sequenceKey, 48 * 60 * 60);
      } catch (redisError) {
        // Redis操作失败时使用时间戳作为后备方案
        ctx.logger.error('Redis序列号生成失败，使用后备方案', redisError);
        sequence = parseInt(now.format('HHmmss')) % 100; // 使用时分秒的末尾2位
      }

      // 格式化序列号为2位数字，补零
      const sequenceStr = String(sequence).padStart(2, '0');

      // 生成最终的订单编码：OHS+年月日时分秒+序列号
      const orderCode = `OHS${dateTimeStr}${sequenceStr}`;

      ctx.auditLog(
        '订单编码生成',
        `生成订单编码: ${orderCode}, 日期: ${dateKey}, 序列号: ${sequence}`,
        'info'
      );

      return orderCode;

    } catch (error) {
      ctx.logger.error('生成订单编码失败', error);
      // 最终后备方案：使用时间戳
      const timestamp = moment().format('YYYYMMDDHHmmss');
      return `OHS${timestamp}01`; // 加上默认序列号01
    }
  }

  /**
   * @summary 发送微信消息推送
   * @description 给待办任务审批人发送微信通知消息
   * @param {string} applicationId - 申请ID
   * @param {number} level - 审批级别
   * @param {Array} tasks - 任务列表
   * @param {string} employeeId - 申请人ID
   * @param {string} employeeName - 申请人姓名
   * @return {Promise<Object>} 推送结果
   */
  async sendWechatNotification(applicationId, level, tasks, employeeId, employeeName) {
    const { ctx } = this;

    try {
      // 调试：打印传入的任务数据
      console.log('========== sendWechatNotification 调试信息 ==========');
      console.log('传入的tasks数量:', tasks.length);
      console.log('传入的tasks数据:');
      tasks.forEach((task, index) => {
        console.log(`任务${index + 1}:`, {
          orderTaskId: task.orderTaskId || task.OrderTaskId,
          currentUserId: task.currentUserId || task.OrderTaskCurrentUserId,
          OrderTaskStatus: task.OrderTaskStatus,
          status: task.status,
        });
      });

      // 过滤出待办状态的任务（状态为1）
      const pendingTasks = tasks.filter(task => {
        // 从OrderTaskStatus或直接检查任务状态
        const status = task.OrderTaskStatus || task.status;
        const isPending = status === '1' || status === 1;
        console.log(`任务${task.orderTaskId || task.OrderTaskId}状态检查: status=${status}, isPending=${isPending}`);
        return isPending;
      });

      console.log('过滤后的待办任务数量:', pendingTasks.length);
      console.log('===============================================');

      if (pendingTasks.length === 0) {
        ctx.logger.info(`申请${applicationId}级别${level}没有待办任务，跳过微信推送`);
        return { success: true, messageCount: 0 };
      }

      // 构造微信消息数据
      const wechatMessages = [];
      const taskNameZh = this.getTaskNameZh(level);
      const taskNameEn = this.getTaskNameEn(level);

      for (const task of pendingTasks) {
        // 获取审批人信息
        const auditorId = task.OrderTaskCurrentUserId || task.currentUserId;
        let auditorName = task.auditorName;
        if (!auditorName) {
          const result = await ctx.model.AdminUser.findOne({ _id: auditorId.toUpperCase() }).select('name');
          auditorName = result.name;
        }
        // 从任务中获取审批人姓名，或使用默认值
        auditorName = auditorName || '审批人';

        // 如果能从员工信息中获取更好
        try {
          const auditorInfo = await ctx.service.db.findOne('AdminUser',
            { employeeId: auditorId },
            'name'
          );
          if (auditorInfo && auditorInfo.name) {
            auditorName = auditorInfo.name;
          }
        } catch (auditorError) {
          ctx.logger.warn(`获取审批人${auditorId}姓名失败，使用默认值`);
        }

        const wechatMessage = {
          // 流程名称（双语格式）
          bpdname: this.wechatMessageUtil.buildBilingualName(
            '防护用品领用申请',
            'Protective Equipment Application'
          ),
          // 环节名称（双语格式）
          activityname: this.wechatMessageUtil.buildBilingualName(
            taskNameZh,
            taskNameEn
          ),
          // 发起人信息
          fromid: employeeId.toLowerCase(),
          fromname: employeeName,
          // 接收人信息
          toid: auditorId.toLowerCase(),
          toname: auditorName,
          // 任务链接
          taskid: task.colum2 || task.Colum2,
          // 创建时间
          ctime: this.wechatMessageUtil.formatDateTime(),
          // 操作类型：1-提交
          action: '1',
        };

        wechatMessages.push(wechatMessage);
      }

      // 发送微信消息
      ctx.logger.info(`准备发送${wechatMessages.length}条微信消息，申请ID: ${applicationId}, 级别: ${level}`);

      const pushResult = await this.wechatMessageUtil.sendMessage('OHS', wechatMessages);

      if (pushResult.success) {
        ctx.logger.info(`微信消息推送成功，申请ID: ${applicationId}, 级别: ${level}, 消息数: ${wechatMessages.length}`);
        ctx.auditLog(
          '微信消息推送',
          `申请ID: ${applicationId}, 级别${level}, 成功推送${wechatMessages.length}条微信消息`,
          'info'
        );
      } else {
        ctx.logger.error(`微信消息推送失败，申请ID: ${applicationId}, 级别: ${level}`, pushResult);
        ctx.auditLog(
          '微信消息推送失败',
          `申请ID: ${applicationId}, 级别${level}, 推送失败: ${pushResult.error}`,
          'error'
        );
      }

      return {
        success: pushResult.success,
        messageCount: wechatMessages.length,
        result: pushResult,
      };

    } catch (error) {
      ctx.logger.error('发送微信消息推送失败', error);
      ctx.auditLog(
        '微信消息推送异常',
        `申请ID: ${applicationId}, 级别${level}, 异常: ${error.message}`,
        'error'
      );

      // 微信推送失败不影响主流程
      return {
        success: false,
        error: error.message,
        messageCount: 0,
      };
    }
  }

  /**
   * @summary 从申请元信息重建任务数据
   * @description 当Redis中缺失任务数据时，从申请元信息重建必要的任务数据
   * @param {string} applicationId - 申请ID
   * @param {number} level - 审批级别
   * @param {string} auditorId - 当前审批人ID
   * @param {boolean} isLastLevel - 是否是最后一级审批
   * @return {Promise<Object>} 重建的任务数据
   */
  async reconstructTaskDataFromMeta(applicationId, level, auditorId, isLastLevel = false) {
    const { ctx } = this;

    try {
      ctx.logger.info(`开始从申请元信息重建级别${level}的任务数据`);

      // 获取申请元信息
      const metaDataStr = await ctx.helper.getRedis(`${this.REDIS_KEYS.META}${applicationId}`);
      if (!metaDataStr) {
        ctx.logger.error(`无法重建任务数据：未找到申请元信息，applicationId: ${applicationId}`);
        return { dataRows: [], completedCount: 0, cancelledCount: 0 };
      }

      // 处理不同的数据类型
      let metaData;
      if (typeof metaDataStr === 'object' && metaDataStr !== null) {
        metaData = metaDataStr;
      } else if (typeof metaDataStr === 'string') {
        try {
          metaData = JSON.parse(metaDataStr);
        } catch (parseError) {
          ctx.logger.error(`申请元信息JSON解析失败: ${parseError.message}`);
          return { dataRows: [], completedCount: 0, cancelledCount: 0 };
        }
      } else {
        ctx.logger.error(`申请元信息数据类型异常: ${typeof metaDataStr}`);
        return { dataRows: [], completedCount: 0, cancelledCount: 0 };
      }

      // 从审批记录中获取指定级别的审批人信息
      const auditRecord = metaData.auditRecords.find(record => record.auditLevel === level);
      if (!auditRecord || !auditRecord.needToAuditIds.length) {
        ctx.logger.warn(`申请元信息中未找到级别${level}的审批人信息`);
        return { dataRows: [], completedCount: 0, cancelledCount: 0 };
      }

      // 重建任务数据
      const dataRows = [];
      let completedCount = 0;
      let cancelledCount = 0;
      const currentTime = moment().format('YYYY-MM-DD HH:mm:ss');

      // 获取或生成订单编码
      let orderCode = metaData.orderCode;
      if (!orderCode) {
        orderCode = await this.generateOrderCode();
        ctx.logger.info(`为重建任务数据生成新的订单编码: ${orderCode}`);
      }

      // 为每个审批人重建任务数据
      for (const userId of auditRecord.needToAuditIds) {
        // 生成任务ID（保持与原有逻辑一致的格式）
        const taskId = `${userId}_${level}_${applicationId}_${Date.now()}`;

        // 构建基础任务数据行
        const baseTaskRow = {
          OrderSource: 'OHS',
          OrderId: orderCode,
          OrderCode: orderCode,
          OrderTypeName: ctx.service.whPortal.whPortalTaskUtil.buildMultiLangField(
            '防护用品领用申请',
            'Protective Equipment Application',
            'Fegyverek hozzáadás'
          ),
          OrderUserId: metaData.employeeId.toLowerCase(),
          OrderTime: metaData.createTime || currentTime,
          OrderTaskId: taskId,
          OrderTaskUrl: this.config.whRequest.qy,
          Colum2: this.config.whRequest.h5sp,
          OrderTaskCurrentUserId: userId.toLowerCase(),
          OrderTaskName: ctx.service.whPortal.whPortalTaskUtil.buildMultiLangField(
            this.getTaskNameZh(level),
            this.getTaskNameEn(level),
            this.getTaskNameZh(level)
          ),
          OrderTaskDesc: ctx.service.whPortal.whPortalTaskUtil.buildMultiLangField(
            `${metaData.employeeName}提交了防护用品领用申请,请您尽快审批`,
            `${metaData.employeeName} submitted Protective Equipment Application, please handle it ASAP`,
            ''
          ),
          OrderTaskTime: metaData.createTime || currentTime,
        };

        if (userId.toLowerCase() === auditorId.toLowerCase()) {
          // 当前审批人的任务设置为已办
          dataRows.push({
            ...baseTaskRow,
            OrderTaskStatus: isLastLevel ? '3' : '2',
            OrderAction: 'submit',
            OrderDoneTime: currentTime,
          });
          completedCount++;
        } else {
          // 其他审批人的任务设置为取消
          dataRows.push({
            ...baseTaskRow,
            OrderTaskStatus: '6', // 6-待办删除
          });
          cancelledCount++;
        }
      }

      ctx.logger.info(`成功从申请元信息重建级别${level}的任务数据: 已办${completedCount}个, 取消${cancelledCount}个`);
      ctx.auditLog(
        '任务数据重建',
        `申请ID: ${applicationId}, 级别${level}, 从元信息重建任务数据: 已办${completedCount}个, 取消${cancelledCount}个`,
        'info'
      );

      return {
        dataRows,
        completedCount,
        cancelledCount,
      };

    } catch (error) {
      ctx.logger.error(`从申请元信息重建任务数据失败: ${error.message}`, error);
      return { dataRows: [], completedCount: 0, cancelledCount: 0 };
    }
  }
}

module.exports = ApprovalWorkflowService;
