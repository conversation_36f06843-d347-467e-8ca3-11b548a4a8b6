const Service = require('egg').Service;
// const { tools } = require('@utils');
const moment = require('moment');
const path = require('path');
const shortid = require('shortid');
const {
  _list,
  _item,
  _count,
  _create,
  _update,
  _removes,
  _safeDelete,
} = require(path.join(process.cwd(), 'app/service/general'));
const _ = require('lodash');

class AdminuserService extends Service {

  async find(payload, {
    query = {},
    searchKeys = [],
    populate = [],
    files = null,
  } = {}) {
    const listdata = _list(this.ctx.model.AdminUser, payload, {
      query,
      searchKeys,
      populate,
      files,
    });
    return listdata;
  }

  async count(params = {}) {
    return _count(this.ctx.model.AdminUser, params);
  }

  async create(payload, options) {
    return _create(this.ctx.model.AdminUser, payload, options);
  }

  async removes(res, values, key = '_id') {
    return _removes(res, this.ctx.model.AdminUser, values, key);
  }

  async safeDelete(res, values) {
    return _safeDelete(res, this.ctx.model.AdminUser, values);
  }

  async update(res, _id, payload, query, options) {
    return _update(res, this.ctx.model.AdminUser, _id, payload, query, options);
  }

  async item(res, {
    query = {},
    populate = [],
    files = null,
    options,
  } = {}) {
    return _item(res, this.ctx.model.AdminUser, {
      files: files ? files : {
        password: 0,
        email: 0,
      },
      query,
      populate,
    }, options);
  }
  // 获取员工当前岗位
  // 获取员工岗位
  async findEmployeeStation(params) {
    try {
      const EnterpriseID = params.companyId ? params.companyId[params.companyId.length - 1] : params.EnterpriseID;
      const res = await this.ctx.model.MillConstruction.aggregate([
        {
          $match: {
            EnterpriseID,
          },
        },
        {
          $unwind: '$children',
        },
        {
          $unwind: '$children.children',
        },
        {
          $match: {
            $or: [{ 'children.children.employees': params.employeeId }, { 'children.children.children.employees': params.employeeId }],
          },
        },
      ]);
      const stations = [];
      res.forEach(item => {
        if (item.category === 'workspaces') {
          item.children.workspace = item.name;
          item.children.workspaceId = item._id;
          item.children.harmFactorsAndSort = item.children.harmFactors;
          item.children.harmFactors = item.children.harmFactors.map(item2 => item2[1]).join('、');
          delete item.children.children;
          stations.push(item.children);
        } else if (item.category === 'mill') {
          item.children.children.workshop = item.name;
          item.children.children.workshopId = item._id;
          item.children.children.workspace = item.children.name;
          item.children.children.workspaceId = item.children._id;
          item.children.children.harmFactorsAndSort = item.children.children.harmFactors;
          item.children.children.harmFactors = item.children.children.harmFactors.map(item2 => item2[1]).join('、');
          delete item.children.children.children;
          stations.push(item.children.children);
        }
      });
      return stations;
    } catch (error) {
      console.log(error);
    }
  }

  // async getCheckResult(params, companyId) { // 没用上
  //   return await this.getStationsCheckResult(params, companyId);
  // }

  // 岗位检测结果
  // 需要参数：岗位
  async getCheckResult(stations, companyId) {
    const { ctx } = this;
    const EnterpriseID = companyId[companyId.length - 1];
    let checkResultItem = [];
    let model = '';
    let matchOrOne = {};
    // let matchOrTwo = {};
    // let matchAnd = {};
    let project = {};
    const checkResult = {};
    let modelName = '';
    let station = {};
    let checkProjectFields = {};

    for (let i = 0; i < stations.length; i++) {
      station = stations[i];
      station.name = station.name.replace('岗位', '').trim();
      station.workspace = station.workspace.replace('车间', '').trim();
      for (let j = 0; j < station.harmFactorsAndSort.length; j++) {
        checkProjectFields = {};
        if (station.harmFactorsAndSort[j][0] === '化学') {
          model = 'chemistryFactors';
          modelName = '化学';
          checkProjectFields[model + '.formData.checkProject'] = station.harmFactorsAndSort[j][1].trim();
        } else if (station.harmFactorsAndSort[j][0] === '粉尘') {
          model = 'dustFactors';
          modelName = '粉尘';
          checkProjectFields[model + '.formData.checkProject'] = { $regex: station.harmFactorsAndSort[j][1].trim() };
        } else if (station.harmFactorsAndSort[j][0] === '生物') {
          model = 'biologicalFactors';
          modelName = '生物';
          checkProjectFields[model + '.formData.checkProject'] = station.harmFactorsAndSort[j][1].trim();
        } else {
          if (station.harmFactorsAndSort[j][1].indexOf('噪声') !== -1) {
            model = 'noiseFactors';
            modelName = '噪声';
          } else if (station.harmFactorsAndSort[j][1].indexOf('高温') !== -1) {
            model = 'heatFactors';
            modelName = '高温';
          } else if (station.harmFactorsAndSort[j][1].indexOf('超高频辐射') !== -1) {
            model = 'ultraHighRadiationFactors';
            modelName = '超高频辐射';
          } else if (station.harmFactorsAndSort[j][1].indexOf('高频电磁场') !== -1) {
            model = 'highFrequencyEleFactors';
            modelName = '高频电磁场';
          } else if (station.harmFactorsAndSort[j][1].indexOf('工频电磁场') !== -1) {
            model = 'powerFrequencyElectric';
            modelName = '工频电场';
          } else if (station.harmFactorsAndSort[j][1].indexOf('激光') !== -1) {
            model = 'laserFactors';
            modelName = '激光辐射';
          } else if (station.harmFactorsAndSort[j][1].indexOf('微波') !== -1) {
            model = 'microwaveFactors';
            modelName = '微博辐射';
          } else if (station.harmFactorsAndSort[j][1].indexOf('紫外线') !== -1) {
            model = 'ultravioletFactors';
            modelName = '紫外线';
          } else if (station.harmFactorsAndSort[j][1].indexOf('振动') !== -1) {
            model = 'handBorneVibrationFactors';
            modelName = '手传振动';
          } else if (station.harmFactorsAndSort[j][1].indexOf('游离二氧化硅') !== -1) {
            model = 'SiO2Factors';
            modelName = '游离二氧化硅';
          }
        }
        if (model) {
          matchOrOne = {};
          // matchOrTwo = {};
          // matchAnd = {};
          project = {};
          matchOrOne[model + '.formData.station'] = { $regex: station.name };
          matchOrOne[model + '.formData.workspace'] = { $regex: station.workspace };
          // matchOrTwo[model + '.formData.checkAddress'] = { $regex: station.workspace.indexOf('车间') ? station.workspace.trim() : station.workspace.trim() + '车间' };
          // matchAnd[model + '.formData.checkAddress'] = { $regex: '车间' + station.name.trim() };
          project[model] = 1;
          project.jobHealthId = 1;
          project['jobHealth.name'] = 1;
          project['jobHealth.reportTime'] = 1;
          checkResultItem = await ctx.model.CheckAssessment.aggregate([
            { $match: { EnterpriseID, process: { $exists: false } } },
            { $unwind: '$' + model + '.formData' },
            {
              $match: {
                ...checkProjectFields,
                $or: [
                  matchOrOne, {
                    $and: [
                      { [model + '.formData.checkAddress']: { $regex: station.name } },
                      { [model + '.formData.checkAddress']: { $regex: station.workspace } },
                    ],
                  },
                ],
              },
            },

            {
              $lookup: {
                from: 'jobhealths',
                localField: 'jobHealthId',
                foreignField: '_id',
                as: 'jobHealth',
              },
            },
            {
              $project: project,
            },
          ]);
          for (let k = 0; k < checkResultItem.length; k++) {
            if (checkResultItem[k] && checkResultItem[k].jobHealth[0] && checkResultItem[k][model].formData) {
              checkResultItem[k][model].formData.checkTime = checkResultItem[k].jobHealth[0].reportTime ? moment(new Date(checkResultItem[k].jobHealth[0].reportTime)).format('YYYY-MM-DD') : '';
              checkResultItem[k][model].formData.checkName = checkResultItem[k].jobHealth[0].name;
              checkResultItem[k][model].formData.checkProject = checkResultItem[k][model].formData.checkProject || modelName;
              checkResultItem[k][model].formData.checkResult = checkResultItem[k][model].formData.checkResult || checkResultItem[k][model].formData.conclusion;
              checkResultItem[k][model].formData.protectiveFacilities = station.protectiveFacilities;
              if (Object.keys(checkResult).indexOf('' + model) === -1) {
                checkResult[model] = { data: [] };
              }
              checkResult[model].model = model;
              checkResult[model].modelName = modelName;
              checkResult[model].data.push(checkResultItem[k][model].formData);
            }
          }
        }
      }
    }
    return Object.values(checkResult);
  }

  // 获取防护用具 - 适配新的数据结构
  async getDefendproducts(params) {
    try {
      const { ctx } = this;
      const EnterpriseID = params.companyId ? params.companyId[0] : params.EnterpriseID;
      const employeeId = params.employeeId;

      // 获取员工信息和分类
      const employee = await ctx.model.Employee.findOne({ _id: employeeId }, { category: 1, departs: 1 }).lean();
      if (!employee) {
        throw new Error('员工不存在');
      }

      // 获取员工所属仓库
      const employeeWarehouse = await this.getEmployeeWarehouse(employeeId, EnterpriseID);

      // 获取配发标准（使用新的扁平化查询）
      const protectionPlans = await this.findProtectionPlanNew(employeeId, EnterpriseID, employee.category);

      // 获取防护用品分类数据（适配新的产品模型）
      const typeList = await this.getProtectiveProductCategories(EnterpriseID, employeeWarehouse.warehouseId);

      // 获取领用记录
      const records = await ctx.model.ReceiveRecord.find({
        EnterpriseID,
        employee: employeeId,
      }).sort({ createdAt: -1 }).lean();

      // 获取申请记录
      const applications = await ctx.model.ApplicationProduct.find({
        EnterpriseID,
        employee: employeeId,
      }).sort({ createdAt: -1 }).lean();

      // 为每个配发标准的每个产品匹配或生成领用记录
      for (let i = 0; i < protectionPlans.length; i++) {
        const plan = protectionPlans[i];
        plan.employee = employeeId;

        for (let j = 0; j < plan.products.length; j++) {
          const product = plan.products[j];
          const targetRecord = records.find(e => {
            if (
              e.planId === plan._id &&
              e.products[0].product === product.product &&
              (!e.sign)
            ) {
              return true;
            }
            return false;
          });

          if (targetRecord) {
            product.todo = targetRecord;
          } else {
            // 生成新的领用记录
            const newReceiveRecord = await this.updateNewReceiveRecord(plan, product);
            product.todo = newReceiveRecord;
          }
        }
      }

      return {
        records,
        typeList,
        applications,
        allProtectionPlan: protectionPlans,
        employeeWarehouse,
      };
    } catch (error) {
      console.log(error);
      return 500;
    }
  }

  // 获取员工所属仓库
  async getEmployeeWarehouse(employeeId, EnterpriseID) {
    const { ctx } = this;

    console.log(`[仓库匹配] 开始为员工 ${employeeId} 匹配仓库`);

    // 获取员工岗位信息
    const employeePositions = await this.getEmployeePositions(employeeId, EnterpriseID);
    console.log(`[仓库匹配] 员工岗位信息:`, employeePositions);

    if (!employeePositions || employeePositions.length === 0) {
      console.log(`[仓库匹配] 员工无岗位信息，使用公共仓库`);
      // 如果没有岗位信息，返回公共仓库
      const publicWarehouse = await ctx.model.Warehouse.findOne({
        EnterpriseID,
        isPublic: true,
      });

      if (!publicWarehouse) {
        throw new Error('未找到可用仓库');
      }

      console.log(`[仓库匹配] 返回公共仓库:`, publicWarehouse.name);
      return {
        warehouseId: publicWarehouse._id,
        warehouseName: publicWarehouse.name,
        isPublic: true,
      };
    }

    // 根据岗位匹配仓库
    for (const position of employeePositions) {
      console.log(`[仓库匹配] 尝试匹配岗位: ${position.name} (${position.fullId})`);
      const warehouse = await ctx.model.Warehouse.findOne({
        EnterpriseID,
        'managementScope.fullId': position.fullId,
      });

      if (warehouse) {
        console.log(`[仓库匹配] 找到匹配仓库: ${warehouse.name} (${warehouse._id})`);
        return {
          warehouseId: warehouse._id,
          warehouseName: warehouse.name,
          isPublic: warehouse.isPublic,
          matchedPosition: position,
        };
      }
    }

    console.log(`[仓库匹配] 未找到专用仓库，使用公共仓库`);
    // 如果没有匹配的仓库，返回公共仓库
    const publicWarehouse = await ctx.model.Warehouse.findOne({
      EnterpriseID,
      isPublic: true,
    });

    console.log(`[仓库匹配] 返回公共仓库:`, publicWarehouse?.name);
    return {
      warehouseId: publicWarehouse._id,
      warehouseName: publicWarehouse.name,
      isPublic: true,
    };
  }

  // 获取员工岗位信息（使用扁平化结构）
  async getEmployeePositions(employeeId, EnterpriseID) {
    const { ctx } = this;

    // 使用新的扁平化结构查询员工岗位
    const positions = await ctx.model.FlatMillConstructionMaterialized.find({
      EnterpriseID,
      level: { $in: [ 'workspaces', 'stations' ] },
      employees: employeeId,
    }).select('fullId name level millName workspaceName stationName');

    return positions;
  }

  // 领取防护用具（适配新的产品模型）
  async receiveProducts(params) {
    try {
      const { ctx } = this;
      const EnterpriseID = ctx.session.adminUserInfo ? ctx.session.adminUserInfo.EnterpriseID : params.EnterpriseID;
      const receiveDate = params.productionDate && params.type === 'receive' ? new Date(params.productionDate) : new Date();
      let res;

      // 查询库存是否充足（适配新的产品模型）
      if (params.products && params.products.length > 0) {
        for (let i = 0; i < params.products.length; i++) {
          const product = params.products[i];

          // 尝试使用新的产品模型查询库存
          let targetProduct = null;

          if (product.productId) {
            // 新模型：直接通过产品ID查询
            targetProduct = await ctx.model.ProtectiveProduct.findOne({
              _id: product.productId,
              EnterpriseID,
              isActive: true,
            }).select('surplus product');

            if (targetProduct) {
              const surplus = targetProduct.surplus;
              if (isNaN(parseFloat(surplus))) {
                return { message: 'surplus!', code: -1 };
              }
              if (parseFloat(surplus) < parseFloat(product.receiveNum)) {
                return { message: 'surplus!', code: -1 };
              }
            } else {
              return { message: 'surplus!', code: -1 };
            }
          } else if (product.productIds && product.productIds.length >= 2) {
            // 旧模型：通过productIds数组查询
            const targetProducts = await ctx.model.ProtectiveSuppliesList.aggregate([
              { $unwind: '$list' },
              { $unwind: '$list.data' },
              { $match: { 'list._id': product.productIds[0] } },
              { $match: { 'list.data._id': product.productIds[1] } },
              { $project: { surplus: '$list.data.surplus' } },
            ]);

            if (targetProducts && targetProducts[0] && targetProducts[0].surplus) {
              const surplus = targetProducts[0].surplus;
              if (isNaN(parseFloat(surplus))) {
                return { message: 'surplus!', code: -1 };
              }
              if (parseFloat(surplus) < parseFloat(product.receiveNum)) {
                return { message: 'surplus!', code: -1 };
              }
            } else {
              return { message: 'surplus!', code: -1 };
            }
          } else {
            return { message: 'surplus!', code: -1 };
          }
        }
      }

      if (params.type === 'receive' || params.type === 'reject') {
        // 查找是否又同款用品，是的话报废以前的
        const scraping = await ctx.model.ReceiveRecord.aggregate([
          {
            $match: {
              EnterpriseID,
              employee: params.employee,
              receiveDate: { $exists: true }, // 表示已经被领取
              scrap: false,
              planId: params.planId,
              isRejected: false,
            },
          },
        ]);
        const date = new Date();
        const currentYear = date.getFullYear(); // 获取当前年份
        const currentMonth = date.getMonth() + 1; // 获取当前月份

        // 若是有计划则创建新的
        const planRes = await ctx.model.ProtectionPlan.aggregate([
          {
            $match: { _id: params.planId },
          },
          {
            $unwind: '$products',
          },
          {
            $match: { 'products.product': params.product },
          },
        ]);
        console.log(88888881111, params, planRes);
        if (planRes.length > 0) {
          const plan = planRes[0];
          const now = new Date(); // 获取当前时间
          const productPlan = plan.products;

          plan.EnterpriseID = EnterpriseID;
          plan.planId = params.planId;
          plan.recordSource = 0;
          plan.receiveStartDate = moment(now).add(productPlan.time, productPlan.timeUnit);
          if (ctx.app.config.branch === 'wh' && params.product === '安全帽') {
            plan.receiveStartDate = moment(receiveDate).add(30, 'months').toDate();
          }
          plan.warningDate = moment(plan.receiveStartDate).add(5, 'd');
          plan.employee = params.employee;
          delete plan._id;
          console.log(plan, 'planxxx');
          new ctx.model.ReceiveRecord(plan).save();
        }
        // 报废以前领取的
        if (scraping.length > 0 && params.type === 'receive') {
          if (scraping.length > 1) console.log('匹配到多个需要报废的了');
          const obj = {
            EnterpriseID, // 企业id
            workspaces: scraping[0].workspacesName, // 车间名称
            workstation: scraping[0].workstationName, // 岗位名称
            workshop: scraping[0].workshopName, // 厂房名称
            products: scraping[0].products,
            employee: params.employee,
            applicationTime: date, // 申请报废时间
            yearNumber: currentYear, // 年份
            mouthNumber: currentMonth, // 月份
            scrapReason: '自动过期报废',
          };
          // 修改记录
          await ctx.model.ReceiveRecord.updateOne({ EnterpriseID, _id: scraping[0]._id }, { scrap: true });
          await new ctx.model.ScrapProduct(obj).save();
        }
        // 更新领用防护用品的具体型号（适配新的产品模型）
        if (params.products && params.products.length > 0 && params.type === 'receive') {
          for (let i = 0; i < params.products.length; i++) {
            const product = params.products[i];

            if (product.productId) {
              // 新模型：直接通过产品ID处理
              const targetProduct = await ctx.model.ProtectiveProduct.findOne({
                _id: product.productId,
                EnterpriseID,
                isActive: true,
              }).select('surplus productSpec product');

              if (targetProduct) {
                const surplus = targetProduct.surplus - product.receiveNum;

                // 更新领用记录
                await ctx.model.ReceiveRecord.updateOne(
                  { _id: params._id },
                  {
                    $set: {
                      'products.$[i].productId': product.productId,
                      'products.$[i].productSpec': targetProduct.productSpec,
                      'products.$[i].product': targetProduct.product,
                    },
                  },
                  { arrayFilters: [{ 'i._id': product.productsOrder }] }
                );

                // 减掉库存
                await ctx.model.ProtectiveProduct.updateOne(
                  { _id: product.productId },
                  { $set: { surplus } }
                );
              }
            } else if (product.productIds && product.productIds.length >= 2) {
              // 旧模型：通过productIds数组处理
              let targetProduct = await ctx.model.ProtectiveSuppliesList.aggregate([
                { $unwind: '$list' },
                { $unwind: '$list.data' },
                { $match: { 'list._id': product.productIds[0] } },
                { $match: { 'list.data._id': product.productIds[1] } },
                { $project: { surplus: '$list.data.surplus', productSpec: '$list.data.productSpec' } },
              ]);
              targetProduct = targetProduct[0] ? targetProduct[0] : null;
              let surplus = targetProduct ? targetProduct.surplus : 0;
              surplus = surplus - product.receiveNum;

              await ctx.model.ReceiveRecord.updateOne(
                { _id: params._id },
                {
                  $set: {
                    'products.$[i].productIds': product.productIds,
                    'products.$[i].productSpec': targetProduct ? targetProduct.productSpec : '',
                  },
                },
                { arrayFilters: [{ 'i._id': product.productsOrder }] }
              );

              // 减掉库存
              await ctx.model.ProtectiveSuppliesList.update(
                { _id: targetProduct._id },
                { $set: { 'list.$[i].data.$[j].surplus': surplus } },
                { arrayFilters: [{ 'i._id': product.productIds[0] }, { 'j._id': product.productIds[1] }] }
              );
            }
          }
        }
        if (params.type === 'receive') {
          // 更新领取状态（签字确认）
          console.log('更新领取记录，签字确认:', params._id);

          const updateData = {
            receiveDate,
            sign: params.sign,
            claimType: params.claimType,
            isReceived: true, // 标记为已领取
            receiveStatus: 'completed', // 领取状态：已完成
          };

          res = await ctx.model.ReceiveRecord.updateOne(
            { EnterpriseID, _id: params._id },
            updateData
          );

          // 记录审计日志
          ctx.auditLog(
            '防护用品签字确认',
            `记录ID: ${params._id}, 员工: ${params.employee}`,
            'info'
          );

        } else if (params.type === 'reject') {
          // 拒绝领取
          console.log('拒绝领取防护用品:', params._id);

          res = await ctx.model.ReceiveRecord.updateOne(
            { EnterpriseID, _id: params._id },
            {
              receiveDate,
              sign: params.sign,
              isRejected: true,
              receiveStatus: 'rejected',
              rejectReason: params.rejectReason || '员工拒绝领取',
            }
          );

          // 记录审计日志
          ctx.auditLog(
            '防护用品拒绝领取',
            `记录ID: ${params._id}, 员工: ${params.employee}`,
            'info'
          );
        }

      } else if (params.type === 'application') {
        // 处理防护用品申请（支持新分类系统）
        console.log('处理防护用品申请，参数:', JSON.stringify(params, null, 2));

        // 从申请数据中获取员工信息
        const firstItem = params.arr[0];
        const employeeId = firstItem.employee || ctx.session.adminUserInfo?.employeeId;
        const employeeName = firstItem.employeeName;

        if (!employeeId) {
          throw new Error('缺少员工ID信息');
        }

        // 获取员工岗位信息
        const station = await this.findEmployeeStation({ employeeId, EnterpriseID });
        const mill = {
          workspace: [], // 所有车间
          workshop: [], // 厂房
          workshopId: [], // 厂房Id
          workspaces: [], // 车间
          workspaceId: [], // 车间id
          workstation: [], // 岗位
          workstationId: [], // 岗位id
        };

        station.forEach(item => {
          if (item.workshop && item.workshopId) {
            mill.workspaces.push(`${item.workshop} > ${item.workspace} > ${item.name}`);
            mill.workshop.push(item.workshop);
            mill.workshopId.push(item.workshopId);
          } else {
            mill.workspaces.push(`${item.workspace} > ${item.name}`);
            mill.workshop.push(0);
            mill.workshopId.push(0);
          }
          mill.workspace.push(item.workspace);
          mill.workspaceId.push(item.workspaceId);
          mill.workstation.push(item.name);
          mill.workstationId.push(item._id);
        });

        const now = new Date();
        const mouth = now.getMonth() + 1;
        const year = now.getFullYear();

        // 存储所有申请记录
        const applicationProducts = [];

        // 处理申请的产品列表（适配新分类系统）
        for (const item of params.arr) {
          console.log('处理申请产品:', item);

          // 构建产品信息（新分类系统格式）
          const productInfo = {
            // 新分类系统字段
            productId: item.productId || item._id,
            categoryId: item.categoryId,
            categoryName: item.categoryName,
            categoryPath: item.categoryPath,

            // 产品基本信息
            product: item.product || item.name,
            productSpec: item.productSpec || '',
            modelNumber: item.modelNumber || '',
            materialCode: item.materialCode || '',
            number: item.number || item.num || 1,

            // 兼容旧字段（保留以防其他地方使用）
            productIds: item.productId || item._id,
            productSpecId: item.productSpecId || '',
          };

          // 创建申请记录（使用新分类系统数据结构）
          const applicationProduct = {
            EnterpriseID,
            // 岗位信息
            workshop: mill.workshop,
            workshopId: mill.workshopId,
            workspaces: mill.workspaces,
            workspace: mill.workspace,
            workspaceId: mill.workspaceId,
            workstation: mill.workstation,
            workstationId: mill.workstationId,

            // 产品信息（新分类系统格式）
            products: [ productInfo ],

            // 申请基本信息
            employee: employeeId,
            employeeName,
            applicationTime: now,
            updateAt: now,
            notes: item.notes || params.arr[0].notes || '',
            claimType: item.claimType || params.arr[0].claimType || '',

            // 时间信息
            yearNumber: year,
            mouthNumber: mouth,

            // 审批状态
            auditStatus: 0, // 初始状态：未审核
            auditLevel: 1, // 初始审批级别：一级审批
            auditRecords: [
              {
                auditLevel: 1,
                auditStatus: 0, // 0-未审核，1-通过，2-驳回
                needToAuditIds: [], // 审批人ID列表，by分支可以为空
                operator: null,
                auditTime: null,
                reason: '',
                _id: shortid.generate(),
              },
            ],

            // 仓库信息
            warehouseId: item.warehouseId || '',
          };
          if (this.config.branch === 'wh' && this.config.whPpeSecondLevelApproval === 'open') {
            // 如果是万华，则寻找一级和二级审批人，一级是HSE管理员，二级是部门经理， 每个层级只要有一个审批通过就通过
            // 查询一级和二级审批人
            const employee = await ctx.model.Employee.findOne({ _id: employeeId });
            const managerUsers = await this.whFindApprovers(employee.departs[0]);
            const firstLevelApprovers = managerUsers.firstLevelApprovers;
            const secondLevelApprovers = managerUsers.secondLevelApprovers;
            if (firstLevelApprovers.length > 0) {
              applicationProduct.auditRecords = [{
                needToAuditIds: firstLevelApprovers,
                auditLevel: 1,
                auditStatus: 0,
                reason: '',
              }];
            }
            if (secondLevelApprovers.length > 0) {
              applicationProduct.auditRecords = [ ...applicationProduct.auditRecords, {
                needToAuditIds: secondLevelApprovers,
                auditLevel: 2,
                auditStatus: 0,
                reason: '',
              }];
            }
            if (applicationProduct.auditRecords.length === 2) {
              applicationProduct.auditLevel = 2;
            }
          }
          applicationProducts.push(applicationProduct);
        }
        if (applicationProducts.length < 1) return 500;
        res = await ctx.model.ApplicationProduct.insertMany(applicationProducts);
        if (this.config.branch === 'wh' && this.config.whPpeSecondLevelApproval === 'open') {
          // 使用新的审批工作流服务来管理多级审批流程
          try {
            const workflowResult = await ctx.service.approvalWorkflow.initializeApproval(
              res,
              employeeId,
              employeeName
            );

            ctx.auditLog(
              '防护用品申请审批流程启动',
              `成功启动${workflowResult.results.length}个申请的审批流程`,
              'info'
            );
          } catch (workflowError) {
            ctx.logger.error('审批工作流启动失败', workflowError);
            // 审批流程启动失败，但申请记录已创建，记录错误但不影响主流程
            ctx.auditLog(
              '防护用品申请审批流程启动失败',
              `错误信息: ${workflowError.message}`,
              'error'
            );
          }
        }
      }
      return res;
    } catch (error) {
      console.log(error);
      return 500;
    }
  }
  async whFindApprovers(nowDingtreeId) {
    const { ctx } = this;
    const nowDingtree = await ctx.model.Dingtree.aggregate([
      // 1. 首先查找HR00003344的完整部门层级路径
      {
        $match: {
          _id: nowDingtreeId,
        },
      },
      {
        $graphLookup: {
          from: 'dingtrees',
          startWith: '$parentid',
          connectFromField: 'parentid',
          connectToField: '_id',
          as: 'ancestors',
          depthField: 'level',
        },
      },
      // 2. 将自身部门与所有祖先部门合并为一个数组，并按照从高到低排序
      {
        $project: {
          departmentPath: {
            $concatArrays: [
              [ '$_id' ],
              {
                $map: {
                  input: {
                    $sortArray: {
                      input: '$ancestors',
                      sortBy: {
                        level: 1,
                      },
                    },
                  },
                  as: 'ancestor',
                  in: '$$ancestor._id',
                },
              },

            ],
          },
        },
      },
      // 3. 展开部门路径，并添加层级信息
      {
        $unwind: {
          path: '$departmentPath',
          includeArrayIndex: 'level',
        },
      },
      // 4. 直接查找相关部门下有权限的用户
      {
        $lookup: {
          from: 'policies',
          let: {
            currentDeptId: '$departmentPath',
          },
          pipeline: [
            {
              $match: {
                $or: [
                  {
                    name: {
                      $regex: /HSE管理员|HSE职能/i,
                    },
                  },
                  {
                    name: {
                      $regex: /部门经理/i,
                    },
                  },
                ],
              },
            },
            {
              $unwind: {
                path: '$dingtree_ids',
                preserveNullAndEmptyArrays: false,
              },
            },
            // 联查这些dingtree_ids对应的部门信息，获取它们的parentid
            {
              $lookup: {
                from: 'dingtrees',
                localField: 'dingtree_ids',
                foreignField: '_id',
                as: 'linkedDepts',
              },
            },
            // 确保找到了对应的部门
            {
              $match: {
                'linkedDepts.0': { $exists: true },
              },
            },
            // 展开联查结果
            {
              $unwind: '$linkedDepts',
            },
            // 筛选出与当前部门有共同父级的部门或者直接包含当前部门的策略
            {
              $match: {
                $expr: {
                  $or: [
                    // 策略直接包含当前部门
                    { $eq: [ '$dingtree_ids', '$$currentDeptId' ] },
                    // 策略的部门与当前部门有共同的父级
                    // { $eq: [ '$linkedDepts.parentid', '$$currentDeptId' ] },
                    // 超级管理员
                    { $eq: [ '$isSuper', true ] },
                  ],
                },
              },
            },
            // 重新分组以恢复原始文档结构
            {
              $group: {
                _id: '$_id',
                name: { $first: '$name' },
                user_ids: { $first: '$user_ids' },
                dingtree_ids: { $push: '$dingtree_ids' },
                isSuper: { $first: '$isSuper' },
                linkedDeptParents: { $addToSet: '$linkedDepts.parentid' },
              },
            },
          ],
          as: 'allPolicies',
        },
      },
      {
        $unwind: {
          path: '$allPolicies',
        },
      },
      // 9. 按部门层级排序(从低到高，即从近到远)
      {
        $sort: {
          level: 1,
          'allPolicies._id': 1,
          'allPolicies.isSuper': 1, // 非超级管理员优先
        },
      },
      // 10. 分组获取HSE管理员和经理
      {
        $group: {
          _id: {
            $cond: {
              if: {
                $regexMatch: {
                  input: '$allPolicies.name',
                  regex: /HSE管理员|HSE职能/i,
                },
              },
              then: 'HSE管理员',
              else: '部门经理',
            },
          },
          firstMatch: {
            $first: '$$ROOT',
          },
        },
      },
      {
        $project: {
          _id: 0,
          policyType: '$_id',
          user_ids: '$firstMatch.allPolicies.user_ids',
          dingtree_ids: '$firstMatch.allPolicies.dingtree_ids',
          name: '$firstMatch.allPolicies.name',
          policyId: '$firstMatch.allPolicies._id',
          isSuper: '$firstMatch.allPolicies.isSuper',
        },
      },
    ]);
    console.log('防护用品查询审批人', nowDingtree);
    if (nowDingtree.length > 0) {
      // 按照一级和二级进行分组
      let firstLevelApprovers = [];
      let secondLevelApprovers = [];
      for (const item of nowDingtree) {
        if (item.policyType === 'HSE管理员') {
          firstLevelApprovers = item.user_ids;
        } else {
          secondLevelApprovers = item.user_ids;
        }
      }
      return {
        firstLevelApprovers,
        secondLevelApprovers,
      };
    }
    return {
      firstLevelApprovers: [],
      secondLevelApprovers: [],
    };
  }
  async ppeSign(params) {
    const { ctx } = this;
    try {
      if (!JSON.parse(params.defendproductsId).length) { // 没有对应的记录的id，
        const num = JSON.parse(params.num);
        const userId = params.userId;
        const employee = await ctx.model.User.findOne({ _id: userId }).populate('employeeId');
        const EnterpriseID = employee.employeeId.EnterpriseID;
        // console.log(EnterpriseID, '?????');
        const list = await ctx.model.Defendproducts.findOne({ EnterpriseID });
        const data = [];
        if (num.quantity_k) {
          data.push({
            acknowledge: true,
            studio: employee.employeeId.station,
            pname: '口罩',
            sku: '3M 9042',
            quantity: num.quantity_k,
            receiveMan: employee.employeeId.name,
            date: new Date(),
            createTime: new Date(),
            sign: params.filename,
          });
        } else if (num.quantity_e) {
          data.push({
            acknowledge: true,
            studio: employee.employeeId.station,
            pname: '耳塞',
            sku: '3M 1110',
            quantity: num.quantity_e,
            receiveMan: employee.employeeId.name,
            date: new Date(),
            createTime: new Date(),
            sign: params.filename,
          });
        }
        if (list) {
          await ctx.model.Defendproducts.updateOne({ EnterpriseID: params.EnterpriseID }, { $addToSet: { formData: { $each: data } } });
        } else {
          await ctx.model.Defendproducts.create({
            updateTime: new Date(),
            invoice: [],
            certifications: [],
            receiveForm: [],
            EnterpriseID,
            formData: data,
          });
        }
      } else {
        await ctx.model.Defendproducts.updateOne({ EnterpriseID: params.EnterpriseID }, { $set: { 'formData.$[i].sign': params.filename, 'formData.$[i].acknowledge': true } }, {
          arrayFilters: [{
            'i._id': { $in: JSON.parse(params.defendproductsId) },
          }],
        });
      }
      // console.log(res, '返回的数据 ');
    } catch (error) {
      console.log(error);
      return 500;
    }

  }

  // 获取转岗信息，从企业端移植过来的
  async getStationChange(params) {
    try {
      const { ctx } = this;
      const { EnterpriseID, employeeId } = params;
      let stationFrom = {};
      let deleteStationFrom = true;
      let deleteStationTo = true;
      const res = await ctx.model.EmployeeStatusChange.aggregate([
        { $match: { employee: employeeId } },
        { $unwind: '$statusChanges' },
        { $match: { 'statusChanges.EnterpriseID': EnterpriseID } },
        { $group: { _id: '$_id', statusChanges: { $push: '$statusChanges' } } },
      ]);
      const employee = await ctx.model.Employee.findOne({ _id: employeeId }, { name: 1 });
      let station = {};
      if (res[0]) {
        res[0].employee = employee;
        for (let i = 0; i < res[0].statusChanges.length; i++) {
          const EnterpriseID = res[0].statusChanges[i].EnterpriseID;
          deleteStationFrom = true;
          deleteStationTo = true;
          res[0].statusChanges[i].EnterpriseID = (await ctx.model.Adminorg.findOne({ _id: res[0].statusChanges[i].EnterpriseID })).cname;
          // 获取岗位信息
          if (res[0].statusChanges[i].stationsTo.length > 0) {
            for (let k = 0; k < res[0].statusChanges[i].stationsTo.length; k++) {
              station = await this.getStationInfo(res[0].statusChanges[i].stationsTo[k], EnterpriseID);
              if (station) {
                res[0].statusChanges[i].stationsTo[k] = station;
                deleteStationTo = false;
              } else {
                res[0].statusChanges[i].stationsTo[k] = '该岗位已被删除';
              }
              // res[0].statusChanges[i].stationsTo[k] = station || '该岗位已删除';
            }
          }
          if (res[0].statusChanges[i].stationFrom) {
            stationFrom = await this.getStationInfo(res[0].statusChanges[i].stationFrom, EnterpriseID);
            if (stationFrom) {
              deleteStationFrom = false;
              res[0].statusChanges[i].stationFrom = stationFrom;
            } else {
              res[0].statusChanges[i].stationFrom = '该岗位已被删除';
            }
            // res[0].statusChanges[i].stationFrom = (await this.getStationInfo(res[0].statusChanges[i].stationFrom, EnterpriseID)) || '该岗位已删除';
          }
          if (res[0].statusChanges[i].changType === 2 && deleteStationFrom && deleteStationTo) {
            // 如果转入岗位和转出岗位都为空 那么删除这条记录
            // await ctx.model.EmployeeStatusChange.updateOne({ employee: params2.employee }, {
            // $pull: {
            // statusChanges: { _id: res[0].statusChanges[i]._id },
            // },
            // });
            await ctx.service.db.updateOne('EmployeeStatusChange', { employee: employeeId }, {
              $pull: {
                statusChanges: { _id: res[0].statusChanges[i]._id },
              },
            });
          }

          // 获取部门信息
          if (res[0].statusChanges[i].departsTo.length > 0) {
            for (let j = 0; j < res[0].statusChanges[i].departsTo.length; j++) {

              for (let k = 0; k < res[0].statusChanges[i].departsTo[j].length; k++) {
                const depart = await ctx.model.Dingtree.findOne({ _id: res[0].statusChanges[i].departsTo[j][k] });
                res[0].statusChanges[i].departsTo[j][k] = depart ? depart.name : '';
                if (depart) {
                  res[0].statusChanges[i].departsTo[j][k] = depart.name;
                  // deleteDepartTo = false
                } else {
                  res[0].statusChanges[i].departsTo[j][k] = '';
                }
              }
            }
            // departTo = res[0].statusChanges[i].departsTo[0];
          }
          if (res[0].statusChanges[i].departFrom.length > 0) {
            for (let j = 0; j < res[0].statusChanges[i].departFrom.length; j++) {
              for (let k = 0; k < res[0].statusChanges[i].departFrom[j].length; k++) {
                const depart = await ctx.model.Dingtree.findOne({ _id: res[0].statusChanges[i].departFrom[j][k] });
                if (depart) {
                  res[0].statusChanges[i].departFrom[j][k] = depart.name;
                  // deleteDepartFrom = false
                } else {
                  res[0].statusChanges[i].departFrom[j][k] = '';
                }
              }
            }
            // departFrom = res[0].statusChanges[i].departFrom[0];
          }
          // if (res[0].statusChanges[i].changType === 3 && !departFrom || departFrom.filter(item => !item)[0] && !departTo || departTo.filter(item => !item)[0]) {
          //   // 如果转入部门和转出部门都为空，那删除这条记录
          //   await ctx.model.employeeStatusChange.update({ employee: params2.employee }, { $pull: { statusChanges: { _id: res[0].statusChanges[i] } } });
          // }
        }
      }
      return res;
    } catch (error) {
      console.log(error);
      return 500;
    }
  }

  // 获取岗位信息
  async getStationInfo(stationId, EnterpriseID) {
    const mills = await this.ctx.model.MillConstruction.aggregate([
      {
        $match: {
          EnterpriseID,
        },
      },
      {
        $unwind: '$children',
      },
      {
        $match: {
          $or: [{ 'children._id': stationId }, { 'children.children._id': stationId }],
        },
      },
    ]);
    if (mills.length > 0) {
      let stationName = '';
      if (mills[0].category === 'mill') {
        mills[0].children.children.forEach(item => {
          if (item._id === stationId) {
            stationName = item.name;
          }
        });
        stationId = `${mills[0].name}/${mills[0].children.name}/${stationName}`;
      } else {
        stationId = `${mills[0].name}/${mills[0].children.name}`;
      }
    } else {
      stationId = '';
    }
    return stationId;
  }

  // 获取消息通知
  async getMessageNotice(payload) {
    const { ctx } = this;
    const { pageSize = 10, pageNum = 1, keywords = [] } = payload;
    const skip = (pageNum - 1) * pageSize;
    const match = {
      reader: {
        $elemMatch: {
          readerID: payload.userId,
        },
      },
      state: 1,
    };
    if (keywords.length > 0) {
      match.keywords = { $all: keywords };
    }
    if (payload.isRead !== null && !isNaN(payload.isRead)) {
      match.reader.$elemMatch.isRead = Number(payload.isRead);
    }
    console.log('🚀那个啥payload', payload, payload.isRead);
    if (payload.isRead !== null && !isNaN(payload.isRead)) {
      match['reader.isRead'] = Number(payload.isRead);
    }
    console.log('🚀那个啥match', match, payload.isRead != null, !isNaN(payload.isRead));
    const res = await ctx.model.MessageNotification.aggregate([
      {
        $match: match,
      },
      {
        $unwind: '$reader',
      },
      {
        $match: {
          'reader.readerID': payload.userId,
        },
      },
      {
        $sort: {
          date: -1,
        },
      },
      {
        $facet: {
          list: [
            { $skip: skip },
            { $limit: pageSize },
          ],
          total: [
            {
              $count: 'total',
            },
          ],
        },
      },
      {
        $addFields: {
          'pageInfo.total': { $arrayElemAt: [ '$total.total', 0 ] },
          'pageInfo.pageSize': pageSize,
          'pageInfo.pageNum': pageNum,
        },
      },
      {
        $project: {
          list: 1,
          pageInfo: 1,
        },
      },
    ]);
    if (res) {
      const upload_http_path = this.config.upload_http_path;
      const docs = res[0].list.map(ele => ({
        ...ele,
        files: ele.files ? ele.files.map(fileName => ({
          url: `/static${upload_http_path}/messageNotification/${ele._id}/${fileName}`,
          name: fileName,
        })) : undefined,
      }));
      res[0].list = docs;
    }
    return res[0] || { list: [], pageInfo: { pageSize, pageNum, total: 0 } };
  }
  // 确认消息通海将消息标记为已读
  async confirmMessageNotice(payload) {
    const { ctx } = this;
    const { messageIds, userId } = payload;
    // 使用updateMany一次性更新所有匹配的消息
    const result = await ctx.model.MessageNotification.updateMany(
      { _id: { $in: messageIds }, 'reader.readerID': userId },
      { $set: { 'reader.$.isRead': 1 } }
    );
    return {
      success: true,
      totalUpdated: result.nModified || 0,
    };
  }
  // 获取违章通知
  async getViolationNotice(payload) {
    const res = await this.ctx.model.ViolationInfo.findById({
      _id: payload.id,
    }).lean();
    const enterprise = await this.ctx.model.Adminorg.findOne({
      _id: res.punishmentUnit,
    }).lean();
    res.cname = enterprise.cname;
    return res;
  }
  // 获取劳动者是否可以编辑个人职业史
  async getLaborIsEdit(params) {
    console.log(params, 'params');
    const res = await this.ctx.model.Adminorg.find({ _id: params.companyId }).select('lobarIsEdit');
    return res;
  }
  async getHistoryList(params) {
    const res = await this.ctx.model.OccupationalHistory.find({ employeeId: params.userId });
    return res;
  }
  async addHistoryList(params) {
    const res = await this.ctx.model.OccupationalHistory.create(params);
    return res;
  }
  async deleteHistoryList(params) {
    const res = await this.ctx.model.OccupationalHistory.deleteOne({ _id: params._id });
    return res;
  }
  async editHistoryList(params) {
    const res = await this.ctx.model.OccupationalHistory.findByIdAndUpdate({ _id: params._id }, params.newData);
    return res;
  }
  // 新的配发标准查询方法（使用扁平化结构）
  async findProtectionPlanNew(employeeId, EnterpriseID, category) {
    const { ctx } = this;
    console.time('findProtectionPlanNew');

    // 获取员工岗位信息（使用扁平化结构）
    const employeePositions = await this.getEmployeePositions(employeeId, EnterpriseID);

    if (!employeePositions || employeePositions.length === 0) {
      console.timeEnd('findProtectionPlanNew');
      return [];
    }

    // 提取所有岗位的fullId
    const positionFullIds = employeePositions.map(pos => pos.fullId);

    // 构建类别匹配条件
    let categoryMatch = {};
    if (category && ctx.app.config.branch === 'wh') {
      categoryMatch = {
        $or: [
          { category },
          { category: { $exists: false } },
          { category: '' },
        ],
      };
    }

    // 查询配发标准（使用新的nodeFullId字段）
    const protectionPlans = await ctx.model.ProtectionPlan.find({
      EnterpriseID,
      nodeFullId: { $in: positionFullIds },
      planStatus: 1, // 启用状态
      configStatus: { $ne: 'no_need' }, // 排除无需配置的
      ...categoryMatch,
    }).lean();

    // 获取员工部门的配发标准
    const employee = await ctx.model.Employee.findById(employeeId).select('departs');
    if (employee && employee.departs && employee.departs.length > 0) {
      const departmentPlans = await ctx.model.ProtectionPlan.find({
        EnterpriseID,
        grantType: 'depart',
        departId: { $in: employee.departs },
        planStatus: 1,
        configStatus: { $ne: 'no_need' },
        ...categoryMatch,
      }).lean();

      // 合并部门配发标准，避免重复
      departmentPlans.forEach(plan => {
        const exists = protectionPlans.find(p => p._id === plan._id);
        if (!exists) {
          protectionPlans.push(plan);
        }
      });
    }

    console.timeEnd('findProtectionPlanNew');
    return protectionPlans;
  }

  // 保留原有方法以兼容旧代码
  async findProtectionPlan(employeeId, EnterpriseID, category) {
    // 调用新方法
    return await this.findProtectionPlanNew(employeeId, EnterpriseID, category);
  }

  // 获取防护用品分类数据（适配新的产品模型）
  async getProtectiveProductCategories(EnterpriseID, warehouseId) {
    const { ctx } = this;

    try {
      console.log(`[产品查询] 查询仓库 ${warehouseId} 的防护用品`);
      // 查询该仓库的所有防护用品
      const products = await ctx.model.ProtectiveProduct.find({
        EnterpriseID,
        warehouseId,
        isActive: true,
        surplus: { $gt: 0 }, // 只返回有库存的产品
      }).populate('categoryId').lean();

      console.log(`[产品查询] 找到 ${products.length} 个有库存的产品`);

      // 按分类组织数据
      const categoryMap = new Map();

      products.forEach(product => {
        const categoryId = product.categoryId?._id || 'uncategorized';
        const categoryName = product.categoryId?.name || '未分类';
        const categoryPath = product.categoryPath || categoryName;

        if (!categoryMap.has(categoryId)) {
          categoryMap.set(categoryId, {
            _id: categoryId,
            name: categoryName,
            categoryPath,
            tableHeader: this.getCategoryIcon(categoryName),
            data: [],
          });
        }

        const category = categoryMap.get(categoryId);
        category.data.push({
          _id: product._id,
          product: product.product,
          productSpec: product.productSpec,
          modelNumber: product.modelNumber,
          surplus: product.surplus,
          materialCode: product.materialCode,
          picture: product.picture,
          // 保持与旧结构兼容的字段
          selectedNum: 0,
        });
      });

      // 转换为数组格式
      const list = Array.from(categoryMap.values());

      return {
        list,
        EnterpriseID,
      };
    } catch (error) {
      console.error('获取防护用品分类数据失败:', error);
      // 如果新模型查询失败，回退到旧模型
      return await this.getProtectiveProductCategoriesLegacy(EnterpriseID);
    }
  }

  // 获取分类图标
  getCategoryIcon(categoryName) {
    const iconMap = {
      头部防护: 'static/images/protection/头部防护.png',
      呼吸防护: 'static/images/protection/呼吸防护.png',
      坠落防护: 'static/images/protection/坠落防护.png',
      听力防护: 'static/images/protection/听力防护.png',
      眼面部防护: 'static/images/protection/眼面部防护.png',
      手部防护: 'static/images/protection/手部防护.png',
      足部防护: 'static/images/protection/足部防护.png',
      防护服装: 'static/images/protection/防护服装.png',
      防护手套: 'static/images/protection/防护手套.png',
      其他防护: 'static/images/protection/其他防护.png',
    };

    return iconMap[categoryName] || 'static/images/none.png';
  }

  // 旧版本的分类数据获取（兼容性）
  async getProtectiveProductCategoriesLegacy(EnterpriseID) {
    const { ctx } = this;

    const typeList = await ctx.model.ProtectiveSuppliesList.findOne({
      EnterpriseID,
    });

    if (!typeList) {
      return { list: [] };
    }

    typeList.list.forEach(item => {
      item.tableHeader = this.getCategoryIcon(item.name);
    });

    return typeList;
  }
  async updateNewReceiveRecord(plan, product) {
    const { ctx } = this;

    try {
      const newPlan = _.cloneDeep(plan);
      newPlan.planId = newPlan._id;
      delete newPlan._id;

      // 直接使用配发标准中的分类信息
      newPlan.products = [{
        ...product,
        // 保持原有的分类信息（配发标准中已经包含）
        categoryId: product.categoryId || '',
        categoryPath: product.categoryPath || '',
        categoryName: product.categoryName || '',
      }];

      // 同时在记录级别也设置分类信息
      newPlan.categoryId = product.categoryId || '';
      newPlan.categoryPath = product.categoryPath || '';
      newPlan.categoryName = product.categoryName || '';

      newPlan.receiveStartDate = new Date();
      newPlan.recordSource = 0;

      if (newPlan.grantType === 'mill') {
        if (!newPlan.workstation) {
          newPlan.workstation = plan.subRegion[plan.subRegion.length - 1];
        }
      } else if (newPlan.grantType === 'depart') {
        if (!newPlan.departId) {
          newPlan.departId = plan.subRegion[plan.subRegion.length - 1];
        }
      }

      const doc = await new ctx.model.ReceiveRecord(newPlan).save();
      return doc;
    } catch (error) {
      ctx.logger.error('按需生成领用记录失败', error);
      throw error;
    }
  }


}

module.exports = AdminuserService;
