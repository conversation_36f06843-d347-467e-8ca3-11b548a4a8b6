const adminuserApiController = require('../controller/api/adminuser');
const adminuserAdminController = require('../controller/manage/adminuser');

module.exports = (options, app) => {

  return async function adminuserRouter(ctx, next) {

    const pluginConfig = app.config.jk_adminuser;
    await app.initPluginRouter(ctx, pluginConfig, adminuserAdminController, adminuserApiController);
    await next();

  };

};
