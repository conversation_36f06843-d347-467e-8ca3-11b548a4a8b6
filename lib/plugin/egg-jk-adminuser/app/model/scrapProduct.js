/*
 * @Author: 曹嘉宏
 * @Date: 2023-10-21
 * @LastEditors: 曹嘉宏
 * @LastEditTime: 2023-10-21
 * @Description: 报废防护用品
 */

module.exports = app => {
  const shortid = require('shortid');
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;

  const scrapProductSchema = new Schema({
    _id: { // 机器id
      type: String,
      default: shortid.generate,
    },
    EnterpriseID: { // 企业id
      type: String,
      ref: 'Adminorg',
    },
    workshop: { // 厂房名称
      type: String,
    },
    workspaces: { // 车间名称
      type: String,
    },
    workstation: { // 岗位名称
      type: String,
    },
    products: [{ // 产品列表id
      _id: {
        type: String,
        default: shortid.generate,
      },
      productIds: [ String ],
      product: String, // 产品名称
      modelNumber: String, // 型号
      number: Number, // 数量
    }],
    status: { // 状态：0 未审核；1 已报废；2 暂定
      type: Number,
      default: 0,
    },
    employee: { // 申请报废人
      type: String,
    },
    operator: { // 操作人
      _id: String, // 操作人ID
      name: String, // 操作人名称
    },
    applicationTime: { // 申请报废时间
      type: Date,
    },
    yearNumber: { // 年份
      type: Number,
    },
    mouthNumber: { // 月份
      type: Number,
    },
    scrapTime: { // 报废时间
      type: Date,
    },
    scrapReason: { // 报废原因
      type: String,
    },
    sign: { // 签字
      type: String,
    },
  }, { timestamps: true });
  return mongoose.model('ScrapProduct', scrapProductSchema, 'scrapProduct');
};
