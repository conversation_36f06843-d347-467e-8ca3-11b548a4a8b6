// lht 各企业的防护用品发放计划
module.exports = (app) => {
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;
  const shortid = require('shortid');
  const protectionPlanSchema = new Schema(
    {
      _id: {
        type: String,
        default: shortid.generate,
      },
      EnterpriseID: { type: String },
      // 新的 fullId 存储方式
      nodeFullId: String, // 完整路径: "mill_id/workspace_id/station_id"
      nodeLevel: String, // 节点级别: "workspaces" | "stations"
      nodeName: String, // 节点名称
      employee: String, // 员工id
      products: [
        {
          // 产品列表id
          _id: {
            type: String,
            default: shortid.generate,
          },
          // 新增分类关联字段
          categoryId: {
            type: String,
            ref: 'protectionCategory',
          }, // 关联品类ID
          categoryPath: String, // 品类路径
          categoryName: String, // 品类名称

          productIds: [String],
          productType: [String], // 用品分类 (保留原有字段以兼容)
          product: String, // 产品名称
          modelNumber: String, // 型号
          number: Number, // 数量
          maxNumber: Number, // 首次发放最大允许量(只针对WH)
          time: Number, // 周期
          timeUnit: String, // 周期单位，day week month year
        },
      ],
      startDate: Date, // 计划开始时间
      planStatus: {
        type: Number,
        default: 1,
      }, // 1 启用 2 暂停
      grantType: String, // mill 车间发放标准 depart 部门发放标准
      departId: String, // 部门id - 发放标准为部门时存在
      departName: String, // 部门名称 - 发放标准为部门时存在
      subRegion: [], // 子区域
      category: String, // Wh 人员分类 1 生产岗位 2 生产类职能岗 3 非生产类职能岗 4 工程管理岗 5 其他
      configStatus: {
        type: String,
        enum: ['configured', 'unconfigured', 'no_need'],
        default: 'configured', // 已有数据默认为已配置
        // configured: 已配置
        // unconfigured: 未配置
        // no_need: 无需配置
      },
    },
    {
      timestamps: true,
    }
  );
  return mongoose.model('protectionPlan', protectionPlanSchema);
};
