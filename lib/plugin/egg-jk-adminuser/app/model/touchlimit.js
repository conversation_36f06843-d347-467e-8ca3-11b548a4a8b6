module.exports = app => {
  // 化学接触限值
  const conn = app.mongoose.createConnection(app.config.mongoose.tools.url, { useNewUrlParser: true, useUnifiedTopology: true });
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;
  const shortid = require('shortid');

  const touchLimit = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    initial: String, // 首字母
    chineseName: String, // 中文名
    englishName: String, // 英文名
    casNum: String, // 化学文摘号CAS 号
    OELs: {
      MAC: String,
      PC_TWA: String,
      PC_STEL: String,
    },
    healthEffect: String, // 临界不良健康效应
    note: String, // 备注
    protectiveEquipment: { type: String }, // 个人防护用品

  });
  return conn.model('TouchLimits', touchLimit, 'touchLimits');
};
