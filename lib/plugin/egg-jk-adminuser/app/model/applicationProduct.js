/*
 * @Author: 曹嘉宏
 * @Date: 2023-10-27
 * @LastEditors: 曹嘉宏
 * @LastEditTime: 2023-10-27
 * @Description: 防护用品申请数据表
 *
 */
module.exports = app => {
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;
  const shortid = require('shortid');
  const applicationProductSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    EnterpriseID: { type: String },
    workspaces: Array, // 车间
    workshop: Array, // 厂房
    workshopId: Array, // 厂房Id
    workspace: Array, // 车间
    workspaceId: Array, // 车间id
    workstation: Array, // 岗位
    workstationId: Array, // 岗位id
    employee: String, // 申请人员工id
    operator: { // 操作人
      _id: String, // 操作人ID
      name: String, // 操作人名称
    },
    products: [{ // 产品列表id
      _id: {
        type: String,
        default: shortid.generate,
      },
      productIds: String, // 产品id
      product: String, // 产品名称
      number: Number, // 数量
      productSpec: String, // 产品规格
      productSpecId: String, // 产品规格id
    }],
    applicationTime: Date, // 创建申请日期
    updateAt: Date, // 更新申请时间
    auditStatus: {
      // 审核状态 0 未审核 1 一级审核通过 2 被驳回 3 二级审核通过
      type: Number,
      default: 0,
    },
    sign: String, // 签名图片
    notes: String, // 备注
    reason: String, // 驳回理由
    yearNumber: Number, // 年份
    mouthNumber: Number, // 月份
    claimType: String, // 申请类型
    auditLevel: {
      // 审核级别 1-一级审核 2-二级审核
      type: Number,
      default: 1, // 默认为一级审核
    },
    auditRecords: [
      {
        auditLevel: Number, // 审批级别：1-一级审批，2-二级审批
        auditStatus: Number, // 审批结果：0-未审核，1-通过，2-驳回
        needToAuditIds: Array, // 需要审批的人的ID
        operator: {
          // 审批人
          _id: String, // 审批人ID
          name: String, // 审批人名称
        },
        auditTime: Date, // 审批时间
        reason: String, // 审批意见/驳回理由
        _id: {
          type: String,
          default: shortid.generate,
        },
      },
    ],
  }, { timestamps: true });
  return mongoose.model('applicationProduct', applicationProductSchema);
};
