// 临安小游戏用户
module.exports = app => {
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;
  const shortid = require('shortid');

  const GameUserSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    wxUnionId: { // 微信unionId
      type: String,
      required: true,
    },
    name: { // 姓名
      type: String,
      trim: true,
    },
    gender: { // 性别
      type: String,
      enum: [ 'male', 'female' ],
    },
    phoneNum: { // 手机号
      type: String,
      trim: true,
    },
    countryCode: { // 手机号前国家代码
      type: String,
      default: '86',
    },
    gameEventId: { // 活动id
      type: String,
      required: true,
      ref: 'GameEvents',
      index: true,
    },
    Enterprise: { // 所属企业id
      type: String,
      ref: 'GameEnterprise',
      index: true,
    },
    // 奖品收货人信息，仅在中奖后才需要填写
    winnerInfo: {
      _id: { type: String, default: shortid.generate },
      EnterpriseName: { type: String, trim: true }, // 企业名称
      name: { type: String, trim: true }, // 收货人姓名
      phoneNum: { type: String, trim: true }, // 手机号
      phoneCarrier: { type: String, trim: true }, // 手机运营商 '移动', '联通', '电信', '其他'
      countryCode: { type: String, default: '86' },
      address: { type: String, trim: true }, // 收货地址
      idType: { type: String, enum: [ '身份证', '护照', '港澳通行证', '台湾通行证', '军官证', '其他' ], default: '身份证' }, // 证件类型
      idCard: { type: String, trim: true }, // 证件号码
    },
    status: { // 用户状态 0：黑名单 1：正常
      type: Number,
      enum: [ 0, 1 ],
      default: 1,
    },
  }, { timestamps: true });

  GameUserSchema.index({ gameEventId: 1, wxUnionId: 1 }, { unique: true });

  return mongoose.model('GameUser', GameUserSchema, 'gameUser');
};
