// 游戏记录存档 xxn 2024-8-30
module.exports = app => {
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;
  const shortid = require('shortid');

  const GameRecordsSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    gameEventId: { // 活动id
      type: String,
      required: true,
      ref: 'GameEvents',
      index: true,
    },
    Enterprise: { // 所属企业id
      type: String,
      ref: 'GameEnterprise',
      required: true,
      index: true,
    },
    userId: { // 用户id
      type: String,
      required: true,
      ref: 'GameUser',
      index: true,
    },
    testPaper: [{ // 试卷，题目ids
      type: String,
      ref: 'Topic',
    }],
    curStep: { // 当前答题进度
      type: Number,
      default: 0,
    },
    curScore: { // 当前总积分
      type: Number,
      default: 0,
    },
    status: { // 答题状态，满足活动设置的可抽奖题数就算完成
      type: Number,
      default: 0,
      enum: [ 0, 1, 2 ], // 0未开始 1未完成 2已完成
    },
    drawTime: { // 抽奖时间（必须是status：2），有抽奖时间但是没有prizeId，表示没有抽中奖品
      type: Date,
    },
    prizeId: { // 奖品id
      type: String,
      ref: 'Prizes',
    },
  }, { timestamps: true });

  return mongoose.model('GameRecords', GameRecordsSchema, 'gameRecords');
};
