// 答题记录存档 xxn 2025-4-27
module.exports = app => {
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;
  const shortid = require('shortid');

  const AnswerRecordsSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    gameRecordId: {
      type: String,
      required: true,
      ref: 'GameRecords',
      index: true,
    },
    topicId: { // 题目id
      type: String,
      required: true,
      ref: 'Topic',
    },
    answers: [ String ], // 用户答案
    isRight: { // 是否正确
      type: Boolean,
      default: false,
    },
    score: { // 获得的积分
      type: Number,
      default: 0,
    },
    createdAt: { // 答题时间戳
      type: Number,
    },
  });

  return mongoose.model('AnswerRecords', AnswerRecordsSchema, 'answerRecords');
};
