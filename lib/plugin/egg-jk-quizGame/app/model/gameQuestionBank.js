module.exports = app => {
  const shortid = require('shortid');
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;

  // 小程序题库
  const GameQuestionBankSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    name: String, // 题库名称
    count: { // 题目总数
      type: Number,
      default: 0,
    },
    singleTopic: { // 单选题数量
      type: Number,
      default: 0,
    },
    multipleTopic: { // 多选题数量
      type: Number,
      default: 0,
    },
    judgeTopic: { // 判断题数量
      type: Number,
      default: 0,
    },
    createAt: { // 创建日期
      type: Date,
    },
    updateAt: { // 更新时间
      type: Date,
    },
    source: { // 数据来源
      type: String,
      default: 'operate',
      enum: [ 'user', 'operate', 'org', 'super' ], // user代表专家 operate是运营人员 org代表培训机构/组织
    },
    authorID: {
      type: String,
      ref: 'OperateUser',
      default: '',
    }, // 创建者ID
  }, { timestamps: true });


  return mongoose.model('GameQuestionBank', GameQuestionBankSchema, 'gameQuestionBank');
};
