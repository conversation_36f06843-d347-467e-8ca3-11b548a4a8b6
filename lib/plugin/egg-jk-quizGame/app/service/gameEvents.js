const Service = require('egg').Service;

class GameEventsService extends Service {
  async getOne(query = {}) {
    query.enable = true;
    let data = await this.ctx.model.GameEvents.findOne(query);
    if (!data) return null;
    data = JSON.parse(JSON.stringify(data));
    data.status = '0'; // 未开始
    const now = new Date().getTime(),
      startTime = new Date(data.startTime).getTime(),
      endTime = new Date(data.endTime).getTime();
    if (now >= startTime && now <= endTime) {
      data.status = '1'; // 进行中
    } else if (now > endTime) {
      data.status = '2'; // 已结束
    }
    return data;
  }
  async getOne2(query = {}, select = {}) {
    query.enable = true;
    return await this.ctx.model.GameEvents.findOne(query, select);
  }

}
module.exports = GameEventsService;
