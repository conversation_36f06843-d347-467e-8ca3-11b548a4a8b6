const Service = require('egg').Service;
class GameRecordsService extends Service {
  // 组卷，获取试卷
  async getTestPaper(requiredQuestionBank, optionalQuestionBank = [], totalTopicNum) {
    const { app } = this;

    if (totalTopicNum === 0) throw new Error('游戏总题数为0，请联系管理员先上传题库。');

    // 1. 缓存必选题库的题目
    const cacheKeyRequired = `requiredTopics:${requiredQuestionBank}`;
    let requiredTopics = await app.redis.get(cacheKeyRequired);
    if (!requiredTopics) {
      requiredTopics = await this.getTopicsByBankId(requiredQuestionBank);
      await app.redis.set(cacheKeyRequired, JSON.stringify(requiredTopics), 'EX', 300); // 缓存 300 秒
    } else {
      requiredTopics = JSON.parse(requiredTopics);
    }

    // 2. 并行获取随机题库的题目
    const randomTopicsPromises = optionalQuestionBank.map(async item => {
      const { _id: questionBankId, num } = item;
      const cacheKeyRandom = `randomTopics:${questionBankId}`;
      let topics = await app.redis.get(cacheKeyRandom);
      if (!topics) {
        topics = await this.getTopicsByBankId(questionBankId);
        await app.redis.set(cacheKeyRandom, JSON.stringify(topics), 'EX', 300); // 缓存 300 秒
      } else {
        topics = JSON.parse(topics);
      }

      if (topics.length > 0) {
        const randomNum = Math.min(num, topics.length);
        return topics.sort(() => Math.random() - 0.5).slice(0, randomNum);
      }
      return [];
    });

    const randomTopics = (await Promise.all(randomTopicsPromises)).flat();

    // 3. 如果必选题库和随机题库都没有题目，则抛出错误
    if (requiredTopics.length + randomTopics.length === 0) {
      throw new Error('题库中没有题目，请联系管理员先上传题库。');
    }

    // 4. 随机抽取随机题库中的题目
    const randomNum = Math.max(totalTopicNum - requiredTopics.length, 0);
    const randomTopicsList = randomTopics.sort(() => Math.random() - 0.5).slice(0, randomNum);

    // 5. 组合题目
    const testPaper = [ ...requiredTopics, ...randomTopicsList ];

    // 6. 打乱题目顺序
    return testPaper.sort(() => Math.random() - 0.5);
  }
  async getTestPaper2(requiredQuestionBank, optionalQuestionBank = [], totalTopicNum) {
    if (totalTopicNum === 0) throw new Error('游戏总题数为0，请联系管理员先上传题库。');
    // 1、获取必选题库的题目
    const requiredTopics = await this.getTopicsByBankId(requiredQuestionBank);
    // 2、获取随机题库的题目
    const randomTopics = [];
    for (const item of optionalQuestionBank) {
      const { _id: questionBankId, num } = item;
      const topics = await this.getTopicsByBankId(questionBankId);
      if (topics.length > 0) {
        const randomNum = Math.min(num, topics.length);
        const randomTopicsList = topics.sort(() => Math.random() - 0.5).slice(0, randomNum);
        randomTopics.push(...randomTopicsList);
      }
    }
    // 3、如果必选题库和随机题库都没有题目，则抛出错误
    if (requiredTopics.length + randomTopics.length === 0) {
      throw new Error('题库中没有题目，请联系管理员先上传题库。');
    }
    // 4、随机抽取随机题库中的题目
    const randomNum = Math.max(totalTopicNum - requiredTopics.length, 0);
    const randomTopicsList = randomTopics.sort(() => Math.random() - 0.5).slice(0, randomNum);
    // 5、组合题目
    const testPaper = [ ...requiredTopics, ...randomTopicsList ];
    // 6、打乱题目顺序
    const shuffledTestPaper = testPaper.sort(() => Math.random() - 0.5);
    return shuffledTestPaper;
  }

  // 根据题库id查询题目
  async getTopicsByBankId(questionBankId) {
    if (!questionBankId) return [];
    const { ctx } = this;
    const topicData = { topicType: 1, steam: 1, options: 1, difficultyLevel: 1, steamPic: 1 };
    const topics = await ctx.model.Topic.find({ questionBankID: questionBankId }, topicData);
    return topics;
  }

  // 创建游戏记录
  async createGameRecord(payload) {
    const { ctx } = this;
    const { userId, testPaper } = payload;
    return ctx.model.GameRecords.create({ userId, testPaper, Enterprise: payload.Enterprise || '', gameEventId: payload.gameEventId || '' });
  }

  // 获取当天已有的游戏记录数量
  async getTodayGameRecordCount(userId) {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const count = await this.ctx.model.GameRecords.countDocuments({ userId, createdAt: { $gte: today } });
    return count;
  }

  // 用户行为分析，防止作弊
  async behaviorAnalysis(userId, gameRecordId) {
    const { ctx, app } = this;
    const { disable, maxRecords, stdDevThreshold } = app.config.fzxyxBehaviorAnalysis;
    if (disable) return;

    const answerRecords = await ctx.model.AnswerRecords.find({ gameRecordId }, { createdAt: 1 }).sort({ createdAt: -1 }).limit(maxRecords);
    if (answerRecords.length < 3) return; // 如果没有足够的记录，则不进行分析
    const timeList = answerRecords.map(record => record.createdAt);
    const timeDifferences = [];
    // let minIntervalAbnormal = false;

    for (let i = 1; i < timeList.length; i++) {
      const difTime = timeList[i - 1] - timeList[i];
      // if (difTime < minInterval) minIntervalAbnormal = true;
      timeDifferences.push(difTime);
    }
    // if (minIntervalAbnormal) {
    //   ctx.auditLog('用户行为分析异常', `userId: ${userId} - 时间间隔小于${minInterval}毫秒`, 'error');
    //   await ctx.model.GameUser.updateOne(
    //     { _id: userId },
    //     { status: 0 }
    //   );
    //   return;
    // }
    // 计算平均时间差
    const averageTimeDifference = timeDifferences.reduce((sum, difference) => sum + difference, 0) / timeDifferences.length;
    if (averageTimeDifference < stdDevThreshold) {
      ctx.auditLog('用户行为分析异常', `userId: ${userId} - 平时间差小于${stdDevThreshold}毫秒`, 'error');
      await ctx.model.GameUser.updateOne(
        { _id: userId },
        { status: 0 }
      );
    }
  }

  // 更新游戏记录
  async updateGameRecord2(gameRecordId, topicId, answers = []) {
    const { ctx, app } = this;

    // 1. 获取游戏记录
    const gameRecord = await ctx.model.GameRecords.findById(gameRecordId, { gameEventId: 1, curScore: 1, curStep: 1, totalTopicNum: 1, userId: 1, testPaper: 1 }).populate('userId', { status: 1 });
    if (!gameRecord) throw new Error('未找到游戏记录: ' + gameRecordId);
    const userId = gameRecord.userId._id;
    if (gameRecord.userId.status === 0) {
      throw new Error('检测到异常行为，您的账户已被限制，请联系管理员。');
    }
    if (!gameRecord.testPaper.includes(topicId)) {
      ctx.auditLog('updateGameRecord异常', `用户${userId}使用非法手段答题，gameRecordId - ${gameRecordId}`, 'error');
      throw new Error('警告：该题目不在您的游戏中，请勿使用非法手段答题。');
    }

    // 2. 异常数据排查
    // if (newStep > gameRecord.totalTopicNum) {
    //   ctx.auditLog('gameRecord异常', `gameRecordId - ${gameRecordId}的答题数据异常(newStep: ${newStep})，topicId：${topicId}`, 'error');
    //   throw new Error('您的答题进度异常，请联系管理员。');
    // }
    // const maxScore = Math.max(...Object.values(topicScore));
    // if (curScore > gameRecord.totalTopicNum * maxScore) {
    //   ctx.auditLog('gameRecord异常', `gameRecordId - ${gameRecordId}的答题数据异常(curScore: ${curScore})，topicId：${topicId}`, 'error');
    //   throw new Error('您的答题积分异常，请联系管理员。');
    // }
    const hasAnswer = await ctx.model.AnswerRecords.findOne({ gameRecordId, topicId });
    if (hasAnswer) {
      throw new Error('您已答过此题，请勿重复答题。');
    }

    // 3. 从缓存中获取活动信息
    const { gameEventId } = gameRecord;
    const cacheKeyGameEvent = `gameEvent2:${gameEventId}`;
    let gameEvent = await app.redis.get(cacheKeyGameEvent);
    if (!gameEvent) {
      gameEvent = await ctx.service.gameEvents.getOne2({ _id: gameEventId }, { getPrizeChanceNum: 1, topicScore: 1 });
      if (!gameEvent) throw new Error('未找到用户活动信息, gameEventId:' + gameEventId);
      await app.redis.set(cacheKeyGameEvent, JSON.stringify(gameEvent), 'EX', 300);
    } else {
      gameEvent = JSON.parse(gameEvent);
    }

    const { getPrizeChanceNum = 0, topicScore = {} } = gameEvent;

    // 4. 从缓存中获取题目信息
    const cacheKeyTopic = `topic:${topicId}`;
    let topic = await app.redis.get(cacheKeyTopic);
    if (!topic) {
      topic = await ctx.model.Topic.findOne({ _id: topicId }).lean();
      if (!topic) throw new Error(`未找到题目: ${topicId}`);
      await app.redis.set(cacheKeyTopic, JSON.stringify(topic), 'EX', 300);
    } else {
      topic = JSON.parse(topic);
    }
    // 5. 判断答案是否正确
    const correctAnswer = topic.answer || [];
    const correct = answers.length === correctAnswer.length && answers.every(ele => correctAnswer.includes(ele));

    // 6. 计算分数和状态
    const difficultyLevelMap = { 易: 'easy', 中: 'medium', 难: 'hard' };
    const difficultyLevel = difficultyLevelMap[topic.difficultyLevel] || null;
    const score = difficultyLevel ? topicScore[difficultyLevel] : 1;
    const curScore = correct ? gameRecord.curScore + score : gameRecord.curScore;

    let newStep = correct ? gameRecord.curStep + 1 : gameRecord.curStep;
    newStep = Math.max(newStep, 0); // 确保 newStep 不小于 0
    const status = newStep >= getPrizeChanceNum ? 2 : 1; // 1: 进行中 2: 已完成

    // 7. 异步更新题目topic表中的answersNumber和correctNumber
    this.updateTopic(topicId, correct, topic);

    // 8. 更新游戏记录表
    const updateData = { curStep: newStep, status, curScore };
    const updateRes = await ctx.model.GameRecords.updateOne(
      { _id: gameRecordId },
      updateData
    );
    if (updateRes.nModified === 0) {
      ctx.auditLog('更新游戏记录失败', `gameRecordId - ${gameRecordId}的答题数据更新失败`, 'error');
      throw new Error('更新游戏记录失败: ' + gameRecordId);
    }
    // 9. 异步创建答题记录
    ctx.service.answerRecords.create({
      gameRecordId,
      topicId,
      answers,
      isRight: correct,
      score,
    });
    // 10. 异步分析用户行为
    this.behaviorAnalysis(userId, gameRecordId).catch(err => {
      ctx.auditLog('用户行为分析失败', `userId: ${userId} - ${err.message}`, 'error');
    });
    // 11. 返回结果
    return {
      topic: {
        _id: topicId,
        answer: topic.answer,
        difficultyLevel: topic.difficultyLevel,
      },
      correct,
      updatedData: updateData,
    };
  }

  async updateGameRecord(gameRecordId, topicId, correct = false) {
    const { ctx, app } = this;

    // 使用 Redis 缓存游戏记录
    const cacheKeyRecord = `gameRecord:${gameRecordId}`;
    let gameRecord = await app.redis.get(cacheKeyRecord);
    if (!gameRecord) {
      // 使用 lean() 获取纯数据对象
      const record = await ctx.model.GameRecords.findById(gameRecordId).lean();
      if (!record) throw new Error('未找到游戏记录: ' + gameRecordId);
      gameRecord = record;
      await app.redis.set(cacheKeyRecord, JSON.stringify(gameRecord), 'EX', 300);
    } else {
      gameRecord = JSON.parse(gameRecord);
    }

    // 使用 Redis 缓存活动信息
    const { gameEventId } = gameRecord;
    const cacheKeyEvent = `gameEvent2:${gameEventId}`;
    let gameEvent = await app.redis.get(cacheKeyEvent);
    if (!gameEvent) {
      gameEvent = await ctx.service.gameEvents.getOne2({ _id: gameEventId }, { getPrizeChanceNum: 1, topicScore: 1 });
      if (!gameEvent) throw new Error('未找到用户活动信息');
      await app.redis.set(cacheKeyEvent, JSON.stringify(gameEvent), 'EX', 300);
    } else {
      gameEvent = JSON.parse(gameEvent);
    }

    const { getPrizeChanceNum = 0, topicScore = {} } = gameEvent;

    // 使用 Redis 缓存题目信息
    const cacheKeyTopic = `topic:${topicId}`;
    let topic = await app.redis.get(cacheKeyTopic);
    if (!topic) {
      topic = await ctx.model.Topic.findOne({ _id: topicId }).lean();
      if (!topic) throw new Error(`未找到题目: ${topicId}`);
      await app.redis.set(cacheKeyTopic, JSON.stringify(topic), 'EX', 300);
    } else {
      topic = JSON.parse(topic);
    }

    let difficultyLevel = topic.difficultyLevel;
    if (difficultyLevel) {
      difficultyLevel = { 易: 'easy', 中: 'medium', 难: 'hard' }[difficultyLevel];
    }

    const score = difficultyLevel ? topicScore[difficultyLevel] : 1;
    const curScore = correct ? gameRecord.curScore + score : gameRecord.curScore;
    let newStep = correct ? gameRecord.curStep + 1 : gameRecord.curStep;
    newStep = Math.max(newStep, 0);
    const status = newStep >= getPrizeChanceNum ? 2 : 1;

    // 使用 Redis 计数器统计答题数和正确数
    if (correct) {
      await app.redis.hincrby(`topic:stats:${topicId}`, 'correctNumber', 1);
    }
    await app.redis.hincrby(`topic:stats:${topicId}`, 'answersNumber', 1);

    // 批量更新数据库
    // 使用 MongoDB 的 $inc 操作符进行原子递增，避免并发更新问题
    try {
      await Promise.all([
        ctx.model.GameRecords.updateOne(
          { _id: gameRecordId },
          { curStep: newStep, status, curScore }
        ),
        ctx.model.Topic.updateOne(
          { _id: topicId },
          {
            $inc: {
              answersNumber: 1,
              correctNumber: correct ? 1 : 0,
            },
          }
        ),
      ]);
      // 创建答题记录
      ctx.service.answerRecords.create({
        gameRecordId,
        topicId,
        answer: '',
        isRight: correct,
        score,
      });

      // 更新缓存
      const updatedGameRecord = { ...gameRecord, curStep: newStep, status, curScore };
      await app.redis.set(cacheKeyRecord, JSON.stringify(updatedGameRecord), 'EX', 300);

      return updatedGameRecord;
    } catch (error) {
      throw new Error('更新记录失败: ' + error.message);
    }
  }

  // 更新topic表中的answersNumber和correctNumber
  async updateTopic(topicId, correct, topic) {
    const { ctx, app } = this;
    try {
      const cacheKey = `topic:${topicId}`;
      const statsKey = `topic:stats:${topicId}`;

      // 使用Redis Multi执行原子操作
      const multi = app.redis.multi();

      // 更新Redis统计数据
      multi.hincrby(statsKey, 'answersNumber', 1);
      if (correct) {
        multi.hincrby(statsKey, 'correctNumber', 1);
      }

      // 更新缓存中的题目数据
      const updatedTopic = {
        ...topic,
        answersNumber: (topic.answersNumber || 0) + 1,
        correctNumber: correct ? (topic.correctNumber || 0) + 1 : (topic.correctNumber || 0),
      };
      multi.set(cacheKey, JSON.stringify(updatedTopic), 'EX', 300);

      // 执行Redis原子操作
      await multi.exec();

      // 异步更新数据库
      await ctx.model.Topic.updateOne(
        { _id: topicId },
        {
          $inc: {
            answersNumber: 1,
            correctNumber: correct ? 1 : 0,
          },
        }
      );

      return updatedTopic;
    } catch (error) {
      ctx.logger.error('更新题目失败:', error);
      throw new Error(`更新题目失败: ${error.message}`);
    }
  }

  // 获取当前个人总积分排行榜
  async getStepRanking(gameEventId) {
    if (!gameEventId) throw new Error('参数错误，gameEventId不能为空');

    const { ctx } = this;

    // 1. 查询活动信息
    const gameEvent = await ctx.model.GameEvents.findOne({ _id: gameEventId }, { rankListNum: 1 });
    if (!gameEvent) throw new Error(`${gameEventId} 未找到活动信息`);
    const limit = gameEvent.rankListNum || 30;

    // 2. 聚合查询，计算每个用户的总积分并关联用户信息
    const aggregatedList = await ctx.model.GameRecords.aggregate([
      { $match: { gameEventId, status: { $ne: 0 } } }, // 排除未开始的记录
      { $group: { _id: '$userId', totalCurScore: { $sum: '$curScore' } } }, // 按用户分组计算总积分
      { $match: { totalCurScore: { $gt: 0 } } }, // 排除总积分为 0 的记录
      { $sort: { totalCurScore: -1 } }, // 按总积分降序排序
      { $limit: limit }, // 限制返回的记录数
      {
        $lookup: {
          from: 'gameUser', // 关联的集合名称
          localField: '_id', // 当前集合中的字段
          foreignField: '_id', // 关联集合中的字段
          as: 'userInfo', // 输出的字段名
        },
      },
      { $unwind: '$userInfo' }, // 展开 userInfo 数组
      { $project: { name: '$userInfo.name', curScore: '$totalCurScore' } }, // 选择需要的字段
    ]);

    // 3. 返回结果
    return aggregatedList;
  }
  async getStepRanking2(gameEventId) {
    if (!gameEventId) throw new Error('参数错误，gameEventId不能为空');
    const { ctx } = this;
    const gameEvent = await ctx.model.GameEvents.findOne({ _id: gameEventId }, { rankListNum: 1 });
    if (!gameEvent) throw new Error(gameEventId + '未找到活动信息');
    const limit = gameEvent.rankListNum || 30;
    // 聚合查询，计算每个用户的 curScore 汇总值
    const aggregatedList = await ctx.model.GameRecords.aggregate([
      { $match: { gameEventId, status: { $ne: 0 } } }, // 未开始的不算
      { $group: { _id: '$userId', totalCurScore: { $sum: '$curScore' } } },
      { $match: { totalCurScore: { $gt: 0 } } }, // 排除 totalCurScore 为 0 的数据
      { $sort: { totalCurScore: -1 } },
      { $limit: limit },
    ]);

    const result = [];
    for (const item of aggregatedList) {
      const { _id: userId, totalCurScore } = item;
      const user = await ctx.model.GameUser.findOne({ _id: userId }, { name: 1 });
      if (!user) continue;
      result.push({ name: user.name, curScore: totalCurScore });
    }
    return result;
  }

  // 获取当前个人总积分
  async getMyTotalScore(gameEventId, userId) {
    if (!userId) throw new Error('参数错误，userId不能为空');
    if (!gameEventId) throw new Error('参数错误，gameEventId不能为空');
    const { ctx } = this;
    const gameRecords = await ctx.model.GameRecords.find({ userId, gameEventId }, { curScore: 1 });
    const totalScore = gameRecords.reduce((acc, record) => acc + record.curScore, 0);
    return totalScore;
  }

  // 获取当前团体总积分排行榜
  async getEnterpriseStepRanking(gameEventId) {
    if (!gameEventId) throw new Error('参数错误，gameEventId不能为空');
    const { ctx } = this;

    // 1. 查询活动信息
    const gameEvent = await ctx.model.GameEvents.findOne({ _id: gameEventId }, { rankListNum: 1 });
    if (!gameEvent) throw new Error(`${gameEventId} 未找到活动信息`);
    const limit = gameEvent.rankListNum || 30;

    // 2. 聚合查询，计算每个企业的总积分并关联企业信息
    const aggregatedList = await ctx.model.GameRecords.aggregate([
      { $match: { gameEventId, status: { $ne: 0 } } }, // 排除未开始的记录
      { $group: { _id: '$Enterprise', totalCurScore: { $sum: '$curScore' } } }, // 按企业分组计算总积分
      { $match: { totalCurScore: { $gt: 0 } } }, // 排除总积分为 0 的记录
      { $sort: { totalCurScore: -1 } }, // 按总积分降序排序
      { $limit: limit }, // 限制返回的记录数
      {
        $lookup: {
          from: 'gameEnterprise', // 关联的集合名称
          localField: '_id', // 当前集合中的字段
          foreignField: '_id', // 关联集合中的字段
          as: 'enterpriseInfo', // 输出的字段名
        },
      },
      { $unwind: '$enterpriseInfo' }, // 展开 enterpriseInfo 数组
      { $project: { cname: '$enterpriseInfo.cname', curScore: '$totalCurScore' } }, // 选择需要的字段
    ]);

    // 3. 返回结果
    return aggregatedList;
  }

}
module.exports = GameRecordsService;
