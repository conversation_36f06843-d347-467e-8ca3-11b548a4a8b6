stages:
  - test
  - release

unit test:
  image: registry.duopu.cn/zyws/docker/node:18-slim
  stage: test
  tags:
    - nodejs-dock
  cache:
    key: JGNPM
    paths:
      - .npm/
  before_script:
    # - npm config set registry https://registry.npmmirror.com
    # - export SHARP_DIST_BASE_URL=https://npm.taobao.org/mirrors/sharp-libvips/v8.10.5/
    # - export SHARP_IGNORE_GLOBAL_LIBVIPS=true
    - npm config set proxy http://***********:7890
    - npm config set https-proxy http://***********:7890
    - npm config set registry https://registry.npmjs.org/
    - npm i --include=dev  --cache .npm --prefer-offline
  after_script:
    - npm config delete proxy
    - npm config delete https-proxy
  script:
    - ls $CI_PROJECT_DIR
    - export EGG_SERVER_ENV=unittest
    - export NODE_ENV=test
    - npm run test
    # store and publish code coverage HTML report folder
    # https://about.gitlab.com/blog/2016/11/03/publish-code-coverage-report-with-gitlab-pages/
    # the coverage report will be available both as a job artifact
    # and at https://gitlab.duopu.cn/jkqy/zyjk/
    - mkdir -p public/$CI_COMMIT_REF_NAME
    - cp -r coverage/lcov-report/* public/$CI_COMMIT_REF_NAME
  artifacts:
    when: always
    paths:
      # save the coverage results
      - coverage
      - public
    expire_in: 30 days
  except:
    - tags


release-image:
  stage: release
  image: registry.duopu.cn/zyws/docker/docker:26.1.4
  tags:
    - nodejs-dock
  services:
    - registry.duopu.cn/zyws/docker/docker:26.1.4-dind
  variables:
    DOCKER_TLS_CERTDIR: "/certs"
    CONTAINER_RELEASE_IMAGE: $CI_REGISTRY_IMAGE:latest
  script:
    - ls -l .
    - source ./version
    # - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
    - docker login -u $CI_TA_ALL -p $CI_TA_ALL_PASS $CI_REGISTRY
    - docker run --rm --privileged  --network zyws-net multiarch/qemu-user-static --reset -p yes
    - docker buildx build --platform=linux/amd64,linux/arm64 -t $CI_REGISTRY_IMAGE:$VERSION -t $CONTAINER_RELEASE_IMAGE --push .
    - docker image rm $CI_REGISTRY_IMAGE:$VERSION $CONTAINER_RELEASE_IMAGE
    - docker builder prune -f && docker image prune -f
  only:
    - master
    - /^EP_.*$/
    - schedule-byTJ
    - wb_yn
    - ep_pay_type
    - feat_amp_authFlag
    - wb_wh
    - fzxyx3
    - feat_jcUpload
    - wb_fz_fixtj
    - px_wx_login
    - wb_wh_ppenewback
  except:
    - tags