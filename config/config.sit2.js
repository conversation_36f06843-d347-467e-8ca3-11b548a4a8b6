const path = require('path');
const port = 7010;

module.exports = appInfo => {

  return {
    // 插件路径
    admin_root_path: '/static',
    // 数据库连接
    mongoose: {
      client: {
        url: 'mongodb://i11.zyws.cn,i12.zyws.cn,i13.zyws.cn',
        options: {
          replicaSet: 'zyws',
          authSource: 'admin',
          dbName: 'sit2',
          user: 'dbadmin',
          pass: '4b10e685-A13C-4D65-86D1-8e7c64d4ffb',
          readPreference: 'nearest',
          ssl: false,
          useUnifiedTopology: true,
        },
      },
      tools: {
        url: 'mongodb://i11.zyws.cn,i12.zyws.cn,i13.zyws.cn',
        options: {
          replicaSet: 'zyws',
          authSource: 'admin',
          dbName: 'sit2-tools',
          user: 'dbadmin',
          pass: '4b10e685-A13C-4D65-86D1-8e7c64d4ffb',
          readPreference: 'nearest',
          ssl: false,
        },
      },
    },
    redis: {
      client: {
        host: 'i3',
        port: 6379,
        password: '',
        db: null,
      },
    },

    // 静态目录
    static: {
      prefix: '/static',
      dir: [
        path.join(appInfo.baseDir, 'backstage/dist'),
        '/opt/share/public/cms',
        '/opt/share/public/super',
        '/opt/share/public/jc',
        '/opt/share/public/operate',
        '/opt/share/public',
        path.join(appInfo.baseDir, 'app/public'),
      ],
      maxAge: 31536000,
    },
    // 加密解密
    session_secret: 'duopu_secret',
    auth_cookie_name: 'zyws_oapi',
    auth_govcookie_name: 'zyws_super',
    auth_jcqlccookie_name: 'zyws_jcqlc',
    encrypt_key: '243d477d-d6bb-4ab1-b0a9-a1b1c9419562',
    cms_encrypt_key: 'duopu_jkqy',
    salt_aes_key: 'duopu_jkqy',
    salt_sha2_key: '7a58917b-d493-4940-87d4-ab2b0fa8a3b7',
    encryptApp_key: '751f621ea5c8f930',
    encryptApp_vi: '2624750004598718',
    enterpriseUserGroup: 'vGEfBpfsv',
    // 日志路径
    logger: {
      dir: '/opt/log/oapi2/',
    },
    enterpriseUpload_path: '/opt/share/public/upload/enterprise',
    // 用户自行上传文件的文件系统目录，代码拼接: upload_path + /EnterpriseID/ + 文件名
    upload_path: '/opt/share/public/upload/enterprise',
    // 上行用户自行上传文件的http路径， 代码拼接：/static + upload_http_path + /EnterpriseID/ + 文件名
    upload_http_path: '/upload/enterprise',

    // CMS上传文件的文件系统目录
    upload_cms_path: {
      upload_path: '/opt/share/public/cms',
      static_root_path: 'cms', // 针对云存储可设置
    },
    // 存放生成的企业档案的文件系统目录
    enterprise_path: '/opt/public/enterprise',
    // 下载企业档案的http路径
    enterprise_http_path: '/enterprise',
    // 存放培训网站相关文件系统目录，代码拼接：trainSitFile_path + 文件名
    trainSitFile_path: '/opt/public/trainSitFile',
    // 浏览培训网站相关http路径，代码拼接：/static+ trainSitFile_http_path + 文件名
    trainSitFile_http_path: '/trainSitFile',

    // 课程封面目录，代码拼接: upload_courses_path + /课程ID/ + 文件名
    upload_courses_path: '/opt/share/public/courses',
    // 上行用户自行上传文件的http路径， 代码拼接：/static + upload_courses_http_path + /课程ID/ + 文件名
    upload_courses_http_path: '/courses',

    // 存放生成的培训证书的文件系统目录，代码拼接：certificate_path + 文件名
    certificate_path: '/opt/share/public/certificate',
    // 下载培训证书的http路径，代码拼接：/static+ certificate_http_path + 文件名
    certificate_http_path: '/certificate',

    // 上传体检报告目录，代码拼接: upload_phy_report_path + /体检机构ID/ + 文件名
    upload_phy_report_path: '/opt/share/public/upload/tjReport',
    // 上行上传体检报告的http路径， 代码拼接：/static + upload_http_phy_report_path + /体检机构ID/ + 文件名
    upload_http_phy_report_path: '/upload/tjReport',

    // 用于生成报告的word模板存放位置，直接获取文件系统目录
    report_template_path: process.cwd() + '/app/public/reportTemplate',
    // 存放生成的报告的文件系统目录，代码拼接：report_path + /EnterpriseID/ + 文件名
    report_path: '/opt/share/public/report',
    // 用于下载生成报告的文件目录，代码拼接：/static + report_http_path + /EnterpriseID/ + 文件名
    report_http_path: '/report',

    sign_path: '/opt/share/public/upload/sign',
    sign_http_path: '/upload/sign',

    // 用于绑定用户表单中各用户类型的ID
    groupID: {
      // 以下是cn环境的！！！
      operateGroupID: 'E1XjEmqA', // 超级管理员，运营用户角色ID
      superGroupID: 'RMwIEyjWK', // 政府用户角色ID
      adminGroupID: 'vGEfBpfsv', // 企业用户角色ID
      serviceGroupID: 'e4Qf2ic6-', // 机构用户角色ID
      userGroupID: 'V7au7L2Rw', // 劳动者用户角色ID
      physicalExamGroupID: 'zHLKFCyXD', // 体检用户角色ID
    },
    // 个人剂量
    // personProcessCodes: [
    //   {
    //     code: 'PROC-95950656-4C1D-42B5-820A-1643990CBD43',
    //     _idField: 'process_instance_id', // 报告审批
    //     statusField: 'reportApproved',
    //   },
    // ],
    radProcessCodes: [
      // 放射流程钉钉审批
      {
        node: 'contractReview',
        _idField: 'process_instance_id', // 合同审批
        statusField: 'approved',
      },
      {
        node: 'reportReview',
        _idField: 'reportProcessInstanceId', // 放射报告审核
        statusField: 'reportReview',
      },
      {
        node: 'officialDraftReview',
        _idField: 'reportProcessInstanceId', // 正式稿审核
        statusField: 'reportReview',
      },
      {
        node: 'radiateMApply',
        // code: 'PROC-E6151FA9-2259-4E14-AF23-133AE80C33A7',
        _idField: 'MApplyId', // 修改审批
        statusField: 'MApplyStatus',
      },
      {
        node: 'schemeReview',
        name: '方案审核',
        _idField: 'samplingPlanApproveId',
        statusField: 'samplingSchemeReview',
      },
    ],
    industrialRadProcessCodes: [
      // 工业放射流程钉钉审批
      {
        code: 'PROC-BA0C756C-8B7E-448F-A17B-798CA84D8A4E',
        _idField: 'samplingPlanApproveId', // 合同审批
        statusField: 'samplingSchemeReview',
      },
    ],
    qlcProcessCodes: [
      // 全流程钉钉审批
      // {
      //   node:['JcqlcProject','samplingPlanApprove'],
      //   code: 'PROC-477BF2BA-4D09-4376-BB01-A03EB2FBE22E',
      //   _idField: 'evaluationSchemeId', // 评价方案审核记录
      //   statusField: 'samplingPlanApprove',
      // },
      {
        node: 'labReportReview',
        _idField: 'reportProcessInstanceId', // 实验室审批单
        statusField: 'reportApproved',
        approveField: 'labreport',
      },
      {
        node: 'contractReview',
        _idField: 'process_instance_id', // 合同审批
        statusField: 'approved',
      },
      {
        node: 'reportReview',
        _idField: 'reportFinalProcessInstanceId', // 报告审批
        statusField: 'reportFinalApproved',
      },
      {
        node: 'samplingPlanApprove', // 方案审批
        _idField: 'samplingPlanApproveId',
        statusField: 'samplingPlanApprove',
        approveField: 'samplingPlan',
      },
      {
        node: 'samplingPlanAmend', // 计划单数据修改审批
        _idField: 'samplingPlanProcessInstanceId',
        statusField: 'samplingPlanAmendApproved',
        approveField: 'samplingPlan',
      },
      {
        node: 'spotRecordAmend', // 现场检测数据修改审批
        _idField: 'spotRecordProcessInstanceId',
        statusField: 'spotRecordAmendApproved',
        approveField: 'spotRecord',
      },
      {
        node: 'labAmend', // 实验室修改审批
        _idField: 'labProcessInstanceId',
        statusField: 'labAmendApproved',
        approveField: 'lab',
      },
      {
        node: 'finishProjectApproved', //  完成项目审批
        _idField: 'finishProjectProcessInstanceId',
        statusField: 'finishProjectApproved',
        approveField: 'finishProject',
      },
      {
        node: 'applyAcceptance', //  标准物质领用审批
        _idField: 'applyAcceptanceProcessInstanceId',
        statusField: 'applyAcceptanceApproved',
        approveField: 'applyAcceptance',
      },
    ],
    zywsApprovalConfig: {
      JcqlcProject: {
        typeName: '职业卫生',
        nodes: [
          {
            node: 'contractReview',
            name: '合同评审',
            _idField: 'process_instance_id',
            statusField: 'approved',
          },
          {
            node: 'samplingPlanApprove',
            name: '检测方案审核',
            _idField: 'samplingPlanApproveId',
            statusField: 'samplingPlanApprove',
          },
          {
            node: 'PJsamplingPlanApprove',
            name: '评价方案审核',
            _idField: 'samplingPlanApproveId',
            statusField: 'samplingPlanApprove',
          },
          {
            node: 'labReportReview',
            name: '实验室报告单审核',
            _idField: 'reportProcessInstanceId',
            statusField: 'reportApproved',
          },
          {
            node: 'reportReview',
            name: '报告审核',
            _idField: 'reportFinalProcessInstanceId',
            statusField: 'reportFinalApproved',
          },
          {
            node: 'finishProjectApproved',
            name: '项目完成审批',
            _idField: 'finishProjectProcessInstanceId',
            statusField: 'finishProjectApproved',
          },
          {
            node: 'samplingPlanAmend',
            name: '方案修改审批',
            _idField: 'samplingPlanProcessInstanceId',
            statusField: 'samplingPlanAmendApproved',
          },
          {
            node: 'spotRecordAmend',
            name: '现场采样修改审批',
            _idField: 'spotRecordProcessInstanceId',
            statusField: 'spotRecordAmendApproved',
          },
          {
            node: 'labAmend',
            name: '实验室修改审批',
            _idField: 'labProcessInstanceId',
            statusField: 'labAmendApproved',
          },
          {
            node: 'applyAcceptance',
            name: '标准物质领用审批',
            // _idField: 'applyAcceptanceProcessInstanceId',
            // statusField: 'applyAcceptanceApproved',
          },
        ],
      },
      RadiateqlcProject: {
        typeName: '放射卫生',
        nodes: [
          {
            node: 'contractReview',
            name: '合同评审',
            _idField: 'process_instance_id',
            statusField: 'approved',
          },
          {
            node: 'schemeReview',
            name: '方案审核',
            _idField: 'samplingPlanApproveId',
            statusField: 'samplingSchemeReview',
          },
          {
            node: 'draftReview',
            name: '底稿审核',
            _idField: 'draftReportInstanceId',
            statusField: 'draftReportApproved',
            allowReturn: true, // 允许回退，审批中退回直接回到指定节点
          },
          {
            node: 'officialDraftReview',
            name: '正式稿审核',
            _idField: 'officialDraftReportInstanceId',
            statusField: 'reportApproved',
            allowReturn: true,
          },
          {
            node: 'radiateMApply',
            name: '放射修改审批',
            _idField: 'reportProcessInstanceId',
            statusField: 'reportReview',
          },
        ],
      },
      PersonalDoseProject: {
        typeName: '个人剂量',
        nodes: [
          {
            node: 'contractReview',
            name: '合同评审',
            _idField: 'process_instance_id',
            statusField: 'approved',
          },
          {
            node: 'schemeReview',
            name: '方案审核',
            _idField: 'samplingPlanApproveId',
            statusField: 'samplingSchemeReview',
          },
          {
            node: 'draftReview',
            name: '底稿审核',
            _idField: 'draftReportInstanceId',
            statusField: 'draftReportApproved',
            allowReturn: true, // 允许回退，审批中退回直接回到指定节点
          },
          {
            node: 'officialDraftReview',
            name: '正式稿审核',
            _idField: 'officialDraftReportInstanceId',
            statusField: 'reportApproved',
            allowReturn: true,
          },
          {
            node: 'yearPersonReport',
            name: '年剂量审核',
            _idField: 'yearReportInstanceId',
            statusField: 'yearReportApproved',
          },
        ],
      },
      IndustrialRayProject: {
        typeName: '工业放射',
        nodes: [
          {
            node: 'contractReview',
            name: '合同评审',
            _idField: 'process_instance_id',
            statusField: 'approved',
          },
          {
            node: 'schemeReview',
            name: '方案审核',
            _idField: 'samplingPlanApproveId',
            statusField: 'samplingSchemeReview',
          },
          {
            node: 'draftReview',
            name: '底稿审核',
            _idField: 'draftReportInstanceId',
            statusField: 'draftReportApproved',
            allowReturn: true, // 允许回退，审批中退回直接回到指定节点
          },
          {
            node: 'officialDraftReview',
            name: '正式稿审核',
            _idField: 'officialDraftReportInstanceId',
            statusField: 'reportApproved',
            allowReturn: true,
          },
        ],
      },
    },
    dingInfo: {
      AppKey: 'dingnqjkhpyjymsdsyha',
      AppSecret:
        '3JXxYrt8C8n48GnzsKmH-AsVvVr7ZBjIYa-CBcIqDOe8cO5zfGwbSb0WD1iz61Fl',
      CorpId: 'ding2fa4df5d78220543',
      aes_key: 'ziURUNyRJlfN3WXaQtyq25b2dEBHfjrB9mrkzxlNbUo',
      token: 'Ut24XKFgH4p8akjtWNXTO',
    },

    // 阿里云短信接口配置
    aliMessage: {
      accessKeyId: process.env.aliMessage_accessKeyId,
      accessKeySecret: process.env.aliMessage_accessKeySecret,
      endpoint: 'https://dysmsapi.aliyuncs.com',
      apiVersion: '2017-05-25',
    },

    // 阿里云视频
    aliVideo: {
      accessKeyId: process.env.aliVideo_accessKeyId,
      secretAccessKey: process.env.aliVideo_secretAccessKey,
      // userId: 'process.env.aliVideo_userId',
    },
    // 人脸识别
    facebody: {
      accessKeyId: process.env.facebody_accessKeyId,
      accessKeySecret: process.env.facebody_accessKeySecret,
    },

    cluster: {
      listen: {
        port,
        hostname: '',
      },
    },

    // cn环境：文档类别id设置 对应的是表contentCategory的id
    categories: {
      learning: [
        { name: '法律', id: 'E1lagiaw' }, // law
        { name: '法规', id: 'V1U2-a9le' }, // legislation
        { name: '规章', id: '4JknAqTv' }, // regulation
        { name: '规范性文件', id: 'N7c-AjP_' }, // normativeDocs
      ],
      industryNews: {
        // 首页上的
        name: '行业动态',
        id: 'PUc7czJc',
      },
      informationPlatform: {
        name: '健康企业信息平台', // 首页上的
        id: '293sdcDe',
      },
      trainingEducation: {
        // 培训首页上的
        name: '培训教育平台',
        id: '6HiSgZEGi',
      },
      health: [
        { name: '健康达人', id: 'DBw994Lmx' },
        { name: '健康企业', id: '5VWyuEgVZ' },
      ],
    },

    // 服务地址配置
    server_path: `http://localhost:${port}`,
    server_api: `http://localhost:${port}/api`,
    // mqtt服务端口配置
    mqtt: {
      ip: 'tcp://mqtt.zyws.net',
      port: 1883,
    },
    iServiceHost: 'http://localhost:8667',
    // 后台管理员登录有效时间
    adminUserMaxAge: 1000 * 60 * 60 * 24, // 1 day
    jwtUserExpiresIn: '1day', // 用户信息结构化赋值有效期
    pay: {
      wxpay: {
        appid: 'wx8d9eada10facd67a', // 这里填写微信小程序的id，用于掉起支付
      },
    },
    // 微信支付宝回调地址
    wxpay_notify_url: 'https://oapi2.zyws.net/app/pay/wxNotify',
    wxpay_refund_notify_url: 'https://oapi2.zyws.net/app/pay/wxRefundsNotify',
    alipay_notify_url: 'https://oapi2.zyws.net/app/pay/alipaysNotify',

    // 新增配置请放在此行上方，branch放在最后
    // 当前所属站点，用于区分不同环境的配置；默认是master；
    branch: 'sit', // 当前所属站点为福建环境, jcqlc2.zyws.net
  };
};
