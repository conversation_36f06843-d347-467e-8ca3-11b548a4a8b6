const path = require('path');
const port = 7009;

module.exports = appInfo => {

  return {
    // 插件路径
    admin_root_path: '/static',
    // 数据库连接
    mongoose: {
      client: {
        url: `${process.env.mdbSrv}/?readPreference=nearest&ssl=false`,
        options: {
          authSource: 'admin',
          dbName: process.env.mdbName,
          user: process.env.mdbUser,
          pass: process.env.mdbPass,
          useNewUrlParser: true,
          useUnifiedTopology: true,

        },
      },
      tools: {
        url: `${process.env.mdbSrv}/?readPreference=nearest&ssl=false`,
        options: {
          authSource: 'admin',
          dbName: process.env.mdbToolsName,
          user: process.env.mdbUser,
          pass: process.env.mdbPass,
          useNewUrlParser: true,
          useUnifiedTopology: true,

        },
      },
    },
    // 阿里云短信接口配置
    aliMessage: {
      accessKeyId: process.env.aliMessage_accessKeyId,
      accessKeySecret: process.env.aliMessage_accessKeySecret,
      endpoint: 'https://dysmsapi.aliyuncs.com',
      apiVersion: '2017-05-25',
    },
    dingTalk: {
      // 钉钉
      AgentId: '1220016837',
      AppKey: 'dingnqjkhpyjymsdsyha',
      AppSecret:
        '3JXxYrt8C8n48GnzsKmH-AsVvVr7ZBjIYa-CBcIqDOe8cO5zfGwbSb0WD1iz61Fl',
      CorpId: 'ding2fa4df5d78220543',
    },
    dingInfo: {
      AppKey: 'dingnqjkhpyjymsdsyha',
      AppSecret:
        '3JXxYrt8C8n48GnzsKmH-AsVvVr7ZBjIYa-CBcIqDOe8cO5zfGwbSb0WD1iz61Fl',
      CorpId: 'ding2fa4df5d78220543',
      aes_key: 'ziURUNyRJlfN3WXaQtyq25b2dEBHfjrB9mrkzxlNbUo',
      token: 'Ut24XKFgH4p8akjtWNXTO',
    },
    // 静态目录
    static: {
      prefix: '/static',
      dir: [
        path.join(appInfo.baseDir, 'backstage/dist'),
        '/opt/public/cms',
        '/opt/public/super',
        '/opt/public/jc',
        '/opt/public/operate',
        '/opt/public',
        path.join(appInfo.baseDir, 'app/public'),
      ],
      maxAge: 31536000,
    },
    // 加密解密
    session_secret: 'jkqy_secret',
    auth_cookie_name: 'zyws_jqky_oapi',
    auth_govcookie_name: 'zyws_jkqy_super',
    auth_jcqlccookie_name: 'zyws_jkqy_jcqlc',
    encrypt_key: '243d477d-d6bb-4ab1-b0a9-a1b1c9419562',
    cms_encrypt_key: 'duopu_jkqy',
    salt_aes_key: 'duopu_jkqy',
    salt_sha2_key: '7a58917b-d493-4940-87d4-ab2b0fa8a3b7',
    encryptApp_key: '751f621ea5c8f930',
    encryptApp_vi: '2624750004598718',
    enterpriseUserGroup: 'vGEfBpfsv',

    // 存放生成的企业档案的文件系统目录
    enterprise_path: '/opt/public/enterprise',
    // 下载企业档案的http路径
    enterprise_http_path: '/enterprise',
    enterpriseUpload_path: '/opt/public/upload/enterprise',
    // 用户自行上传文件的文件系统目录，代码拼接: upload_path + /EnterpriseID/ + 文件名
    upload_path: '/opt/public/upload/enterprise',
    // 上行用户自行上传文件的http路径， 代码拼接：/static + upload_http_path + /EnterpriseID/ + 文件名
    upload_http_path: '/upload/enterprise',

    // CMS上传文件的文件系统目录
    upload_cms_path: {
      upload_path: '/opt/public/cms',
      static_root_path: 'cms', // 针对云存储可设置
    },
    // 存放培训网站相关文件系统目录，代码拼接：trainSitFile_path + 文件名
    trainSitFile_path: '/opt/public/trainSitFile',
    // 浏览培训网站相关http路径，代码拼接：/static+ trainSitFile_http_path + 文件名
    trainSitFile_http_path: '/trainSitFile',

    // 课程封面目录，代码拼接: upload_courses_path + /课程ID/ + 文件名
    upload_courses_path: '/opt/public/courses',
    // 上行用户自行上传文件的http路径， 代码拼接：/static + upload_courses_http_path + /课程ID/ + 文件名
    upload_courses_http_path: '/courses',

    // 存放生成的培训证书的文件系统目录，代码拼接：certificate_path + 文件名
    certificate_path: '/opt/public/certificate',
    // 下载培训证书的http路径，代码拼接：/static+ certificate_http_path + 文件名
    certificate_http_path: '/certificate',

    // 上传体检报告目录，代码拼接: upload_phy_report_path + /体检机构ID/ + 文件名
    upload_phy_report_path: '/opt/public/upload/tjReport',
    // 上行上传体检报告的http路径， 代码拼接：/static + upload_http_phy_report_path + /体检机构ID/ + 文件名
    upload_http_phy_report_path: '/upload/tjReport',

    // 用于绑定用户表单中各用户类型的ID
    groupID: {
      // 以下是cn环境的！！！
      operateGroupID: 'E1XjEmqA', // 超级管理员，运营用户角色ID
      superGroupID: 'RMwIEyjWK', // 政府用户角色ID
      adminGroupID: 'vGEfBpfsv', // 企业用户角色ID
      serviceGroupID: 'e4Qf2ic6-', // 机构用户角色ID
      userGroupID: 'V7au7L2Rw', // 劳动者用户角色ID
      physicalExamGroupID: 'zHLKFCyXD', // 体检用户角色ID
    },

    // 阿里云视频
    aliVideo: {
      accessKeyId: process.env.aliVideo_accessKeyId,
      secretAccessKey: process.env.aliVideo_secretAccessKey,
      // userId: 'process.env.aliVideo_userId',
    },
    // 人脸识别
    facebody: {
      accessKeyId: process.env.facebody_accessKeyId,
      accessKeySecret: process.env.facebody_accessKeySecret,
    },

    cluster: {
      listen: {
        port,
        hostname: '',
      },
    },

    // cn环境：文档类别id设置 对应的是表contentCategory的id
    categories: {
      learning: [
        { name: '法律', id: 'E1lagiaw' }, // law
        { name: '法规', id: 'V1U2-a9le' }, // legislation
        { name: '规章', id: '4JknAqTv' }, // regulation
        { name: '规范性文件', id: 'N7c-AjP_' }, // normativeDocs
      ],
      industryNews: {
        // 首页上的
        name: '行业动态',
        id: 'PUc7czJc',
      },
      informationPlatform: {
        name: '健康企业信息平台', // 首页上的
        id: '293sdcDe',
      },
      trainingEducation: {
        // 培训首页上的
        name: '培训教育平台',
        id: '6HiSgZEGi',
      },
      health: [
        { name: '健康达人', id: 'DBw994Lmx' },
        { name: '健康企业', id: '5VWyuEgVZ' },
      ],
    },
    wxAutho: {
      // 用户微信授权
      appid: 'wx1e2839a340f00288',
      secret: 'de6ff31ed989d3730486434286741ee2',
    },
    // 服务地址配置
    server_path: `http://localhost:${port}`,
    server_api: `http://localhost:${port}/api`,
    mqtt: {
      ip: 'tcp://mqtt',
      port: 1883,
    },
    iServiceHost: 'http://iservice',
    rabbitmq_url: `amqp://${process.env.rmqUser}:${process.env.rmqPass}@${process.env.rmqHost}:${process.env.rmqPort}`,
    coolHost: 'https://cool.zyws.cn/',
    // 微信支付宝回调地址
    wxpay_notify_url: 'https://oapi.jkqy2.cn/app/pay/wxNotify',
    wxpay_refund_notify_url: 'https://oapi.jkqy2.cn/app/pay/wxRefundsNotify',
    alipay_notify_url: 'https://oapi.jkqy2.cn/app/pay/alipaysNotify',
    isGetFZData: false, // 是否启动获取福州数据的定时任务
    fzDataApiHost: 'http://*************:8002/BhkDataService', // 福州数据接口地址
    // 新增配置请放在此行上方，branch放在最后
    // 当前所属站点，用于区分不同环境的配置；默认是master；
    branch: 'jkqy2', // 当前所属站点为内网环境, jkqy.cn
    subscriptionVerification: true, // 套餐支付校验开关

    fzxyx_token: { agentId: 'kOkfJ-ulr', appKey: 'd9cc0nlck9ed9f90enai', appSecret: '57b62c3f5fe093c6c240eb68da8c8fa75817a5cb1f59837f04dfd7a96dc04c1b' }, // jkqy2.cn
  };
};
