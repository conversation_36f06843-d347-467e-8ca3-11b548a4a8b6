
const path = require('path');

exports.mongoose = {
  enable: true,
  package: 'egg-mongoose',
};

exports.cors = {
  enable: true,
  package: 'egg-cors',
};

exports.jk_adminorg = {
  enable: true,
  package: 'egg-jk-adminorg',
  path: path.join(__dirname, '../lib/plugin/egg-jk-adminorg'),
};

exports.jk_adminuser = {
  enable: true,
  package: 'egg-jk-adminuser',
  path: path.join(__dirname, '../lib/plugin/egg-jk-adminuser'),
};

exports.jk_testPaper = {
  enable: true,
  package: 'egg-jk-testPaper',
  path: path.join(__dirname, '../lib/plugin/egg-jk-testPaper'),
};

exports.jk_training = {
  enable: true,
  package: 'egg-jk-training',
  path: path.join(__dirname, '../lib/plugin/egg-jk-training'),
};

exports.nunjucks = {
  enable: true,
  package: 'egg-view-nunjucks',
};

exports.validate = {
  enable: true,
  package: 'egg-dora-validate',
};

exports.jk_UploadFile = {
  enable: true,
  package: 'egg-jk-uploadfile',
  path: path.join(__dirname, '../lib/plugin/egg-jk-uploadfile'),
};

exports.jk_SynchroChenKeProjects = {
  enable: true,
  package: 'egg-jk-synchroChenKeProjects',
  path: path.join(__dirname, '../lib/plugin/egg-jk-synchroChenKeProjects'),
};

exports.jk_PPESelfLiftCabinet = {
  enable: true,
  package: 'egg-jk-PPESelfLiftCabinet',
  path: path.join(__dirname, '../lib/plugin/egg-jk-PPESelfLiftCabinet'),
};

exports.jk_physicalexamorg = {
  enable: true,
  package: 'egg-jk-physicalexamorg',
  path: path.join(__dirname, '../lib/plugin/egg-jk-physicalExamOrg'),
};

exports.swaggerdoc = {
  enable: true,
  package: 'egg-swagger-doc',
};

exports.jk_examSyllabus = {
  enable: true,
  package: 'egg-jk-examSyllabus',
  path: path.join(__dirname, '../lib/plugin/egg-jk-examSyllabus'),
};

exports.jk_quizGame = {
  enable: true,
  package: 'egg-jk-quizGame',
  path: path.join(__dirname, '../lib/plugin/egg-jk-quizGame'),
};

exports.redis = {
  enable: true,
  package: 'egg-redis',
};
