
oapi:1.5 (alpine 3.21.0)
========================
Total: 6 (UNKNOWN: 2, LOW: 0, MEDIUM: 2, HIGH: 2, CRITICAL: 0)

┌────────────┬────────────────┬──────────┬────────┬───────────────────┬───────────────┬─────────────────────────────────────────────────────────────┐
│  Library   │ Vulnerability  │ Severity │ Status │ Installed Version │ Fixed Version │                            Title                            │
├────────────┼────────────────┼──────────┼────────┼───────────────────┼───────────────┼─────────────────────────────────────────────────────────────┤
│ libcrypto3 │ CVE-2024-12797 │ HIGH     │ fixed  │ 3.3.2-r4          │ 3.3.3-r0      │ openssl: RFC7250 handshakes with unauthenticated servers    │
│            │                │          │        │                   │               │ don't abort as expected                                     │
│            │                │          │        │                   │               │ https://avd.aquasec.com/nvd/cve-2024-12797                  │
│            ├────────────────┼──────────┤        │                   ├───────────────┼─────────────────────────────────────────────────────────────┤
│            │ CVE-2024-13176 │ MEDIUM   │        │                   │ 3.3.2-r5      │ openssl: Timing side-channel in ECDSA signature computation │
│            │                │          │        │                   │               │ https://avd.aquasec.com/nvd/cve-2024-13176                  │
├────────────┼────────────────┼──────────┤        │                   ├───────────────┼─────────────────────────────────────────────────────────────┤
│ libssl3    │ CVE-2024-12797 │ HIGH     │        │                   │ 3.3.3-r0      │ openssl: RFC7250 handshakes with unauthenticated servers    │
│            │                │          │        │                   │               │ don't abort as expected                                     │
│            │                │          │        │                   │               │ https://avd.aquasec.com/nvd/cve-2024-12797                  │
│            ├────────────────┼──────────┤        │                   ├───────────────┼─────────────────────────────────────────────────────────────┤
│            │ CVE-2024-13176 │ MEDIUM   │        │                   │ 3.3.2-r5      │ openssl: Timing side-channel in ECDSA signature computation │
│            │                │          │        │                   │               │ https://avd.aquasec.com/nvd/cve-2024-13176                  │
├────────────┼────────────────┼──────────┤        ├───────────────────┼───────────────┼─────────────────────────────────────────────────────────────┤
│ musl       │ CVE-2025-26519 │ UNKNOWN  │        │ 1.2.5-r8          │ 1.2.5-r9      │ musl libc 0.9.13 through 1.2.5 before 1.2.6 has an          │
│            │                │          │        │                   │               │ out-of-bounds write ......                                  │
│            │                │          │        │                   │               │ https://avd.aquasec.com/nvd/cve-2025-26519                  │
├────────────┤                │          │        │                   │               │                                                             │
│ musl-utils │                │          │        │                   │               │                                                             │
│            │                │          │        │                   │               │                                                             │
│            │                │          │        │                   │               │                                                             │
└────────────┴────────────────┴──────────┴────────┴───────────────────┴───────────────┴─────────────────────────────────────────────────────────────┘

Node.js (node-pkg)
==================
Total: 4 (UNKNOWN: 0, LOW: 0, MEDIUM: 1, HIGH: 3, CRITICAL: 0)

┌────────────────────────────┬────────────────┬──────────┬──────────┬───────────────────┬───────────────┬───────────────────────────────────────────────────┐
│          Library           │ Vulnerability  │ Severity │  Status  │ Installed Version │ Fixed Version │                       Title                       │
├────────────────────────────┼────────────────┼──────────┼──────────┼───────────────────┼───────────────┼───────────────────────────────────────────────────┤
│ cross-spawn (package.json) │ CVE-2024-21538 │ HIGH     │ fixed    │ 7.0.3             │ 7.0.5, 6.0.6  │ cross-spawn: regular expression denial of service │
│                            │                │          │          │                   │               │ https://avd.aquasec.com/nvd/cve-2024-21538        │
├────────────────────────────┼────────────────┤          ├──────────┼───────────────────┼───────────────┼───────────────────────────────────────────────────┤
│ ip (package.json)          │ CVE-2024-29415 │          │ affected │ 1.1.9             │               │ node-ip: Incomplete fix for CVE-2023-42282        │
│                            │                │          │          │                   │               │ https://avd.aquasec.com/nvd/cve-2024-29415        │
│                            │                │          │          ├───────────────────┼───────────────┤                                                   │
│                            │                │          │          │ 2.0.1             │               │                                                   │
│                            │                │          │          │                   │               │                                                   │
├────────────────────────────┼────────────────┼──────────┼──────────┼───────────────────┼───────────────┼───────────────────────────────────────────────────┤
│ nanoid (package.json)      │ CVE-2024-55565 │ MEDIUM   │ fixed    │ 2.1.11            │ 5.0.9, 3.3.8  │ nanoid: nanoid mishandles non-integer values      │
│                            │                │          │          │                   │               │ https://avd.aquasec.com/nvd/cve-2024-55565        │
└────────────────────────────┴────────────────┴──────────┴──────────┴───────────────────┴───────────────┴───────────────────────────────────────────────────┘

/app/app/public/wxpem/apiclient_key.pem (secrets)
=================================================
Total: 1 (UNKNOWN: 0, LOW: 0, MEDIUM: 0, HIGH: 1, CRITICAL: 0)

HIGH: AsymmetricPrivateKey (private-key)
══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════
Asymmetric Private Key
──────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
 /app/app/public/wxpem/apiclient_key.pem:1 (added by 'COPY ./app/ app/ # buildkit')
──────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
   1 [ -----BEGIN PRIVATE KEY-----***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************-----END PRIVATE KEY-----
   2   
──────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────



/app/config/config.local.js (secrets)
=====================================
Total: 2 (UNKNOWN: 0, LOW: 0, MEDIUM: 0, HIGH: 2, CRITICAL: 0)

HIGH: Alibaba (alibaba-access-key-id)
══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════
Alibaba AccessKey ID
──────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
 /app/config/config.local.js:56 (added by 'COPY ./config/ config/ # buildkit')
──────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
  54         accessKeyId: process.env.accessKey
  55           ? process.env.accessKey.replace('\n', '')
  56 [         : '************************',
  57         accessKeySecret: process.env.secretKey
──────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────


HIGH: Alibaba (alibaba-access-key-id)
══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════
Alibaba AccessKey ID
──────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
 /app/config/config.local.js:195 (added by 'COPY ./config/ config/ # buildkit')
──────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
 193       //   accessKeyId: process.env.accessKey
 194       //     ? process.env.accessKey.replace('\n', '')
 195 [     //     : '************************',
 196       //   accessKeySecret: process.env.secretKey
──────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────


