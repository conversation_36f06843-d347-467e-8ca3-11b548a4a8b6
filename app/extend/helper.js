require('module-alias/register');
const Axios = require('axios');
const _ = require('lodash');
const path = require('path');
const { PDFDocument, StandardFonts } = require('pdf-lib');
const PizZip = require('pizzip');
const Docxtemplater = require('docxtemplater');
const expressions = require('angular-expressions');
const mkdirp = require('mkdirp');
const Minio = require('minio');
const Base64 = require('js-base64').Base64;
const url = require('url');
const awaitWriteStream = require('await-stream-ready').write;
const streamWormhole = require('stream-wormhole');
const assign = require('lodash/assign');
const last = require('lodash/last');
const sizeOf = require('image-size');
const ImageModule = require('docxtemplater-image-module-free-norepeat');
const sharp = require('sharp');
const combineWord = require('combine-word');
// 文件操作对象
const fs = require('fs');
const CryptoJS = require('crypto-js');
const RPCClient = require('@alicloud/pop-core').RPCClient;
const { integratesBy } = require('@utils');
const sm3 = require('sm-crypto').sm3;

const noStampPathStr = '_noStamp';
function initVodClient(accessKeyId, secretAccessKey) {
  const regionId = 'cn-shanghai'; // 点播服务接入区域
  const client = new RPCClient({
    accessKeyId,
    secretAccessKey,
    endpoint: 'http://vod.' + regionId + '.aliyuncs.com',
    apiVersion: '2017-03-21',
  });
  return client;
}

module.exports = {
  async getRadiateProjectYear(projectSN, modelName = 'RadiateqlcProject') {
    const { ctx } = this;
    const url = ctx.request.url;
    const params = new URLSearchParams(url.split('?')[1]);
    const serviceOrgId = params.get('serviceOrgId');
    const project = await ctx.model[modelName].findOne({ serviceOrgId, projectSN }, { createdAt: 1 });
    let year = new Date().getFullYear(); // 默认时间
    if (project && project.createdAt) {
      year = new Date(project.createdAt).getFullYear();
    }
    return year + '';
  },
  async fillMergerWord(ctx, templateFileName = '', wordData = {}, wordConfig = {}, mergerConfig = {
    mergerDemand: undefined, // 拼合文件的顺序 , 如果为空数组或为undefined，则默认遍历目录下所有文件按文件名排序生成
    insert: [], // 额外插入文件，内容为 {url:'', name:'', mergerKey:''}, name提供给排序依据
  }) {
    let { mergerDemand, insert } = mergerConfig;
    const convertToPdf = wordConfig.convertToPdf !== false;
    // 自定义导出路径
    // const outName = wordConfig.outName || templateFileName;
    const outFileDir = wordConfig.outFileDir || null;
    // const sign = wordConfig.sign || false;
    // 2025-04-09 直接按文件名匹配质控编号
    const url = ctx.request.url;
    const params = new URLSearchParams(url.split('?')[1]);
    const superID = params.get('serviceOrgId');
    const qcMatch = { 'qualityControlFiles.fileName': templateFileName };
    const qcNumberInfo = await ctx.model.ArchivalList.aggregate([
      { $match: { serviceOrgId: superID, projectType: 'segmentFile', ...qcMatch } },
      {
        $unwind: {
          path: '$qualityControlFiles',
          preserveNullAndEmptyArrays: false, // 如果是空数组或者没有元素则不会继续执行
        },
      },
      { $match: { ...qcMatch } },
    ]);
    if (qcNumberInfo && qcNumberInfo.length) {
      const { segment: segmentFileNumber, mergerDemand: isMergerDemand } = qcNumberInfo[0].qualityControlFiles || {};
      segmentFileNumber.forEach((item, index) => {
        wordData['qcNumber' + (index ? index : '')] = ctx.app.config.orgAbbr + '-' + item.QCnumber;
        wordData['QCversion' + (index ? index : '')] = item.QCversion || '0';
      });
      // 如果由多个文件拼成且配置了按需生成，将查询该文件内的参数是否有值，有则插入这个文件，没有则不插入
      if (isMergerDemand && segmentFileNumber && segmentFileNumber.length) {

        mergerDemand = segmentFileNumber.filter(item => {
          return !!wordData[item.segmentKey];
        }).map(item => item.segmentFileName).filter(item => item);
      }
    }
    // 模版文件夹下所有板块
    const mergerTemplateFilePath = path.join(ctx.app.config.report_template_path, 'merger', templateFileName);
    const files = await fs.promises.readdir(mergerTemplateFilePath);
    // 拼合内容
    const mergerContent = [];

    // 有额外插入文件
    if (insert && insert.length > 0) {
      for (const insertItem of insert) {
        // 获取文件目录和文件名信息
        const insertFileDir = path.dirname(insertItem.url);
        const insertFileName = path.basename(insertItem.url, path.extname(insertItem.url));
        wordConfig.temPath = insertFileDir;
        wordConfig.convertToPdf = false;
        const wordRes = await this.fillWord(ctx, insertFileName, wordData, wordConfig);
        const realPath = wordRes.path.replace('/static' + ctx.app.config.report_http_path + '/', '');
        mergerContent.push({ ...wordRes, path: realPath, mergerKey: insertItem.mergerKey });
      }
    }
    for (const tempFile of files) {
      wordConfig.temPath = mergerTemplateFilePath;
      wordConfig.convertToPdf = false;
      const tempName = path.basename(tempFile, path.extname(tempFile));
      if (mergerDemand && mergerDemand.length && !mergerDemand.includes(tempName)) {
        continue;
      }
      const wordRes = await this.fillWord(ctx, tempName, wordData, wordConfig);
      const realPath = wordRes.path.replace('/static' + ctx.app.config.report_http_path + '/', '');
      mergerContent.push({ ...wordRes, path: realPath, mergerKey: tempName });
    }
    // 排序规则 => 请将文件按照 [ '封面_Number', '声明_Number', '正文_Number||正文_String',  ] 形式命名

    mergerContent.sort((a, b) => {
      const typeA = a.mergerKey.split('_')[0];
      const typeB = b.mergerKey.split('_')[0];
      const numA = typeA.length > 1 ? parseInt(a.mergerKey.split('_')[1]) : 0;
      const numB = typeB.length > 1 ? parseInt(b.mergerKey.split('_')[1]) : 0;

      // 类型优先排序：封面、声明、正文
      const order = [ '封面', '声明', '正文' ];
      const indexA = order.indexOf(typeA);
      const indexB = order.indexOf(typeB);

      if (indexA !== indexB) {
        return indexA - indexB;
      }

      // 如果类型相同，按照数字排序
      return numA - numB;
    });

    if (mergerDemand && mergerDemand.length) {
      // 创建一个映射，以便按 mergerDemand 顺序重新排列 files 数组
      const sequenceMap = new Map(mergerDemand.map((item, index) => [ item, index ]));
      // 根据 mergerDemand 数组的顺序调整排序后的 files 数组
      mergerContent.sort((a, b) => {
        const indexA = sequenceMap.get(a.mergerKey);
        const indexB = sequenceMap.get(b.mergerKey);
        return indexA - indexB;
      });
    }

    let { projectSN, autoTimeStamp = true, year = '', prefix = '', dir = '', adminOrgName = '' } = wordConfig;

    if (projectSN && !year) {
      year = await this.getProjectYear(projectSN); // 项目创建年份
    }
    if (!dir) {
      dir = '';
    }
    if (year) {
      year = year + '';
    }

    let outputPath = `/${superID}` + (year ? `/${year}` : '') + (projectSN ? `/${projectSN}` : '') + (dir ? `/${dir}` : '');
    if (outFileDir) {
      outputPath = `/${superID}` + (outFileDir.startsWith('/') ? outFileDir : '/' + outFileDir);
    }

    const configFilePath = path.resolve(path.join(ctx.app.config.report_path, outputPath));
    mkdirp.sync(configFilePath);

    const docxFiles = [];
    // 遍历mergerContent数组，读取每个文件并将其添加到docxFiles数组中
    for (const mergerWord of mergerContent) {
      const filePath = path.join(ctx.app.config.report_path, mergerWord.path); // 获取文件的完整路径
      if (fs.existsSync(filePath)) {
        docxFiles.push(filePath); // 将文件内容添加到数组中
      } else {
        console.error(`文件不存在: ${filePath}`); // 输出文件不存在的错误信息
      }
    }

    const fileName = autoTimeStamp ? prefix + '_' + adminOrgName + templateFileName + '_' + new Date().getTime() : prefix + '_' + adminOrgName + templateFileName;
    // 使用DocxMerger合并读取的所有文件

    if (docxFiles.length > 1) {
      const fileContent = docxFiles.map(_file => fs.readFileSync(_file).buffer); // 读取文件内容
      const docxMerger = new combineWord({}, fileContent);
      const newSingleFilePath = path.join(ctx.app.config.report_path, outputPath, fileName + '.docx');
      docxMerger.save('nodebuffer', function(data) {
        fs.promises.writeFile(newSingleFilePath, data, function(err) {
          if (err) {
            console.error('保存文件时出错:', err);
          } else {
            console.log('文件已保存');
          }
        });
      });
      // 删除过程临时文件
      for (const delWord of docxFiles) {
        this.deleteFile(path.basename(delWord), path.dirname(delWord));
      }
    } else if (docxFiles.length === 1) {
      // 如果只有一个文件，直接重命名并保存
      const singleFilePath = docxFiles[0]; // 获取单个文件的内容
      const newSingleFilePath = path.join(ctx.app.config.report_path, outputPath, fileName + '.docx'); // 获取单个文件的保存路
      fs.promises.rename(singleFilePath, newSingleFilePath, function(err) {
        if (err) {
          console.error('重命名文件时出错:', err);
        } else {
          console.log('文件已成功重命名');
        }
      });
    } else {
      throw new Error('文件生成失败，请检查');
    }

    // 需要转化为 PDF
    if (convertToPdf) {
      // 将拼合后的文件转为pdf
      let departPath = '';
      if (year && projectSN) {
        departPath = '/static' + ctx.app.config.report_http_path + '/' + superID + '/' + year + '/' + projectSN + '/' + dir + '/' + fileName;
      } else {
        departPath = '/static' + ctx.app.config.report_http_path + '/' + superID + '/' + dir + '/' + fileName;
      }
      const pathDir = path.resolve(path.join(ctx.app.config.report_path, superID, year, projectSN));
      const filePath = path.join(pathDir, fileName);
      const PDFRes = await this.docx2pdf(departPath + '.docx', filePath + '.pdf');
      return PDFRes;
    }
    return {
      path: '/static' + ctx.app.config.report_http_path + '/' + outputPath + '/' + fileName + '.docx',
      originName: fileName,
      staticName: fileName + '.docx',
    };
  },
  async delFile({ model, match, projectField, filePath, fileName, delall = false }) {
    // 1. 需要从数据库拿文件名的删除
    if (model && match && projectField) {
      // jhw Note: 神奇query与aggregate返回的引用不一样
      // let oldFile = await model.find({ projectSN }, filefields)
      const oldFile = await model.aggregate([{ $match: match }, { $project: projectField }]);
      const { fileName } = oldFile[0] || {};
      if (oldFile !== null && fileName) {
        fs.promises.unlink(path.resolve(filePath, fileName), err => {
          if (err) {
            console.log(err);
            return err;
          }
          console.log('删除成功');
        });
      }
    } else if (filePath.indexOf('.') === -1 && delall) {
      // 2. 删除文件下的所有文件
      let files = [];
      if (fs.existsSync(filePath)) {
        files = fs.readdirSync(filePath);
        files.forEach(file => {
          const curPath = filePath + '/' + file;
          fs.unlinkSync(curPath); // 删除文件
        });
      }
    } else if (filePath && fileName) {
      // 3. 删除单个文件夹
      fs.promises.unlink(path.resolve(filePath, fileName), err => {
        if (err) {
          console.log(err);
          return err;
        }
        console.log('删除成功');
      });
    }

  },
  async getProjectYear(projectSN) {
    const { ctx } = this;
    // 获取项目创建日期，如果是子项目那么获取主项目的创建日期
    const projectSN_array = projectSN.split('-');
    let creatProject = projectSN;
    if (projectSN_array.length === 3) {
      creatProject = projectSN_array[0] + '-' + projectSN_array[1];
    }
    const parentProject = await ctx.model.JcqlcProject.findOne({ projectSN: creatProject }, { 'progress.createProject_time': 1 }).lean();
    let year = '2022';
    if (parentProject && parentProject.progress && parentProject.progress.createProject_time && parentProject.progress.createProject_time.completedTime) {
      year = new Date(parentProject.progress.createProject_time.completedTime).getFullYear() + '';
    }
    return year;
  },
  /**
   * 向PDF文档添加骑缝章
   *
   * @param {PDFDocument} pdfDoc - PDF文档对象
   * @param {string} stampImagePath - 骑缝章图像（PNG）路径
   * @param {number} desiredSegmentWidth - 每个页面上骑缝章部分的期望宽度
   *
   * @param startPage
   * @param endPage
   * @return {PDFDocument} 添加骑缝章后的PDF文档对象
   */
  async addSeamStampToPDF(pdfDoc, stampImagePath, desiredSegmentWidth, startPage = 0, endPage = 0) {
    // 读取PDF文件
    // 读取骑缝章图像并获取其尺寸
    const buffer = fs.readFileSync(stampImagePath);
    // const stampInfo = await sharp(buffer).metadata();
    const embeddedSignImage = await pdfDoc.embedPng(buffer);
    if (!endPage) {
      endPage = pdfDoc.getPages().length - 1;
    }
    const totalPageCount = endPage - startPage + 1;

    // const repeatedStamps = 0;
    let signedPageCount = 0; // 已签章的页面数量
    const imgWidth = embeddedSignImage.width;
    const toSinPageCount = Math.ceil(imgWidth / desiredSegmentWidth); // 签章图片可签的页面数量
    let stampCount = 0;// 签章数量
    if (toSinPageCount > totalPageCount) {
      desiredSegmentWidth = Math.floor(imgWidth / totalPageCount);
    }
    let flag = true;
    while (flag) {
      let x = 0;
      // 计算当前签章的最后一个页面索引
      for (let pageIndex = 0; pageIndex < toSinPageCount; pageIndex++) {
        // 计算裁剪的起始点
        x = (pageIndex % totalPageCount) * (desiredSegmentWidth);

        // 计算待裁剪的宽度
        const imgExtractWidth = imgWidth - x;
        const widthToExtract =
          imgExtractWidth >= desiredSegmentWidth
            ? desiredSegmentWidth
            : imgExtractWidth;

        // 创建一个包含骑缝章部分的sharp图像实例
        const partialStamp = sharp(buffer).extract({
          left: x,
          top: 0,
          width: widthToExtract,
          height: embeddedSignImage.height,
        });

        // 将裁剪后的骑缝章图像嵌入到PDF中
        const embeddedImage = await pdfDoc.embedPng(
          await partialStamp.toBuffer()
        );

        // 获取页面尺寸
        const curPageIndex = stampCount * toSinPageCount + pageIndex + startPage;
        if (curPageIndex > endPage) {
          flag = false;
          break;
        }
        const page = pdfDoc.getPage(curPageIndex); // 页面索引从0开始
        const { width, height } = page.getSize();

        // 计算在页面上放置骑缝章的位置
        // const left = (width - segmentWidth) / 2; // 水平居中
        const top = (height - embeddedSignImage.height) / 2; // 垂直居中

        const newWidth = (widthToExtract / imgWidth) * 150;
        const newHeight = 150;
        // 创建内容流并绘制骑缝章部分
        page.drawImage(embeddedImage, {
          x: width - newWidth,
          y: top,
          width: newWidth,
          height: newHeight,
          opacity: 0.6,
        });
        signedPageCount++;
      }
      if (signedPageCount >= totalPageCount) {
        // 所有页面已盖章，结束循环
        flag = false;
      }
      stampCount++;
    }

    // 保存修改后的PDF文档
    return pdfDoc;
  },

  /**
   * 从PDF文件中提取包含特定文本的项及其坐标信息
   *
   * @param {string} pdfPath - PDF文件路径
   * @param {string} text - 要搜索的文本字符串
   * @param {string} allMatch - 是否根据文本匹配所有项 默认fasle
   *
   *
   * @return {Object|undefined} 包含匹配文本项的坐标（x, y）和所在页面索引（从0开始）的对象，未找到匹配项时返回`undefined`
   */
  async extractTextWithCoordinates(pdfPath, text) {
    const { getDocument } = require('pdfjs-dist');
    const buffer = fs.readFileSync(pdfPath);
    const uint8Array = new Uint8Array(
      buffer.buffer,
      buffer.byteOffset,
      buffer.byteLength
    );
    // 初始化 PDF.js 参数
    const loadingTask = getDocument({ data: uint8Array });

    // 加载 PDF 文档
    const pdfDocument = await loadingTask.promise;

    // 遍历 PDF 页面
    for (let pageNum = 1; pageNum <= pdfDocument.numPages; pageNum++) {
      const page = await pdfDocument.getPage(pageNum);

      // 获取页面的文本内容和位置信息
      const content = await page.getTextContent();
      const positions = [];
      let str = '';
      for (let i = 0; i < content.items.length; i++) {
        const item = content.items[i];
        if (item.str) {
          positions.push({
            x: item.transform[4],
            y: item.transform[5] - 32,
            page: pageNum - 1,
          });
          str += item.str;
          if (str.indexOf(text) !== -1) {
            return { ...positions[positions.length - 1], lastPage: pdfDocument.numPages - 1 };
          }
        }

      }

    }
    return {
      x: 0, y: 0, page: pdfDocument.numPages - 1,
    };
  },


  /**
   * 根据配置计算PDF文档中签名位置的坐标信息
   *
   * @param {Object} signConfig - 签名配置对象
   * @param {PDFDocument} pdfDoc - PDF文档对象
   * @param {string} pdfPath - PDF文件路径（仅在`signConfig.text`存在时使用）

  * @return {Object} 包含签名位置坐标（x, y）和所在页面索引（page）的对象
  */
  async getSignPosition(signConfig, pdfDoc, pdfPath) {
    const x = signConfig.x || 0;
    const y = signConfig.y || 0;
    let position = { x, y, page: 0 };
    const curPage = pdfDoc.getPage(signConfig.page || 0);
    if (signConfig.text) {
      position = await this.extractTextWithCoordinates(pdfPath, signConfig.text, signConfig.page);
    }
    if (signConfig.x === 'center') {
      // x坐标点水平居中
      position.x = curPage.getSize().width / 2;
    }
    if (signConfig.y === 'center') {
      // y 坐标点垂直居中
      position.y = curPage.getSize().height / 2 - signConfig.height / 2;
    }
    position.x = position.x - signConfig.width / 2;
    if (signConfig.offsetX) {
      position.x += signConfig.offsetX;
    }
    if (signConfig.offsetY) {
      position.y += signConfig.offsetY;
    }
    const pages = signConfig.pages;
    if (!pages) {
      return [ position ];
    }
    const positions = pages.map(item => {
      return {
        ...position,
        page: item,
      };
    });
    return positions;
  },

  /**
   * 在PDF文档指定位置绘制图像
   *
   * @param {PDFDocument} pdfDoc - PDF文档对象
   * @param {Object} signConfig - 签名位置配置对象
   * @param {PDFImage} img - 已嵌入PDF文档的图像对象
   * @return {PDFDocument} 绘制图像后的PDF文档对象
  */
  async drawImage(pdfDoc, signConfig, img) {
    // const signatureImageBytes = fs.readFileSync(signatureImagePath);
    const pages = pdfDoc.getPages();
    console.log(888, signConfig);
    pages[signConfig.page].drawImage(img, {
      x: signConfig.x,
      y: signConfig.y,
      width: signConfig.width,
      height: signConfig.height,
      opacity: signConfig.opacity || 1,
      // width: signSize.width,
      // height: signSize.height,
    });
    return pdfDoc;
  },

  async drawFont(pdfDoc, signConfig) {
    const pages = pdfDoc.getPages();
    // const fontkit = require('fontkit');
    // const url = process.cwd() + '/app/public/fonts/STSONG.TTF';
    // const fontBuffer = await fs.readFileSync(url);
    // const fontBytes = await fetch(url).then((res) => res.arrayBuffer());
    // 设置字体和大小
    // pdfDoc.registerFontkit(fontkit);
    // const font = await pdfDoc.embedFont(fontBuffer); // 确保路径正确
    const font = await pdfDoc.embedFont(StandardFonts.TimesRoman);


    const fontSize = signConfig.fontSize || 13;

    // 设置文本颜色
    // let textColor = { r: 0, g: 0, b: 0 }; // 黑色
    // 设置文本位置和内容
    const text = signConfig.value;
    const x = signConfig.x; // 横坐标
    const y = signConfig.y; // 纵坐标
    pages[signConfig.page].drawText(text, {
      x,
      y,
      size: fontSize,
      font,
      // color: textColor,
    });
    return pdfDoc;
  },

  async docx2pdf(wordConfigFilePath, pdfConfigFilePath) {
    try {
      const { ctx } = this;
      const { config } = ctx.app;
      // 获取
      const host = ctx.request.header.host;
      const url = 'https://' + host + wordConfigFilePath;
      // const url = 'https://jcqlc.zyws.net/static/report/sIC8I-fxA/2023/ZJDPZR-230077/_ZJDPZR-24035372苍南天信汽车销售服务有限公司检测结果报告单_23011734142198364.docx'
      const originFileRes = await ctx.curl(url);
      if (originFileRes.status === 404) {
        console.log('源文件路径错误或不存在', url);
        return;
      }
      // console.log(url, 34353465429);
      // const url = 'https://jcqlc.zyws.net/static/report/sIC8I-fxA/2022/ZJDPZR-221328/ZJDPFR-240621_永嘉李安口腔诊所_报告 (1).docx';
      // const url = 'https://'+'jcqlc.zyws.net/static/report/sIC8I-fxA/2024/ZJDPZC-240231/初稿_1721635665221_平湖市森森服饰有限公司检测报告.docx';
      const kkfileviewUrl = `${config.filepreviewHost}/onlinePreview?url=${encodeURIComponent(Base64.encode(url))}`;
      await ctx.curl(kkfileviewUrl, {
        timeout: 300000,
      });
      const filename = path.basename(url, path.extname(url)) + 'docx.pdf';
      const res = await ctx.curl(`${config.filepreviewHost}/${filename}`);
      if (res.status === 404) {
        console.log('word转pdf失败，源文件路径：' + wordConfigFilePath);
      }
      // console.log(`${config.filepreviewHost}/${filename}`, 8796234926);
      await fs.writeFileSync(pdfConfigFilePath, res.data);
      const fileNames = path.parse(wordConfigFilePath).name;
      return {
        originName: fileNames,
        staticName: fileNames + '.pdf',
        path: wordConfigFilePath.replace('.docx', '.pdf'),
      };

    } catch (error) {
      console.log(error, '转pdf失败，源文件路径：' + wordConfigFilePath);
    }
  },
  async docx2pdf2(wordConfigFilePath, pdfConfigFilePath) {
    try {
      console.log(pdfConfigFilePath);
      const res = await this.ctx.curl(`${this.ctx.app.config.coolHost}/cool/convert-to/pdf`, {
        files: pdfConfigFilePath.replace('pdf', 'docx'),
        data: {
          Format: 'pdf',
          // PDFVer:'PDF/A-1b'
        },
        timeout: 300000,
        method: 'POST',
      });
      console.log(res, '转pdf结果');
      await fs.writeFileSync(pdfConfigFilePath, res.data);
      const fileNames = path.parse(wordConfigFilePath).name;
      return {
        originName: fileNames,
        staticName: fileNames + '.pdf',
        path: wordConfigFilePath.replace('.docx', '.pdf'),
      };
    } catch (error) {
      console.log(error);
    }
  },

  // 合并多个pdf文件
  async mergePdfFiles(pdfPaths) {
    // 创建一个空的 PDF 文档作为目标文档
    const mergedPdf = await PDFDocument.create();

    // 遍历要合并的 PDF 文件路径
    for (const pdfPath of pdfPaths) {
      // 读取 PDF 文件内容
      let pdfBytes;
      try {
        pdfBytes = fs.readFileSync(pdfPath);
      } catch (err) {
        console.error(`读取文件 ${pdfPath} 失败: ${err.message}`);
        continue; // 读取失败，跳过当前 pdfPath
      }
      // 解析 PDF 文件
      const sourcePdf = await PDFDocument.load(pdfBytes);

      // 将源 PDF 的所有页面添加到目标 PDF 文档中
      const copiedPages = await mergedPdf.copyPages(
        sourcePdf,
        sourcePdf.getPageIndices()
      );
      copiedPages.forEach(page => mergedPdf.addPage(page));
    }

    // 导出合并后的 PDF
    const mergedPdfBytes = await mergedPdf.save();

    // 返回合并后的 PDF 字节数组
    return mergedPdfBytes;
  },

  /**
   * 对指定PDF文件进行电子签名
   *
   * @param {string} sourcePath - 待签名的PDF文件路径
   * @param {string} outputFilePath - 输出已签名PDF文件路径（可选）；若未提供，则在原文件基础上添加时间戳后缀作为输出文件名
   *
   * @return {Object} 包含已签名文件HTTP访问路径的对象
   */
  async signPdf(sourcePath, outputFilePath) {
    const pdflibAddPlaceholder =
      require('@signpdf/placeholder-pdf-lib').pdflibAddPlaceholder;
    const signpdf = require('@signpdf/signpdf').default;
    const P12Signer = require('@signpdf/signer-p12').P12Signer;
    const certificatePath = path.join(__dirname, './certificate.p12');
    const certificateBuffer = fs.readFileSync(certificatePath);
    const signer = new P12Signer(certificateBuffer, {
      passphrase: 'Duopu6668',
      asn1StrictParsing: true,
    });
    const pdfDoc = await PDFDocument.load(fs.readFileSync(sourcePath));
    // 电子签名
    // The PDF needs to have a placeholder for a signature to be signed.
    pdflibAddPlaceholder({
      pdfDoc,
      reason: 'The user is decalaring consent through JavaScript.',
      contactInfo: '<EMAIL>',
      name: 'John Doe',
      location: 'Free Text Str., Free World',
      signatureLength: 16384, // 增加占位符大小到 16KB
    });
    const pdfbytes = await pdfDoc.save();
    const signedPdf = await signpdf.sign(pdfbytes, signer);

    if (!outputFilePath) {
      // const basename = path.basename(sourcePath, '.pdf');
      // outputFilePath = sourcePath.replace(basename, basename + '_sign_' + new Date().getTime());
      outputFilePath = sourcePath;
    }
    fs.writeFileSync(outputFilePath, signedPdf);
    const fileDir = outputFilePath.split(this.ctx.app.config.report_path);
    return {
      filePath: path.join(
        'static',
        this.ctx.app.config.report_http_path,
        fileDir[1]
      ),
    };
  },

  /**
   * 合并多个PDF文件，添加新页面，并对最终文档进行电子签名
   *
   * @param {string} sourcePath - 原始PDF文件路径
   * @param {string} outputFilePath - 输出合并及添加新页面后的PDF文件路径
   * @param {string[]} mergeFiles - 需要与原始文件合并的PDF文件路径列表（可选）
   * @param {Object} addPage - 描述新页面添加信息的对象（可选）
   * @param {string} addPage.imagePath - 新页面要插入的PNG图片路径
   * @param {number} addPage.page - 新页面插入的位置（索引从0开始）
   *
   * @return {Object} 包含处理后文件HTTP访问路径的对象
   */
  async mergeFileAndAddPage(sourcePath, outputFilePath, mergeFiles, addPage) {
    let pdfDoc = null;
    let noStampPdfDoc = null;
    const noStampOutputFilePath = (outputFilePath.replace('.pdf', '')) + noStampPathStr + '.pdf';
    // 合并文件
    if (mergeFiles) {
      // 读取需要合并的文件
      if (mergeFiles.length === 1) {
        mergeFiles = [ sourcePath, ...mergeFiles ];
      }
      const noStampMergeFiles = [];
      mergeFiles.forEach(item => {
        const noStampPath = (item.replace('.pdf', '')) + noStampPathStr + '.pdf';
        noStampMergeFiles.push(noStampPath);
      });

      const mergedPdfBytes = await this.mergePdfFiles(mergeFiles);
      fs.writeFileSync(outputFilePath, mergedPdfBytes);
      const noStampMergedPdfBytes = await this.mergePdfFiles(noStampMergeFiles);
      fs.writeFileSync(noStampOutputFilePath, noStampMergedPdfBytes);

      noStampPdfDoc = await PDFDocument.load(fs.readFileSync(noStampOutputFilePath));
      pdfDoc = await PDFDocument.load(fs.readFileSync(outputFilePath));
    } else {
      noStampPdfDoc = await PDFDocument.load(fs.readFileSync((sourcePath.replace('.pdf', '')) + noStampPathStr + '.pdf'));
      pdfDoc = await PDFDocument.load(fs.readFileSync(sourcePath));
    }

    // 给pdf添加页面
    if (addPage) {
      await this.addPageToPdf(addPage, pdfDoc, outputFilePath);
      await this.addPageToPdf(addPage, noStampPdfDoc, noStampOutputFilePath);
    }

    await this.signPdf(outputFilePath, outputFilePath);

    const fileDir = outputFilePath.split(this.ctx.app.config.report_path);
    const noStampFileDir = noStampOutputFilePath.split(this.ctx.app.config.report_path);
    return {
      noStampFilePath: path.join(
        'static',
        this.ctx.app.config.report_http_path,
        noStampFileDir[1]
      ),
      filePath: path.join(
        'static',
        this.ctx.app.config.report_http_path,
        fileDir[1]
      ),
    };
  },

  // 获取等比例缩放后的图片尺寸
  getScaledImageSize(width, imgWidth, imgHeight) {
    const ratio = width / imgWidth;
    const height = Math.round(imgHeight * ratio);
    return height;
  },

  async addPageToPdf(addPage, pdfDoc, outputFilePath) {
    // 添加新页面
    const newPage = pdfDoc.addPage();
    const img = await pdfDoc.embedPng(fs.readFileSync(addPage.imagePath));
    const XOFFSET = 20;
    const YOFFSET = 20;
    const width = newPage.getSize().width - XOFFSET * 2;
    const height = this.getScaledImageSize(width, img.width, img.height);
    newPage.drawImage(img, {
      x: XOFFSET,
      y: YOFFSET,
      width,
      height,
      opacity: 1,
    });
    pdfDoc.insertPage(addPage.page, newPage);
    // 删除最后一页新插入的页面
    pdfDoc.removePage(pdfDoc.getPageCount() - 1);
    fs.writeFileSync(outputFilePath, await pdfDoc.save());
  },

  /**
 * 给指定PDF文件绘制图像（作为审批或盖章）、添加骑缝章（如需），并在审批完成后进行电子签名
 *
 * @param {string} sourcePath - 源PDF文件路径
 * @param {string} signatureImagePath - 待绘制的签名图像（PNG）路径
 * @param {boolean} approvalComplete - 是否处于审批完成状态（默认：false）
 * @param {boolean} addSeamStamp - 是否添加骑缝章（默认：true，仅在`approvalComplete`为`true`时生效）
 * @param {Object[]} signConfig - 签名配置数组，包含每个签名位置的信息（仅在`approvalComplete`为`false`时使用单个配置）
 *
 * @param textConfig
 * @return {Object} 包含已签名文件HTTP访问路径的对象（仅在`approvalComplete`为`true`时返回）
 */
  async drawImageToPdfAndSign(sourcePath, signatureImagePath, approvalComplete = false, addSeamStamp = true, signConfig, textConfig) {
    const fs = require('fs');
    let pdfBuffer = fs.readFileSync(sourcePath);
    let pdfDoc = await PDFDocument.load(pdfBuffer);
    console.log(sourcePath, 'sourcePathsourcePath1');
    if (!fs.existsSync(signatureImagePath)) {
      return;
    }
    const img = await pdfDoc.embedPng(fs.readFileSync(signatureImagePath));
    console.log(approvalComplete, 875234832374);
    if (approvalComplete) {

      // 添加审核完成日期
      if (textConfig && textConfig.length) {

        for (let i = 0; i < textConfig.length; i++) {
          const item = textConfig[i];
          const height = item.height || 0;
          const width = item.width || 0;
          const textPosition = await this.getSignPosition({ ...item, width, height }, pdfDoc, sourcePath);
          for (let j = 0; j < textPosition.length; j++) {
            pdfDoc = await this.drawFont(pdfDoc, { ...textPosition[j], value: item.value });
          }
        }
        fs.writeFileSync(sourcePath, await pdfDoc.save());
      }
      pdfBuffer = fs.readFileSync(sourcePath);
      const pdfBuffer2 = fs.readFileSync(sourcePath);
      pdfDoc = await PDFDocument.load(pdfBuffer);
      // 获取不盖章的文件路径 盖章文件路径后面拼接上_noStamp
      const noStampPdfDoc = await PDFDocument.load(pdfBuffer2);
      const extname = path.extname(sourcePath);
      let noStampFilePath = sourcePath.replace(extname, '');
      noStampFilePath = noStampFilePath + noStampPathStr + '.pdf';
      const noStampPdfDocBytes = await noStampPdfDoc.save();
      fs.writeFileSync(noStampFilePath, noStampPdfDocBytes);
      // 审批结束后 需要添加骑缝章和盖章
      // 骑缝章
      if (addSeamStamp) {
        pdfDoc = await this.addSeamStampToPDF(pdfDoc, signatureImagePath, 50);
      }

      // 盖章
      const height = 150;
      const width = 150;
      for (let i = 0; i < signConfig.length; i++) {
        const item = signConfig[i];
        const signPosition = await this.getSignPosition({ ...item, width, height }, pdfDoc, sourcePath);
        for (let j = 0; j < signPosition.length; j++) {
          pdfDoc = await this.drawImage(pdfDoc, { ...signPosition[j], width, height, opacity: 0.6 }, img);
        }
      }
      fs.writeFileSync(sourcePath, await pdfDoc.save());

      if (addSeamStamp) {
        // 电子签名
        await this.signPdf(sourcePath);
      }
      const fileDir = sourcePath.split(this.ctx.app.config.report_path);
      const noStampFileDir = noStampFilePath.split(this.ctx.app.config.report_path);
      return {
        noStampFilePath: path.join(
          'static',
          this.ctx.app.config.report_http_path,
          noStampFileDir[1]
        ),
        noStampFilePath2: noStampFileDir[1],
        filePath: path.join(
          'static',
          this.ctx.app.config.report_http_path,
          fileDir[1]
        ),
        filePath2: fileDir[1],
      };

      // return {
      //   noSignFileName: basename,
      //   signedFileName: basename + '_sign',
      // };
    }
    // 审批过程，每审批一个节点就给pdf的相应位置添加签名
    const width = 60;
    const ratio = width / img.width;
    const height = Math.round(img.height * ratio);
    const signPosition = await this.getSignPosition(
      { ...signConfig, width, height },
      pdfDoc,
      sourcePath
    );

    for (let i = 0; i < signPosition.length; i++) {
      pdfDoc = await this.drawImage(
        pdfDoc,
        { ...signPosition[i], width, height, opacity: 1 },
        img
      );
    }

    fs.writeFileSync(sourcePath, await pdfDoc.save());
  },
  async reqJsonData(url, params = {}, method = 'get') {
    let responseData;

    let targetUrl = '';
    if (url.indexOf('manage/') === 0) {
      targetUrl = this.app.config.server_path + '/' + url;
    } else if (url.indexOf('http') === 0) {
      targetUrl = url;
    } else {
      targetUrl = this.app.config.server_api + '/' + url;
    }

    if (method === 'get') {
      responseData = await Axios.get(targetUrl, {
        params,
      });
    } else if (method === 'post') {
      responseData = await Axios.post(targetUrl, params);
    }
    // this.ctx.auditLog(
    //   'renderJson方法',
    //   `${JSON.stringify(responseData.data)}`,
    //   'info'
    // );
    if (
      (responseData &&
        responseData.status === 200 &&
        !_.isEmpty(responseData.data)) ||
      responseData.data.status === 200
    ) {
      return responseData.data;
    }
    // throw new Error(responseData.data.message);
    // 便于单元测试，改为如下，结果是一样的
    return { status: 500, message: responseData.data.message, data: {} };
  },
  clearCache(str, cacheKey) {
    // console.log('cacheStr', str);
    const currentKey = this.app.config.session_secret + cacheKey + str;
    this.setCache(currentKey, '', 2000);
  },
  setCache(key, value, time) {
    // 多进程消息同步
    console.log('setCache', key, value, time);
    this.app.messenger.sendToApp('refreshCache', {
      key,
      value,
      time,
    });
  },
  getCache(key) {
    return this.app.cache.get(key);
  },
  /**
   * 获取Redis缓存数据
   * @param {string} key - 缓存键名
   * @return {Promise<any>} 返回缓存的值，如果不存在则返回null
   */
  async getRedis(key) {
    try {
      return await this.app.redis.get(key);
    } catch (error) {
      this.ctx.logger.error('获取Redis缓存失败', error);
      return null;
    }
  },
  /**
   * 设置Redis缓存数据
   * @param {string} key - 缓存键名
   * @param {string|number|Object} value - 缓存值
   * @param {number} ttl - 过期时间（秒）
   * @return {Promise<boolean>} 设置成功返回true，失败返回false
   */
  async setRedis(key, value, ttl) {
    try {
      if (typeof value === 'object') {
        value = JSON.stringify(value);
      }
      if (ttl) {
        await this.app.redis.set(key, value, 'EX', ttl);
      } else {
        await this.app.redis.set(key, value);
      }
      return true;
    } catch (error) {
      this.ctx.logger.error('设置Redis缓存失败', error);
      return false;
    }
  },
  /**
   * 删除Redis缓存数据
   * @param {string} key - 缓存键名
   * @return {Promise<boolean>} 删除成功返回true，失败返回false
   */
  async delRedis(key) {
    try {
      await this.app.redis.del(key);
      return true;
    } catch (error) {
      this.ctx.logger.error('删除Redis缓存失败', error);
      return false;
    }
  },
  /**
   * 批量删除Redis缓存数据
   * @param {string} pattern - 缓存键名模式
   * @return {Promise<boolean>} 删除成功返回true，失败返回false
   */
  async delRedisPattern(pattern) {
    try {
      // 获取所有匹配模式的键
      const keys = await this.app.redis.keys(pattern);

      // 如果没有匹配的键，直接返回成功
      if (!keys || keys.length === 0) {
        return true;
      }

      // 使用 pipeline 批量删除以提高效率
      const pipeline = this.app.redis.pipeline();
      keys.forEach(key => pipeline.del(key));
      await pipeline.exec();

      this.ctx.logger.info(`成功删除 ${keys.length} 个匹配 ${pattern} 的缓存`);
      return true;
    } catch (error) {
      this.ctx.logger.error(`批量删除 Redis 缓存失败: ${pattern}`, error);
      return false;
    }
  },
  renderSuccess(ctx, { data = {}, message = '' } = {}) {
    ctx.body = {
      status: 200,
      data,
      message,
    };
    ctx.status = 200;
  },
  renderCustom(ctx, { status = 200, data = {}, message = '' } = {}) {
    ctx.body = {
      status,
      data,
      message,
    };
    ctx.status = status;
  },
  renderFail(
    ctx,
    { message = '哎呀！网页开小差了！', data = {}, status = 500 } = {}
  ) {
    if (message instanceof Object) {
      message = message.message;
    }
    ctx.body = {
      status,
      message,
      data,
    };
    ctx.status = status;
  },

  // 此函数仅限一对多模式
  async getAdminPower(ctx) {
    const userInfo = await ctx.service.user.item(ctx, {
      query: {
        _id: ctx.session.user._id,
        // _id: '4JiWCMhzg',
      },
      populate: [
        {
          path: 'group',
          select: 'power _id enable name',
        },
      ],
      files: 'group',
    });
    const adminPower = userInfo.group ? userInfo.group.power : [];
    return adminPower;
  },

  async getGovManagePower(ctx) {
    const superUserInfo = await ctx.service.superUser.item(ctx, {
      query: {
        _id: ctx.session.superUser._id,
        // _id: '4JiWCMhzg',
      },
      populate: [
        {
          path: 'group',
          select: 'power _id enable name',
        },
      ],
      files: 'group',
    });
    const adminPower = superUserInfo.group.power || [];
    return adminPower;
  },

  async getQlcPower(ctx) {
    const sessionUser = ctx.session.jcqlcUserInfo;
    let userInfo = {};
    let adminPower = {};
    if (sessionUser.type === 1) {
      userInfo = await ctx.service.serviceUser.item(ctx, {
        query: {
          _id: sessionUser._id,
        },
        populate: [
          {
            path: 'group',
            select: 'power _id enable name',
          },
        ],
        files: 'group',
      });
      adminPower = userInfo.group.power || [];
    } else {
      // userInfo = await ctx.service.jcUser.item(ctx, {
      //   query: {
      //     _id: sessionUser._id,
      //   },
      //   populate: [{
      //     path: 'group',
      //     select: 'power _id enable name',
      //   }],
      //   files: 'group',
      // });
      // console.log(JSON.stringify(userInfo), 'userInfo=========')

      userInfo = await ctx.model.JcUser.aggregate([
        { $match: { _id: sessionUser._id } },
        {
          $lookup: {
            from: 'jcGroups',
            localField: 'group',
            foreignField: '_id',
            as: 'group',
          },
        },
        { $unwind: '$group' },
        { $unwind: '$group.power' },
        {
          $group: {
            jsUser: { $first: '$$ROOT' },
            _id: '$_id',
            power1: { $push: '$group.power' },
          },
        },
        { $addFields: { 'jsUser.group.power': '$power1' } },
        { $replaceRoot: { newRoot: '$jsUser' } },
      ]);
      adminPower = (userInfo[0] && userInfo[0].group.power) || [];
      // jhw note
    }
    return adminPower;
  },

  async getApiPower(ctx) {
    const sessionApiUser = ctx.session.api;
    const apiUserInfo = await ctx.service.oAppGroup.item(ctx, {
      query: {
        agentId: sessionApiUser.agentId,
        appKey: sessionApiUser.appKey,
        appSecret: sessionApiUser.appSecret,
      },
      files: 'apiPower',
    });
    const apiPower = apiUserInfo.apiPower || [];
    return apiPower;
  },

  deleteFolder(path) {
    // console.log("---del path--" + path);
    return new Promise(resolve => {
      let files = [];
      if (fs.existsSync(path)) {
        // console.log("---begin to del--");
        if (fs.statSync(path).isDirectory()) {
          const walk = function(path) {
            files = fs.readdirSync(path);
            files.forEach(function(file) {
              const curPath = path + '/' + file;
              if (fs.statSync(curPath).isDirectory()) {
                // recurse
                walk(curPath);
              } else {
                // delete file
                fs.unlinkSync(curPath);
              }
            });
            fs.rmdirSync(path);
          };
          walk(path);
          // console.log("---del folder success----");
          resolve();
        } else {
          fs.unlink(path, function(err) {
            if (err) {
              console.log(err);
            } else {
              console.log('del file success');
              resolve();
            }
          });
        }
      } else {
        resolve();
      }
    });
  },

  hashSha256(data, salt) {
    return CryptoJS.SHA256(data + salt).toString();
  },

  // sm3hmc计算
  async hashSm3(data, passwordEncryptionAlgorithm) {
    const id = this.ctx.session.superUserInfo._id;
    try {
      if (passwordEncryptionAlgorithm === 'FZsm3') {
        const { fzgmBaseUrl, fzgmKey } = this.app.config;
        // 获取福州token
        if (!fzgmBaseUrl || !fzgmKey) {
          this.ctx.auditLog(
            '福州GM地址或者key未配置',
            '福州GM地址或者key未配置',
            'error'
          );
          throw new Error('福州GM地址或者key未配置');
        }
        const fzgmToken = await this.getFZToken(fzgmBaseUrl, fzgmKey);
        const base64Data = Buffer.from(data).toString('base64');
        const encryptData = await Axios({
          method: 'post',
          url: `${fzgmBaseUrl}/csmp/v3/paas/hsm/sm3-hmac`,
          data: {
            data: base64Data,
            appid: fzgmKey,
            keyid: '',
          },
          headers: {
            Authorization: fzgmToken,
          },
        });
        this.ctx.auditLog(
          `fz密码sm3计算成功${encryptData}`,
          `HMAC计算成功${data}`,
          'info'
        );
        return encryptData.data.value;
      }
      const hashData = sm3(data, {
        key: 'daac25c1512fe50f79b0e4526b93f5c0e1460cef40b6dd44af13caec62e8c60e0d885f3c6d6fb51e530889e6fd4ac743a6d332e68a0f2a3923f42585dceb93e9', // 要求为 16 进制串或字节数组
      });
      return hashData;
    } catch (error) {
      await this.ctx.service.operateLog.create('OperateUser', {
        optType: 'hsm',
        supplementaryNotes: `HMAC计算失败${error}`,
        optUserId: id,
      });
      this.ctx.auditLog('HMAC计算失败', `HMAC计算失败${error}`, 'error');
    }
  },

  // sm3hmc校验
  async verifySm3(data, passwordEncryptionAlgorithm, hash) {
    const id = this.ctx.session.operateUserInfo && this.ctx.session.operateUserInfo._id || '';
    try {
      if (passwordEncryptionAlgorithm === 'FZsm3') {
        const { fzgmBaseUrl, fzgmKey } = this.app.config;
        // 获取福州token
        if (!fzgmBaseUrl || !fzgmKey) {
          this.ctx.auditLog(
            '福州GM地址或者key未配置',
            '福州GM地址或者key未配置',
            'error'
          );
          throw new Error('福州GM地址或者key未配置');
        }
        const fzgmToken = await this.getFZToken(fzgmBaseUrl, fzgmKey);
        const base64Data = Buffer.from(data).toString('base64');
        const encryptData = await Axios({
          method: 'post',
          url: `${fzgmBaseUrl}/csmp/v3/paas/hsm/sm3-hmac-verify`,
          data: {
            data: base64Data,
            appid: fzgmKey,
            value: hash,
            keyid: '',
          },
          headers: {
            Authorization: fzgmToken,
          },
        });
        this.ctx.auditLog(
          `fz密码sm3校验成功${encryptData}`,
          `HMAC校验成功${hash}`,
          'info'
        );
        return encryptData.data.isvalid;
      }
      const hashData = sm3(data, {
        key: 'daac25c1512fe50f79b0e4526b93f5c0e1460cef40b6dd44af13caec62e8c60e0d885f3c6d6fb51e530889e6fd4ac743a6d332e68a0f2a3923f42585dceb93e9', // 要求为 16 进制串或字节数组
      });
      return hashData === hash;
    } catch (error) {
      await this.ctx.service.operateLog.create('OperateUser', {
        optType: 'hsm',
        supplementaryNotes: `HMAC校验失败${error}, ${error}`,
        optUserId: id,
      });
      this.ctx.auditLog('HMAC校验失败', `HMAC校验失败${error}`, 'error');
      return false;
    }
  },

  // 获取福州token
  async getFZToken(fzgmBaseUrl, fzgmKey) {
    let fzgmToken = await this.getCache('fzgmToken');
    if (!fzgmToken) {
      try {
        const response = await Axios({
          method: 'get',
          url: `${fzgmBaseUrl}/apisix/plugin/jwt/sign`,
          params: {
            key: fzgmKey,
          },
        });
        fzgmToken = response.data;
        this.ctx.auditLog(
          '获取福州GMtoken成功',
          `获取福州GMtoken成功${fzgmToken}`,
          'info'
        );
        this.setCache('fzgmToken', fzgmToken, 1000 * 60);
        return fzgmToken;
      } catch (error) {
        throw new Error('获取福州GMtoken失败', error);
      }
    } else {
      return fzgmToken;
    }
  },

  encrypt(data, key) {
    // 密码加密
    return CryptoJS.AES.encrypt(data, key).toString();
  },

  decrypt(data, key) {
    // 密码解密
    try {
      const bytes = CryptoJS.AES.decrypt(data, key);
      return bytes.toString(CryptoJS.enc.Utf8);
    } catch (error) {
      console.log('密码解密失败：' + error);
      return '';
    }
  },

  async request_alivod(action, param) {
    const aliclient = initVodClient(
      this.app.config.aliVideo.accessKeyId,
      this.app.config.aliVideo.secretAccessKey
    );
    return await aliclient
      .request(action, param, {})
      .then(response => {
        return response;
      })
      .catch(response => {
        return response;
      });
  },
  // 获取版本号 .以point表示
  getAPIVersion(accept, acceptStr1, acceptStr2) {
    console.log(accept, acceptStr1, acceptStr2);
    accept = accept.replace(acceptStr1, '');
    accept = accept.replace(acceptStr2, '');
    let version = accept.split('.');
    version = version[0] + '_' + version[1];
    return version;
  },
  // 删除存在本地的文件 htt+++++
  deleteFile(fileName, configPath) {
    fs.unlink(path.resolve(configPath, fileName), err => {
      if (err) {
        console.log('删除文件失败', err);
        return err;
      }
      console.log('删除成功');
    });
  },
  async fillWord(ctx, templateFileName, wordData, {
    EnterpriseID = '',
    adminOrgName = '',
    prefix = '',
    oldWordName = '',
    dir = '',
    imgSize = {
      samll: [ 60, 40 ],
      big: [ 400, 400 ],
    },
    convertToPdf = true,
    emptyTxt = '-',
    temPath = ctx.app.config.report_template_path,
    projectSN = '',
    year = '', // 年份
    autoTimeStamp = true,
    sign = false, // 是否需要签名
    imgCompressVale = 100, // 压缩图片的值
  } = {}) {
    console.log(111, imgCompressVale, sign);
    if (EnterpriseID) {
      const Enterprise = await ctx.model.ServiceOrg.findOne({ _id: EnterpriseID });
      const connect = await ctx.model.ServiceUser.findOne({ org_id: EnterpriseID });
      // 机构信息
      // 机构地址及联系人等信息
      wordData.orgName = Enterprise.name || '';
      wordData.address = Enterprise.address || ''; // 地址
      wordData.zipCode = connect.zipCode || ''; // 邮编
      wordData.netWorkURL = connect.netWorkURL || ''; // 网址
      wordData.fax = connect.fax || ''; // 传真
      wordData.freeService = connect.freeService || ''; // 免费
      wordData.logo = connect.logoImg ? path.join(ctx.app.config.upload_path, connect.logoImg.replace('/static/upload/enterprise/', '')) : ''; // logo
      wordData.concatPerson = connect.concatPerson || '';
      wordData.concatPhone = connect.concatPhone || '';
      wordData.archivesAddress = Enterprise.archivesAddress || '';
    }
    // const temPath = ctx.app.config.report_template_path;
    await mkdirp(temPath);
    const content = fs.readFileSync(
      path.resolve(temPath, templateFileName + '.docx'),
      'binary'
    );
    const zip = new PizZip(content);
    // 初始化 image 插件，并传入参数
    const opts = {};
    opts.centered = false;
    opts.fileType = 'docx';
    try {
      opts.getImage = function(tagValue) {
        try {
          const fileContent = fs.readFileSync(tagValue);
          return fileContent;
        } catch (error) {
          console.log(error);
          return '';
        }
      };
      opts.getSize = function(img, tagValue, tagName) {
        if (!img) return [ 60, 40 ];
        // 设置forceWidth宽度之后 使用image-size模块可以根据宽度自动调整图片的比例，使原图片比例不变
        const sizeObj = sizeOf(img);
        let forceWidth = 60;
        if (tagName === 'logo') {
          imgSize[tagName] = [ 100, 100 ];
        }
        if (imgSize[tagName]) {
          forceWidth = imgSize[tagName][0];
        }
        const ratio = forceWidth / sizeObj.width;
        return [
          forceWidth,
          // calculate height taking into account aspect ratio
          Math.round(sizeObj.height * ratio),
        ];
      };
    } catch (error) {
      throw error;
    }
    const imageModule = new ImageModule(opts);
    expressions.filters.lower = function(input) {
      if (!input) return input;
      return input.toLowerCase();
    };
    function angularParser(tag, parserContext) {
      if (tag === '.') {
        return {
          get: s => {
            if (s) {
              return s;
            }
            return '';
          },
        };
      }
      const expr = expressions.compile(
        tag.replace(/(’|‘)/g, "'").replace(/(“|”)/g, '"')
      );
      return {
        get(scope, context) {
          if (tag.includes('$index') && tag.includes('$key')) {
            const key = tag.split('$key')[1];
            const targetIndex = tag.split('$key')[0].split('$index')[1];
            // console.log(333, targetIndex, key)
            if (context.scopePathItem[context.scopePathItem.length - 1] === parseInt(targetIndex)) {
              return scope[key];
            }
            return '';
          }
          let obj = {};
          const index = last(context.scopePathItem);
          const scopeList = context.scopeList;
          const num = context.num;
          for (let i = 0, len = num + 1; i < len; i++) {
            obj = assign(obj, scopeList[i]);
          }
          obj = assign(obj, {
            $index: index,
            $index2: index + 1,
          });
          let res = expr(scope, obj);

          if (res !== '' && res !== undefined && res !== null) {
            if (!parserContext.tag.module && res instanceof Array && !res.length) {
              return emptyTxt;
            }
            if (typeof res === 'string') {
              res = res.replace(/\&nbsp\;/g, ' ');
            }
            return res;
          }
          if (!parserContext.tag.module) { // 如果只是文本标签{} 不是{#xxx} 没有值的话 返回-
            return emptyTxt;
          }
          return '';
        },
      };
    }
    // 添加分页符，除了循环中的最后一项
    function parser(tag, context) {
      // We write an exception to handle the tag "$pageBreakExceptLast"
      if (tag === '$pageBreakExceptLast') {
        return {
          get(scope, context) {
            const totalLength =
              context.scopePathLength[
                context.scopePathLength.length - 1
              ];
            const index =
              context.scopePathItem[
                context.scopePathItem.length - 1
              ];
            const isLast = index === totalLength - 1;
            if (!isLast) {
              return '<w:p><w:r><w:br w:type="page"/></w:r></w:p>';
            }
            return '';
          },
        };
      }
      // We use the angularParser as the default fallback
      // If you don't wish to use the angularParser,
      // you can use the default parser as documented here:
      // https://docxtemplater.com/docs/configuration#default-parser
      return angularParser(tag, context);
    }
    const doc = new Docxtemplater();
    // doc.setData(wordData);
    doc.loadZip(zip);
    doc.attachModule(imageModule);
    doc.setOptions({
      parser,
      linebreaks: false,
    });
    // complie需要在renderAsync前调用，注意顺序
    doc.compile();
    let res = {};
    const fileName = autoTimeStamp ? prefix + '_' + adminOrgName + templateFileName + '_' + new Date().getTime() : prefix + '_' + adminOrgName + templateFileName;
    try {
      await doc.renderAsync(wordData);
      const buf = doc.getZip()
        .generate({
          type: 'nodebuffer',
          compression: 'DEFLATE',
        });
      if (projectSN && !year) {
        year = await this.getProjectYear(projectSN); // 项目创建年份
      }
      if (year) {
        year = year + '';
      }
      const configFilePath = path.resolve(path.join(ctx.app.config.report_path, `/${EnterpriseID}`, year, projectSN), dir);
      mkdirp.sync(configFilePath);
      // 上一次生成的word清除
      if (oldWordName && autoTimeStamp) {
      // 清空
      // 已经审批的话，再次保存需要把原有文件删除，删除后
        const fileName = this.getFileName(oldWordName);
        this.deleteFile(fileName + '.docx', configFilePath);
        this.deleteFile(fileName + '.pdf', configFilePath);
      }
      // console.log(314253645, configFilePath, fileName)
      await fs.writeFileSync(path.resolve(configFilePath, fileName + '.docx'), buf);
      if (convertToPdf) {
        let departPath = '';
        if (year && projectSN) {
          departPath = '/static' + ctx.app.config.report_http_path + '/' + EnterpriseID + '/' + year + '/' + projectSN + '/' + dir + '/' + fileName;
        } else {
          departPath = '/static' + ctx.app.config.report_http_path + '/' + EnterpriseID + '/' + dir + '/' + fileName;
        }
        const pathDir = path.resolve(path.join(ctx.app.config.report_path, EnterpriseID, year, projectSN));
        const filePath = path.join(pathDir, fileName);
        res = await this.docx2pdf(departPath + '.docx', filePath + '.pdf');
      } else {
        res = {
          originName: fileName,
          staticName: fileName + '.docx',
          path: (year && projectSN ? '/static' + ctx.app.config.report_http_path + '/' + EnterpriseID + '/' + year + '/' + projectSN + '/' + fileName + '.docx' : '/static' + ctx.app.config.report_http_path + '/' + EnterpriseID + '/' + dir + '/' + fileName + '.docx'),
        };
      }
      return res;
    } catch (error) {
      console.log('生成word文件出错！', error);
      // const e = {
      //   message: error.message,
      //   name: error.name,
      //   stack: error.stack,
      //   properties: error.properties,
      // };
      console.log(error.properties);
      // res = {
      //   code: 500,
      //   e,
      //   message: '请关闭正在操作的word文件',
      // };
      throw error;
    }
  },
  getFileName(fileName) {
    const fileNames = fileName.split('.');
    if (fileNames.length > 1) {
      fileNames.splice(-1, 1);
      fileNames.join('.');
      return fileNames;
    }
    return fileName;
  },

  /**
   * @description: 初始化client
   * @return {*}
   */
  async initCilent() {
    const clientOption = this.app.config.oss; // 获取oss配置

    // 实例化client

    const endPoint = clientOption.endPoint;
    const parsedUrl = url.parse(endPoint);

    const protocol = parsedUrl.protocol;
    const hostname = parsedUrl.hostname;
    const port = parsedUrl.port;

    if (!protocol || !hostname) {
      throw Error(`请检查url是否正确：${endPoint}`);
    }
    // minio
    const client = new Minio.Client({
      endPoint: hostname,
      region: clientOption.region,
      accessKey: clientOption.accessKeyId,
      secretKey: clientOption.accessKeySecret,
      port: port ? parseInt(port) : '',
      useSSL: protocol.includes('https'),
    });
    return client;
  },

  /**
   * @description: stream写入
   * @param {*} readableStream 可读流 必填
   * @param {*} target 写入路径 必填 例
   * @param {*} bucketObj bucket配置对象 非必填 app.oss.buckets.xxx ，默认app.oss.buckets.default
   * @return {*}
   */
  async pipe({ readableStream, target, bucketObj }) {
    const { app } = this;
    // 获取文件存储类型
    const storageType = app.config.storageType;
    const option = app.config.oss;
    const result = await this.processEnterprisePath(target);
    target = result.newTarget;
    if (app.config.branch === 'wkzwy') {
      console.log('wkzwy oss === ', storageType, option);
    }
    if (storageType.includes('oss')) {
      try {
        const targetSplit = target.replace(/\\/gi, '/').split('/public/');
        let filePath = targetSplit[targetSplit.length - 1];
        if (filePath.startsWith('/')) {
          filePath = filePath.substring(1);
        }

        let bucketName = '';
        let accessPolicy = '';
        if (bucketObj) {
          bucketName = bucketObj.name;
          accessPolicy = bucketObj.accessPolicy;
        } else {
          bucketName = option.buckets.default.name;
          accessPolicy = option.buckets.default.accessPolicy;
        }
        const client = await this.initCilent();

        const parsedUrl = url.parse(option.endPoint);

        const protocol = parsedUrl.protocol;
        const hostname = parsedUrl.hostname;
        const port = parsedUrl.port;

        const res = await client.putObject(
          bucketName,
          filePath,
          readableStream
        );
        // console.log('ossPipeRes', res);

        let fileUrl = '';
        if (accessPolicy === 'private') {
          fileUrl = await client.presignedUrl(
            'GET',
            bucketName,
            filePath,
            24 * 60 * 60
          );
        } else {
          fileUrl = `${protocol}//${hostname}${
            port ? ':' + port : ''
          }/${bucketName}/${filePath}`;
        }
        console.log('ossPipeFileUrl', fileUrl);

        if (res && res.etag) {
          return {
            status: 200,
            type: storageType,
            url: fileUrl,
          };
        }
      } catch (error) {
        console.log('pipeError', error);
        return {
          status: 500,
          message: 'error',
          type: storageType,
          url: '',
        };
      }
    } else {
      // const fileName = path.basename(target)
      // 解析路径，创建缺少的目录结构
      const dirname = path.dirname(target);
      await mkdirp(dirname);
      // 默认本地存储
      const writeStream = fs.createWriteStream(target);
      try {
        await awaitWriteStream(readableStream.pipe(writeStream));
        return {
          status: 200,
          type: storageType ? storageType : 'local',
        };
      } catch (error) {
        console.log(4444444, error);
        await streamWormhole(writeStream);
        writeStream.destroy();
        return {
          status: 500,
          message: 'error',
          type: storageType,
        };
      }
    }
  },
  /**
   * @description: 删除文件
   * @param {*} target 必填 文件路径
   * @param {*} bucketObj 可选
   * @return {*}
   */
  async deleteObject(target, bucketObj) {
    const { app } = this;
    // 获取文件存储类型
    const storageType = app.config.storageType;
    const result = await this.processEnterprisePath(target);
    target = result.newTarget;
    if (storageType === 'oss') {
      try {
        const option = app.config.oss;
        let bucketName = '';
        if (bucketObj) {
          bucketName = bucketObj.name;
        } else {
          bucketName = option.buckets.default.name;
        }
        const client = await this.initCilent();
        const targetSplit = target.replace(/\\/gi, '/').split('public/');
        const filePath = targetSplit[targetSplit.length - 1];
        console.log('deleteParmas', bucketName, filePath);
        const ossRes = await client.removeObject(bucketName, filePath);
        if (!ossRes) {
          return {
            status: '200',
            message: 'success',
            type: storageType,
          };
        }
      } catch (error) {
        console.log('deleteObjectError', error);
        return {
          status: 500,
          message: 'error',
          type: storageType,
        };
      }
    } else {
      // 默认本地存储
      try {
        if (fs.existsSync(target)) {
          fs.unlinkSync(target);
        }
        return {
          status: 200,
          message: 'sucess',
          type: storageType,
        };
      } catch (error) {
        return {
          status: 500,
          message: 'error',
          type: storageType,
        };
      }
    }
  },
  /**
   * @description: 下载对象并将其保存为本地文件系统中的文件
   * @param {*} objectPath 必填 存在oss上的文件路径
   * @param {*} filePath 必填 存在本机上的文件路径
   * @param {*} bucketObj 可选
   * @return {*}
   */
  async fGetObject({ objectPath, filePath, bucketObj }) {
    const { app } = this;
    const option = app.config.oss;
    const result = await this.processEnterprisePath(objectPath);
    const result2 = await this.processEnterprisePath(filePath);
    objectPath = result.newTarget;
    filePath = result2.newTarget;
    let bucketName = '';
    if (bucketObj) {
      bucketName = bucketObj.name;
    } else {
      bucketName = option.buckets.default.name;
    }
    const client = await this.initCilent();
    console.log('fGetObject参数', bucketName, objectPath, filePath);
    const res = await client.fGetObject(bucketName, objectPath, filePath);
    console.log('fGetObjectRes', res);
    if (!res) {
      return {
        status: '200',
        message: 'success',
        type: app.config.storageType,
      };
    }
    return {
      status: 500,
      message: 'error',
      type: app.config.storageType,
    };
  },
  async concatenatePath({ path, bucketObj }) {
    const { app } = this;
    // 获取文件存储类型
    const storageType = app.config.storageType;
    console.log('storageType：', storageType);
    console.log('storageType === oss', storageType === 'oss');
    const result = await this.processEnterprisePath(path);
    path = result.newTarget;
    try {
      if (storageType.includes('oss')) {
        console.log('oss拼接路径了');
        const clientOpt = app.config.oss;
        let bucketName = '';
        let accessPolicy = '';
        if (bucketObj) {
          bucketName = bucketObj.name;
          accessPolicy = bucketObj.accessPolicy;
        } else {
          bucketName = clientOpt.buckets.default.name;
          accessPolicy = clientOpt.buckets.default.accessPolicy;
        }
        // 判断bucket是否是私有的
        // 如果是私有，要对url签名然后返回给前端
        if (accessPolicy === 'private') {
          console.log('如果是私有，要对url签名然后返回给前端');
          const client = await this.initCilent(bucketName);

          // // 如果路径首个带/，就把它去掉
          const pathUrl = path.replace(/^\//, '');
          // 获取临时的URL
          // 通过判断pathUrl最后结尾的文件类型来判断 MIME type
          const reqParams = {
            'response-content-type': this.getMIME(pathUrl),
          };
          const res = await client.presignedUrl(
            'GET',
            bucketName,
            pathUrl,
            24 * 60 * 60,
            reqParams
          );
          // const res = await client.presignedGetObject(bucketName, pathUrl, 24 * 60 * 60, options);
          console.log('获取的临时的URL', res);
          if (res) {
            return res.replace(`${clientOpt.endPoint}`, '/oss');
          }
          return '';
        }
        // 如果是公开的oss，就直接返回这个
        console.log(`${clientOpt.endPoint}/${bucketName}${path}`);
        return `${clientOpt.endPoint}/${bucketName}${path}`;
      }
      // 默认本地存储
      return app.config.static.prefix + path;
    } catch (error) {
      // console.log('concatenatePathError', error);
      return '';
    }
  },
  async formatUrl(url) {
    try {
      // const ossConfig = this.app.config.oss;
      return url;
    } catch (error) {
      return '';
    }
  },
  getMIME(pathUrl) {
    const pathUrlSplit = pathUrl.split('.');
    const extension = pathUrlSplit[pathUrlSplit.length - 1].toLowerCase();

    const mimeTypes = {
      pdf: 'application/pdf',
      xls: 'application/vnd.ms-excel',
      xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      doc: 'application/msword',
      docx: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      jpg: 'image/jpeg',
      jpeg: 'image/jpeg',
      bmp: 'image/bmp',
      png: 'image/png',
      gif: 'image/gif',
      csv: 'text/csv',
      txt: 'text/plain',
      rtf: 'application/rtf',
      html: 'text/html',
      htm: 'text/html',
      zip: 'application/zip',
      tar: 'application/x-tar',
      rar: 'application/vnd.rar',
      // Add more as needed...
    };

    return mimeTypes[extension] || 'application/octet-stream';
  },

  /**
   * @description: 北元用 生成通知代办接口sign
   * @param {*} params.apiName 接口名称
   * @param {*} params.params 请求参数
   */
  async signAkSk({ apiName = '', params = {} }) {
    const { app } = this;
    try {
      if (app.config.branch !== 'by') return;
      const APIS = {
        sendNotification: {
          uri: '/open-api/p/notification/v2/messages',
          method: 'POST',
          params: {
            businessCode: {
              type: 'string',
              required: true,
              description: '消息发送方业务编号',
            },
            businessName: {
              type: 'string',
              required: true,
              description: '消息发送方业务名称',
            },
            receivers: {
              type: 'Array',
              required: true,
              description: '消息接收人',
            },
            contents: {
              type: 'Array',
              required: true,
              description: '消息内容',
            },
          },
        },
        getNoiseDustEquipmentList: {
          uri: '/open-api/systemcode/v2/entities/oodm_ZYFC/values',
          method: 'GET',
          params: {
            current: {
              type: 'number',
              required: true,
              description: '当前页',
            },
            pageSize: {
              type: 'number',
              required: true,
              description: '每页数量',
            },
          },
        },
        getH2SPH3EquipmentList: {
          uri: '/open-api/systemcode/v2/entities/oodm_H2SPH3/values',
          method: 'GET',
          params: {
            current: {
              type: 'number',
              required: true,
              description: '当前页',
            },
            pageSize: {
              type: 'number',
              required: true,
              description: '每页数量',
            },
          },
        },
        getEquipmentRealTimeData: {
          uri: '/open-api/supos/oodm/v2/attribute/current',
          method: 'POST',
          params: {
            inputs: {
              type: 'array',
              required: true,
              description: '当前页',
            },
          },
        },
      };
      if (!app.config.byNotice) return;
      const { api_host: API_BASE_HOST, ak, sk } = app.config.byNotice;
      const headerJson = {
        'Content-Type': 'application/json;charset=utf-8',
      };
      const queryJson = APIS[apiName].method === 'GET' ? params : null;
      const wholeUrl = API_BASE_HOST + APIS[apiName].uri;
      integratesBy.signHeaderWithAkSk(
        APIS[apiName].uri,
        APIS[apiName].method,
        queryJson,
        headerJson,
        ak,
        sk
      );
      let res = null;
      if (APIS[apiName].method === 'GET') {
        res = await Axios({
          method: APIS[apiName].method,
          url: wholeUrl,
          headers: headerJson,
          params,
        });
      } else {
        console.log('params', params, wholeUrl, headerJson, queryJson);
        try {
          res = await Axios({
            method: APIS[apiName].method,
            url: wholeUrl,
            headers: headerJson,
            data: params,
          });
        } catch (error) {
          console.log('error', error, res.data);
        }
      }
      return res;
    } catch (error) {
      console.log(error);
    }

  },
  // json转查询串
  json2query(jsonObj) {
    let res = '';
    for (const key in jsonObj) {
      res += key + '=' + jsonObj[key] + '&';
    }
    return res.substring(0, res.length - 1);
  },

  /**
   * @description 获取最顶层企业id
   * @param {String} id 企业id
   * @return  {String} 最顶层企业id
   */
  async getTopEnterpriseId(id) {
    const { ctx } = this;
    const EnterpriseID = id || ctx.session.user.EnterpriseID;
    try {
      const topCompany = await ctx.model.Adminorg.aggregate([
        {
          $match: {
            _id: EnterpriseID,
          },
        },
        {
          $graphLookup: {
            from: 'adminorgs',
            startWith: '$parentId',
            connectFromField: 'parentId',
            connectToField: '_id',
            as: 'parentHierarchy',
            depthField: 'depth',
          },
        },
        {
          $unwind: '$parentHierarchy',
        },
        {
          $sort: {
            'parentHierarchy.depth': -1,
          },
        },
        {
          $group: {
            _id: '$_id',
            maxDepthParent: {
              $first: '$parentHierarchy',
            },
          },
        },
        {
          $addFields: {
            'maxDepthParent.label': {
              $cond: {
                if: {
                  $gt: [
                    {
                      $ifNull: [ '$maxDepthParent.shortName', null ],
                    },
                    null,
                  ],
                },
                then: '$maxDepthParent.shortName',
                else: '$maxDepthParent.cname',
              },
            },
            'maxDepthParent.children': {
              $cond: {
                if: {
                  $gt: [
                    {
                      $size: '$maxDepthParent.childrenId',
                    },
                    0,
                  ],
                },
                then: null,
                else: '$$REMOVE',
              },
            },
          },
        },
        {
          $project: {
            _id: 0,
            maxDepthParent: {
              id: '$maxDepthParent._id',
              label: '$maxDepthParent.label',
              children: '$maxDepthParent.children',
            },
          },
        },
        {
          $replaceRoot: {
            newRoot: '$maxDepthParent',
          },
        },
      ]);
      if (topCompany.length === 0) {
        const companyInfo = await ctx.model.Adminorg.findOne(
          { _id: EnterpriseID },
          { _id: 1 }
        ).lean();

        if (companyInfo) {
          return companyInfo._id;
        }
        return '';
      }
      return topCompany[0].id;
    } catch (error) {
      ctx.auditLog('获取公司失败', `${error}`, 'error');
      return []; // 在出错时返回空数组
    }
  },


  /**
   * 处理路径中的企业ID替换
   * @param {string} target - 目标路径
   * @return {Promise<{success: boolean, newTarget: string }>}
  */
  async processEnterprisePath(target) {
    if (this.ctx.app.config.tjReportEnterpriseID === '1') {
      return { success: true, newTarget: target };
    }
    const regex = /([\\/](?:enterprise|tjReport)[\\/])([^\\\/]+)/;
    const match = regex.exec(target);

    if (!match) {
      return { success: false, newTarget: target };
    }

    const id = match[2];

    try {
      const EnterpriseID = await this.getTopEnterpriseId(id) || id;
      const newTarget = target.replace(regex, `$1${EnterpriseID}`);
      return { success: true, newTarget };
    } catch (error) {
      return { success: false, newTarget: target };
    }
  },

};
