

module.exports = app => {

  const {
    router,
    controller,
  } = app;
  const branchCheck = require('../middleware/branchCheck')();
  router.use('/api/whData', branchCheck);
  // 获取系统验配置
  router.get('/api/getSystemConfig', controller.app.user.getSystemConfig);

  // 关于token等接口写在此路由和对应api控制器中
  // router.get('/api/address/list', controller.api.systemConfig.addressList);

  // 企业、行政、运营用户头像上传
  // router.post('/api/operateUser/uploadLogo', controller.api.operateUser.uploadLogo);

  // 企业培训记录
  router.post('/api/training/addPropagate', controller.api.propagate.add);
  router.post('/api/training/editPropagate', controller.api.propagate.update);
  router.post('/api/training/deletePropagate', controller.api.propagate.delete);
  // 在线监测 mqtt
  router.get('/mqtt/broker', controller.api.mqtt.broker); // 开启代理服务器
  router.get('/mqtt/closeBroker', controller.api.mqtt.onSIGINT);
  router.get('/mqtt/client', controller.api.mqtt.client);
  router.post('/mqtt/dataReception', controller.api.mqtt.dataReception);
  router.post('/mqtt/dataReception2', controller.api.mqtt.dataReception2);
  router.post('/mqtt/issueOrder', controller.api.mqtt.issueOrder);

  // 报告对接
  router.post('/api/report/reportData', controller.api.report.postReportData);
  router.post('/api/report/reportFile', controller.api.report.postReportFile);
  router.get('/api/report/reportData', controller.api.report.getCheckAssessment); // 获取检测结果
  router.post('/api/report/projectList', controller.api.report.projectList); // 获取检测项目列表

  // 大屏统计数据
  router.get('/api/jgAreaStatistics', controller.api.dashboard.jgStatistics);

  // 体检报告数据对接
  // router.post('/api/tjReportedUp', controller.api.report.tjReportedUp);
  // router.get('/aaa', controller.api.report.test);
  router.get('/api/physical/physicalExamOrg', controller.api.report.getPhysicalExamOrg); // 获取体检机构
  router.get('/api/physical/diagnosis', controller.api.report.getOdisease); // 获取诊断信息
  router.get('/api/physical/physicalExamInfo', controller.api.report.getPhysicalExamInfo); // 获取个人体检信息
  router.post('/api/physical/uploadHealthExamRecordFile', controller.api.healthCheckAppointment.uploadHealthExamRecordFile); // 上传个人体检信息（文件）

  // 判断数据库连接断开问题
  router.get('/api/heartbeat', controller.api.apiResource.adoConnection);
  // 导入福州用，以失效
  router.get('/api/report/createPhyFz', controller.api.report.createPhyFz);
  // router.get('/api/report/createEmployees', controller.api.report.createPhy2);

  // 北元对接 测试接口
  router.get('/api/byTest', controller.api.test.addTest);
  // 北元对接 oidc 回调小程序用
  router.get('/api/byCallback', controller.app.user.byCallback);
  router.get('/api/whcallback', controller.app.user.whCallback);
  router.get('/whh5', controller.app.user.whH5Enter);
  router.get('/wh', controller.app.user.whEnter);
  router.post('/api/healCheckResult', controller.api.healthMachine.healCheckResult);
  router.post('/api/healCheckFile', controller.api.healthMachine.healCheckFile);
  // 焦煤对接 企微 回调小程序用
  router.get('/api/wxWorkAuthAction', controller.app.user.wxWorkAuthAction);
  router.get('/api/validateUrlWxWorkAuthAction', controller.app.user.validateUrlWxWorkAuthAction);
  // 体检预约 体检系统对接
  router.put('/api/healthCheckAppointment/confirm', controller.api.healthCheckAppointment.confirmDate);
  router.get('/api/healthCheckAppointment/list', controller.api.healthCheckAppointment.list);
  router.get('/api/healthCheckAppointment', controller.api.healthCheckAppointment.getOne);
  router.post('/api/healthExamRecord', controller.api.healthCheckAppointment.healthExamRecordList); // 职业健康档案 - 国家标准
  router.post('/api/healthExamRecordWh', controller.api.whData.healthExamRecordList); // 职业健康档案（万华） - 国家标准
  router.post('/api/enterpriseInfo', controller.api.healthCheckAppointment.enterpriseInfoList); // 用人单位信息 - 国家标准
  router.post('/api/zjEmployer', controller.api.healthCheckAppointment.zjEmployerList); // 用人单位信息 - 浙江省标准
  router.post('/api/zjReportCard', controller.api.healthCheckAppointment.zjHealthExamRecordList); // 职业健康档案 - 浙江省标准
  router.post('/api/shaanxiReportCard', controller.api.healthCheckAppointment.shaanxiHealthExamRecordList); // 职业健康档案 - 陕西省标准
  // 山西焦煤体检系统对接
  router.get('/api/physical/tjPlanList', controller.api.sxccPhysicalAppointment.tjPlanList); // 山西焦煤获取体检计划列表
  router.get('/api/physical/tjPlanListDetails', controller.api.sxccPhysicalAppointment.tjPlanListDetails); // 山西焦煤获取体检计划单详情
  router.get('/api/physical/IndividualAppointment', controller.api.sxccPhysicalAppointment.IndividualAppointment); // 个人体检预约详情
  router.post('/api/physical/IndividualCheckIn', controller.api.sxccPhysicalAppointment.IndividualCheckIn); // 体检人员现场签到

  // 测试接口
  // router.get('/api/testEncrypt', controller.api.test.testEncrypt);
  router.get('/api/checkItems/getIndicator', controller.api.sxccPhysicalAppointment.getIndividualCheckIn); // 获取指标字典
  // 山西焦煤体检对接测试接口
  router.get('/api/sxccTest', controller.api.test.sxccTest);
  router.get('/api/sxccTestFile', controller.api.test.sxccTestFile);
  // 答卷完成更新体检状态
  router.post('/api/sxccPhysicalAppointment/handleCompletePhysicalExam', controller.api.sxccPhysicalAppointment.handleCompletePhysicalExam);

  // 万华基础数据对接
  router.post('/api/whData/getPersonData', controller.api.whData.getPersonData);
  router.post('/api/whData/getOrgData', controller.api.whData.getOrgData);
  // 接收账号数据
  router.post('/api/whData/getAccountData', controller.api.whData.getAccountData);
  // 接收万华岗位(管理组)数据
  router.post('/api/whData/getPostData', controller.api.whData.getPostData);
  // 接收万华岗位人员（管理组）数据
  router.post('/api/whData/getPostAndPersonData', controller.api.whData.getPostAndPersonData);
};
