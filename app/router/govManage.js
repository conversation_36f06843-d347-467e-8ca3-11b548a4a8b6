module.exports = app => {
  const {
    router,
    controller,
  } = app;
  // 行政端
  // 用户登录信息校验
  router.get('/govManage/superUser/loginVerification', controller.manage.superUser.loginVerification);
  // 退出登录
  router.get('/govManage/superUser/logOut', controller.manage.superUser.logOutAction);
  // 修改用户信息(手机号和密码)
  router.put('/govManage/superUser/updateInfo', controller.manage.superUser.updateInfo);
  // 获取执法脑图数据
  router.get('/govManage/guideMap/find', controller.manage.superUser.getGuideMap); // url同步pc端的
  // 获取培训统计数据 // url同步pc端的
  router.get('/govManage/adminTraining/getSatisticByYear', controller.manage.govAdminTraining.getSatisticByYear);
  router.get('/govManage/adminTraining/getCoverageLastManyYears', controller.manage.govAdminTraining.getCoverageLastManyYears);
  router.get('/govManage/adminTraining/getPlanList', controller.manage.govAdminTraining.getPlanList);

};
