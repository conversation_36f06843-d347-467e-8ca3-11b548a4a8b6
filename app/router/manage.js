module.exports = app => {
  const {
    router,
    controller,
  } = app;

  // 修改user手机号或密码
  router.put('/manage/user/updateInfo', controller.manage.user.updateInfo);
  // 修改user手机号、密码、姓名等个人信息
  router.post('/manage/user/updateInfo', controller.app.user.updateInfo);
  // 退出登录
  router.get('/manage/user/logOut', controller.manage.user.logOutAction);
  // 用于前端判断用户是否已登录
  router.get('/manage/user/loginVerification', controller.manage.user.loginVerification);
  // user上传头像
  router.post('/manage/user/uploadLogo', controller.manage.user.uploadLogo);
  // 更新user信息
  router.post('/manage/user/updateUserInfo', controller.manage.user.updateUserInfo);
  // 绑定微信
  router.post('/manage/user/bindWxInfo', controller.manage.user.bindWxInfo);
  // 根据用户手机号码匹配可绑定的企业
  router.get('/manage/user/getCompanysByPhoneNum', controller.manage.user.getCompanysByPhoneNum);
  // 用户绑定企业
  router.post('/manage/user/boundEnterprise', controller.manage.user.boundEnterprise);
  // 用户解绑当前企业
  router.get('/manage/user/unbindEnterprise', controller.manage.user.unbindEnterprise);
  // 查看体检信息
  router.post('/manage/user/physicalExamination', controller.manage.user.physicalExamination);
  router.post('/manage/user/confirmPhysicalExamination', controller.manage.user.confirmPhysicalExamination);
  router.post('/manage/user/getphysicalExaminationNotice', controller.manage.user.getphysicalExaminationNotice);
  router.post('/manage/user/uploadGuideForm', controller.manage.user.uploadGuideForm);
  router.post('/manage/user/confirmSelectHospital', controller.manage.user.confirmSelectHospital);
  router.post('/manage/user/deleteGuideForm', controller.manage.user.deleteGuideForm);
  // 获取用户权限
  router.get('/manage/user/getListByPower', controller.manage.user.listByPower);
  router.get('/manage/user/getLastPhysicalDataAndDeteData', controller.manage.user.getLastPhysicalDataAndDeteData);
  // 获取体检医院列表
  router.get('/manage/user/findAllReservation', controller.manage.user.findAllReservation);
  router.post('/manage/user/createReservation', controller.manage.user.createReservation);
  // 添加留言反馈建议
  router.post('/manage/comment/add', controller.manage.comment.add);
  // 获取留言反馈建议
  router.post('/manage/comment/get', controller.manage.comment.get);
  // 获取个人历年体检指标趋势
  router.get('/manage/user/indicatorsTrend', controller.manage.user.indicatorsTrend);
  // 获取所有体检指标
  router.get('/manage/user/getAllIndicators', controller.manage.user.getAllIndicators);
  // 获取问卷列表
  router.get('/manage/user/getQuestionnaireList', controller.manage.user.getQuestionnaireList);

  // 查询用户危害告知书
  // router.post('/manage/user/findRecordPDF', controller.manage.user.findRecordPDF);


  // ===========课程相关=============
  router.get('/manage/training/courses/getHotCourses', controller.manage.courses.getHotCourses);
  router.get('/manage/training/courses/getClassification', controller.manage.courses.getClassification);
  router.get('/manage/training/courses/getCourseOne', controller.manage.courses.getCourseOne);
  router.get('/manage/training/courses/refreshVideoUrl', controller.manage.courses.refreshVideoUrl);
  router.get('/manage/training/courses/getVideoUrl', controller.manage.courses.getVideoUrl);
  router.post('/manage/training/courses/updateCourseProgress', controller.manage.courses.updateCourseProgress);
  router.post('/manage/training/courses/likeCourse', controller.manage.courses.likeCourse);
  router.get('/manage/training/courses/getCourseByClass', controller.manage.courses.getCourseByClass);
  router.get('/manage/training/courses/searchCourse', controller.manage.courses.searchCourse);
  router.post('/manage/training/courses/creatComment', controller.manage.courses.creatComment);
  router.get('/manage/training/courses/getComments', controller.manage.courses.getComments);
  router.post('/manage/training/courses/createReply', controller.manage.courses.createReply);
  router.post('/manage/training/courses/likeComment', controller.manage.courses.likeComment);
  router.get('/manage/training/courses/updateCompleteState', controller.manage.courses.updateCompleteState);
  router.get('/manage/training/courses/getPauseTime', controller.manage.courses.getPauseTime);


  // 投诉建议
  router.post('/manage/complaints/add', controller.manage.complaints.add);
  router.get('/manage/complaints/get', controller.manage.complaints.get);
  router.post('/manage/complaints/update', controller.manage.complaints.update);
  router.get('/manage/complaints/delete', controller.manage.complaints.delete);
  router.get('/manage/complaints/getDetail', controller.manage.complaints.getDetail);
  router.get('/manage/complaints/getSupervision', controller.manage.complaints.getSupervision);
  router.post('/manage/complaints/scoring', controller.manage.complaints.scoring); // 评价

  // 问卷调查
  router.post('/manage/questionnaire/getPersonalQuestionnaire', controller.manage.questionnaire.getPersonalQuestionnaire);
  router.post('/manage/questionnaire/getPersonalQuestionnaireById', controller.manage.questionnaire.getPersonalQuestionnaireById);
  router.post('/manage/questionnaire/submitSurvey', controller.manage.questionnaire.submitSurvey);
  router.post('/manage/questionnaire/getQuestionnaireByCode', controller.manage.questionnaire.getQuestionnaireByCode);

  // 体检预约
  // 获取体检计划信息
  router.get('/manage/tjAppointment/getTjPlan', controller.manage.tjAppointment.getTjPlan);
  // 获取体检预约信息
  router.get('/manage/tjAppointment/getTjAppointment', controller.manage.tjAppointment.getTjAppointment);
  // 创建体检预约
  router.post('/manage/tjAppointment/createTjAppointment', controller.manage.tjAppointment.createTjAppointment);
  // 搜索员工信息
  router.post('/manage/tjAppointment/getTjEmployee', controller.manage.tjAppointment.getTjEmployee);
  // 取消体检预约
  router.post('/manage/tjAppointment/cancelTjAppointment', controller.manage.tjAppointment.cancelTjAppointment);
  // 更新体检预约
  router.post('/manage/tjAppointment/updateTjAppointment', controller.manage.tjAppointment.updateTjAppointment);
  // 获取必选检查项目列表
  router.get('/manage/tjAppointment/getRequiredCheckItemList', controller.manage.tjAppointment.getRequiredCheckItemList);
  // 获取职业病检查项目列表
  router.get('/manage/tjAppointment/getOccupationalHealth', controller.manage.tjAppointment.getOccupationalHealth);
  // 获取可选检查项目列表
  router.get('/manage/tjAppointment/getOptionalCheckItemList', controller.manage.tjAppointment.getOptionalCheckItemList);
  // 获取体检计划每日人数
  router.get('/manage/tjAppointment/getTjPlanCount', controller.manage.tjAppointment.getTjPlanCount);
  // 获取当前分支
  router.get('/manage/tjAppointment/getBranch', controller.manage.tjAppointment.getBranch);

  // 培训小程序
  router.get('/manage/pxapp/getPxOrgList', controller.manage.pxapp.getPxOrgList);
  router.get('/manage/pxapp/getHomePxRecords', controller.manage.pxapp.getHomePxRecords);
  router.get('/manage/pxapp/getPxRecords', controller.manage.pxapp.getPxRecords);
  router.get('/manage/pxapp/getPxDetail', controller.manage.pxapp.getPxDetail);
  router.get('/manage/pxapp/statistics', controller.manage.pxapp.getMyStatistics);
  router.post('/manage/pxapp/trainingClass', controller.manage.pxapp.getTrainingClass);
  router.get('/manage/pxapp/getCertificate', controller.manage.pxapp.getCertificate);

  // 检修维护
  router.get('/manage/diseasesOverhaul/list', controller.manage.diseasesOverhaul.list);
  router.post('/manage/diseasesOverhaul/add', controller.manage.diseasesOverhaul.add);
  router.put('/manage/diseasesOverhaul/update', controller.manage.diseasesOverhaul.update);
  router.delete('/manage/diseasesOverhaul/delete', controller.manage.diseasesOverhaul.delete);
  router.get('/manage/diseasesOverhaul/getWorkplace', controller.manage.diseasesOverhaul.getWorkplace);
  router.get('/manage/diseasesOverhaul/searchPerson', controller.manage.diseasesOverhaul.searchPerson);
  // 获取问卷答卷地址
  router.get('/manage/tjAppointment/getQuestionnaireUrl', controller.manage.tjAppointment.getQuestionnaireUrl);

  // 文件上传测试 (pipe)
  router.post('/manage/file/upload', controller.manage.file.upload);

  // 文件删除测试 (deleteObject)
  router.delete('/manage/file/delete', controller.manage.file.delete);

  // 文件下载测试 (fGetObject)
  router.post('/manage/file/download', controller.manage.file.download);

  // 获取文件URL测试 (concatenatePath)
  router.post('/manage/file/getUrl', controller.manage.file.getUrl);

  // 福州数据处理日志管理相关路由 - 简化版本，只保留串行处理核心功能
  // 日志管理模块
  router.get('/manage/fzData/logs', controller.manage.fzDataFix.getLogs);
  router.get('/manage/fzData/stats', controller.manage.fzDataFix.getStats);
  router.get('/manage/fzData/taskDetails', controller.manage.fzDataFix.getTaskDetails);
  // 任务操作模块
  router.post('/manage/fzData/createTasks', controller.manage.fzDataFix.createTasks);
  router.post('/manage/fzData/processSingleTask', controller.manage.fzDataFix.processSingleTask);
  router.post('/manage/fzData/continue-processing', controller.manage.fzDataFix.continueProcessing);
  router.post('/manage/fzData/retry', controller.manage.fzDataFix.retryFailedTasks);
  router.post('/manage/fzData/test/company', controller.manage.fzDataFix.testCompanyDataCleaning);
  router.post('/manage/fzData/test/healthCheck', controller.manage.fzDataFix.testHealthCheckDataCleaning);
  router.post('/manage/fzData/test/diagnosis', controller.manage.fzDataFix.testDiagnosisDataCleaning);

  // 文件管理模块
  router.get('/manage/fzData/files', controller.manage.fzDataFix.getFiles);
  router.get('/manage/fzData/fileDetails', controller.manage.fzDataFix.getFileDetails);
  router.delete('/manage/fzData/cache', controller.manage.fzDataFix.clearCache);

  // 串行处理模块（核心功能）
  router.post('/manage/fzData/serial/company', controller.manage.fzDataFix.serialProcessCompany);
  router.post('/manage/fzData/serial/healthCheck', controller.manage.fzDataFix.serialProcessHealthCheck);
  router.post('/manage/fzData/serial/diagnosis', controller.manage.fzDataFix.serialProcessDiagnosis);

  // 进度监控（简化版）
  router.get('/manage/fzData/progress', controller.manage.fzDataFix.sseProgress);
  router.get('/manage/fzData/progress/:taskId', controller.manage.fzDataFix.sseProgressByTaskId);

  // 任务队列管理
  // router.get('/manage/fzData/queue/stats', controller.manage.fzDataFix.getQueueStats);
  // router.post('/manage/fzData/queue/start', controller.manage.fzDataFix.startTaskProcessor);
  // router.post('/manage/fzData/queue/stop', controller.manage.fzDataFix.stopTaskProcessor);
  // router.post('/manage/fzData/task/retry/:taskId', controller.manage.fzDataFix.retryTask);
  // router.post('/manage/fzData/task/cancel/:taskId', controller.manage.fzDataFix.cancelTask);

  // Redis任务管理
  router.get('/manage/fzData/redis/tasks', controller.manage.fzDataFix.getRedisTaskList);
  router.get('/manage/fzData/redis/stats', controller.manage.fzDataFix.getRedisQueueStats);
  router.post('/manage/fzData/redis/cleanup', controller.manage.fzDataFix.cleanupRedisTasks);

  // 手动数据同步
  router.post('/manage/fzData/manual/getAllZipFromApi', controller.manage.fzDataFix.manualGetAllZipFromApi);

  // 解密ZIP包功能
  router.get('/manage/fzData/decrypt/files', controller.manage.fzDataFix.getDecryptFiles);
  router.post('/manage/fzData/decrypt/zip', controller.manage.fzDataFix.decryptZipFile);
  router.post('/manage/fzData/decrypt/task', controller.manage.fzDataFix.decryptTaskZip);
};

