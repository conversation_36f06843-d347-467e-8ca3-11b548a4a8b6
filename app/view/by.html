<html>

<head>
    <title>跳转登录</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1, maximum-scale=1" />
    <script>
        window.onerror = (e) => {
            console.error(e);
            alert('发生错误' + e);
        };
    </script>
    <!-- <script src="https://cdn.jsdelivr.net/npm/eruda"></script>
    <script>eruda.init();</script> -->
    <script type="text/javascript" src="{{staticRootPath}}/plugins/uniapp/vconsole.min.js"></script>
    <script type="text/javascript" src="{{staticRootPath}}/plugins/uniapp/jwxwork-1.0.0.js" referrerpolicy="origin"></script>
    <script type="text/javascript" src="{{staticRootPath}}/plugins/uniapp/forge.min.js"></script>
    <script type="text/javascript" src="{{staticRootPath}}/plugins/jquery/1.10.2/jquery.min.js" ></script>
    <script src="https://res.wx.qq.com/open/js/jweixin-1.6.0.js" referrerpolicy="origin"></script>
    <!-- <script src="{{staticRootPath}}/plugins/uniapp/jweixin-1.6.0.js"></script> -->
    <script>
        new VConsole(); //手机调试的控制台
        // token从https://qyapi.weixin.qq.com/cgi-bin/gettoken get 方法获取
        // ticket从https://qyapi.weixin.qq.com/cgi-bin/get_jsapi_ticket get 方法获取
        let corpid = "{{corpid}}"; //企业ID
        let agentid = "{{agentid}}"; //应用ID
        let ticket = "{{ticket}}"; //应用ticket
        let appid = "{{appid}}"; //小程序appid
        console.log('ticket', ticket);
        console.log('appid', appid);
        console.log('agentid', agentid);
        console.log('corpId', corpid);
        let timeStamp = new Date().getTime();
        let obj = {
            jsapi_ticket: ticket, //应用ticketg
            noncestr: getNonceStr(), //随机字符串, wx.agentConfig内的nonceStr值要与此值一致
            timestamp: timeStamp, //时间戳, wx.agentConfig内的timestamp值要与此值一致
            url: window.location.href, //当前网页的url
        };
        function  getNonceStr() {
            let str = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
            let nonceStr = '';
            for (let i = 0; i < 16; i++) {
                let index = Math.floor(Math.random() * str.length);
                nonceStr += str[index];
            }
            return nonceStr;
        }
        let signature = getSignature(obj); //签名权限
        const userData = JSON.parse('{{data | safe}}');
        // console.log('userData', userData);
        //wx 引入 企业微信的js-sdk
        // const wx = window.jWeixin;
        setTimeout(() => {
            wx.agentConfig({
                corpid: corpid, // 必填，企业微信的corpid，必须与当前登录的企业一致
                agentid:agentid , // 必填，企业微信的应用id
                timestamp: timeStamp, // 必填，生成签名的时间戳
                nonceStr: obj.noncestr, // 必填，生成签名的随机串
                signature: signature, // 必填，签名，见附录-JS-SDK使用权限签名算法
                jsApiList: ['launchMiniprogram'], //必填
                success: function (res) {
                    console.log(res + 'agentConfig成功回调');
                    wx.invoke(
                        'launchMiniprogram',
                        {
                            appid: appid, // 要打开的小程序的appid
                             // 所需跳转的小程序内页面路径及参数token
                            path: 'pages/login/login?data=' + JSON.stringify(userData),
                        },
                        function (res) {
                            if (res.err_msg == 'launchMiniprogram:ok') {
                                // 正常
                                wx.closeWindow();
                            } else {
                                // 错误处理
                            }
                        }
                    );
                },
                fail: function (res) {
                    console.log('agentConfig失败回调', res);
                    if (res.errMsg.indexOf('function not exist') > -1) {
                        alert('版本过低请升级');
                    }
                },
            });
        }, 500);
        function getSignature(obj) {
            let sign =
                'jsapi_ticket=' +
                obj.jsapi_ticket +
                '&noncestr=' +
                obj.noncestr +
                '&timestamp=' +
                obj.timestamp +
                '&url=' +
                obj.url;
            return sha1(sign);
        }
        function sha1(str) {
            var md = forge.md.sha1.create();
            md.update(str, 'utf8');
            return md.digest().toHex();
        }
    </script>
</head>

<body>
    <div class="public-web-container breathe">
        <p>正在跳转登录中...</p>
    </div>
    <style>
        .public-web-container {
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        @keyframes breathe {
            0% {
                opacity: 0;
            }

            50% {
                opacity: 1;
            }

            100% {
                opacity: 0;
            }
        }
    </style>
</body>

</html>