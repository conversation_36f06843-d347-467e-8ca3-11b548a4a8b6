/**
 * Created by ML on 2020/11/16
 * 机构员工信息表
 */

module.exports = app => {
  const shortid = require('shortid');
  // 员工信息表
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;
  const serviceEmployee = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    hasRecocrd: { type: Boolean, default: false }, // 是否已生成档案
    departs: [{ type: Array, ref: 'ServiceDingtrees' }], // 部门
    EnterpriseID: { type: String, ref: 'ServiceOrg' }, // 机构id
    status: { type: Number, default: 1 }, // 1表示在岗、0表示离岗
    name: String, // 员工姓名
    certificate: [
      { // 个人资质信息
        _id: {
          type: String,
          default: shortid.generate,
        },
        professional: String, // 专业方向
        technical: String, // 技术职称
        coding: String, // 资质编号
      },
    ],
    qualificationImg: [{ type: String }], // 资质证书图片
    gender: String, // 员工性别 1女  0男
    nativePlace: String, // 籍贯
    IDNum: String, // 身份证号
    phoneNum: String, // 手机号码
    // education: String, // 文化程度
    // hobby: String, // 嗜好
    // workingSystem: String, // 工作制度
    station: String, // 岗位
    // workType: String, // 工种
    workStart: Date, // 开始工作时间
    workYears: String, // 工龄
    headImg: String, // 头像
    // marriage: String, // 婚姻
    // email: String, // 邮箱
    // age: Number, // 年龄
    signPath: String, // 签名地址
    rangeOfBusiness: Array, // 业务区域
    appListType: String, // app 首页的列表类型
  }, { timestamps: true });
  return mongoose.model('ServiceEmployee', serviceEmployee, 'serviceEmployee');
};
