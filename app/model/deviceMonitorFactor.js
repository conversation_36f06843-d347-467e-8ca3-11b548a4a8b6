const shortid = require('shortid');
module.exports = app => {
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;

  const deviceMonitorFactorSchema = new Schema(
    {
      _id: {
        type: String,
        default: shortid.generate,
      },
      key: { type: String, required: true }, // 因子key
      name: { type: String, required: true }, // 因子名称
      code: { type: String }, // 因子编码
      unit: { type: String, required: true }, // 单位
      limitValue: { type: Number }, // 超标限值
      description: { type: String }, // 描述
      quality: { type: Number }, // 分子质量
      isConversion: { type: Boolean, default: false }, // 是否需要转换
    },
    { timestamps: true }
  );

  return mongoose.model(
    'DeviceMonitorFactor',
    deviceMonitorFactorSchema,
    'deviceMonitorFactors'
  );
};
