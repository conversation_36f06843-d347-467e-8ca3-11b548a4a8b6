/**
 * 课程分类
 */

module.exports = app => {
  const mongoose = app.mongoose;
  const shortid = require('shortid');
  const Schema = mongoose.Schema;
  // const moment = require('moment');
  // const CryptoJS = require('crypto-js');

  const CourseClassificationSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    parentID: {
      type: String,
      default: '0',
    }, // 父ID，最高级0，参考district
    level: {
      type: Number,
      default: 1,
    }, // 等级
    name: {
      type: String,
      default: '',
    }, // 分类名称
    author: {
      type: String,
      ref: 'OperateUser',
    }, // 创建这个分类的运营账号ID
    createdTime: {
      type: Date,
      default: Date.now,
    }, // 分类创建时间
    explain: {
      type: String,
      default: '',
    }, // 分类介绍或说明
  });

  return mongoose.model('CourseClassification', CourseClassificationSchema, 'CourseClassification');


};

