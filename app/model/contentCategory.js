/**
 * Created by Zhanglc on 2022/3/18.
 * 知识库文章分类
 */

module.exports = app => {
  const mongoose = app.mongoose;
  const shortid = require('shortid');
  const Schema = mongoose.Schema;
  const moment = require('moment');

  const ContentCategorySchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    uid: { // 用户ID
      type: Number,
      default: 0,
    },
    name: String, // 姓名
    keywords: String, // 关键字
    type: {
      type: String,
      default: '1',
    }, // 类别类型默认1,2单页面
    sortId: {
      type: Number,
      default: 1,
    }, // 排序 正整数
    parentId: { // 父级
      type: String,
      default: '0',
    },
    enable: {
      type: Boolean,
      default: true,
    }, // 是否公开 默认公开
    date: { // 创建时间
      type: Date,
      default: Date.now,
    },
    contentTemp: {
      type: String,
      ref: 'TemplateItems',
    }, // 内容模板
    defaultUrl: {
      type: String,
      default: '',
    }, // seo link
    homePage: {
      type: String,
      default: 'ui',
    }, // 必须唯一
    sortPath: {
      type: String,
      default: '0',
    }, // 存储所有父节点结构
    comments: String, // 描述
    sImg: { // 分类图片
      type: String,
    },
  });

  ContentCategorySchema.index({
    creator: 1,
  }); // 添加索引


  ContentCategorySchema.set('toJSON', {
    getters: true,
    virtuals: true,
  });
  ContentCategorySchema.set('toObject', {
    getters: true,
    virtuals: true,
  });

  ContentCategorySchema.path('date').get(function(v) {
    return moment(v).format('YYYY-MM-DD HH:mm:ss');
  });

  return mongoose.model('ContentCategory', ContentCategorySchema, 'contentcategories');
};
