/**
 * policy权限表
 */

module.exports = app => {
  const mongoose = app.mongoose;
  const shortid = require('shortid');
  const Schema = mongoose.Schema;

  const PolicySchema = new Schema(
    {
      _id: {
        type: String,
        default: shortid.generate,
      },
      name: String, // 管理员名称
      user_ids: Array, // 用户组ID
      scope_type: {
        type: String,
      }, // Enterprise, Dingtree, MillConstruction
      enterprise_ids: Array, // 企业ID
      dingtree_ids: Array, // 部门ID
      millConstruction_ids: Array, // 工作场所ID
      group_ids: Array, // 角色组ID
      enable: {
        // 是否可编辑
        type: Boolean,
        default: true,
      },
      enterpriseScopeType: {
        type: Number,
      },
      dingtreeScopeType: {
        type: Number,
      },
      millScopeType: {
        type: Number,
      },
      // 是否是超级管理员
      isSuper: {
        type: Boolean,
        default: false,
      },
      sortId: {
        type: Number,
        default: 0,
      },
      lastOperator: {
        type: String,
      },
    },
    {
      timestamps: true,
    }
  );

  return mongoose.model('Policy', PolicySchema);
};
