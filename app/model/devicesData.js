module.exports = app => {
  const shortid = require('shortid');
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;
  // 在线监控的近期详细数据
  const devicesDataSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    deviceType: { // 检测的危害因素类别
      type: Number, // 1是噪声 2 粉尘
      require: true,
    },
    deviceID: { // 设备序列号
      type: String,
      require: true,
      index: true,
    },
    data: {
      LAFp: Number,
      LCFp: Number,
      LZFp: Number,
      LASp: Number, // 噪声取的是这个值作为value
      LCSp: Number,
      LZSp: Number,
      Lp: Number,
      value: Number,
      status: String,
    },
    dateTime: {
      type: Date,
      default: Date.now,
    },
  });


  return mongoose.model('DevicesData', devicesDataSchema, 'devicesData');
};
