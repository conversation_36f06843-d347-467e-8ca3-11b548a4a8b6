/**
 * 全流程个人剂量项目表
 */


module.exports = app => {
  const mongoose = app.mongoose;
  const shortid = require('shortid');
  const Schema = mongoose.Schema;

  // 定义子 Schema,项目表主子 Schema 在下面
  const progressSchema = new Schema({
    createProject_time: { // 项目创建时间
      status: { // 0 未完成 1 进行中 2 已完成已完成
        type: Number,
        default: 2,
      },
      completedTime: {
        type: Date,
        default: Date.now,
      },
    },
    approved: { // 合同评审状态
      status: { // 0 未创建 1 进行中（审批中） 2 已完成（已同意）3 已拒绝  4 被终止  5 已取消
        type: Number,
        default: 0,
      },
      completedTime: Date, // 状态变化时间
    },
    personInCharge: { // 检测项目经理分配状态
      status: { // 0 未完成 1 进行中 2 已完成
        type: Number,
        default: 0,
      },
      completedTime: Date, // 完成时间
    },
    samplingScheme: { // 方案
      status: { // 0 未完成 1 进行中 2 已完成
        type: Number,
        default: 0,
      },
      completedTime: Date, // 完成时间
    },
    samplingSchemeReview: { // 方案审核状态
      status: { // 0 未完成 1 进行中 2 已完成
        type: Number,
        default: 0,
      },
      completedTime: Date, // 完成时间
    },
    scene: { // 现场调查状态
      status: { // 0 未完成 1 进行中 2 已完成
        type: Number,
        default: 0,
      },
      completedTime: Date, // 完成时间
    },
    instrumentsApply: { // 仪器申领
      status: { // 0 未申领 1 未分配 2 已分配
        type: Number,
        default: 0,
      },
      completedTime: Date, // 状态变化时间
    },
    spotRecord: { // 现场检测状态
      status: { // 0 未完成 1 进行中 2 已完成
        type: Number,
        default: 0,
      },
      completedTime: Date, // 状态变化时间
    },
    originalFile: { // 原始记录单
      status: { // 0 未完成 1 进行中 2 已完成
        type: Number,
        default: 0,
      },
      completedTime: Date, // 状态变化时间
    },
    originalReview: {
      status: { // 0 未完成 1 进行中 2 已完成
        type: Number,
        default: 0,
      },
      completedTime: Date, // 状态变化时间
    }, // 原始记录复核状态
    reportAnalysis: { // 报告分析状态
      status: { // 0 未完成 1 进行中 2 已完成
        type: Number,
        default: 0,
      },
      completedTime: Date, // 完成时间
    },
    industrialReport: { // 报告生成状态
      status: { // 0 未生成 1 进行中 2 已生成
        type: Number,
        default: 0,
      },
      completedTime: Date, // 完成时间 每次生成报告单的时间
    },
    reportUpload: { // 报告上传状态
      status: { // 0 未生成 1 进行中 2 已生成
        type: Number,
        default: 0,
      },
      completedTime: Date, // 完成时间 每次生成报告单的时间
    },
    reportApproved: { // 报告单审批状态
      status: { // 0 未创建 1 进行中（审批中） 2 已完成（已同意）3 已拒绝  4 被终止
        type: Number,
        default: 0,
      },
      completedTime: Date, // 完成时间
    },
    reportIssue: { // 报告签发
      status: { // 0 未签发 2 已签发
        type: Number,
        default: 0,
      },
      completedTime: Date, // 完成时间
    },
    printReport: { // 报告打印状态
      status: { // 0 未打印 2 已打印
        type: Number,
        default: 0,
      },
      completedTime: Date, // 完成时间
    },
    pigeonhole: { // 归档状态
      status: { // 0 未完成 2 已完成
        type: Number,
        default: 0,
      },
      completedTime: Date, // 完成时间
    },
  }, { _id: false });
  // 主文档 Schema
  const IndustrialRayProjectSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    startAt: Date, // 项目启动时间
    parentId: { // 项目父级id，根级是0
      type: String,
      default: '0',
    },
    hasChildren: { // 是否有子项目
      type: Boolean,
      default: false,
    },
    subProjects_type: {
      type: String,
      default: '',
    },
    pigeonholeCatalogue: { // 归档目录
      name: { type: String, default: '' }, // 文件夹名称
      url: { type: String, default: '' }, // 文件夹下载地址
    },
    pigeonholeFile: { // 归档文件夹
      name: { type: String, default: '' }, // 文件夹名称
      url: { type: String, default: '' }, // 文件夹下载地址
    },
    sampleListFile: { // 流转单
      name: { type: String, default: '' }, // 文件夹名称
      url: { type: String, default: '' }, // 文件夹下载地址
    },
    inspectionReportFile: { // 检测报告文件
      name: String,
      url: String,
    },
    firstDraftFile: { // 定期报告文件
      name: String,
      url: String,
    },
    testReportFile: { // 上传/确定 的检测报告文件
      name: String,
      url: String,
    },
    reportFile: { // 上传/确定 的定期报告文件
      name: String,
      url: String,
    },
    testReportUploadFileName: { // 检测报告正式稿
      name: { type: String, default: '' }, // 文件夹名称
      url: { type: String, default: '' }, // 文件夹下载地址
    },
    periodicalReportUploadFileName: { // 定期报告正式稿
      name: { type: String, default: '' }, // 文件夹名称
      url: { type: String, default: '' }, // 文件夹下载地址
    },
    reportRecordUploadFile: { // 报告审核记录表
      staticName: { type: String },
      url: { type: String },
    },
    issuer: { // 报告签发人
      name: String,
      employeeId: String, // serviceEmployee id
    },
    issueDate: Date, // 报告签发日期
    completeReportArchive: {
      status: { // 0 未完成 1 已完成
        type: Number,
        default: 0,
      }, // 是否确认签收归档 默认不签收0 已经签收1
      completedTime: Date, // 完成时间
    },
    reportArchiveSigner: {
      signPath: String, // 签名
      serviceEmployeeId: String,
    }, // 档案签收人
    // ==================== 受检单位信息
    EnterpriseID: { type: String, ref: 'Adminorg' }, // 企业ID
    EnterpriseName: { type: String }, // 企业名称
    companyID: {
      type: String,
    },
    corp: String, // 受检单位法人
    regType: String, // 受检单位注册类型/经济类型
    companyScale: {
      type: String,
      enum: [ '大型', '中型', '小型', '微型', '其他' ],
    },
    companyContact: {
      type: String,
      default: '',
    }, // 受检单位联系人
    companyContactPhoneNumber: {
      type: String,
      default: '',
    }, // 受检单位联系人手机号码
    companyContactEmail: {
      type: String,
      default: '',
    }, // 受检单位邮箱
    companyIndustry: {
      type: Array,
      default: [],
    }, // 受检单位'所属行业',
    riskLevel: {
      type: String,
    }, // 受检单位风险等级，初始时从adminorg中的level来，随行业分类（companyIndustry）而改变
    regAdd: String, // 注册地址
    districtRegAdd: {
      type: Array,
      default: [],
    }, // 受检单位注册地址
    // 申报地址，由于用户可以自行修改，所以和下面的workAdd区别开，
    // 存area_code，该code用于后注册的行政端也能读取申报的项目列表，
    // 存area_code前六位，即区县代码有效位数，其父级单位通过代码即可找到，全国的则需特殊处理
    workPlaces: [{ // 受检单位的工作场所地址
      _id: {
        type: String,
        default: shortid.generate,
      },
      workAddName: String, // 工作场所的中文地址

      name: String, // 工作场所的名称
      workAdd: {
        type: Array,
      }, // 工作场所具体地址，area_code
      checked: {
        type: Boolean,
        default: true,
      }, // 是否被检测，默认一般是检测了
    }], // 检测的工作场所，用于识别
    EntrustClientID: { type: String, ref: 'EntrustClient' }, // 委托单位合同ID
    // ==================== 委托单位信息
    anthemEnterpriseID: {
      type: String,
      ref: 'Adminorg',
    }, // 委托单位id
    anthemCompanyID: {
      type: String,
    }, // 委托单位ID ，取信用代码
    newAnthemCompany: {
      type: String,
    }, // 新增的委托单位
    anthemCompanyName: {
      type: String,
      default: '',
    }, // 委托单位名称
    anthemCompanyContact: {
      type: String,
      default: '',
    }, // 用人单位联系人
    anthemCompanyContactPhoneNumber: {
      type: String,
      default: '',
    }, // 委托单位联系人手机号码
    anthemCompanyContactEmail: {
      type: String,
      default: '',
    }, // 委托单位邮箱
    anthemCompanyRegAdd: String, // 注册地址
    anthemCompanyDistrictRegAdd: {
      type: Array,
      default: [],
    }, // 委托单位注册地址
    // anthemCompanyAddress: [{
    //   districts: Array, // 营业执照注册地址
    //   address: String, // 具体地址
    //   _id: {
    //     type: String,
    //     default: shortid.generate,
    //   },
    // }], // 委托单位工作地址
    serviceOrgId: { type: String, ref: 'ServiceOrg' }, // 机构id
    serviceCon: {
      type: String,
      default: '',
    }, // 服务内容

    // ============================================项目相关信息
    preparedDate: Date, // 方案编制日期 方案确定日期
    projectName: { type: String }, // 检测项目名称
    shortProjectName: { type: String }, // 检测项目简称
    projectSN: { type: String }, // 检测项目编号
    serviceType: { type: String }, // 技术服务类型
    detectionType: { type: String }, // 检测类型根据服务类型而变化 定期检测/现状检测
    // expectStartTime: [ Date, Date ], // 预计开始时间 方案拟定日期
    expectStartTime: Date, // 预计开始时间 方案拟定日期 25.5.7由数组类型修改为Date类型
    expectStopTime: {
      type: Date,
    }, // 预计结束时间,
    requiredTime: {
      type: Date,
    }, // 要求/期望完成的时间
    detectionDate: [ Date, Date ], //  检测时间 和现场检测时间对应
    completeStatus: {
      type: Number,
      default: 1,
    }, // 完成状态，1，未完成； 2，已完成
    // applyTime: {
    //   type: Date,
    // }, // 上报时间
    completedTime: {
      type: Date,
    }, // 实际完成时间
    // projectStatus:{
    //   type:Number,
    //   default:0
    // }, // 项目状态 0 新创建 1 进行中 2 已完成 3 暂停 4 终止
    projectStop: {
      type: Boolean,
      dafault: false,
    }, // 项目是否暂停，false：未暂停，true：暂停
    projectCancel: {
      type: Boolean,
      dafault: false,
    }, // 项目是否终止，false：未终止，true：终止
    personInCharge: {
      type: String,
      default: '',
      ref: 'ServiceEmployee',
    }, // '项目负责人ID',
    archivePerson: { // 归档人
      type: String,
      default: '',
      ref: 'ServiceEmployee',
    },
    salesman: { // 业务经理 serviceemployeeId
      type: String,
      default: '',
    },
    farmOut: { // 是否分包lht+(chenke)
      type: Boolean,
    },
    farmOutParams: { // 分包参数
      type: String,
    },
    description: { // 项目简介
      type: String,
    },
    vipRequirement: { // vip需求
      type: String,
    },

    // =============钱money
    projectPrice: {
      type: Number,
      default: 0,
    }, // 项目价格(合同金额)
    cooperationFee: {
      type: Number,
      default: 0,
    }, // 合作费
    expectedCooperationFee: {
      type: Number,
      default: 0,
    }, // 预计合作费
    reviewFee: {
      type: Number,
      default: 0,
    }, // 评审费
    expectedReviewFee: {
      type: Number,
      default: 0,
    }, // 预计评审费
    otherFee: {
      type: Number,
      default: 0,
    }, // 其他费
    expectedOtherFee: {
      type: Number,
      default: 0,
    }, // 预计其他费
    netEarnings: {
      type: Number,
      default: 0,
    }, // 净赚=价格-合作费-评审费-其他费
    expectedNetEarnings: {
      type: Number,
      default: 0,
    }, // 预计净赚=价格-合作费-评审费-其他费
    outsourceFee: { // 分包费
      type: Number,
      default: 0,
    },
    expectedOutsourceFee: { // 预计分包费
      type: Number,
      default: 0,
    },

    // ============== 快递
    mailingAddress: Array, // 邮寄地址
    mailingAddressInfo: String, // 邮寄详细地址
    recipient: {
      type: String,
      default: '',
    }, // 收件人
    recipientPhoneNum: {
      type: String,
      default: '',
    }, // 收件号码

    // =============银行
    accountsBank: {
      type: String,
      default: '',
    }, // 开户行
    accountsAddress: {
      type: String,
      default: '',
    }, // 开户地址
    accountsNumber: {
      type: String,
      default: '',
    }, // 开户账号
    accountsPhoneNum: {
      type: String,
      default: '',
    }, // 开票电话
    tariffNumber: {
      type: String,
      default: '',
    }, // 税号
    isVIP: { // 会员类型：战略客户、vip客户、普通客户
      type: String, // svip、vip、ordinary
      default: 'ordinary',
    },
    source: {
      type: String,
      default: 'service', // ck || service
    }, // 数据来源
    isUrgent: {
      type: Boolean,
      default: false,
    }, // 是否加急
    comment: String, // 整个项目的备注
    serviceArea: [{
      type: String,
      match: /^[0-9]*$/,
    }], // 关于此次项目的企业涉及的技术服务领域，存得是编码，报送信息中要用 [采矿业:'1','化工、石化及医药':'2','冶金、建材':'3','机械制造、电力、纺织、建筑和交通运输等行业领域':'4','核设施':'5','核技术应用':'6']
    contractDate: { type: Date }, // 签订日期
    entrustDate: { type: Date }, // 委托时间
    projectType: String, // 项目类型
    checkType: String, // 检测类型
    reportCopies: { type: Number }, // 报告份数

    // radiationSourceType: String, // 辐射源类型
    templateName: String, // 模板名称
    environmentInfo: {
      startTemperature: String, //  检测开始温度
      endTemperature: String, // 检测结束温度
      starthumidity: String, //  检测开始湿度
      endhumidity: String, // 检测结束湿度
      pressure: String, // 气压
      weather: String, // 天气
    }, // 检测环境信息
    measurementSituation: {
      measureValues: [ Number ], // 测量值
      measurerange: [ Number, Number ],
      averageVal: String, // 平均值
      calibrationVal: String, // 校准值
      deviationValue: String, // 偏差值
    }, // 本底测量情况
    // 文件
    radiateDetectReportDate: Date, // 初稿时间

    // 审批实例id
    process_instance_id: String, // 合同评审审批实例ID
    samplingPlanApproveId: String, // 方案审核审批实例ID
    reportProcessInstanceId: String, // 报告单审批实例id
    officialDraftReportInstanceId: String, // 报告单审批实例ID
    modifyRecordFileName: String, // 归档修改记录单
    progress: progressSchema, // 项目进度
    // 审核记录
    auditRecordsFile: {
      name: String,
      url: String,
    },
    // 合同评审
    approvedWordFile: {
      name: String,
      url: String,
    },
    noticeData: { // 客户通知相关数据
      status: { // 通知状态，0：未通知 1：已通知
        type: Number,
        default: 0,
      },
      expirationDate: Date, // 有效期
      contactNumber: String, // 联系电话
      code: String, // 提取码
      downloadNumer: {
        type: Number,
        default: 0,
      }, // 下载次数
    },

    // instrument: [{
    //   instrumentName: String, // 仪器名
    //   internalNumber: String, // 仪器编号
    //   instrumentID: String, // 仪器id
    //   instrumentModel: String, // 仪器型号
    //   calibrationFactor: String, // 校准因子
    //   detectionLevel: String, // 探测水平
    //   inspectionCertificateId: String, // 检定证书id
    //   completeSampleTime: String, // 填写时间
    //   _id: {
    //     type: String,
    //     default: shortid.generate,
    //   },
    // }], // 仪器

    sceneTime: Date, // 调查时间
    checkPointImg: String, // 工艺流程图
    backgroundInstrumentId: String, // 本底校准仪器id
    backgroundSubInstrumentId: String, // 本底校准仪器id
    checkPeopleCount: {
      type: Number,
      default: 0,
    }, // 方案预计检测人数
    certHoldersCount: {
      type: Number,
      default: 0,
    }, // 持证人数
    assistantsCount: {
      type: Number,
      default: 0,
    }, // 助理人数
    instrumentsApplyId: { type: String, ref: 'RadiateDeviceApply' }, // 仪器申请Id
    detectionData: [{
      _id: {
        type: String,
        default: shortid.generate,
      },
      hasMachine: Boolean, // 是否存在机房
      remark: String, // 备注
      machineRoom: String, // 机房名称 机房和设备是一对一的关系
      machineRoomId: String, // 机房id
      // isMobileDevice: Boolean, // 是否可移动设备 如果是可移动设备，那么没有机房名称（会存储一条机房数据，只是名称为空）
      radiateParameterId: String, // 设备id
      radiateParameterName: String, // 设备名称
      // radiateDeviceTypeId: String, // 设备大类id
      // generalOrspecialized: String, // 通用或专用设备 general通用 specialized专用
      // generalParameterId: String, // 专用设备对应的通用设备id 如果当前选择的设备是专用设备才有这个字段
      // commonProjectName: String, // 专用或通用项目名称
      // testCriterion: String, // 检测方法
      // detectionSN: String, // 检测编号
      detectionSNs: Array, // 检测编号
      // instruments:[{
      //   instrumentId:String,// 仪器id
      //   certificateId:String,// 仪器证书编号
      // }],
      checkPointFile: String, // 布点图
      checkProjects: [{ // 检测项目
        _id: {
          type: String,
          default: shortid.generate,
        },
        checkProjectName: String, // 检测项目名称
        checkProjectId: String, // 检测项目id 关联参数表中project的id
        instrument: { // 仪器信息
          // _id: {
          //   type: String,
          //   default: shortid.generate,
          // },
          instrumentId: String, // 仪器id
          certificateId: String, // 仪器证书编号
        },
        checkConditionDatas: [ // 检测条件和点位信息
          {
            _id: {
              type: String,
              default: shortid.generate,
            },
            checkConditions: {
              checkConditionsOne: {
                voltage: Number, // 管电压
                current: Number, // 电流
                factoryActivity: String, // 源活度
                exposureTime: Number, // 曝光时间
                sealNuclide: String, // 源核素
              }, // kV ma min Bq
              calibrationFactor: Number, // 校准因子
            }, // 检测条件
            checkPointData: [{
              _id: {
                type: String,
                default: shortid.generate,
              },
              DistributeArea: String, // 检测点位置
              DistributeSN: String, // 检测点编号
              dataOne: String, // 读数1
              dataTwo: String, // 读数2
              dataThree: String, // 读数3
              aver: String, // 平均值
              result: String, // 检测结果
              conditionId: String, // 判定限值条件id
              limitSelectionPrecondition: String, // 前置条件
              limitVal: String, // 限值
              limitSymbol: String, // 限值条件 大于等于 小于等于 正负
              checkResult: String, // 符合 or 不符合
            }], // 布点信息
          },
        ],
      }],
      // instrument: [{
      //   instrumentName: String, // 仪器名
      //   internalNumber: String, // 仪器编号
      //   instrumentID: String, // 仪器id
      //   instrumentModel: String, // 仪器型号
      //   calibrationFactor: String, // 校准因子
      //   detectionLevel: String, // 探测水平
      //   inspectionCertificateId: String, // 检定证书id
      //   completeSampleTime: String, // 填写时间
      //   _id: {
      //     type: String,
      //     default: shortid.generate,
      //   },
      // }], // 仪器


    }],
    // 方案文件
    samplingSchemeFile: {
      name: String,
      url: String,
    },
    samplingSchemeFileName: String, // 方案文件名称
    // 现场调查文件
    sceneFile: {
      name: String,
      url: String,
    },
    // 现场照片
    scenePhotos: [
      {
        _id: {
          type: String,
          default: shortid.generate,
        },
        fileName: String,
        src: String,
        describe: String,
        latitude: String,
        longitude: String,
        address: String,
        createTime: Date,
        sceneType: String, // 照片类型：现场照片、标志物前照片
      },
    ],
    // 签名
    signManage: [{
      _id: {
        type: String,
        default: shortid.generate,
      },
      signRole: String, // 陪同人 采样人 测量人
      name: String, // 姓名
      signPath: String, // 签名文件储存位置
      signTime: Date, // 签名时间
    }],
    // 陪同人签名库
    companionLib: [{
      _id: {
        type: String,
        default: shortid.generate,
      },
      signRole: String, // 陪同人 采样人 测量人
      name: String, // 姓名
      signPath: String, // 签名文件储存位置
      signTime: Date, // 签名时间
    }],
    SurveysignManage: [{
      _id: {
        type: String,
        default: shortid.generate,
      },
      signRole: String, // 陪同人
      name: String, // 姓名
      signPath: String, // 签名文件储存位置
      signTime: Date, // 签名时间
    }],
    SurveycompanionLib: [{
      _id: {
        type: String,
        default: shortid.generate,
      },
      signRole: String, // 陪同人
      name: String, // 姓名
      signPath: String, // 签名文件储存位置
      signTime: Date, // 签名时间
    }],
    originalFileName: {
      name: String,
      url: String,
    }, // 原始记录文件
    detectPerson: [{
      type: String,
      default: '',
      ref: 'ServiceEmployee',
    }], // 检测人
    personPairings: [{
      type: String,
      default: '',
      ref: 'ServiceEmployee',
    }], // 现场检测复核人成员ID
    planReviewer: {
      type: String,
      default: '',
      ref: 'ServiceEmployee',
    }, // 方案审核人
    originalReviewer: {
      type: String,
      default: '',
      ref: 'ServiceEmployee',
    }, // 原始记录复核人成员ID
    // 项目参与人员信息-------------------------------
    personsOfProject: [{
      type: String,
      default: '',
      ref: 'ServiceEmployee',
    }], // '项目组成员ID',
    personsOfCompiling: {
      type: String,
      default: '',
      ref: 'ServiceEmployee',
    }, // 编制人
    personsOfReviewer: {
      type: String,
      default: '',
      ref: 'ServiceEmployee',
    }, // 审核人
    personsOfIssuer: {
      type: String,
      default: '',
      ref: 'ServiceEmployee',
    }, // 签发人
    surveyPerson: {
      type: String,
      default: '',
      ref: 'ServiceEmployee',
    }, // 现场调查 调查人
  }, { timestamps: true });
  return mongoose.model('industrialRayProject', IndustrialRayProjectSchema);

};

