
/*
 * @Author: 黄婷婷
 * @Date: 2020-06-01 09:00
 * @LastEditors: 肖小年
 * @LastEditTime: 2022-10-33 15:52
 * @Description: 企业端员工表
 *
 */
const encryptionPlugin = require('../utils/encryptionPlugin');
module.exports = app => {
  const shortid = require('shortid');
  // 员工信息表
  const ctx = app.createAnonymousContext();
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;
  const { isGroupBranch = '', dbEncryption = false, isUnique = true } = app.config;
  const employeeSchema = new Schema(
    {
      _id: {
        type: String,
        default: shortid.generate,
      },
      unitCode: {
        type: String,
        default: '',
      },
      userId: {
        // 对应的是users表中的id
        type: String,
        ref: 'User',
      },
      source: {
        type: String,
        default: 'oapi',
      }, // 文件存储来源端
      signatrue: String,
      management: Number, // 是否高管 1是 0 否
      hasRecocrd: { type: Boolean, default: false }, // 是否已生成档案
      departs:
        isGroupBranch === '1'
          ? [{ type: String, ref: 'Dingtrees', index: true }]
          : [[{ type: String, ref: 'Dingtrees', index: true }]], // 部门
      EnterpriseID: {
        // 企业id
        type: String,
        ref: 'Adminorg',
      },
      status: { type: Number, default: 1 }, // 1表示在岗、0表示离岗
      name: {
        type: String,
        index: true,
      }, // 员工姓名
      nameForStore: {
      // 加密姓名
        type: String,
      },
      nameSplitEncrypted: {
      // 分段加密的姓名
        type: String,
      },
      gender: String, // 员工性别 '0' 男, '1' 女
      userName: String, // 用户名
      nativePlace: String, // 籍贯
      IDNum: String, // 身份证号
      IDNumForStore: {
      // 用于加密存储的身份证号
        type: String,
      },
      IDNumSplitEncrypted: {
      // 分段加密的身份证号
        type: String,
      },
      phoneNum: String, // 手机号码
      phoneNumForStore: {
      // 用于加密存储的手机号
        type: String,
      },
      phoneNumSplitEncrypted: {
      // 分段加密的手机号
        type: String,
      },
      education: String, // 文化程度
      hobby: String, // 嗜好
      workingSystem: String, // 工作制度
      station: String, // 岗位
      workType: String, // 工种
      workStart: Date, // 开始工作时间
      workYears: String, // 工龄
      headImg: String, // 头像
      marriage: String, // 婚姻
      email: String, // 邮箱
      age: Number, // 年龄
      dingId: String, // 钉钉id
      laborDispatching: {
        // 劳务派遣  默认 非劳务派遣
        type: String,
        default: '否',
      },
      employmentInjuryInsuranceValidity: Number, // 工伤保险有效期
      departIds: [[{ type: String }]], // 钉钉部门id
      enable: { type: Boolean, default: true }, // 是否可用，用于假删除
      survivalStatus: {
        // 存活状态 true 存活， false 死亡
        type: Boolean,
        default: true,
      },
      deathDate: Date, // 死亡日期
      isInjured: { type: Boolean, default: false }, // 是否工伤期
      isInjuredSurgery: { type: Boolean, default: false }, // 是否工伤手术期
      isPregnancy: { type: Boolean, default: false }, // 是否孕期
      isLactation: { type: Boolean, default: false }, // 是否哺乳期
      encryptionAlgorithm: {
        type: String,
      },
    },
    { timestamps: true }
  );
  const phoneNumField = dbEncryption ? 'phoneNumForStore' : 'phoneNum';

  employeeSchema.index({ departs: 1 });
  employeeSchema.index({ name: 1 });
  // employeeSchema.index(
  //   { [phoneNumField]: -1 },
  //   {
  //     unique: isUnique,
  //     partialFilterExpression: { [phoneNumField]: { $exists: true } },
  //   }
  // );
  const indexOptions = {
    partialFilterExpression: { [phoneNumField]: { $exists: true } }, // 仅索引存在 phoneNum 字段的文档
  };

  if (isUnique) {
  // 如果 isUnique 为 true，设置唯一索引
    employeeSchema.index(
      { [phoneNumField]: -1 }, // 降序索引
      {
        ...indexOptions,
        unique: true, // 创建唯一索引，必须这么写
      }
    );
    employeeSchema.index(
      { [phoneNumField]: 1 },
      {
        ...indexOptions,
        unique: true, // 创建唯一索引，必须这么写
      }
    );
  } else {
  // 如果 isUnique 为 false，设置普通索引
    employeeSchema.index(
      { [phoneNumField]: -1 }, // 降序索引
      indexOptions // 不设置唯一约束
    );
    employeeSchema.index(
      { [phoneNumField]: 1 },
      indexOptions // 不设置唯一约束
    );
  }
  // employeeSchema.index(
  //   { [phoneNumField]: 1 },
  //   {
  //     unique: isUnique,
  //     partialFilterExpression: { [phoneNumField]: { $exists: true } },
  //   }
  // );
  employeeSchema.index(
    { userName: -1 },
    {
      unique: true,
      partialFilterExpression: { userName: { $exists: true } },
    }
  );
  employeeSchema.index(
    { userName: 1 },
    {
      unique: true,
      partialFilterExpression: { userName: { $exists: true } },
    }
  );
  employeeSchema.pre('updateOne', function(next) {
    const update = this.getUpdate();
    if (update.$set) {
      Object.keys(update.$set).forEach(key => {
        // 保留布尔值 false，只删除 null、undefined、空字符串等
        if (update.$set[key] === null || update.$set[key] === undefined || update.$set[key] === '') {
          delete update.$set[key];
        }
      });
    }
    next();
  });
  dbEncryption &&
    employeeSchema.plugin(encryptionPlugin, {
      fields: {
        name: 3,
        phoneNum: 11,
        IDNum: 18,
      },
      model: 'Employee',
      ctx,
    });

  return mongoose.model('Employees', employeeSchema, 'employees');
};
