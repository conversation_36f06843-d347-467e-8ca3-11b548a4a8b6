/**
 * 盖章记录
 */

module.exports = app => {
  const mongoose = app.mongoose;
  const shortid = require('shortid');
  const Schema = mongoose.Schema;


  const StampRecordSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    serviceOrgId: { // 机构id
      type: String,
      required: true,
    },
    modelId: {
      type: String,
      required: true,
    }, // 模型id
    modelName: {
      type: String,
      required: true,
    }, // 模型名称
    stampFile: {
      type: String,
      required: true,
    }, // 盖章文件
    stampFilePath: {
      type: String,
      required: true,
    }, // 盖章文件路径
    optServiceEmployeeId: {
      type: String,
      required: true,
    }, // 盖章员工id
  }, {
    timestamps: true,
  });

  return mongoose.model('stampRecord', StampRecordSchema, 'stampRecords');
};
