/*
 * @Author: 汪周强
 * @Date: 2022-03-11 14:29:27
 * @LastEditors: 汪周强
 * @LastEditTime: 2022-03-25 09:16:26
 * @Description:
 *
 */
module.exports = app => {
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;
  const shortid = require('shortid');

  const tjPlanSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    EnterpriseID: String, // 企业id
    EnterpriseName: String, // 企业名称
    physicalExaminationOrgID: {
      type: String,
      ref: 'PhysicalExamOrg',
    }, // 体检医院id
    physicalExaminationOrgName: String, // 体检医院名称
    checkType: { // 检查类型 0是上岗前 1是在岗 2是离岗 3复查 4应急  警告：用于过渡，3复查 废除
      type: String,
      require: true,
      default: '1',
      enum: [ '0', '1', '2', '3', '4' ],
    },
    checkStartDate: { // 体检开始时间
      type: Date,
      require: true,
    },
    checkEndDate: { // 体检结束时间
      type: Date,
    },

    employees: [{ // 体检人员
      _id: {
        type: String,
        default: shortid.generate,
      },
      employeeId: String, // 人员id
      departId: String, // 部门id
      departName: String, // 部门名称
      workSpaceId: String, // 工作场所id（和部门不会同时存在，存在工作场所id才会有危害因素）
      workSpaceName: String, // 工作场所名称
      harmFactors: [ String ], // 危害因素 [ '苯', '甲苯', '噪声' ]
      appointmentStatus: { // 预约状态，0是待预约 1是已预约
        type: Number,
        default: 0,
      },
      checkStatus: { // 体检状态，0是待体检 1是已体检, 2是体检中
        type: Number,
        default: 0,
      },
      isCheckIn: {
        type: String,
        default: '0',
      }, // 是否签到
      checkResultId: String, // 关联的体检结果id
    }],

    regionType: { // 选择的组织架构类型 - 部门 / 工作场所
      type: String, // depart/mill
    },
    selectNode: { // 选择的节点
      type: Object,
      default: {},
    },

    reviewStatus: { // 审核状态，0是待审核 1是通过 2是拒绝
      type: Number,
      default: 0,
    },

    selectionQuota: Number, // 选检额度

    createAt: { // 创建日期
      type: Date,
    },
    updateAt: { // 更新时间
      type: Date,
    },
    // 计划状态
    status: {
      type: Number,
      default: 1, // 1是进行中 0是已取消
    },
    // 年度
    year: {
      type: Number,
    },
    planLevel: { // 计划级别：group-集团, enterprise-基地/成员单位, mill-装置
      type: String,
      enum: [ 'group', 'enterprise', 'mill' ],
    },
    checkDates: [{ // 具体体检日期及配额
      date: Date,
      quota: {
        type: Number,
        default: 0,
      },
      _id: false,
      isAppoint: { // 已预约
        type: Number,
        default: 0,
      },
    }],
    parentId: String, // 父级id
    millId: String, // 装置id
  }, { timestamps: true });
  return mongoose.model('tjPlan', tjPlanSchema, 'tjPlan');
};
