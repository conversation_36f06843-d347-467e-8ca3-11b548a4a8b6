// 一个人学了一门课程，就产生一条记录。

module.exports = app => {
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;
  const shortid = require('shortid');

  const CoursesProgress = new Schema({ // 该表没用了！！！
    _id: {
      type: String,
      default: shortid.generate,
    },
    userID: {
      type: String,
      ref: 'User',
    },
    courseID: {
      type: String,
      ref: 'Courses',
    },
    like: {
      type: Boolean,
      default: false,
    }, // 是否点赞
    coursePlayProgress: [{
      _id: {
        type: String,
        default: shortid.generate,
      },
      chapterID: String, // 章节ID，存的是courses表里的sort里的_id
      videoProgress: {
        type: Number, //
        default: 0, // 非视频也可以是0，反正用不到
      },
    }], // 课程学习进度
    chapterPosition: {
      type: String, // 章节ID，存的是courses表里的sort里的_id
      default: '', // 就是上面数组的_id
    }, // 上次播放的章节位置， 空就为没开始学习。
  });

  return mongoose.model('coursesProgress', CoursesProgress, 'coursesProgress');
};
