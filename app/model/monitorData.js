const shortid = require('shortid');
module.exports = app => {
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;

  const monitorDataSchema = new Schema(
    {
      _id: {
        type: String,
        default: shortid.generate,
      },
      metadata: Schema.Types.Mixed,
      deviceID: { type: String, required: true },
      timestamp: { type: Date, default: Date.now },
      data: Schema.Types.Mixed,
    },
    {
      timeseries: {
        timeField: 'timestamp', // 时间字段
        metaField: 'metadata', // 元数据
        granularity: 'seconds', // 时间跨度
      },
      autoCreate: false,
      expireAfterSeconds: 86400,
    }
  );
  // 给timestamp 和 deviceID 创建索引
  monitorDataSchema.index({ timestamp: 1, deviceID: 1 });
  return mongoose.model('MonitorData', monitorDataSchema, 'monitorData');
};
