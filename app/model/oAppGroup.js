/**
 * 运营端-OAPI APP表
 */

module.exports = app => {
  const mongoose = app.mongoose;
  const shortid = require('shortid');
  const Schema = mongoose.Schema;

  const OAppGroupSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    agentId: { // apiUser的_id
      type: String,
      index: true,
      ref: 'ApiUser',
    },
    appKey: {
      type: String,
      default: '',
    },
    appSecret: {
      type: String,
      default: '',
    },
    name: String,
    comments: {
      type: String,
      default: '',
    },
    ips: {
      type: String,
      default: '',
    },
    apiPower: [{
      type: String,
      ref: 'ApiResource',
    }],
    enable: { // 是否有效
      type: Boolean,
      default: true,
    },
    date: {
      type: Date,
      default: Date.now,
    },
  });

  return mongoose.model('OAppGroup', OAppGroupSchema, 'oAppGroups');

};

