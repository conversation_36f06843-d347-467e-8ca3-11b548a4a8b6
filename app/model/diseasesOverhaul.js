/*
 * @Author: 汪周强
 * @Date: 2020-09-22 08:42:37
 * @LastEditors: 汪周强
 * @LastEditTime: 2022-03-25 14:00:54
 * @Description: 设施检修管理
 * 企业端 监管端
 */

module.exports = app => {
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;
  const shortid = require('shortid');

  const DiseasesOverhaulSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    EnterpriseID: String, // 公司id
    createTime: Date, // 创建时间
    workShop: Array, // 车间
    workShopHeader: String, // 车间负责人
    protectiveEquip: String, // 防护设备名称
    repairTime: Array, // 检查时间
    checkOpinion: String, // 验收意见
    checkHeader: String, // 验收负责人
    checkTime: Date, // 检查时间
    protectiveState: String, // 检修情况
    signImg: String, // 验收人签名图片
    creater: String, // 创建人
  });

  return mongoose.model('DiseasesOverhaul', DiseasesOverhaulSchema, 'diseasesoverhauls');
};
