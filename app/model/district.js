/**
 * Created by Administrator on 2015/4/15.
 * 管理员用户组对象
 */


module.exports = app => {
  const mongoose = app.mongoose;
  const shortid = require('shortid');
  const Schema = mongoose.Schema;
  const DistrictSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    id: String,
    parent_code: {
      type: String,
      index: true,
    },
    name: String,
    merger_name: String,
    area_code: {
      type: String,
      index: true,
    },
    city_code: String,
    lat: String,
    lng: String,
    level: String,
    short_name: String,
    pinyin: String,
    zip_code: String,
  });


  return mongoose.model('District', DistrictSchema, 'district');

};
