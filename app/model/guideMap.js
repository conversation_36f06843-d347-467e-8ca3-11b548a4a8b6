module.exports = app => {
  const shortid = require('shortid');
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;

  // 执法脑图
  const guideMapSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    superID: { // 监管id, 监管端创建的则需要
      type: String,
      ref: 'SuperUser',
    },
    content: Array,
    img: {
      type: String,
    },
  }, { timestamps: true });


  return mongoose.model('GuideMap', guideMapSchema, 'guideMap');
};
