const shortid = require('shortid');
module.exports = app => {
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;

  const deviceTypeSchema = new Schema(
    {
      _id: {
        type: String,
        default: shortid.generate,
      },
      name: { type: String, required: true },
      description: { type: String },
      parameters: Array, // 检测因素
    },
    { timestamps: true }
  );

  return mongoose.model('DeviceType', deviceTypeSchema, 'deviceTypes');
};
