/*
 * @Author: 汪周强
 * @Date: 2022-01-10 15:07:59
 * @LastEditors: 汪周强
 * @LastEditTime: 2022-08-16 13:50:27
 * @Description: 检测app版本管理
 * 全流程 运营
 */

module.exports = app => {
  // 检测app版本管理
  const mongoose = app.mongoose;
  const shortid = require('shortid');
  const Schema = mongoose.Schema;

  const appManageSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    appName: String,
    version: String, // 版本号
    log: String, // 更新日志
    importantUpdate: Boolean, // 是否重要更新
    platform: String, // 平台
    annex_app: [{
      _id: {
        type: String,
        default: shortid.generate,
      },
      fileType: String,
      name: String,
      staticSrc: String,
    }], // 安装包
    time: Date, // 发布时间
    status: {
      type: Boolean,
      default: false,
    }, // 是否发布
    createAt: { // 创建日期
      type: Date,
    },
    updateAt: { // 更新时间
      type: Date,
    },
  }, { timestamps: true });

  return mongoose.model('appManage', appManageSchema, 'appManage');
};
