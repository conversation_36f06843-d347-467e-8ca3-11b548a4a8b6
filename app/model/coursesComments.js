/**
 * 课程评论表
 */

module.exports = app => {
  const mongoose = app.mongoose;
  const shortid = require('shortid');
  const Schema = mongoose.Schema;

  const CoursesCommentsSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },

    level: {
      type: Number,
      default: 1,
    }, // 评论的类型，是最外边的，还是回复的，1最外边，2345678.....回复

    userID: {
      type: String,
      ref: 'User',
    }, // 作者ID

    content: {
      type: String,
      default: '',
    }, // 评论内容

    courseID: {
      type: String,
      ref: 'Courses',
    }, // 课程ID

    createDate: {
      type: Date,
      default: Date.now,
    }, // 评论时间

    allowToOpen: {
      type: Boolean,
      default: true,
    }, // 是否展示，这个目前用不到，万一要做审核，就用到，预留着免得跑库

    // images: [{
    //   url: {
    //     type: String,
    //   }
    // }], // 图片先去掉，

    replys: [{
      type: String,
      ref: 'CoursesComments',
    }],

    likes: {
      type: Number,
      default: 0,
    }, // 点赞数
    unlikes: {
      type: Number,
      default: 0,
    },

  });

  return mongoose.model('CoursesComments', CoursesCommentsSchema, 'CoursesComments');


};
