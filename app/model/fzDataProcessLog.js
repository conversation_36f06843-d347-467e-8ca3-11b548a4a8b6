/**
 * 福州数据处理日志表
 * 用于记录和追踪福州数据同步处理的详细信息，支持重放和恢复功能
 */

module.exports = app => {
  const mongoose = app.mongoose;
  const shortid = require('shortid');
  const Schema = mongoose.Schema;

  const FzDataProcessLogSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    taskId: {
      // 任务ID，用于关联同一批次的处理任务
      type: String,
      required: true,
      index: true,
    },
    taskType: {
      // 任务类型: 'company' | 'healthCheck' | 'diagnosis'
      type: String,
      required: true,
      enum: [ 'company', 'healthCheck', 'diagnosis' ],
      index: true,
    },
    dataSource: {
      // 数据源信息
      fileType: {
        // 文件类型: 'FromcrptDownLoad' | 'FrombhkDataDownLoad' | 'FromoccdiscaseDownLoad'
        type: String,
        enum: [ 'FromcrptDownLoad', 'FrombhkDataDownLoad', 'FromoccdiscaseDownLoad' ],
      },
      filePath: String, // 源文件路径
      fileName: String, // 文件名
      fileSize: Number, // 文件大小（字节）
      zoneCode: String, // 区域代码
    },
    processingInfo: {
      // 处理信息
      status: {
        // 处理状态: 'pending' | 'processing' | 'success' | 'failed' | 'skipped' | 'retrying'
        type: String,
        default: 'pending',
        enum: [ 'pending', 'processing', 'success', 'failed', 'skipped', 'retrying' ],
        index: true,
      },
      startTime: Date, // 开始处理时间
      endTime: Date, // 结束处理时间
      duration: Number, // 处理耗时（毫秒）
      retryCount: {
        // 重试次数
        type: Number,
        default: 0,
      },
      maxRetries: {
        // 最大重试次数
        type: Number,
        default: 3,
      },
    },
    dataStats: {
      // 数据统计
      totalRecords: {
        // 总记录数
        type: Number,
        default: 0,
      },
      processedRecords: {
        // 成功处理记录数
        type: Number,
        default: 0,
      },
      failedRecords: {
        // 失败记录数
        type: Number,
        default: 0,
      },
      skippedRecords: {
        // 跳过记录数
        type: Number,
        default: 0,
      },
    },
    result: {
      // 处理结果
      success: {
        type: Boolean,
        default: false,
      },
      message: String, // 结果消息
      errorInfo: {
        // 错误信息
        code: String, // 错误代码
        message: String, // 错误消息
        stack: String, // 错误堆栈
        details: Schema.Types.Mixed, // 详细错误信息
      },
    },
    replayInfo: {
      // 重放信息
      canReplay: {
        // 是否可以重放
        type: Boolean,
        default: true,
      },
      replayParams: Schema.Types.Mixed, // 重放参数
      lastReplayTime: Date, // 最后重放时间
      replayHistory: [{
        // 重放历史
        _id: {
          type: String,
          default: shortid.generate,
        },
        replayTime: Date, // 重放时间
        result: String, // 重放结果
        message: String, // 重放消息
      }],
    },
    metadata: {
      // 元数据
      version: {
        // 处理版本
        type: String,
        default: '1.0.0',
      },
      environment: String, // 环境信息
      processedBy: String, // 处理者
      correlationId: String, // 关联ID
      tags: [ String ], // 标签
      additionalInfo: Schema.Types.Mixed, // 额外信息
    },
  }, {
    timestamps: true, // 自动添加 createdAt 和 updatedAt
  });

  // 添加索引
  FzDataProcessLogSchema.index({ taskId: 1, taskType: 1 });
  FzDataProcessLogSchema.index({ 'processingInfo.status': 1, createdAt: -1 });
  FzDataProcessLogSchema.index({ 'dataSource.zoneCode': 1, createdAt: -1 });
  FzDataProcessLogSchema.index({ 'result.success': 1, 'processingInfo.retryCount': 1 });

  // 虚拟字段
  FzDataProcessLogSchema.virtual('successRate').get(function() {
    if (this.dataStats.totalRecords === 0) return 0;
    return ((this.dataStats.processedRecords / this.dataStats.totalRecords) * 100).toFixed(2);
  });

  FzDataProcessLogSchema.virtual('isRetryable').get(function() {
    return this.processingInfo.retryCount < this.processingInfo.maxRetries &&
           this.processingInfo.status === 'failed' &&
           this.replayInfo.canReplay;
  });

  // 实例方法
  FzDataProcessLogSchema.methods.markAsProcessing = function() {
    this.processingInfo.status = 'processing';
    this.processingInfo.startTime = new Date();
    return this.save();
  };

  // 安全的时间差计算辅助方法
  FzDataProcessLogSchema.methods._calculateSafeDuration = function() {
    const endTime = this.processingInfo.endTime || new Date();
    const startTime = this.processingInfo.startTime || endTime;

    // 检查并记录时间设置异常情况
    if (!this.processingInfo.startTime) {
      console.warn(`[FzDataProcessLog] 任务 ${this.taskId} 的 startTime 未设置，使用 endTime 作为默认值`);
    }

    // 确保时间是有效的 Date 对象
    const validEndTime = endTime instanceof Date && !isNaN(endTime.getTime()) ? endTime : new Date();
    const validStartTime = startTime instanceof Date && !isNaN(startTime.getTime()) ? startTime : validEndTime;

    // 记录时间对象无效的情况
    if (endTime !== validEndTime) {
      console.warn(`[FzDataProcessLog] 任务 ${this.taskId} 的 endTime 无效，使用当前时间作为默认值`);
    }
    if (startTime !== validStartTime) {
      console.warn(`[FzDataProcessLog] 任务 ${this.taskId} 的 startTime 无效，使用 endTime 作为默认值`);
    }

    const duration = validEndTime.getTime() - validStartTime.getTime();

    // 确保返回值是一个有效的非负数
    const safeDuration = isNaN(duration) || duration < 0 ? 0 : duration;

    // 记录异常的时间计算结果
    if (safeDuration === 0 && (isNaN(duration) || duration < 0)) {
      console.warn(`[FzDataProcessLog] 任务 ${this.taskId} 的时间计算异常，duration: ${duration}，已设置为 0`);
    }

    return safeDuration;
  };

  FzDataProcessLogSchema.methods.markAsSuccess = function(result = {}) {
    this.processingInfo.status = 'success';
    this.processingInfo.endTime = new Date();
    this.processingInfo.duration = this._calculateSafeDuration();
    this.result.success = true;
    this.result.message = result.message || '处理成功';

    // 存储数据统计
    if (result.dataStats) {
      Object.assign(this.dataStats, result.dataStats);
    }

    // 存储额外的处理数据到metadata.additionalInfo
    if (result.decryptedData || result.dataAnalysis || result.performance) {
      this.metadata.additionalInfo = {
        decryptedData: result.decryptedData,
        dataAnalysis: result.dataAnalysis,
        performance: result.performance,
        serviceResult: result.serviceResult,
      };
    }

    return this.save();
  };

  FzDataProcessLogSchema.methods.markAsFailed = function(error, canRetry = true, additionalData = {}) {
    this.processingInfo.status = 'failed';
    this.processingInfo.endTime = new Date();
    this.processingInfo.duration = this._calculateSafeDuration();
    this.result.success = false;
    this.result.message = error.message || '处理失败';
    this.result.errorInfo = {
      code: error.code,
      message: error.message,
      stack: error.stack,
      details: error.details,
    };
    this.replayInfo.canReplay = canRetry;

    // 存储额外的失败数据
    if (additionalData.decryptedData || additionalData.dataAnalysis) {
      this.metadata.additionalInfo = {
        decryptedData: additionalData.decryptedData,
        dataAnalysis: additionalData.dataAnalysis,
        performance: additionalData.performance,
        errorDetails: additionalData,
      };
    }

    return this.save();
  };

  FzDataProcessLogSchema.methods.markAsSkipped = function(result = {}) {
    this.processingInfo.status = 'skipped';
    this.processingInfo.endTime = new Date();
    this.processingInfo.duration = this._calculateSafeDuration();
    this.result.success = false;
    this.result.message = result.message || '跳过处理';

    // 存储数据统计
    if (result.dataStats) {
      Object.assign(this.dataStats, result.dataStats);
    }

    // 存储跳过原因和相关数据
    if (result.decryptedData || result.dataAnalysis || result.skipReason) {
      this.metadata.additionalInfo = {
        skipReason: result.skipReason,
        decryptedData: result.decryptedData,
        dataAnalysis: result.dataAnalysis,
        details: result.details,
      };
    }

    return this.save();
  };

  FzDataProcessLogSchema.methods.incrementRetry = function() {
    this.processingInfo.retryCount += 1;
    this.processingInfo.status = 'retrying';
    return this.save();
  };

  return mongoose.model('FzDataProcessLog', FzDataProcessLogSchema, 'fzDataProcessLogs2');
};
