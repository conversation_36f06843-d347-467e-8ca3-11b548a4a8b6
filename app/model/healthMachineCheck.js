module.exports = app => {
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;
  const shortid = require('shortid');
  // const moment = require('moment');
  // const CryptoJS = require('crypto-js');

  const HealthMachineCheckSchema = new Schema(
    {
      _id: {
        type: String,
        default: shortid.generate,
      },
      EnterpriseID: {
        // 企业ID
        type: String,
        ref: 'Adminorg',
      },
      employeeId: String,
      sfz: {
        name: String, // '姓名',
        sex: String, // '性别',
        nation: String, //  '民族',
        birthday: String, //  '出生日期',
        idnumber: String, // '身份证号',
        age: String, // '年龄',
        address: String, // '地址',
        data: String, // '身份证照片数据',
        fileName: String, // "身份证照片路径",
        len: String, // '身份证照片数据长度',
      },
      hw: {
        // 身高体重
        height: {
          value: String,
          tips: String,
          standard: String,
          unit: {
            type: String,
            default: 'cm',
          },
        },
        weight: {
          value: String,
          tips: String,
          standard: String, // 参考范围
          unit: {
            type: String,
            default: 'kg',
          },
        },
        bmi: {
          value: String,
          tips: String,
          standard: String,
        },
      },
      fat: {
        zflv: {
          // 脂肪率#提示#参考范围
          value: String,
          tips: String,
          standard: String,
          unit: {
            type: String,
            default: '%',
          },
        },
        jcdx: {
          // 基础代谢#提示#参考范围"
          value: String,
          tips: String,
          standard: String,
          unit: {
            type: String,
            default: 'kcal',
          },
        },
        tsfl: {
          // 体水分量#提示#参考范围
          value: String,
          tips: String,
          standard: String,
          unit: {
            type: String,
            default: 'kg',
          },
        },
        tsflv: {
          // 体水分率#提示#参考范围
          value: String,
          tips: String,
          standard: String,
          unit: {
            type: String,
            default: '%',
          },
        },
        zfl: {
          // 脂肪量#提示#参考范围
          value: String,
          tips: String,
          standard: String,
          unit: {
            type: String,
            default: 'kg',
          },
        },
        jrl: {
          // 肌肉量#提示#参考范围
          value: String,
          tips: String,
          standard: String,
          unit: {
            type: String,
            default: 'kg',
          },
        },
        jrlv: {
          // 肌肉率#提示#参考范围
          value: String,
          tips: String,
          standard: String,
          unit: {
            type: String,
            default: '%',
          },
        },
        gy: {
          // 骨盐#提示#参考范围
          value: String,
          tips: String,
          standard: String,
          unit: {
            type: String,
            default: 'kg',
          },
        },
        qztz: {
          // 去脂体重#提示#参考范围
          value: String,
          tips: String,
          standard: String,
          unit: {
            type: String,
            default: 'kg',
          },
        },
        dbzlv: {
          // 蛋白质率#提示#参考范围
          value: String,
          tips: String,
          standard: String,
          unit: {
            type: String,
            default: '%',
          },
        },
        xbnyl: {
          // 细胞内液量#提示#参考范围
          value: String,
          tips: String,
          standard: String,
          unit: {
            type: String,
            default: 'kg',
          },
        },
        xbwyl: {
          // 细胞外液量#提示#参考范围
          value: String,
          tips: String,
          standard: String,
          unit: {
            type: String,
            default: 'kg',
          },
        },
        xbnylv: {
          // 细胞内液率#提示#参考范围
          value: String,
          tips: String,
          standard: String,
          unit: {
            type: String,
            default: '%',
          },
        },
        xbwylv: {
          // 细胞外液率#提示#参考范围
          value: String,
          tips: String,
          standard: String,
          unit: {
            type: String,
            default: '%',
          },
        },
        dbz: {
          // 蛋白质#提示#参考范围
          value: String,
          tips: String,
          standard: String,
          unit: {
            type: String,
            default: 'kg',
          },
        },
      },
      blood: {
        high: {
          // 高压#提示#参考范围
          value: String,
          tips: String,
          standard: String,
          unit: {
            type: String,
            default: 'mmHg',
          },
        },
        low: {
          // 低压#提示#参考范围
          value: String,
          tips: String,
          standard: String,
          unit: {
            type: String,
            default: 'mmHg',
          },
        },
        rate: {
          // 心率#提示#参考范围
          value: String,
          tips: String,
          standard: String,
          unit: {
            type: String,
            default: '次/分',
          },
        },
        avi: {
          // 中心动脉硬度指标#提示#参考范围
          value: String,
          tips: String,
          standard: String,
        },
        api: {
          // 上臂动脉硬度指标#提示#参考范围
          value: String,
          tips: String,
          standard: String,
        },
        result: {
          // 四肢动脉硬化诊断结果。
          value: String,
          tips: String,
          standard: String,
        },
        data: String, // 四肢动脉硬化报告图片数据
        len: String, // 四肢动脉硬化报告图片数据长度
      },
      spo2: {
        sp: {
          // 血氧#提示#参考范围
          value: String,
          tips: String,
          standard: String,
          unit: {
            type: String,
            default: '%',
          },
        },
      },
      tiwen: {
        // 体温#提示#参考范围
        value: String,
        tips: String,
        standard: String,
        unit: {
          type: String,
          default: '°C',
        },
      },
      ecg: {
        result: String, // "心电结果"
        data: String, // '心电波形图片数据'
        fileName: String, // '心电波形图片路径'
        len: String, // '心电波形图片数据长度'
      },
      ecg12: {
        data: String, // '心电报告图片数据'
        fileName: String, // '心电报告图片路径'
        len: String, // '心电报告图片数据长度'
        ecg_result: String, // '诊断结果'
        // '心率',
        heart_rate: {
          value: String,
          tips: String,
          standard: String,
          unit: {
            type: String,
            default: 'bpm',
          },
        },
        // 'P轴',
        p_axis: {
          value: String,
          tips: String,
          standard: String,
          unit: {
            type: String,
            default: '°',
          },
        },
        qrs_axis: {
          value: String,
          tips: String,
          standard: String,
          unit: {
            type: String,
            default: '°',
          },
        }, // 'QRS轴',
        t_axis: {
          value: String,
          tips: String,
          standard: String,
          unit: {
            type: String,
            default: '°',
          },
        }, // 'T轴',
        pr: {
          value: String,
          tips: String,
          standard: String,
          unit: {
            type: String,
            default: 'ms',
          },
        }, // 'PR间期',
        qrs: {
          value: String,
          tips: String,
          standard: String,
          unit: {
            type: String,
            default: 'ms',
          },
        }, // 'QRS时限',
        qt: {
          value: String,
          tips: String,
          standard: String,
          unit: {
            type: String,
            default: 'ms',
          },
        }, // 'QT间期',
        qtc: {
          value: String,
          tips: String,
          standard: String,
          unit: {
            type: String,
            default: 'ms',
          },
        }, // 'QTc间期',
        rv5: {
          value: String,
          tips: String,
          standard: String,
          unit: {
            type: String,
            default: 'uV',
          },
        }, // 'RV5值',
        sv1: {
          value: String,
          tips: String,
          standard: String,
          unit: {
            type: String,
            default: 'uV',
          },
        }, // 'SV1值',
        sample_rate: {
          value: String,
          tips: String,
          standard: String,
          unit: {
            type: String,
            default: 'Hz',
          },
        }, // '采样率',
        sample_time: {
          value: String,
          tips: String,
          standard: String,
          unit: {
            type: String,
            default: 's',
          },
        }, // '采样时长',
      },
      xt: {
        type: {
          // '血糖测量类型##',
          value: String,
          tips: String,
          standard: String,
          unit: {
            type: String,
            default: '',
          },
        },
        value: {
          value: String,
          tips: String,
          standard: String, // '血糖值#提示#参考范围',
          unit: {
            type: String,
            default: 'mmol/L',
          },
        },
      },
      ns: {
        // '尿酸值#提示#参考范围',
        value: String,
        tips: String,
        standard: String,
        unit: {
          type: String,
          default: 'umol/L',
        },
      },
      dgc: {
        // '胆固醇值#提示#参考范围',
        value: String,
        tips: String,
        standard: String,
        unit: {
          type: String,
          default: 'mmol/L',
        },
      },
      xhdb: {
        value: {
          value: String,
          tips: String,
          standard: String,
          unit: {
            type: String,
            default: 'g/dL',
          },
        }, // '血红蛋白值#提示#参考范围',
        hxbyj: {
          // 红细胞压积值#提示#参考范围',
          value: String,
          tips: String,
          standard: String,
          unit: {
            type: String,
            default: '%',
          },
        },
      },
      xzsx: {
        chol: {
          value: String,
          tips: String,
          standard: String,
          unit: {
            type: String,
            default: 'mmol/L',
          },
        },
        hdl: {
          value: String,
          tips: String,
          standard: String,
          unit: {
            type: String,
            default: 'mmol/L',
          },
        },
        tg: {
          value: String,
          tips: String,
          standard: String,
          unit: {
            type: String,
            default: 'mmol/L',
          },
        },
        ldl: {
          value: String,
          tips: String,
          standard: String,
          unit: {
            type: String,
            default: 'mmol/L',
          },
        },
        chd: {
          value: String,
          tips: String,
          standard: String,
          unit: {
            type: String,
            default: '',
          },
        },
      },
      nyfx: {
        leu: {
          //   leu: '白细胞#提示#参考范围',
          value: String,
          tips: String,
          standard: String,
          unit: {
            type: String,
            default: '',
          },
        },
        bld: {
          //   bld: '潜血#提示#参考范围',
          value: String,
          tips: String,
          standard: String,
          unit: {
            type: String,
            default: '',
          },
        },
        //   ph: 'PH值#提示#参考范围',
        ph: {
          value: String,
          tips: String,
          standard: String,
          unit: {
            type: String,
            default: '',
          },
        },
        //   pro: '蛋白质#提示#参考范围',
        pro: {
          value: String,
          tips: String,
          standard: String,
          unit: {
            type: String,
            default: '',
          },
        },
        //   ubg: '尿胆原#提示#参考范围',
        ubg: {
          value: String,
          tips: String,
          standard: String,
          unit: {
            type: String,
            default: '',
          },
        },
        //   nit: '亚硝酸盐#提示#参考范围',
        nit: {
          value: String,
          tips: String,
          standard: String,
          unit: {
            type: String,
            default: '',
          },
        },
        //   vc: '维生素#提示#参考范围',
        vc: {
          value: String,
          tips: String,
          standard: String,
          unit: {
            type: String,
            default: '',
          },
        },
        //   glu: '葡萄糖#提示#参考范围',
        glu: {
          value: String,
          tips: String,
          standard: String,
          unit: {
            type: String,
            default: '',
          },
        },
        //   bil: '胆红素#提示#参考范围',
        bil: {
          value: String,
          tips: String,
          standard: String,
          unit: {
            type: String,
            default: '',
          },
        },
        //   ket: '酮体#提示#参考范围',
        ket: {
          value: String,
          tips: String,
          standard: String,
          unit: {
            type: String,
            default: '',
          },
        },
        //   sg: '比重#提示#参考范围',
        sg: {
          value: String,
          tips: String,
          standard: String,
          unit: {
            type: String,
            default: '',
          },
        },
        //   ma: '微量白蛋白#提示#参考范围',
        ma: {
          value: String,
          tips: String,
          standard: String,
          unit: {
            type: String,
            default: '',
          },
        },
        //   cre: '肌酐#提示#参考范围',
        cre: {
          value: String,
          tips: String,
          standard: String,
          unit: {
            type: String,
            default: '',
          },
        },
        //   ca: '钙离子#提示#参考范围',
        ca: {
          value: String,
          tips: String,
          standard: String,
          unit: {
            type: String,
            default: '',
          },
        },
      },
      zybs: {
        // 中医体质类型
        tcmConstitutionType: String,
        //  #阳虚质得分
        yangDeficiencyQualityScore: String,
        // #阴虚质得分
        yinDeficiencyQualityScore: String,
        // #气虚质得分
        qiDeficiencyQualityScore: String,
        // #痰湿质得分
        phlegmdampnessScore: String,
        // #湿热质得分
        humidityAndHeatQualityScore: String,
        // #血瘀质得分
        bloodAndVesiculationQualityScore: String,
        // #特禀质得分
        specialQualityScore: String,
        // #气郁质得分
        qiYuQualityScore: String,
        // #平和质得分
        peaceQualityScore: String,
      },
      ytb: {
        waist: {
          // 腰围## "
          value: String,
          tips: String,
          standard: String,
          unit: {
            type: String,
            default: 'cm',
          },
        },
        hip: {
          // 臀围## "
          value: String,
          tips: String,
          standard: String,
          unit: {
            type: String,
            default: 'cm',
          },
        },
        whr: {
          // 腰臀比
          value: String,
          tips: String,
          standard: String,
          unit: {
            type: String,
            default: '',
          },
        },
      },
      fgn: {
        pef: {
          // '呼气峰值流量#提示#参考范围',
          value: String,
          tips: String,
          standard: String,
          unit: {
            type: String,
            default: 'L/min',
          },
        },
        fev1: {
          // '第一秒用力呼气量#提示#参考范围',
          value: String,
          tips: String,
          standard: String,
          unit: {
            type: String,
            default: 'L',
          },
        },
        fvc: {
          // '肺活量#提示#参考范围',
          value: String,
          tips: String,
          standard: String,
          unit: {
            type: String,
            default: 'L',
          },
        },
        bz: {
          // '第一秒用力呼气量/用力肺活量#提示#参考范围',
          values: String,
          tips: String,
          standard: String,
          unit: {
            type: String,
            default: '%',
          },
        },
      },
      gmd: {
        // 骨密度相关
        data: String, // 骨密度报告图片数据
        fileName: String, // 骨密度报告图片路径
        len: String, // 骨密度报告图片长度
        bone_age: String, // 骨龄
        pheight: String, // 预测身高
        sos: String, // 超声波传播速度
        tscore: String, // T值
        zscore: String, // Z值
        comment: String, // 诊断结果
        t_ratio: String, // 成人比
        z_ratio: String, // 同龄比
        bqi: String, // 骨质指数
        rrf: String, // 相对骨折风险
        eoa: String, // 预计发生骨质疏松的年龄
      },
      shili: {
        // 视力
        left_eye: {
          value: String,
          visualAcuity: String, // 视力度数
          tips: String, // 提示
        },
        right_eye: {
          value: String,
          visualAcuity: String, // 视力度数
          tips: String, // 提示
        },
      },
      semang: {
        value: String,
        tips: String,
        standard: String, // 标准
      },
      alcohol: {
        // 酒精
        value: String,
        tips: String,
        standard: String, // 标准
        unit: {
          type: String,
          default: 'mg/100ml',
        },
      },
      xlcp: {
        ucla: {
          // UCLA孤独量表得分
          value: String,
          result: String,
        },
        lnyy: {
          // 老年抑郁量表得分
          value: String,
          result: String,
        },
        zpyy: {
          // 自评抑郁量表得分
          value: String,
          result: String,
        },
        hmdjl: {
          // 汉密顿焦虑量表得分
          value: String,
          result: String,
        },
        qxjkd: {
          // 情绪健康度测试得分
          value: String,
          result: String,
        },
        zcjkpd: {
          // 自测健康评定量表得分
          value: String,
          result: String,
        },
        shmyd: {
          // 生活满意度量表得分
          value: String,
          result: String,
        },
        rgza: {
          // 人格障碍性格测试得分
          value: String,
          result: String,
        },
        pstr: {
          // PSTR成人心理压力测试得分
          value: String,
          result: String,
        },
        hfxx: {
          // 哈佛性向测试得分
          value: String,
          result: String,
        },
        eq: {
          // 情商(EQ)测试得分
          value: String,
          result: String,
        },
        smzkpg: {
          // 睡眠状况评估得分#
          value: String,
          result: String,
        },
      },
      deviceID: String, // '设备码',
      examNo: String, // '体检编号',
      checkDate: {
        // 体检日期
        type: Date,
      },
      fileName: String, // 体检报告pdf路径 身份证号_体检编号_设备号.pdf
    },
    { timestamps: true }
  );
  // 给enterpriseID添加索引
  HealthMachineCheckSchema.index({ EnterpriseID: 1 });
  HealthMachineCheckSchema.index({ examNo: 1 });
  HealthMachineCheckSchema.index({ 'sfz.name': 1 });
  HealthMachineCheckSchema.index({ 'sfz.idnumber': 1 });

  return mongoose.model(
    'HealthMachineCheck',
    HealthMachineCheckSchema,
    'healthMachineChecks'
  );
};
