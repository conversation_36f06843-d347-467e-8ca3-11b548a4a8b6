// 机构端-注册机构表
module.exports = app => {
  const mongoose = app.mongoose;
  const shortid = require('shortid');
  const Schema = mongoose.Schema;
  const upload_http_path = app.config.upload_http_path || '';

  const ServiceOrgSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    name: { // 机构名称
      type: String,
      trim: true,
      require: true,
    },
    organization: { // 社会统一信用代码
      type: String,
      trim: true,
      require: true,
      index: true,
    },
    dingInfo: { // 钉钉应用信息
      AppKey: String,
      AppSecret: String,
      CorpId: String,
      aesKey: String,
      token: String,
      AgentId: String,
    },
    regAddr: { // 注册的省市区
      default: [],
      type: Array,
    },
    address: { // 注册详细地址
      type: String,
      trim: true,
    },
    corp: String, // 法人代表
    managers: [ // 管理人员id集合
      {
        type: String,
        ref: 'ServiceUser',
      },
    ],
    managersAndArea: [ // 管理人员id与技术服务区域集合
      {
        _id: {
          type: String,
          default: shortid.generate,
        },
        managers: { // 负责该监管区域的负责人
          type: String,
          ref: 'ServiceUser',
        },
        AreaRegAddr: { // 该监管区域的地址
          default: [],
          type: Array,
        },
        isactive: { // 是否已经通过该监管部门的同意
          type: Boolean,
          default: false,
        },
        companyIn: { // 是否是企业自己添加的，false代表是机构正常走流程申请登记，true代表是企业添加的
          type: Boolean,
          default: false,
        },
      },
    ],
    lineOfBusiness: { // 业务范围
      type: String,
      trim: true,
      default: '',
    },
    regType: { // 组成形式
      type: String,
      default: '',
    },
    ctime: { // 创建/更改时间
      type: Date,
      default: Date.now,
    },
    img: { // 营业执照
      type: String,
      // require: true,
      trim: true,
      set(val) {
        return (val && val.indexOf(upload_http_path) !== -1) ? val.split(upload_http_path)[1] : val;
      },
    },
    qualifies: [
      { // 资质证书id集合
        type: String,
        ref: 'DetectionMechanism',
      },
    ],
    message: String, // 审核结果
    status: { // 状态
      type: Number,
      default: 0, // 0未注册；1已上传营业执照；2已上传资质证书；3审核通过；4审核不通过；5注销
      enum: [ 0, 1, 2, 3, 4, 5 ],
    },
    landline: String, // 座机
    administrator: { // 超级管理员，第一个用户，也是绑定了社会统一信用代码的人
      type: String,
      ref: 'ServiceUser',
    },
    sourse: { // 哪个端创建的
      type: String,
      default: 'oapi', // 比如 'operate' / 'service' / 'jcqlc' / 'oapi'
    },
  });
  return mongoose.model('ServiceOrg', ServiceOrgSchema, 'serviceOrg');

};

