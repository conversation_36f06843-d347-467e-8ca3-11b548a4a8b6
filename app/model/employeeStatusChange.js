/*
 * @Author: 黄婷婷
 * @Date: 2020-09-22 09:00
 * @LastEditors: 汪周强
 * @LastEditTime: 2023-10-12 14:29:04
 * @Description: 员工工作状态变更
 *
 */
module.exports = app => {
  const shortid = require('shortid');
  const path = require('path');
  require(path.join(process.cwd(), 'app/model/adminUser'));
  // 员工工作状态变更
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;
  const employeeStatusChange = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    employee: { type: String, ref: 'Employees' }, // 员工
    statusChanges: [
      {
        // 工作状态变更信息
        _id: {
          type: String,
          default: shortid.generate,
        },
        changType: Number, // 变更类型,1.离岗/2.转岗/3.转部门/4.入职
        EnterpriseID: { type: String, ref: 'Adminorg' }, // 当前变更状态下所在企业
        departFrom: [[{ type: String, ref: 'Dingtrees' }]], // 原部门
        departsTo: [[{ type: String, ref: 'Dingtrees' }]], // 目的部门
        stationFrom: String, // 原岗位
        changStationReason: String, // 转岗原因
        stationsTo: [{ type: String }], // 目的岗位
        message: String, // 变更信息
        files: [
          {
            // 附件
            originName: String, // 用户上传文件名称
            staticName: String, // 后台处理后存储到public静态资源的文件名
          },
        ],
        signPath: String, // 签名路径
        signTime: Date, // 签名时间
        signStatus: Boolean, // 签名确认 true
        hazardInform: String, // 签字后生成的危害告知书
        timestamp: { type: Date, default: Date.now }, // 变更时间
      },
    ],
  });
  employeeStatusChange.virtual('EnterpriseIDRef', {
    ref: 'Adminorg',
    localField: 'statusChanges.EnterpriseID',
    foreignField: '_id',
  });
  employeeStatusChange.index({ employee: 1 });
  return mongoose.model('EmployeeStatusChange', employeeStatusChange, 'employeeStatusChanges');
};
