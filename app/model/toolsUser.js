module.exports = app => {
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;
  const shortid = require('shortid');

  const ToolsUserSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    userName: String,
    countryCode: {
      type: String,
      default: '86',
    }, // 手机号前国家代码
    phoneNum: {
      type: String,
      trim: true,
      unique: true,
    },
  });

  return mongoose.model('ToolsUser', ToolsUserSchema, 'toolsuser');
};
