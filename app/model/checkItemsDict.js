/*
 * @Author: 刘香环
 * @Date: 2024-06-11 13：36：32
 * @LastEditors: 刘香环
 * @LastEditTime: 2024-06-11 13：36：32s
 * @Description: 体检项目
 * 企业 监管
 */

module.exports = app => {
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;
  const shortid = require('shortid');

  const checkItemsDictSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    name: { // 套餐名称 必选男上,必选女上,必选男下,必选女下,可选上,可选下,特殊女,特殊上,职业健康
      type: String,
      required: true,
    },
    type: { // 体检项目类型 0 必选 1可选  2 特殊项目 3 职业健康检查
      type: String,
      required: true,
    },
    sex: { // 性别 0 男 1 女 不限 2
      type: String,
      default: '2',
      required: true,
    },
    age: { // 年龄段 0 40岁及以上 1 40岁以下  2 45岁以上 3 不限
      type: String,
      default: '3',
      required: true,
    },
  },
  { timestamps: true }
  );
  return mongoose.model('CheckItemsDict', checkItemsDictSchema);
};

