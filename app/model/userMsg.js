/**
 * 给user用户的消息通知表
 */

module.exports = app => {
  const mongoose = app.mongoose;
  const shortid = require('shortid');
  const Schema = mongoose.Schema;


  const UserMsgSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    type: {
      type: Number,
      default: 1,
      enum: [ 1, 2 ], // 1是企业发给员工的信息 2 是监管端发给我企业管理员的信息
    },
    title: { // 消息标题
      type: String,
      require: true,
    },
    content: { // 消息主要内容
      type: String,
    },
    hasRead: {
      type: Boolean,
      default: false,
    },
    toUrl: String, // 点击之后跳转的链接
    userId: { // 这个必须有
      type: String,
      ref: 'User',
      require: true,
    },
    EnterpriseID: { // 企业id，企业发给员工的信息才有
      type: String,
      ref: 'Adminorg',
      default: '',
    },
    superID: { // 监管id, 监管端给管理员的信息才有
      type: String,
      ref: 'SuperUser',
      default: '', // 运营端创建自主培训则不需要
    },
    date: { // 消息产生的时间
      type: Date,
      default: Date.now,
    },
  });


  return mongoose.model('UserMsg', UserMsgSchema, 'userMsg');
};
