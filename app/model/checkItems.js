/*
 * @Author: 刘香环
 * @Date: 2024-06-11 13：36：32
 * @LastEditors: 刘香环
 * @LastEditTime: 2024-06-11 13：36：32s
 * @Description: 体检项目
 * 企业 监管
 */

module.exports = app => {
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;
  const shortid = require('shortid');

  const checkItemsSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    // 体检项目名称
    name: {
      type: String,
      required: true,
    },
    // 套餐类型
    packageList: [ // 存储checkItemsDict套餐id
      {
        type: String,
        ref: 'CheckItemsDict',
      },
    ],
    // 体检项目描述
    comments: [ // 存储indicatorDict指标id
      {
        type: String,
        ref: 'IndicatorDict',
      },
    ],
    // 体检项目指导价格
    price: {
      type: Number,
    },
  },
  { timestamps: true }
  );
  return mongoose.model('CheckItems', checkItemsSchema);
};

