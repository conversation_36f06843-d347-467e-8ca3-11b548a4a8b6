/*
 * @Author: 黄婷婷
 * @Date: 2020-07-26 09:00
 * @LastEditors: 黄婷婷
 * @LastEditTime: 2020-07-26 09:00
 * @Description: 工作场所
 *
 */
module.exports = app => {
  const shortid = require('shortid');
  // 厂房结构
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;
  const millConstruction = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    serviceOrgId: String, // 机构id
    adminorgMillId: String, // 企业工作场所关联id
    status: {
      default: '0',
      type: String,
    }, // 是否经过企业确认 '0'表示未确认，'1'表示已确认
    EnterpriseID: String, // 企业ID
    // companyId: String, // 公司id
    name: String, // 名称
    category: String, // 类别 mill/workspaces
    children: [{ // 下级
      _id: {
        type: String,
        default: shortid.generate,
      },
      status: {
        default: '0',
        type: String,
      }, // 是否经过企业确认 '0'表示未确认，'1'表示已确认
      category: { type: String }, // 类别 workspaces/stations
      workType: String, // 工种
      name: String, // 车间名称
      harmFactors: { type: Array }, // 危害因素
      customizeHarm: String, // 自定义危害因素（不在数据库中的危害因素）
      workWay: { type: String }, // 作业方式
      peopleNumber: { type: Number }, // 作业人数(统计的)
      batchEmployeeRefStationId: [ String ], // 同一批员工引用的厂房、车间、岗位id
      customizePeopleNumber: { type: Number }, // 作业人数（手填）
      dailyProduce: { type: String }, // 生产班制
      customizeProduce: String, // 自定义班制
      workTimeDay: Number, // 每天工作时间(小时)
      workTimeWeek: Number, // 每周工作时间(小时)
      workDayWeek: Number, // 每周接触几天
      // contactTime: { type: String }, // 每班接触时间
      protectiveEquipment: { type: String }, // 个人防护用品
      protectiveFacilities: { type: String }, // 职业病防护设施
      time: { type: Number }, // 每班接触时间
      value: { type: String }, // 时间单位
      equipCount: Number, // 设备总数
      newLinkDeparts: {
        EnterpriseID: String, // 企业id
        departs: [{
          label: String, // 名称
          departId: String, // 部门名称
          _id: {
            type: String,
            default: shortid.generate,
          },
        }], // 部门id
      }, // 关联的部门
      children: [{ // 岗位
        status: {
          default: '0',
          type: String,
        }, // 是否经过企业确认 '0'表示未确认，'1'表示已确认
        _id: {
          type: String,
          default: shortid.generate,
        },
        workType: String, // 工种
        employees: { type: String, ref: 'Employees' }, // 员工id
        isPass: { type: String }, // 转岗是否经过本人确认 ==> jhw
        harmFactors: { type: Array }, // 危害因素
        customizeHarm: String, // 自定义危害因素（不在数据库中的危害因素）
        workWay: { type: String }, // 作业方式
        // batchEmployeeRefStationId: [ String ], // 同一批员工引用的厂房、车间、岗位id
        peopleNumber: { type: Number }, // 作业人数
        dailyProduce: { type: String }, // 生产班制
        customizePeopleNumber: { type: Number }, // 作业人数（手填）
        customizeProduce: String, // 自定义班制
        workTimeDay: Number, // 每天工作时间(小时)
        workTimeWeek: Number, // 每周工作时间(小时)
        workDayWeek: Number, // 每周接触几天
        contactTime: { type: String }, // 每班接触时间
        protectiveEquipment: { type: String }, // 个人防护用品
        protectiveFacilities: { type: String }, // 职业病防护设施
        time: { type: Number }, // 每班接触时间
        value: { type: String }, // 时间单位
        category: { type: String }, // 类别 stations/workspaces/workshops
        name: String, // 岗位名称
        newLinkDeparts: {
          EnterpriseID: String, // 企业id
          departs: [{
            label: String, // 名称
            departId: String, // 部门名称
            _id: {
              type: String,
              default: shortid.generate,
            },
          }], // 部门id
        }, // 关联的部门
        children: [
          {
            _id: {
              type: String,
              default: shortid.generate,
            },
            employees: { type: String, ref: 'Employees' }, // 员工id
            isPass: { type: String }, // 转岗是否经过本人确认 ==> jhw
          },
        ], // 员工
      }],
    }],
  });
  return mongoose.model('MillConstruction', millConstruction, 'millConstructions');
};
