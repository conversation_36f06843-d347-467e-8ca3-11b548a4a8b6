/**
 * Created by Zhanglc on 2022/4/7.
 * 企业用户对象
 */
const encryptionPlugin = require('../utils/encryptionPlugin');
module.exports = app => {
  const mongoose = app.mongoose;
  const ctx = app.createAnonymousContext();
  const shortid = require('shortid');
  const Schema = mongoose.Schema;
  const CryptoJS = require('crypto-js');
  const { dbEncryption = false, isUnique = true } = app.config;
  const AdminUserSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    name: String, // 姓名
    nameForStore: {
      // 加密姓名
      type: String,
    },
    nameSplitEncrypted: {
      // 分段加密的姓名
      type: String,
    },
    userName: String, // 用户名
    password: {
      type: String,
      set(val) {
        if (!dbEncryption) {
          return CryptoJS.SHA256(val + app.config.salt_sha2_key).toString();
        }
        return val;
      },
    }, // 密码
    readedNotice: {
      type: Bo<PERSON>an,
      default: false,
    }, // 用户是否已读职业病防治法告知书 越城在使用，校准时间：2022.4.7
    email: String, // 邮箱
    phoneNum: {
      type: String,
      sparse: true,
      trim: true,
    }, // 手机号
    phoneNumForStore: {
      // 用于加密存储的手机号
      type: String,
    },
    phoneNumSplitEncrypted: {
      // 分段加密的手机号
      type: String,
    },
    countryCode: {
      type: String,
      default: '86',
    }, // 手机号前国家代码
    comments: String, // 描述
    date: {
      type: Date,
      default: Date.now,
    }, // 创建时间
    logo: {
      type: String,
      default: '/static/upload/images/defaultlogo.png',
    }, // 头像
    enable: {
      type: Boolean,
      default: true,
    }, // 是否有效
    state: {
      type: String,
      default: '1', // 1正常，0删除
    }, // 假删除
    auth: {
      type: Boolean,
      default: false,
    }, // 是否是发布文章的作者 知识库用
    group: {
      type: String,
      ref: 'AdminGroup',
      default: app.config.groupID.adminGroupID,
    }, // 角色
    targetEditor: {
      type: String,
      ref: 'Employees',
    }, // 企业员工绑定ID
    IDcard: {
      type: String,
      default: '',
    }, // 身份证号
    IDcardForStore: {
      // 用于加密存储的身份证号
      type: String,
    },
    IDcardSplitEncrypted: {
      // 分段加密的身份证号
      type: String,
    },
    expireDate: {
      type: String,
      default: '',
    }, // 身份证有效期
    userId: {
      type: String,
      ref: 'User',
    }, // 对应的是users表中的id
    newAddEnterpriseID: {
      type: String,
      default: '',
      ref: 'Adminorg',
    }, // 当前新添加企业ID 是企业添加多个企业时用于告知是添加的哪个企业，以此获取其审核状态
    landline: String, // 座机,
    employees: [
      {
        type: String,
        ref: 'Employees',
      },
    ], // 新的绑定员工ID
    source: {
      // 来源端
      type: String,
      default: 'oapi',
      enum: [ 'enterprise', 'tj', 'super', 'dbscript', 'opt', 'jc', 'oapi' ],
    },
    encryptionAlgorithm: {
      type: String,
    },
    passwordEncryptionAlgorithm: {
      // 密码加密hmac算法
      type: String,
    },
    // 人员角色权限hmac
    user_role_power_hmac: String,
    // 人员角色权限hmac算法
    user_role_power_hmac_algorithm: {
      type: String,
    },
  });
  const phoneNumField = dbEncryption ? 'phoneNumForStore' : 'phoneNum';

  // AdminUserSchema.index(
  //   { [phoneNumField]: -1 },
  //   {
  //     unique: isUnique,
  //     partialFilterExpression: { [phoneNumField]: { $exists: true } },
  //   }
  // );
  // AdminUserSchema.index(
  //   { [phoneNumField]: 1 },
  //   {
  //     unique: isUnique,
  //     partialFilterExpression: { [phoneNumField]: { $exists: true } },
  //   }
  // );
  const indexOptions = {
    partialFilterExpression: { [phoneNumField]: { $exists: true } }, // 仅索引存在 phoneNum 字段的文档
  };

  if (isUnique) {
    // 如果 isUnique 为 true，设置唯一索引
    AdminUserSchema.index(
      { userName: -1 },
      {
        unique: true,
        partialFilterExpression: { phoneNum: { $exists: true } },
      }
    );
    AdminUserSchema.index(
      { [phoneNumField]: -1 }, // 降序索引
      {
        ...indexOptions,
        unique: true, // 创建唯一索引，必须这么写
      }
    );
    AdminUserSchema.index(
      { [phoneNumField]: 1 },
      {
        ...indexOptions,
        unique: true, // 创建唯一索引，必须这么写
      }
    );
  } else {
  // 如果 isUnique 为 false，设置普通索引
    AdminUserSchema.index(
      { [phoneNumField]: -1 }, // 降序索引
      indexOptions // 不设置唯一约束
    );
    AdminUserSchema.index(
      { [phoneNumField]: 1 },
      indexOptions // 不设置唯一约束
    );
  }

  AdminUserSchema.index(
    { userName: 1 },
    {
      unique: true,
      partialFilterExpression: { phoneNum: { $exists: true } },
    }
  );
  AdminUserSchema.index({ userId: -1 });
  AdminUserSchema.index({ employees: -1 });
  AdminUserSchema.pre('findOneAndUpdate', function(next) {
    const update = this.getUpdate();
    if (update) {
      Object.keys(update).forEach(key => {
        if (!update[key]) {
          delete update[key];
        }
      });
    }
    next();
  });
  dbEncryption &&
    AdminUserSchema.plugin(encryptionPlugin, {
      fields: {
        phoneNum: 11,
        IDcard: 18,
        name: 3,
      },
      model: 'AdminUser',
      ctx,
    });

  // targetEditor废弃，但代码中有使用的地方，无法清除，故未写修改脚本

  return mongoose.model('AdminUser', AdminUserSchema, 'adminusers');
};
