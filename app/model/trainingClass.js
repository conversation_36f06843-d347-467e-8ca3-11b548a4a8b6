// 培训班 xxn 2024-07-04
module.exports = app => {
  const shortid = require('shortid');
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;

  const trainingClassSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    pxOrgId: { // 培训机构id
      type: String,
      ref: 'PxOrg',
      required: true,
    },
    examSyllabusId: { // 考试大纲id
      type: String,
      ref: 'ExamSyllabus',
      required: true,
    },
    name: { // 培训班名称
      type: String,
      required: true,
    },
    trainer: { // 培训讲师
      type: String,
      // ref: 'PxOrgUser',
    },
    coursesList: [{ // 课程id列表
      type: String,
      ref: 'Courses',
    }],
    creator: { // 创建人id
      type: String,
      ref: 'PxOrgUser',
      required: true,
    },
    source: { // 数据来源
      type: String,
      enum: [ 'pxOrg', 'adminorg', 'super' ], // pxOrg: 培训机构, adminorg: 企业, super: 监管端
      default: 'pxOrg',
    },
    cover: { // 封面
      _id: {
        type: String,
        default: shortid.generate,
      },
      name: String, // 原始文件名
      staticName: String, // 服务器静态文件名
    },
    status: { // 状态
      type: Number,
      enum: [ 0, 1, 2, 3, 4 ], // 0: 草稿 1: 已上报 2: 审核通过 3: 审核未通过 4: 已下架
      default: 0,
    },
    reviewRecord: [{ // 审核记录
      reviewerId: {
        type: String,
        ref: 'SuperUser',
      },
      reviewTime: { // 审核时间
        type: Date,
      },
      reviewComments: { // 审核意见
        type: String,
      },
    }],
    trainingStartDate: { // 培训开始时间
      type: Date,
      require: true,
    },
    trainingEndDate: { // 培训结束时间
      type: Date,
      require: true,
    },

  }, { timestamps: true });


  return mongoose.model('trainingClass', trainingClassSchema, 'trainingClass');
};
