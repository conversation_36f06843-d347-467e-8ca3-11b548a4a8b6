/*
 * @Author: 刘香环
 * @Date: 2024-08-12 17：36：32
 * @LastEditors: 刘香环
 * @LastEditTime: 2024-08-12 17：36：32s
 * @Description: 检查详情字典表
 * 企业
 */
const nanoid = async () => (await import('nanoid')).nanoid;


module.exports = app => {
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;

  const indicatorDictSchema = new Schema({
    _id: {
      type: String,
      default: nanoid(),
    },
    name: {
      type: String,
      required: true,
      index: true,
    },
    cnCode: { // 国家标准 健康指标代码表
      type: Number,
      required: true,
    },
    parentId: {
      type: String,
      ref: 'indicatorDict',
    },
  },
  { timestamps: true }
  );
  return mongoose.model('IndicatorDict', indicatorDictSchema);
};

