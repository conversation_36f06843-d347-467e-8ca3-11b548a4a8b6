module.exports = app => {
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;
  const shortid = require('shortid');

  // 定义WhPostAndPersonInfoSchema模型
  const WhPostAndPersonInfoSchema = new Schema(
    {
      _id: {
        type: String,
        default: shortid.generate,
      },
      taskBillLine: {
        type: String,
      },
      userId: {
        type: String,
      },
      positionId: {
        type: String,
      },
      validFrom: {
        type: Date,
      },
      validTo: {
        type: Date,
      },
      deleteFlag: {
        type: String,
        enum: [ 'X', '' ], // 'X'表示删除标志，空表示未删除
        default: '',
      },
      validFlag: {
        type: String,
        enum: [ 'X', '' ], // 'X'表示有效，空表示无效
      },
      pushType: {
        type: String,
        enum: [ 'M', 'A' ], // 'M' 表示手动推送 'A'表示自动
      },
      timestamp: {
        type: String, // 保留原始的时间戳格式
      },
    },
    { strict: false, timestamps: true } // 允许灵活的字段，且自动添加 `createdAt` 和 `updatedAt`
  );

  // 返回模型
  return mongoose.model('WhPostAndPersonInfo', WhPostAndPersonInfoSchema);
};
