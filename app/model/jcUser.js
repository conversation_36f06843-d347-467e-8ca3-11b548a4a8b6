/**
 * 员工账户表
 */

module.exports = app => {
  const mongoose = app.mongoose;
  const shortid = require('shortid');
  const Schema = mongoose.Schema;
  const CryptoJS = require('crypto-js');

  const JcUserSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    serviceEmployeeID: {
      type: String,
      ref: 'ServiceEmployee',
    },
    EnterpriseID: String, // 机构id
    name: String,
    userName: String,
    password: {
      type: String,
      set(val) {
        return CryptoJS.SHA256(val + app.config.salt_sha2_key).toString();
      },
    },
    email: {
      type: String,
      default: '',
    },
    phoneNum: String,
    countryCode: {
      type: String,
      default: '86',
    }, // 手机号前国家代码
    comments: {
      type: String,
      default: '',
    },
    date: {
      type: Date,
      default: Date.now,
    },
    logo: {
      type: String,
      default: '/static/upload/images/defaultlogo.png',
    },
    enable: {
      type: Boolean,
      default: false,
    },
    state: {
      type: String,
      default: '1', // 1正常，0删除
    },
    group: {
      type: String,
      ref: 'JcGroup',
    },
  });

  return mongoose.model('JcUser', JcUserSchema, 'jcUsers');

};
