/**
 * 运营端-OAPI账户表
 */

module.exports = app => {
  const mongoose = app.mongoose;
  const shortid = require('shortid');
  const Schema = mongoose.Schema;

  const ApiUserSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    orgId: { // 某个身份的ID 企业、政府、机构、体检
      type: String,
      index: true,
    },
    group: {
      type: String,
      ref: 'AdminGroup',
    },
    message: String, // 审核反馈结果
    enable: { // 是否有效
      type: Boolean,
      default: false,
    },
    status: { // 审核状态
      type: Number,
      default: 0, // 0未审核；1审核通过；2审核不通过；
      enum: [ 0, 1, 2 ],
    },
    date: {
      type: Date,
      default: Date.now,
    },
  });

  return mongoose.model('ApiUser', ApiUserSchema, 'apiUsers');

};

