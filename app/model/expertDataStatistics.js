// 培训后台主页 数据统计

module.exports = app => {
  const mongoose = app.mongoose;
  const shortid = require('shortid');
  const Schema = mongoose.Schema;

  const ExpertDataStatisticsSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    userID: { // 专家发布的培训
      type: String,
      ref: 'User',
    },
    date: { // 创建时间
      type: Date,
      default: Date.now,
    },
    income: { // 收入
      type: Number,
      default: 0,
    },
    views: {
      type: Number,
      default: 0,
    },
    likes: {
      type: Number,
      default: 0,
    },
    fans: {
      type: Number,
      default: 0,
    },
  });

  return mongoose.model('ExpertDataStatistics', ExpertDataStatisticsSchema, 'expertDataStatistics');
};
