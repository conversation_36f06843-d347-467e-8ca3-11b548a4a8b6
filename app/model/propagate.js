
module.exports = app => {
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;
  const shortid = require('shortid');

  const PropagateSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    othersId: String, // 第三方的培训记录id,作为对接用的
    EnterpriseID: String, // 公司id
    createTime: Date, // 创建时间
    type: String, // 培训类型
    hours: String, // 培训学时
    partakeDepartment: Array, // 参与部门
    personnel: Array, // 参与人员
    content: String, // 培训内容
    organizeDepartment: String, // 组织部门
    lecturer: String, // 授课人
    implementData: Date, // 实施日期
    annex: String, // 附件数量
    year: String, // 年度
    startTime: Date, // 开始时间
    endTime: Date, // 结束时间
    allPeopleNumber: String, // 当前人数
    annex_plan: [{ // 培训计划附件
      fileType: String,
      name: String,
      staticName: String,
      staticSrc: String,
    }],
    annex_prove: [{ // 培训证明附件
      fileType: String,
      name: String,
      staticName: String,
      staticSrc: String,
    }],
    annex_summary: [{ // 培训总结附件
      fileType: String,
      name: String,
      staticName: String,
      staticSrc: String,
    }],
    annex_data: [{ // 培训资料附件
      fileType: String,
      name: String,
      staticName: String,
      staticSrc: String,
    }],
    personalTrainingId: { // 个人培训id
      type: String,
      ref: 'PersonalTraining',
    },
    source: { // 数据来源
      type: String,
      default: 'oapi',
      enum: [ 'oapi', 'qzpx', 'qy', 'selfCreated' ],
    },
  });

  return mongoose.model('propagate', PropagateSchema);
};
