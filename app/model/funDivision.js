/**
 * created by JHw
 * 机构端-service 员工角色表
 * @param app
 */
module.exports = app => {
  const mongoose = app.mongoose;
  const shortid = require('shortid');
  const Schema = mongoose.Schema;

  const funDivisionSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    serviceOrgId: { type: String, ref: 'Adminorg' }, // 机构id
    employeeIds: Array, // 员工id
    departIds: Array, // 部门id 二维数组 部门和人员只能选一个
    assignmentType: { // 分配类型 部门或员工
      type: String,
      enum: [ 'depart', 'employee' ],
    },
    parentGroup: String, // 上层分组
    desc: String, // 描述
    name: String, // 角色名
    // employees: Array, // 员工id 关联
    alias: String, // 为了保证以下角色的唯一性
    readonly: { // 是否不可编辑
      type: Boolean,
      default: false,
    },
    /**
       * 1. 市场：marketing，2. 技术：technicist，3. 检测: detection，4. 评价： comment，
       * 5. 实验室：lab，6.实验室(油雾组)：YW，7. 实验室(原吸组)：AAS，8. 实验室(分光组)：FG，9. 实验室(电极法)：ISE，
       * 10. 实验室(粉尘组)：Dust，11. 实验室(气相色谱组)：GLC，12.实验室(气质组)：GC-MS， 13.实验室(游离二氧化硅组)：FSiO2 ，14.'实验室(离子色谱组)：IC
       * 15. 签收人：signer
       */
    // tableData: [{
    //   _id: {
    //     type: String,
    //     default: shortid.generate,
    //   },
    //   employeeIds: Array, // 员工id
    //   name: String, // 角色名
    //   // employees: Array, // 员工id 关联
    //   alisa: String, // 为了保证以下角色的唯一性
    //   /**
    //    * 1. 市场：marketing，2. 技术：technicist，3. 检测: detection，4. 评价： comment，
    //    * 5. 实验室：lab，6.实验室(油雾组)：YW，7. 实验室(原吸组)：AAS，8. 实验室(分光组)：FG，9. 实验室(电极法)：ISE，
    //    * 10. 实验室(粉尘组)：Dust，11. 实验室(气相色谱组)：GLC，12.实验室(气质组)：GC-MS， 13.实验室(游离二氧化硅组)：FSiO2 ，14.'实验室(离子色谱组)：IC
    //    * 11. 综合:comprehensiveOfTechnicist，12.业务统计:businessStatistics 13.实验室主管 labManager 14.实验室复核人 labReviewers
    //    */

    // }],
  });

  return mongoose.model('funDivision', funDivisionSchema, 'funDivision');

};

