/**
 * 全流程个人剂量项目表
 */


module.exports = app => {
  const mongoose = app.mongoose;
  const shortid = require('shortid');
  const Schema = mongoose.Schema;

  // 定义子 Schema,项目表主子 Schema 在下面
  const progressSchema = new Schema({
    // 项目创建时间
    createProject_time: {
      status: { // 0 未完成 1 进行中 2 已完成
        type: Number,
        default: 2,
      },
      completedTime: {
        type: Date,
        default: Date.now,
      },
    },
    // 合同评审状态
    approved: {
      status: { // 0 未创建 1 进行中（审批中） 2 已完成（已同意）3 已拒绝  4 被终止  5 已取消
        type: Number,
        default: 0,
      },
      completedTime: Date, // 状态变化时间
    },
    // 检测组分配状态
    projectGroup: {
      status: { // 0 未完成 1 进行中 2 已完成
        type: Number,
        default: 0,
      },
      completedTime: Date, // 完成时间
    },
    // 检测项目经理分配状态
    PersonInCharge: {
      status: { // 0 未完成 1 进行中 2 已完成
        type: Number,
        default: 0,
      },
      completedTime: Date, // 完成时间
    },
    wearCycleStatus: {
      status: { // 0.未分配  1.待分配 2.已分配
        type: Number,
        default: 0,
      },
      completedTime: Date, // 完成时间
    },
    // 寄样收样状态
    sampleStatus: {
      status: { // 0.未寄出  1.已寄出 2.已收样  (这里记录整个项目的收样寄样状态，缺样状态将由缺样数得来)
        type: Number,
        default: 0,
      },
      completedTime: Date, // 完成时间
    },
    reportAnalysis: { // 报告分析状态
      status: { // 0 未完成 1 进行中 2 已完成
        type: Number,
        default: 0,
      },
      completedTime: Date, // 完成时间
    },
    personReport: { // 报告生成状态
      status: { // 0 未生成 1 进行中 2 已生成
        type: Number,
        default: 0,
      },
      completedTime: Date, // 完成时间 每次生成报告单的时间
    },
    // 报告单审批状态
    reportApproved: {
      status: { // 0 未创建 1 进行中（审批中） 2 已完成（已同意）3 已拒绝  4 被终止
        type: Number,
        default: 0,
      },
      completedTime: Date, // 完成时间
    },
    // 报告签发
    reportIssue: {
      status: { // 0 未签发 2 已签发
        type: Number,
        default: 0,
      },
      completedTime: Date, // 完成时间
    },
    officialReportUpload: { // 正式稿
      status: { // 0 未完成 2 已完成
        type: Number,
        default: 0,
      },
      completedTime: Date, // 完成时间
    },
    // 报告打印状态
    printReport: {
      status: { // 0 未打印 2 已打印
        type: Number,
        default: 0,
      },
      completedTime: Date, // 完成时间
    },
    // 归档状态
    pigeonhole: {
      status: { // 0 未完成 2 已完成
        type: Number,
        default: 0,
      },
      completedTime: Date, // 完成时间
    },
  }, { _id: false });
  // 主文档 Schema
  const PersonalDoseProjectSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    createdAt: {
      type: Date,
      default: Date.now(),
    }, // 创建时间
    updatedAt: Date, // 修改时间
    startAt: Date, // 项目启动时间
    parentId: { // 项目父级id，根级是0
      type: String,
      default: '0',
    },
    hasChildren: { // 是否有子项目
      type: Boolean,
      default: false,
    },
    subProjects_type: {
      type: String,
      default: '',
    },
    pigeonholeCatalogue: { // 归档目录
      name: { type: String, default: '' }, // 文件夹名称
      url: { type: String, default: '' }, // 文件夹下载地址
    },
    // 子项目类型，Y是预采样，F是复测，B是补测，Z是子项目，数组为第几周期的检测项，正式项目则为空字符串
    pigeonholeFile: { // 归档文件夹
      name: { type: String, default: '' }, // 文件夹名称
      url: { type: String, default: '' }, // 文件夹下载地址
    },
    sampleListFile: { // 流转单
      name: { type: String, default: '' }, // 文件夹名称
      url: { type: String, default: '' }, // 文件夹下载地址
    },
    officialReportUploadFileName: { // 正式稿
      name: { type: String, default: '' }, // 文件夹名称
      url: { type: String, default: '' }, // 文件夹下载地址
      noStampUrl: { type: String, default: '' }, // 无章版，pdf
    },
    issuer: { // 报告签发人
      name: String,
      employeeId: String, // serviceEmployee id
    },
    issueDate: Date, // 报告签发日期

    completeReportArchive: {
      status: { // 0 未完成 1 已完成
        type: Number,
        default: 0,
      }, // 是否确认签收归档 默认不签收0 已经签收1
      completedTime: Date, // 完成时间
    },
    reportArchiveSigner: {
      signPath: String, // 签名
      serviceEmployeeId: String,
    }, // 档案签收人

    // ==================== 受检单位信息
    EnterpriseID: { type: String, ref: 'Adminorg' }, // 企业ID
    EnterpriseName: { type: String }, // 企业名称
    companyID: {
      type: String,
    },
    corp: String, // 受检单位法人
    regType: String, // 受检单位注册类型/经济类型
    companyScale: {
      type: String,
      // enum: [ '大型', '中型', '小型', '微型', '其他' ],
    },
    companyContact: {
      type: String,
      default: '',
    }, // 受检单位联系人
    companyContactPhoneNumber: {
      type: String,
      default: '',
    }, // 受检单位联系人手机号码
    companyContactEmail: {
      type: String,
      default: '',
    }, // 受检单位邮箱
    companyIndustry: {
      type: Array,
      default: [],
    }, // 受检单位'所属行业',
    riskLevel: {
      type: String,
    }, // 受检单位风险等级，初始时从adminorg中的level来，随行业分类（companyIndustry）而改变
    regAdd: String, // 注册地址
    districtRegAdd: {
      type: Array,
      default: [],
    }, // 受检单位注册地址
    // 申报地址，由于用户可以自行修改，所以和下面的workAdd区别开，
    // 存area_code，该code用于后注册的行政端也能读取申报的项目列表，
    // 存area_code前六位，即区县代码有效位数，其父级单位通过代码即可找到，全国的则需特殊处理
    workPlaces: [{ // 受检单位的工作场所地址
      _id: {
        type: String,
        default: shortid.generate,
      },
      workAddName: String, // 工作场所的中文地址

      name: String, // 工作场所的名称
      workAdd: {
        type: Array,
      }, // 工作场所具体地址，area_code
      checked: {
        type: Boolean,
        default: true,
      }, // 是否被检测，默认一般是检测了
    }], // 检测的工作场所，用于识别


    // ==================== 委托单位信息
    anthemEnterpriseID: {
      type: String,
      ref: 'Adminorg',
    }, // 委托单位id
    anthemCompanyID: {
      type: String,
    }, // 委托单位ID ，取信用代码
    newAnthemCompany: {
      type: String,
    }, // 新增的委托单位
    anthemCompanyName: {
      type: String,
      default: '',
    }, // 委托单位名称
    anthemCompanyContact: {
      type: String,
      default: '',
    }, // 用人单位联系人
    anthemCompanyContactPhoneNumber: {
      type: String,
      default: '',
    }, // 委托单位联系人手机号码
    anthemCompanyContactEmail: {
      type: String,
      default: '',
    }, // 委托单位邮箱
    anthemCompanyRegAdd: String, // 注册地址
    anthemCompanyDistrictRegAdd: {
      type: Array,
      default: [],
    }, // 委托单位注册地址
    // anthemCompanyAddress: [{
    //   districts: Array, // 营业执照注册地址
    //   address: String, // 具体地址
    //   _id: {
    //     type: String,
    //     default: shortid.generate,
    //   },
    // }], // 委托单位工作地址
    serviceOrgId: { type: String, ref: 'ServiceOrg' }, // 机构id
    serviceCon: {
      type: String,
      default: '',
    }, // 服务内容


    // ============================================项目相关信息
    preparedDate: [ Date, Date ], // 方案编制日期
    projectName: { type: String }, // 检测项目名称
    shortProjectName: { type: String }, // 检测项目简称
    projectSN: { type: String }, // 检测项目编号
    serviceType: { type: String }, // 技术服务类型
    expectStartTime: {
      type: Date,
    }, // 预计开始时间
    expectStopTime: {
      type: Date,
    }, // 预计结束时间,
    requiredTime: {
      type: Date,
    }, // 要求/期望完成的时间
    detectionDate: {
      type: Date,
    }, //  检测时间 和现场检测时间对应
    completeStatus: {
      type: Number,
      default: 1,
    }, // 完成状态，1，未完成； 2，已完成
    // applyTime: {
    //   type: Date,
    // }, // 上报时间
    completedTime: {
      type: Date,
    }, // 实际完成时间
    // status: {
    //   type: Number,
    //   default: 0,
    // }, // 申报状态,0，未报送，1，已报送，
    // projectStatus:{
    //   type:Number,
    //   default:0
    // }, // 项目状态 0 新创建 1 进行中 2 已完成 3 暂停 4 终止
    projectStop: {
      type: Boolean,
      dafault: false,
    }, // 项目是否暂停，false：未暂停，true：暂停
    projectCancel: {
      type: Boolean,
      dafault: false,
    }, // 项目是否终止，false：未终止，true：终止
    personInCharge: {
      type: String,
      default: '',
      ref: 'ServiceEmployee',
    }, // '项目负责人ID',
    archivePerson: { // 归档人
      type: String,
      default: '',
      ref: 'ServiceEmployee',
    },
    salesman: { // 业务经理 serviceemployeeId
      type: String,
      default: '',
    },
    farmOut: { // 是否分包lht+(chenke)
      type: Boolean,
    },
    farmOutParams: { // 分包参数
      type: String,
    },
    description: { // 项目简介
      type: String,
    },
    vipRequirement: { // vip需求
      type: String,
    },

    // =============钱money
    projectPrice: {
      type: Number,
      default: 0,
    }, // 项目价格(合同金额)
    cooperationFee: {
      type: Number,
      default: 0,
    }, // 合作费
    expectedCooperationFee: {
      type: Number,
      default: 0,
    }, // 预计合作费
    reviewFee: {
      type: Number,
      default: 0,
    }, // 评审费
    expectedReviewFee: {
      type: Number,
      default: 0,
    }, // 预计评审费
    otherFee: {
      type: Number,
      default: 0,
    }, // 其他费
    expectedOtherFee: {
      type: Number,
      default: 0,
    }, // 预计其他费
    netEarnings: {
      type: Number,
      default: 0,
    }, // 净赚=价格-合作费-评审费-其他费
    expectedNetEarnings: {
      type: Number,
      default: 0,
    }, // 预计净赚=价格-合作费-评审费-其他费
    outsourceFee: { // 分包费
      type: Number,
      default: 0,
    },
    expectedOutsourceFee: { // 预计分包费
      type: Number,
      default: 0,
    },

    // ============== 快递
    mailingAddress: Array, // 邮寄地址
    mailingAddressInfo: String, // 邮寄详细地址
    recipient: {
      type: String,
      default: '',
    }, // 收件人
    recipientPhoneNum: {
      type: String,
      default: '',
    }, // 收件号码

    // =============银行
    accountsBank: {
      type: String,
      default: '',
    }, // 开户行
    accountsAddress: {
      type: String,
      default: '',
    }, // 开户地址
    accountsNumber: {
      type: String,
      default: '',
    }, // 开户账号
    accountsPhoneNum: {
      type: String,
      default: '',
    }, // 开票电话
    tariffNumber: {
      type: String,
      default: '',
    }, // 税号
    isVIP: { // 会员类型：战略客户、vip客户、普通客户
      type: String, // svip、vip、ordinary
      default: 'ordinary',
    },
    source: {
      type: String,
      default: 'service', // ck || service
    }, // 数据来源
    isUrgent: {
      type: Boolean,
      default: false,
    }, // 是否加急
    comment: String, // 整个项目的备注
    serviceArea: [{
      type: String,
      match: /^[0-9]*$/,
    }], // 关于此次项目的企业涉及的技术服务领域，存得是编码，报送信息中要用 [采矿业:'1','化工、石化及医药':'2','冶金、建材':'3','机械制造、电力、纺织、建筑和交通运输等行业领域':'4','核设施':'5','核技术应用':'6']
    entrustDate: { type: Date }, // 委托时间
    process_instance_id: String, // 合同审批实例id

    projectType: String, // 项目类型
    checkType: String, // 检测类型 内照射 外照射 默认外照射
    radiationSourceType: String, // 辐射源类型
    templateName: String, // 模板名称
    environmentInfo: { // 检测环境信息
      startTemperature: String, //  检测开始温度
      endTemperature: String, // 检测结束温度
      starthumidity: String, //  检测开始湿度
      endhumidity: String, // 检测结束湿度
      pressure: String, // 气压
    },
    // measurementSituation: {
    //   category: String, // 防护检测|性能检测
    //   measureValues: Array, // 测量值
    //   measurerange: [ String, String ],
    //   averageVal: String, // 平均值
    //   calibrationVal: String, // 校准值
    //   deviationValue: String, // 偏差值
    // }, // 本底测量情况
    // 设备检测数据
    // 文件
    radiateDetectReportDate: Date, // 初稿时间
    firstDraftFileName: {
      name: String,
      url: String,
    }, // 初稿文件

    // 报告审批状态以及审批实例id
    reportProcessInstanceId: String, // 报告单审批实例id
    // reportApprovedStartTime: Date, // 报告单审批发起时间
    // approvedWordFileName: String, // 合同审批word文件名称
    modifyRecordFileName: String, // 归档修改记录单
    // contractApprovedUsers: [{ // 合同审批人
    //   _id: {
    //     type: String,
    //     default: shortid.generate,
    //   },
    //   name: String, // employeeName
    //   signType: String, // 审批类型 approvedUsers(评审人员)|reviewTeamLeader（评审负责人）
    //   serviceEmployeeId: String, // serviceEmployeeId
    //   fileName: String, // 文件名
    // }],

    // 第几周期
    quarterStatus: [{
      _id: {
        type: String,
        default: shortid.generate,
      },
      quarter: Number, // 第几周期
      quarterProjectId: String, // 第几周期的项目ID
      progress: progressSchema, // 第几周期的项目进度
    }],

    progress: progressSchema, // 项目进度

    // 个人剂量报告单
    personReportFileName: {
      name: String,
      url: String,
    },
    // 审核记录
    auditRecordsFile: {
      name: String,
      url: String,
    },
    // 合同评审
    approvedWordFile: {
      name: String,
      url: String,
    },
    // 方案预计检测人数
    checkPeopleCount: {
      type: Number,
      default: 0,
    },
    // 佩戴周期
    wearCycle: {
      wearStartTime: Date, // 佩戴开始时间
      wearEndTime: Date, // 佩戴结束时间
    },
    detectionCycle: { // 检测周期
      type: Number,
      default: 90,
    },

    sentSampleTime: Date, // 寄样时间
    receiveSampleDate: Date, // 收样时间
    expectedReturnTime: Date, // 预期收回时间  = 寄样时间+检测周期

    // 寄样人
    sampleSender: {
      name: String,
      employeeId: String,
    },
    sentSamplesNumber: { type: Number, default: 0 }, // 寄样数量
    receiveSamplesNumber: { type: Number, default: 0 }, // 收样数量
    missingSampleCount: { type: Number, default: 0 }, // 缺样数

    instrument: [{
      instrumentName: String, // 仪器名
      internalNumber: String, // 仪器编号
      instrumentID: String, // 仪器id
      instrumentModel: String, // 仪器型号
      calibrationFactor: String, // 校准因子
      detectionLevel: String, // 探测水平
      inspectionCertificateId: String, // 检定证书id
      completeSampleTime: String, // 填写时间
      _id: {
        type: String,
        default: shortid.generate,
      },
    }], // 仪器
    // 被测人员名单
    peopleOrder: [
      {
        _id: {
          type: String,
          default: shortid.generate,
        },
        peopleName: String, // 姓名
        peopleSex: String, // 性别
        occupation: String, // 职业类别
        radiationQuality: String, // 放射质量
        department: String, // 部门
        instrument: [{
          instrumentName: String, // 仪器名
          internalNumber: String, // 仪器编号
          instrumentID: String, // 仪器id
          instrumentModel: String, // 仪器型号
          calibrationFactor: String, // 校准因子
          completeSampleTime: String, // 填写时间
          _id: {
            type: String,
            default: shortid.generate,
          },
        }], // 仪器
        sampleComment: String, // 收样异常备注
        sampleSN: String, // 样品编号
        // sampleNo: String, // 序号  0001 0002
        // 佩戴周期
        wearCycle: {
          wearStartTime: Date, // 佩戴开始时间
          wearEndTime: Date, // 佩戴结束时间
        },
        sample: {
          // 样品编号
          sentSampleTime: Date, // 寄样时间
          isCompleteConfirm: Boolean, // 是否完成寄样
          remark: String, // 缺样/异常备注
          receiveSampleDate: Date, // 每个样品的收样时间
          isCompleteReceive: Boolean, // 是否完成收样
          sampleStatus: String, // 寄样状态  0.未寄出  1.已寄出 2.已收样  3.缺样  4.异常

          XValue: [ String ], // X值
          AValue: [ String ], // A值
          A_avgValue: String, // 个人剂量当量A均值
          resultValue: String, // 检测值

          completeSampleTime: Date, // 填写时间
          samplingStaff: String, // 检测人
        },
      },
    ],

    noticeData: { // 客户通知相关数据
      status: { // 通知状态，0：未通知 1：已通知
        type: Number,
        default: 0,
      },
      expirationDate: Date, // 有效期
      contactNumber: String, // 联系电话
      code: String, // 提取码
      downloadNumer: {
        type: Number,
        default: 0,
      }, // 下载次数
    },

    // 分析数据
    analysisRecord: {
      analysisDate: Date, // 分析检测日期
      temperature: String, // 温度
      humidity: String, // 湿度
      baselineValue: [], // 本底值
      baselineAvgValue: Number, // 本底值平均值
      samplingStaff: String, // 检测人
    },
    // 分析报告
    analysisReport: {
      staticName: String,
      filePath: String,
    },
  });


  return mongoose.model('personalDoseProject', PersonalDoseProjectSchema, 'personalDoseProject');

};

