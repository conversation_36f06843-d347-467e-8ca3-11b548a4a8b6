/**
 * 机构端-机构资质表
 */

module.exports = app => {
  const mongoose = app.mongoose;
  const shortid = require('shortid');
  const Schema = mongoose.Schema;
  const upload_http_path = app.config.upload_http_path || '';

  const DetectionMechanismSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    name: { // 机构名称
      type: String,
      default: '',
    },
    org_id: {
      type: String,
      default: '',
      ref: 'ServiceOrg',
    },
    mechanism_name: { // 资质证书名称
      type: String,
      default: '',
    },
    img: { // 机构资质图
      type: String,
      default: '',
      trim: true,
      set(val) {
        return (val && val.indexOf(upload_http_path) !== -1) ? val.split(upload_http_path)[1] : val;
      },
    },
    COC: String, // 组织机构代码
    corp: String, // 法人代表
    level: { // 机构资质等级
      type: String,
      default: '无',
    },
    levelNum: Number, // 机构资质等级编号(用于排序) 0：无等级 1：甲级 2：乙级 3：丙级
    NO: String, // 机构资质证书编号
    lineOfBusiness: String, // 业务范围
    regType: String, // 登记注册类型
    validTime: { // 资质证书有效期至
      type: Date,
    },
    ctime: { // 创建/更改时间
      type: Date,
      default: Date.now,
    },
    status: { // 存续/启用状态
      type: Boolean,
      default: true,
    },
  });


  return mongoose.model('DetectionMechanism', DetectionMechanismSchema, 'detectionMechanism');

};

