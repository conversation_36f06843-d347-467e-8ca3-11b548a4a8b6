/**
 * 放射全流程状态信息
 * 当创建流程时，会生成一份属于该机构的状态信息
 * 当创建项目时，会查询该机构的状态信息，并创建一条属于该项目的状态信息
 */

module.exports = app => {
  const mongoose = app.mongoose;
  const shortid = require('shortid');
  const Schema = mongoose.Schema;

  const RadiateqlcProgressSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    createdAt: {
      type: Date,
      default: Date.now(),
    }, // 创建时间
    updatedAt: {
      type: Date,
      default: Date.now(),
    }, // 修改时间
    serviceOrgId: String, // 机构id
    isInit: {
      type: Boolean,
      default: false,
    }, // 是否为机构初始化的状态信息
    radiateqlcProjectId: String, // 项目id
    currentProgress: String, // 当前所在节点(进行中的节点),子节点除外
    currentSortNum: Number, // 当前所在节点(进行中的节点)索引,子节点除外
    progresses: [{
      _id: '',
      nodeName: String, // 节点名称
      field: String, // 状态字段
      status: Number, // 状态 1：进行中  2：已完成
      completedTime: Date, // 节点变化时的时间
      parentField: String, // 如果有父级状态，那么当前状态是子级状态，比如 性能检测 父级状态是 现场检测
      sortNum: Number, // 节点序号 从0开始 子节点没有序号
      routerName: String, // 节点路由
    }],
  });

  return mongoose.model('radiateqlcProgress', RadiateqlcProgressSchema, 'radiateqlcProgress');

};

