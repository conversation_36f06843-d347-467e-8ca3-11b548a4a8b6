
module.exports = app => {
  const shortid = require('shortid');
  // 员工信息表
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;
  const dingtree = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    unitCode: {
      type: String,
      default: '',
    },
    EnterpriseID: String,
    name: String, // 部门名称
    id: String, // 钉钉生成的id
    parentid: String, // 父级id
    type: String,
    sortId: {
      type: Number,
      default: 0,
    },
    staff: [{ type: String, ref: 'Employees' }], // 员工
    topLevelOfTheEnterprise: {
      // 是否为企业顶级部门
      type: Boolean,
      default: false,
    },
    // 假删
    isDelete: {
      type: Boolean,
      default: false,
    },
  });
  return mongoose.model('Dingtrees', dingtree, 'dingtrees');
};
