module.exports = app => {
  const mongoose = app.mongoose;
  const shortid = require('shortid');
  const Schema = mongoose.Schema;

  const UserGroupSchema = new Schema({
    _id: {
      type: String,

      default: shortid.generate,
    },
    name: String,
    power: [{
      type: String,
      ref: 'UserResource',
    }],
    date: {
      type: Date,
      default: Date.now,
    },
    comments: String,
  });


  return mongoose.model('UserGroup', UserGroupSchema, 'userGroups');

};
