module.exports = app => {
  const shortid = require('shortid');
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;

  // 支付信息记录 xxn 2023/2/1
  const PayInfoSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    out_trade_no: { // 支付订单编号
      type: String,
      require: true,
      unique: true,
    },
    payStatus: { // 支付状态
      type: Number,
      default: 0,
      enum: [ 0, 1, 2, 3, 4, 5, 6, 7, 8, 9 ], // 0 待支付 1 支付成功 2 支付失败 3 放弃支付 4 已退款 5 交易结束，不支持退款 6 退款异常 7 退款关闭 8 退款中 9 待审核
    },
    type: { // 支付方式
      type: String,
      require: true,
      enum: [ 'wxpay', 'alipay', 'bankTransfer' ], // 微信支付 支付宝支付 银行转账
    },
    price: { // 订单总价, 单位为人民币/元
      type: Number,
      default: 0,
      set: val => Number(val),
    },
    productCategory: { // 支付的产品类别
      type: Number,
      default: 1,
      enum: [ 1, 2, 3 ], // 1 培训证书 2 培训团购CDK 3 企业套餐
    },
    isCommission: { // 是否给代理分配过佣金
      type: Number,
      default: 0,
      enum: [ 0, 1, 2, 3, 4 ], // 0 未分配 1 分配中 2 已分配 3 分配失败 4 分配撤销
    },
    commissionRemarks: { // 佣金分配备注
      type: String,
      default: '',
    },
    description: { // 产品名称/描述
      type: String,
      require: true,
    },
    paymentUnitID: { // 付款单位_id
      type: String,
      ref: 'Adminorg',
    },
    userID: { // 支付者登录时的用户_id
      type: String,
      ref: 'User',
    },
    refundInfo: { // 退款信息
      out_refund_no: { // 退单号
        type: String,
        require: true,
      },
      reason: { // 退款原因描述
        type: String,
        require: true,
      },
      applicationTime: Date, // 退款申请时间
      refundAmount: Number, // 退款金额，单位为元
      refundTime: Date, // 退款到账时间
      authorID: { // 运营端退款操作人id,
        type: String,
        ref: 'OperateUser',
      },
    },
    invoiceStatus: { // 发票状态
      type: Number,
      default: 0,
      enum: [ 0, 1, 2, 3, 4 ], // 0 未申请 // 1 处理中 // 2 开票中  // 3 开票失败 // 4 开票成功
    },
    invoice: { // 发票信息
      tax_type: { // 发票类型
        type: Number,
        enum: [ 0, 1 ], // 0 电子普通发票 1 增值税专用发票
      },
      title_type: { // 抬头类型
        type: Number,
        enum: [ 0, 1 ], // 个人 单位
      },
      payer_name: { // 抬头名称
        type: String,
      },
      payer_tax_no: { // 单位税号
        type: String,
      },
      payer_address: { // 注册地址
        type: String,
      },
      payer_phone: { // 注册电话
        type: String,
      },
      payer_bank_name: { // 开户银行
        type: String,
      },
      payer_bank_account: { // 银行账号
        type: String,
      },
      payer_email: { // 邮箱
        type: String,
        require: true,
      },
      url: { // 用于上传发票
        type: String,
      },
    },
    paySuccessTime: Date, // 支付成功时间
    usable: { // 是否可用,假删备用的字段
      type: Boolean,
      default: true,
    },
    source: { // 数据来源
      type: String,
      enum: [ 'oapi', 'qy' ],
      default: 'oapi',
    },
  }, {
    timestamps: true,
  });


  return mongoose.model('PayInfo', PayInfoSchema, 'payInfo');
};
