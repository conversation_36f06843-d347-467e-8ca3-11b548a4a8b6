// 预警列表
module.exports = app => {
  const shortid = require('shortid');
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;

  const WarningSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    supervisionId: [
      { // 监管id
        type: String,
        ref: 'SuperUser',
      },
    ],
    jobHealthId: { // 危害检测报告checkAssessment 或者体检报告healthcheck 或者职业病odiseases, 个人体检 suspect
      type: String,
      require: true,
    },
    modelName: { // jobHealthId所指向的表
      type: String,
    },
    type: { // 预警类型：1 危害检测 2 是体检报告 3是职业病诊断
      type: Number,
      default: 2,
      enum: [ 1, 2, 3 ],
    },
    companyId: {
      type: String,
      ref: 'Adminorg',
      require: true,
    },
    companyName: {
      type: String,
      require: true,
    },
    workAddress: [ Array ], // 项目的工作场所， 比如 [["浙江省", "衢州市", "龙游县","湖镇镇"]],
    delete: { // 是否已被删除
      type: Boolean,
      default: false,
    },
    modifiedStatus: { // 修改项目和预警数据 状态： 0是可修改 1是不可修改 2是已提交修改申请 3是监管端已同意修改申请 4是申请被驳回
      type: Number,
      default: 0,
      enum: [ 0, 1, 2, 3, 4 ], // 提交修改的企业/机构名称会记录在process中
    },
    status: { // 预警状态： 0是生成预警 1 处理预警 2 完成整改 3解除预警 4驳回整改 5 已撤销
      type: Number,
      default: 0,
      enum: [ 0, 1, 2, 3, 4, 5 ],
    },
    lever: { // 风险等级
      type: Number,
      default: 4,
      enum: [ 1, 2, 3, 4 ],
    },
    ctime: { // 预警时间
      type: Date,
      default: Date.now,
    },
    rectifyTime: { // 最新整改时间
      type: Date,
    },
    content: [
      { // 最新的预警内容
        type: String,
        trim: true,
      },
    ],
    process: [{ // 事件经过：生成预警，处理预警，完成整改，解除预警
      time: { // 时间
        type: Date,
        default: '',
      },
      thing: { // 事件名称：生成预警，处理预警，完成整改，解除预警
        type: String,
        default: '',
      },
      files: [ // 整改报告
        {
          type: String, // 存的是报告的文件名， 取得时候路径拼接: app.config.upload_warning_http_path + warning_id + 文件名
        },
      ],
      documents: [ String ], // 执法文书, 路径及格式跟files一样
      disposalPlan: Array, // 处置方案
      remark: String, // 备注，解除预警的理由
      content: [
        { // 预警内容快照
          type: String,
          trim: true,
        },
      ],

    }],
    source: {
      type: String,
      default: 'oapi',
    },
  }, { timestamps: true });


  return mongoose.model('Warning', WarningSchema, 'warning');
};
