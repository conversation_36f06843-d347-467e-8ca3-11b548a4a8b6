System.register([],function(t){"use strict";return{execute:function(){t({addErrorHandler:f,checkActivityFunctions:At,declareChildApplication:Pt,ensureJQuerySupport:mt,getAppNames:Et,getAppStatus:Ot,getMountedApps:bt,mountRootParcel:z,navigateToUrl:rt,registerApplication:Tt,removeErrorHandler:l,setBootstrapMaxTime:C,setMountMaxTime:M,setUnloadMaxTime:L,setUnmountMaxTime:I,start:It,triggerAppChange:xt,unloadApplication:Dt,unloadChildApplication:St});var n=Object.freeze({get start(){return It},get ensureJQuerySupport(){return mt},get setBootstrapMaxTime(){return C},get setMountMaxTime(){return M},get setUnmountMaxTime(){return I},get setUnloadMaxTime(){return L},get registerApplication(){return Tt},get getMountedApps(){return bt},get getAppStatus(){return Ot},get unloadApplication(){return Dt},get checkActivityFunctions(){return At},get getAppNames(){return Et},get declareChildApplication(){return Pt},get unloadChildApplication(){return St},get navigateToUrl(){return rt},get triggerAppChange(){return xt},get addErrorHandler(){return f},get removeErrorHandler(){return l},get mountRootParcel(){return z},get NOT_LOADED(){return m},get LOADING_SOURCE_CODE(){return d},get NOT_BOOTSTRAPPED(){return h},get BOOTSTRAPPING(){return v},get NOT_MOUNTED(){return g},get MOUNTING(){return w},get UPDATING(){return b},get MOUNTED(){return y},get UNMOUNTING(){return E},get SKIP_BECAUSE_BROKEN(){return P}}),e=("undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{}).CustomEvent;var r=function(){try{var t=new e("cat",{detail:{foo:"bar"}});return"cat"===t.type&&"bar"===t.detail.foo}catch(t){}return!1}()?e:"undefined"!=typeof document&&"function"==typeof document.createEvent?function(t,n){var e=document.createEvent("CustomEvent");return n?e.initCustomEvent(t,n.bubbles,n.cancelable,n.detail):e.initCustomEvent(t,!1,!1,void 0),e}:function(t,n){var e=document.createEventObject();return e.type=t,n?(e.bubbles=Boolean(n.bubbles),e.cancelable=Boolean(n.cancelable),e.detail=n.detail):(e.bubbles=!1,e.cancelable=!1,e.detail=void 0),e};function o(t){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function i(t,n,e){return n in t?Object.defineProperty(t,n,{value:e,enumerable:!0,configurable:!0,writable:!0}):t[n]=e,t}function a(t,n){var e=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);n&&(r=r.filter(function(n){return Object.getOwnPropertyDescriptor(t,n).enumerable})),e.push.apply(e,r)}return e}function u(t){for(var n=1;n<arguments.length;n++){var e=null!=arguments[n]?arguments[n]:{};n%2?a(e,!0).forEach(function(n){i(t,n,e[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(e)):a(e).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(e,n))})}return t}var c=[];function s(t,n){var e=p(t,n);c.length?c.forEach(function(t){return t(e)}):setTimeout(function(){throw e})}function f(t){if("function"!=typeof t)throw Error("a single-spa error handler must be a function");c.push(t)}function l(t){if("function"!=typeof t)throw Error("a single-spa error handler must be a function");var n=!1;return c=c.filter(function(e){var r=e===t;return n=n||r,!r}),n}function p(t,n){var e,r=n.unmountThisParcel?"Parcel":"Application",o="".concat(r," '").concat(n.name,"' died in status ").concat(n.status,": ");if(t instanceof Error){try{t.message=o+t.message}catch(t){}e=t}else{console.warn("While ".concat(n.status,", '").concat(n.name,"' rejected its lifecycle function promise with a non-Error. This will cause stack traces to not be accurate."));try{e=Error(o+JSON.stringify(t))}catch(n){e=t}}e.appName=n.name,e.appOrParcelName=n.name;try{e.name=n.name}catch(t){}return e}var m=t("NOT_LOADED","NOT_LOADED"),d=t("LOADING_SOURCE_CODE","LOADING_SOURCE_CODE"),h=t("NOT_BOOTSTRAPPED","NOT_BOOTSTRAPPED"),v=t("BOOTSTRAPPING","BOOTSTRAPPING"),g=t("NOT_MOUNTED","NOT_MOUNTED"),w=t("MOUNTING","MOUNTING"),y=t("MOUNTED","MOUNTED"),b=t("UPDATING","UPDATING"),E=t("UNMOUNTING","UNMOUNTING"),O="UNLOADING",P=t("SKIP_BECAUSE_BROKEN","SKIP_BECAUSE_BROKEN");function T(t){return t.status===y}function A(t){return!T(t)}function N(t){return t.status!==m&&t.status!==d}function S(t){return!N(t)}function D(t){try{return t.activeWhen(window.location)}catch(n){s(n,t),t.status=P}}function U(t){try{return!t.activeWhen(window.location)}catch(n){s(n,t),t.status=P}}function j(t){return t!==P&&(!t||t.status!==P)}function _(t){return t.name}var x={bootstrap:{millis:4e3,dieOnTimeout:!1},mount:{millis:3e3,dieOnTimeout:!1},unmount:{millis:3e3,dieOnTimeout:!1},unload:{millis:3e3,dieOnTimeout:!1}};function C(t){var n=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if("number"!=typeof t||t<=0)throw Error("bootstrap max time must be a positive integer number of milliseconds");x.bootstrap={millis:t,dieOnTimeout:n}}function M(t){var n=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if("number"!=typeof t||t<=0)throw Error("mount max time must be a positive integer number of milliseconds");x.mount={millis:t,dieOnTimeout:n}}function I(t){var n=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if("number"!=typeof t||t<=0)throw Error("unmount max time must be a positive integer number of milliseconds");x.unmount={millis:t,dieOnTimeout:n}}function L(t){var n=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if("number"!=typeof t||t<=0)throw Error("unload max time must be a positive integer number of milliseconds");x.unload={millis:t,dieOnTimeout:n}}function B(t,n,e){var r=1e3;return new Promise(function(o,i){var a=!1,u=!1;function c(t){if(!a)if(!0===t)u=!0,e.dieOnTimeout?i("".concat(n," did not resolve or reject for ").concat(e.millis," milliseconds")):console.error("".concat(n," did not resolve or reject for ").concat(e.millis," milliseconds -- we're no longer going to warn you about it."));else if(!u){var o=t,s=o*r;console.warn("".concat(n," did not resolve or reject within ").concat(s," milliseconds")),s+r<e.millis&&setTimeout(function(){return c(o+1)},r)}}t.then(function(t){a=!0,o(t)}).catch(function(t){a=!0,i(t)}),setTimeout(function(){return c(1)},r),setTimeout(function(){return c(!0)},e.millis)})}function G(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return u({},x,{},t)}function R(t,n){for(var e=0;e<t.length;e++)if(n(t[e]))return t[e];return null}function q(t){return t&&("function"==typeof t||(n=t,Array.isArray(n)&&!R(n,function(t){return"function"!=typeof t})));var n}function k(t,n){return 0===(t=Array.isArray(t)?t:[t]).length&&(t=[function(){return Promise.resolve()}]),function(e){return new Promise(function(r,o){!function i(a){var u=t[a](e);W(u)?u.then(function(){a===t.length-1?r():i(a+1)}).catch(o):o("".concat(n," at index ").concat(a," did not return a promise"))}(0)})}}function W(t){return t&&"function"==typeof t.then&&"function"==typeof t.catch}function K(t){var n=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return Promise.resolve().then(function(){return t.status!==h?t:(t.status=v,B(t.bootstrap(Z(t)),"Bootstrapping appOrParcel '".concat(t.name,"'"),t.timeouts.bootstrap).then(function(){return t.status=g,t}).catch(function(e){if(t.status=P,n)throw p(e,t);return s(e,t),t}))})}function F(t){var n=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return Promise.resolve().then(function(){if(t.status!==y)return t;t.status=E;var e=Object.keys(t.parcels).map(function(n){return t.parcels[n].unmountThisParcel()});return Promise.all(e).then(r,function(e){return r().then(function(){var r=Error(e.message);if(n){var o=p(r,t);throw t.status=P,o}s(r,t),t.status=P})}).then(function(){return t});function r(){return B(t.unmount(Z(t)),"Unmounting application ".concat(t.name,"'"),t.timeouts.unmount).then(function(){t.status=g}).catch(function(e){if(n){var r=p(e,t);throw t.status=P,r}s(e,t),t.status=P})}})}var H=!1,$=!1;function J(t){var n=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return Promise.resolve().then(function(){return t.status!==g?t:(H||(window.dispatchEvent(new r("single-spa:before-first-mount")),H=!0),B(t.mount(Z(t)),"Mounting application '".concat(t.name,"'"),t.timeouts.mount).then(function(){return t.status=y,$||(window.dispatchEvent(new r("single-spa:first-mount")),$=!0),t}).catch(function(e){return t.status=y,F(t).then(r,r);function r(){if(n){var r=p(e,t);throw t.status=P,r}return s(e,t),t.status=P,t}}))})}var Q=0,V={parcels:{}};function z(){return X.apply(V,arguments)}function X(t,n){var e=this;if(!t||"object"!==o(t)&&"function"!=typeof t)throw Error("Cannot mount parcel without a config object or config loading function");if(t.name&&"string"!=typeof t.name)throw Error("Parcel name must be a string, if provided");if("object"!==o(n))throw Error("Parcel ".concat(name," has invalid customProps -- must be an object"));if(!n.domElement)throw Error("Parcel ".concat(name," cannot be mounted without a domElement provided as a prop"));var r,i=Q++,a="function"==typeof t,u=a?t:function(){return Promise.resolve(t)},c={id:i,parcels:{},status:a?d:h,customProps:n,parentName:e.name,unmountThisParcel:function(){if(c.status!==y)throw Error("Cannot unmount parcel '".concat(name,"' -- it is in a ").concat(c.status," status"));return F(c,!0).then(function(t){return c.parentName&&delete e.parcels[c.id],t}).then(function(t){return f(t),t}).catch(function(t){throw c.status=P,l(t),t})}};e.parcels[i]=c;var s=u();if(!s||"function"!=typeof s.then)throw Error("When mounting a parcel, the config loading function must return a promise that resolves with the parcel config");var f,l,m=(s=s.then(function(t){if(!t)throw Error("When mounting a parcel, the config loading function returned a promise that did not resolve with a parcel config");var n=t.name||"parcel-".concat(i);if(!q(t.bootstrap))throw Error("Parcel ".concat(n," must have a valid bootstrap function"));if(!q(t.mount))throw Error("Parcel ".concat(n," must have a valid mount function"));if(!q(t.unmount))throw Error("Parcel ".concat(n," must have a valid unmount function"));if(t.update&&!q(t.update))throw Error("Parcel ".concat(n," provided an invalid update function"));var e=k(t.bootstrap),o=k(t.mount),a=k(t.unmount);c.status=h,c.name=n,c.bootstrap=e,c.mount=o,c.unmount=a,c.timeouts=G(t.timeouts),t.update&&(c.update=k(t.update),r.update=function(t){return c.customProps=t,Y(function(t){return Promise.resolve().then(function(){if(t.status!==y)throw Error("Cannot update parcel '".concat(t.name,"' because it is not mounted"));return t.status=b,B(t.update(Z(t)),"Updating parcel '".concat(t.name,"'"),t.timeouts.mount).then(function(){return t.status=y,t}).catch(function(n){var e=p(n,t);throw t.status=P,e})})}(c))})})).then(function(){return K(c,!0)}),v=m.then(function(){return J(c,!0)}),w=new Promise(function(t,n){f=t,l=n});return r={mount:function(){return Y(Promise.resolve().then(function(){if(c.status!==g)throw Error("Cannot mount parcel '".concat(name,"' -- it is in a ").concat(c.status," status"));return e.parcels[i]=c,J(c)}))},unmount:function(){return Y(c.unmountThisParcel())},getStatus:function(){return c.status},loadPromise:Y(s),bootstrapPromise:Y(m),mountPromise:Y(v),unmountPromise:Y(w)}}function Y(t){return t.then(function(){return null})}function Z(t){var e=u({},t.customProps,{name:t.name,mountParcel:X.bind(t),singleSpa:n});return t.unmountThisParcel&&(e.unmountSelf=t.unmountThisParcel),e}function tt(t){return Promise.resolve().then(function(){return t.status!==m?t:(t.status=d,Promise.resolve().then(function(){var e=t.loadImpl(Z(t));if(!W(e))throw Error("single-spa loading function did not return a promise. Check the second argument to registerApplication('".concat(t.name,"', loadingFunction, activityFunction)"));return e.then(function(e){var r;return"object"!==o(n=e)&&(r="does not export anything"),q(n.bootstrap)||(r="does not export a bootstrap function or array of functions"),q(n.mount)||(r="does not export a mount function or array of functions"),q(n.unmount)||(r="does not export an unmount function or array of functions"),r?(console.error("The loading function for single-spa application '".concat(t.name,"' resolved with the following, which does not have bootstrap, mount, and unmount functions"),n),s(r,t),t.status=P,t):(n.devtools&&n.devtools.overlays&&(t.devtools.overlays=u({},t.devtools.overlays,{},n.devtools.overlays)),t.status=h,t.bootstrap=k(n.bootstrap,"App '".concat(t.name,"' bootstrap function")),t.mount=k(n.mount,"App '".concat(t.name,"' mount function")),t.unmount=k(n.unmount,"App '".concat(t.name,"' unmount function")),t.unload=k(n.unload||[],"App '".concat(t.name,"' unload function")),t.timeouts=G(n.timeouts),t)})}).catch(function(n){return s(n,t),t.status=P,t}));var n})}var nt={hashchange:[],popstate:[]},et=["hashchange","popstate"];function rt(t){var n,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if("string"==typeof t)n=t;else if(this&&this.href)n=this.href;else{if(!(t&&t.currentTarget&&t.currentTarget.href&&t.preventDefault))throw Error("singleSpaNavigate must be either called with a string url, with an <a> tag as its context, or with an event whose currentTarget is an <a> tag");n=t.currentTarget.href,t.preventDefault()}var r=lt(window.location.href),o=lt(n);if(0===n.indexOf("#"))window.location.hash="#"+o.anchor;else if(r.host!==o.host&&o.host){if(e.isTestingEnv)return{wouldHaveReloadedThePage:!0};window.location.href=n}else!function(t,n){return n===t||n==="/"+t}(o.path+"?"+o.query,r.path+"?"+r.query)?window.history.pushState(null,null,n):window.location.hash="#"+o.anchor}function ot(t){var n=this;if(t){var e=t[0].type;et.indexOf(e)>=0&&nt[e].forEach(function(e){e.apply(n,t)})}}function it(){Ct([],arguments)}window.addEventListener("hashchange",it),window.addEventListener("popstate",it);var at=window.addEventListener,ut=window.removeEventListener;window.addEventListener=function(t,n){if(!("function"==typeof n&&et.indexOf(t)>=0)||R(nt[t],function(t){return t===n}))return at.apply(this,arguments);nt[t].push(n)},window.removeEventListener=function(t,n){if(!("function"==typeof n&&et.indexOf(t)>=0))return ut.apply(this,arguments);nt[t]=nt[t].filter(function(t){return t!==n})};var ct=window.history.pushState;window.history.pushState=function(t){var n=ct.apply(this,arguments);return it(ft(t)),n};var st=window.history.replaceState;function ft(t){try{return new PopStateEvent("popstate",{state:t})}catch(e){var n=document.createEvent("PopStateEvent");return n.initPopStateEvent("popstate",!1,!1,t),n}}function lt(t){for(var n={strictMode:!0,key:["source","protocol","authority","userInfo","user","password","host","port","relative","path","directory","file","query","anchor"],q:{name:"queryKey",parser:/(?:^|&)([^&=]*)=?([^&]*)/g},parser:{strict:/^(?:([^:\/?#]+):)?(?:\/\/((?:(([^:@]*)(?::([^:@]*))?)?@)?([^:\/?#]*)(?::(\d*))?))?((((?:[^?#\/]*\/)*)([^?#]*))(?:\?([^#]*))?(?:#(.*))?)/,loose:/^(?:(?![^:@]+:[^:@\/]*@)([^:\/?#.]+):)?(?:\/\/)?((?:(([^:@]*)(?::([^:@]*))?)?@)?([^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/}},e=n.parser.strict.exec(t),r={},o=14;o--;)r[n.key[o]]=e[o]||"";return r[n.q.name]={},r[n.key[12]].replace(n.q.parser,function(t,e,o){e&&(r[n.q.name][e]=o)}),r}window.history.replaceState=function(t){var n=st.apply(this,arguments);return it(ft(t)),n},window.singleSpaNavigate=rt;var pt=!1;function mt(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:window.jQuery;if(t||window.$&&window.$.fn&&window.$.fn.jquery&&(t=window.$),t&&!pt){var n=t.fn.on,e=t.fn.off;t.fn.on=function(t,e){return dt.call(this,n,window.addEventListener,t,e,arguments)},t.fn.off=function(t,n){return dt.call(this,e,window.removeEventListener,t,n,arguments)},pt=!0}}function dt(t,n,e,r,o){return"string"!=typeof e?t.apply(this,o):(e.split(/\s+/).forEach(function(t){et.indexOf(t)>=0&&(n(t,r),e=e.replace(t,""))}),""===e.trim()?this:t.apply(this,o))}var ht={};function vt(t){return Promise.resolve().then(function(){var n=ht[t.name];return n?t.status===m?(gt(t,n),t):t.status===O?n.promise.then(function(){return t}):t.status!==g?t:(t.status=O,B(t.unload(Z(t)),"Unloading application '".concat(t.name,"'"),t.timeouts.unload).then(function(){return gt(t,n),t}).catch(function(e){return function(t,n,e){delete ht[t.name],delete t.bootstrap,delete t.mount,delete t.unmount,delete t.unload,s(e,t),t.status=P,n.reject(e)}(t,n,e),t})):t})}function gt(t,n){delete ht[t.name],delete t.bootstrap,delete t.mount,delete t.unmount,delete t.unload,t.status=m,n.resolve()}function wt(t,n,e,r){ht[t.name]={app:t,resolve:e,reject:r},Object.defineProperty(ht[t.name],"promise",{get:n})}var yt=[];function bt(){return yt.filter(T).map(_)}function Et(){return yt.map(_)}function Ot(t){var n=R(yt,function(n){return n.name===t});return n?n.status:null}function Pt(t,n,e){return console.warn('declareChildApplication is deprecated and will be removed in the next major version, use "registerApplication" instead'),Tt(t,n,e)}function Tt(t,n,e){var r,i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};if("string"!=typeof t||0===t.length)throw Error("The first argument must be a non-empty string 'appName'");if(-1!==Et().indexOf(t))throw Error("There is already an app declared with name ".concat(t));if("object"!==o(i)||Array.isArray(i))throw Error("customProps must be an object");if(!n)throw Error("The application or loading function is required");if(r="function"!=typeof n?function(){return Promise.resolve(n)}:n,"function"!=typeof e)throw Error("The activeWhen argument must be a function");yt.push({name:t,loadImpl:r,activeWhen:e,status:m,parcels:{},devtools:{overlays:{options:{},selectors:[]}},customProps:i}),mt(),Ct()}function At(t){for(var n=[],e=0;e<yt.length;e++)yt[e].activeWhen(t)&&n.push(yt[e].name);return n}function Nt(){return yt.filter(j).filter(S).filter(D)}function St(t,n){return console.warn('unloadChildApplication is deprecated and will be removed in the next major version, use "unloadApplication" instead'),Dt(t,n)}function Dt(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{waitForUnmount:!1};if("string"!=typeof t)throw Error("unloadApplication requires a string 'appName'");var e=R(yt,function(n){return n.name===t});if(!e)throw Error("Could not unload application '".concat(t,"' because no such application has been registered"));var r,o=function(t){return ht[t]}(e.name);if(n&&n.waitForUnmount){if(o)return o.promise;var i=new Promise(function(t,n){wt(e,function(){return i},t,n)});return i}return o?(r=o.promise,Ut(e,o.resolve,o.reject)):r=new Promise(function(t,n){wt(e,function(){return r},t,n),Ut(e,t,n)}),r}function Ut(t,n,e){F(t).then(vt).then(function(){n(),setTimeout(function(){Ct()})}).catch(e)}var jt=!1,_t=[];function xt(){return Ct()}function Ct(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],n=arguments.length>1?arguments[1]:void 0;if(jt)return new Promise(function(t,e){_t.push({resolve:t,reject:e,eventArguments:n})});jt=!0;var e=!0;return Mt?Promise.resolve().then(function(){window.dispatchEvent(new r("single-spa:before-routing-event",a()));var n=Object.keys(ht).map(function(t){return ht[t].app}).filter(A).map(vt),u=yt.filter(j).filter(T).filter(U).map(F).map(function(t){return t.then(vt)}),c=u.concat(n);c.length>0&&(e=!1);var s=Promise.all(c),f=Nt(),l=f.map(function(t){return tt(t).then(K).then(function(t){return s.then(function(){return J(t)})})});l.length>0&&(e=!1);var p=yt.filter(j).filter(A).filter(N).filter(D).filter(function(t){return f.indexOf(t)<0}).map(function(t){return K(t).then(function(){return s}).then(function(){return J(t)})});return p.length>0&&(e=!1),s.catch(function(t){throw i(),t}).then(function(){return i(),Promise.all(l.concat(p)).catch(function(n){throw t.forEach(function(t){return t.reject(n)}),n}).then(function(){return o(!1)})})}):Promise.resolve().then(function(){var t=Nt().map(tt);return t.length>0&&(e=!1),Promise.all(t).then(o).catch(function(t){throw i(),t})});function o(){var n=!(arguments.length>0&&void 0!==arguments[0])||arguments[0],o=bt();n&&i(),t.forEach(function(t){return t.resolve(o)});try{var u=e?"single-spa:no-app-change":"single-spa:app-change";window.dispatchEvent(new r(u,a())),window.dispatchEvent(new r("single-spa:routing-event",a()))}catch(t){setTimeout(function(){throw t})}if(jt=!1,_t.length>0){var c=_t;_t=[],Ct(c)}return o}function i(){t.forEach(function(t){ot(t.eventArguments)}),ot(n)}function a(){var t={detail:{}};return n&&n[0]&&(t.detail.originalEvent=n[0]),t}}var Mt=!1;function It(){Mt=!0,Ct()}setTimeout(function(){Mt||console.warn("singleSpa.start() has not been called, ".concat(5e3,"ms after single-spa was loaded. Before start() is called, apps can be declared and loaded, but not bootstrapped or mounted. See https://github.com/CanopyTax/single-spa/blob/master/docs/single-spa-api.md#start"))},5e3);var Lt={getRawAppData:function(){return[].concat(yt)},reroute:Ct,NOT_LOADED:m,toLoadPromise:tt,toBootstrapPromise:K,unregisterApplication:function(t){if(!yt.find(function(n){return n.name===t}))throw Error("Cannot unregister application '".concat(t,"' because no such application has been registered"));return Dt(t).then(function(){var n=yt.findIndex(function(n){return n.name===t});yt.splice(n,1)})}};window&&window.__SINGLE_SPA_DEVTOOLS__&&(window.__SINGLE_SPA_DEVTOOLS__.exposedMethods=Lt)}}});
//# sourceMappingURL=single-spa.min.js.map
