{"version": 3, "file": "single-spa.min.js", "sources": ["../../node_modules/custom-event/index.js", "../../src/applications/app-errors.js", "../../src/applications/app.helpers.js", "../../src/applications/timeouts.js", "../../src/utils/find.js", "../../src/lifecycles/lifecycle.helpers.js", "../../src/lifecycles/bootstrap.js", "../../src/lifecycles/unmount.js", "../../src/lifecycles/mount.js", "../../src/parcels/mount-parcel.js", "../../src/lifecycles/update.js", "../../src/lifecycles/prop.helpers.js", "../../src/lifecycles/load.js", "../../src/navigation/navigation-events.js", "../../src/jquery-support.js", "../../src/lifecycles/unload.js", "../../src/applications/apps.js", "../../src/navigation/reroute.js", "../../src/start.js", "../../src/devtools/devtools.js", "../../src/single-spa.js"], "sourcesContent": ["\nvar NativeCustomEvent = global.CustomEvent;\n\nfunction useNative () {\n  try {\n    var p = new NativeCustomEvent('cat', { detail: { foo: 'bar' } });\n    return  'cat' === p.type && 'bar' === p.detail.foo;\n  } catch (e) {\n  }\n  return false;\n}\n\n/**\n * Cross-browser `CustomEvent` constructor.\n *\n * https://developer.mozilla.org/en-US/docs/Web/API/CustomEvent.CustomEvent\n *\n * @public\n */\n\nmodule.exports = useNative() ? NativeCustomEvent :\n\n// IE >= 9\n'undefined' !== typeof document && 'function' === typeof document.createEvent ? function CustomEvent (type, params) {\n  var e = document.createEvent('CustomEvent');\n  if (params) {\n    e.initCustomEvent(type, params.bubbles, params.cancelable, params.detail);\n  } else {\n    e.initCustomEvent(type, false, false, void 0);\n  }\n  return e;\n} :\n\n// IE <= 8\nfunction CustomEvent (type, params) {\n  var e = document.createEventObject();\n  e.type = type;\n  if (params) {\n    e.bubbles = Boolean(params.bubbles);\n    e.cancelable = Boolean(params.cancelable);\n    e.detail = params.detail;\n  } else {\n    e.bubbles = false;\n    e.cancelable = false;\n    e.detail = void 0;\n  }\n  return e;\n}\n", "let errorHandlers = []\n\nexport function handleAppError(err, app) {\n  const transformedErr = transformErr(err, app);\n\n  if (errorHandlers.length) {\n    errorHandlers.forEach(handler => handler(transformedErr));\n  } else {\n    setTimeout(() => {\n      throw transformedErr;\n    });\n  }\n}\n\nexport function addErrorHandler(handler) {\n  if (typeof handler !== 'function') {\n    throw Error('a single-spa error handler must be a function');\n  }\n\n  errorHandlers.push(handler);\n}\n\nexport function removeErrorHandler(handler) {\n  if (typeof handler !== 'function') {\n    throw Error('a single-spa error handler must be a function');\n  }\n\n  let removedSomething = false;\n  errorHandlers = errorHandlers.filter(h => {\n    const isHandler = h === handler;\n    removedSomething = removedSomething || isHandler;\n    return !isHandler;\n  })\n\n  return removedSomething;\n}\n\nexport function transformErr(ogErr, appOrParcel) {\n  const objectType = appOrParcel.unmountThisParcel ? 'Parcel' : 'Application';\n  const errPrefix = `${objectType} '${appOrParcel.name}' died in status ${appOrParcel.status}: `;\n\n  let result;\n\n  if (ogErr instanceof Error) {\n    try {\n      ogErr.message = errPrefix + ogErr.message;\n    } catch(err) {\n      /* Some errors have read-only message properties, in which case there is nothing\n       * that we can do.\n       */\n    }\n    result = ogErr;\n  } else {\n    console.warn(`While ${appOrParcel.status}, '${appOrParcel.name}' rejected its lifecycle function promise with a non-Error. This will cause stack traces to not be accurate.`);\n    try {\n      result = Error(errPrefix + JSON.stringify(ogErr));\n    } catch(err) {\n      // If it's not an Error and you can't stringify it, then what else can you even do to it?\n      result = ogErr;\n    }\n  }\n\n  result.appName = appOrParcel.name;\n  result.appOrParcelName = appOrParcel.name;\n  try {\n    result.name = appOrParcel.name\n  } catch (err) {\n    // See https://github.com/CanopyTax/single-spa/issues/323\n    // In a future major release, we can remove the `name` property altogether,\n    // as a breaking change, in favor of appOrParcelName.\n  }\n\n  return result;\n}\n", "import { handleAppError } from './app-errors.js';\n\n// App statuses\nexport const NOT_LOADED = 'NOT_LOADED';\nexport const LOADING_SOURCE_CODE = 'LOADING_SOURCE_CODE';\nexport const NOT_BOOTSTRAPPED = 'NOT_BOOTSTRAPPED';\nexport const BOOTSTRAPPING = 'BOOTSTRAPPING';\nexport const NOT_MOUNTED = 'NOT_MOUNTED';\nexport const MOUNTING = 'MOUNTING';\nexport const MOUNTED = 'MOUNTED';\nexport const UPDATING = 'UPDATING';\nexport const UNMOUNTING = 'UNMOUNTING';\nexport const UNLOADING = 'UNLOADING';\nexport const SKIP_BECAUSE_BROKEN = 'SKIP_BECAUSE_BROKEN';\n\nexport function isActive(app) {\n  return app.status === MOUNTED;\n}\n\nexport function isntActive(app) {\n  return !isActive(app);\n}\n\nexport function isLoaded(app) {\n  return app.status !== NOT_LOADED && app.status !== LOADING_SOURCE_CODE;\n}\n\nexport function isntLoaded(app) {\n  return !isLoaded(app);\n}\n\nexport function shouldBeActive(app) {\n  try {\n    return app.activeWhen(window.location);\n  } catch (err) {\n    handleAppError(err, app);\n    app.status = SKIP_BECAUSE_BROKEN;\n  }\n}\n\nexport function shouldntBeActive(app) {\n  try {\n    return !app.activeWhen(window.location);\n  } catch (err) {\n    handleAppError(err, app);\n    app.status = SKIP_BECAUSE_BROKEN;\n  }\n}\n\nexport function notBootstrapped(app) {\n  return app.status !== NOT_BOOTSTRAPPED;\n}\n\nexport function notSkipped(item) {\n  return item !== SKIP_BECAUSE_BROKEN && (!item || item.status !== SKIP_BECAUSE_BROKEN);\n}\n\nexport function toName(app) {\n  return app.name;\n}\n", "const globalTimeoutConfig = {\n  bootstrap: {\n    millis: 4000,\n    dieOnTimeout: false,\n  },\n  mount: {\n    millis: 3000,\n    dieOnTimeout: false,\n  },\n  unmount: {\n    millis: 3000,\n    dieOnTimeout: false,\n  },\n  unload: {\n    millis: 3000,\n    dieOnTimeout: false,\n  },\n};\n\nexport function setBootstrapMaxTime(time, dieOnTimeout = false) {\n  if (typeof time !== 'number' || time <= 0) {\n    throw Error(`bootstrap max time must be a positive integer number of milliseconds`);\n  }\n\n  globalTimeoutConfig.bootstrap = {\n    millis: time,\n    dieOnTimeout,\n  };\n}\n\nexport function setMountMaxTime(time, dieOnTimeout = false) {\n  if (typeof time !== 'number' || time <= 0) {\n    throw Error(`mount max time must be a positive integer number of milliseconds`);\n  }\n\n  globalTimeoutConfig.mount = {\n    millis: time,\n    dieOnTimeout,\n  };\n}\n\nexport function setUnmountMaxTime(time, dieOnTimeout = false) {\n  if (typeof time !== 'number' || time <= 0) {\n    throw Error(`unmount max time must be a positive integer number of milliseconds`);\n  }\n\n  globalTimeoutConfig.unmount = {\n    millis: time,\n    dieOnTimeout,\n  };\n}\n\nexport function setUnloadMaxTime(time, dieOnTimeout = false) {\n  if (typeof time !== 'number' || time <= 0) {\n    throw Error(`unload max time must be a positive integer number of milliseconds`);\n  }\n\n  globalTimeoutConfig.unload = {\n    millis: time,\n    dieOnTimeout,\n  };\n}\n\nexport function reasonableTime(promise, description, timeoutConfig) {\n  const warningPeriod = 1000;\n\n  return new Promise((resolve, reject) => {\n    let finished = false;\n    let errored = false;\n\n    promise\n    .then(val => {\n      finished = true;\n      resolve(val);\n    })\n    .catch(val => {\n      finished = true;\n      reject(val);\n    });\n\n    setTimeout(() => maybeTimingOut(1), warningPeriod);\n    setTimeout(() => maybeTimingOut(true), timeoutConfig.millis);\n\n    function maybeTimingOut(shouldError) {\n      if (!finished) {\n        if (shouldError === true) {\n          errored = true;\n          if (timeoutConfig.dieOnTimeout) {\n            reject(`${description} did not resolve or reject for ${timeoutConfig.millis} milliseconds`);\n          } else {\n            console.error(`${description} did not resolve or reject for ${timeoutConfig.millis} milliseconds -- we're no longer going to warn you about it.`);\n            //don't resolve or reject, we're waiting this one out\n          }\n        } else if (!errored) {\n          const numWarnings = shouldError;\n          const numMillis = numWarnings * warningPeriod;\n          console.warn(`${description} did not resolve or reject within ${numMillis} milliseconds`);\n          if (numMillis + warningPeriod < timeoutConfig.millis) {\n            setTimeout(() => maybeTimingOut(numWarnings + 1), warningPeriod);\n          }\n        }\n      }\n    }\n  });\n}\n\nexport function ensureValidAppTimeouts(timeouts = {}) {\n  return {\n    ...globalTimeoutConfig,\n    ...timeouts,\n  }\n}\n", "/* the array.prototype.find polyfill on npmjs.com is ~20kb (not worth it)\n * and lodash is ~200kb (not worth it)\n */\n\nexport function find(arr, func) {\n  for (let i=0; i<arr.length; i++) {\n    if (func(arr[i])) {\n      return arr[i];\n    }\n  }\n\n  return null;\n}\n", "import { find } from '../utils/find.js';\n\nexport function validLifecycleFn(fn) {\n  return fn && (typeof fn === 'function' || isArrayOfFns(fn));\n\n  function isArrayOfFns(arr) {\n    return Array.isArray(arr) && !find(arr, item => typeof item !== 'function');\n  }\n}\n\nexport function flattenFnArray(fns, description) {\n  fns = Array.isArray(fns) ? fns : [fns];\n  if (fns.length === 0) {\n    fns = [() => Promise.resolve()];\n  }\n\n  return function(props) {\n    return new Promise((resolve, reject) => {\n      waitForPromises(0);\n\n      function waitForPromises(index) {\n        const promise = fns[index](props);\n        if (!smellsLikeAPromise(promise)) {\n          reject(`${description} at index ${index} did not return a promise`);\n        } else {\n          promise\n            .then(() => {\n              if (index === fns.length - 1) {\n                resolve();\n              } else {\n                waitForPromises(index + 1);\n              }\n            })\n            .catch(reject);\n        }\n      }\n    });\n  }\n}\n\nexport function smellsLikeAPromise(promise) {\n  return promise && typeof promise.then === 'function' && typeof promise.catch === 'function';\n}\n", "import { NOT_BOOTSTRAPPED, BOOTSTRAPPING, NOT_MOUNTED, SKIP_BECAUSE_BROKEN } from '../applications/app.helpers.js';\nimport { reasonableTime } from '../applications/timeouts.js';\nimport { handleAppError, transformErr } from '../applications/app-errors.js';\nimport { getProps } from './prop.helpers.js'\n\nexport function toBootstrapPromise(appOrParcel, hardFail = false) {\n  return Promise.resolve().then(() => {\n    if (appOrParcel.status !== NOT_BOOTSTRAPPED) {\n      return appOrParcel;\n    }\n\n    appOrParcel.status = BOOTSTRAPPING;\n\n    return reasonableTime(appOrParcel.bootstrap(getProps(appOrParcel)), `Bootstrapping appOrParcel '${appOrParcel.name}'`, appOrParcel.timeouts.bootstrap)\n      .then(() => {\n        appOrParcel.status = NOT_MOUNTED;\n        return appOrParcel;\n      })\n      .catch(err => {\n        appOrParcel.status = SKIP_BECAUSE_BROKEN;\n        if (hardFail) {\n          const transformedErr = transformErr(err, appOrParcel)\n          throw transformedErr\n        } else {\n          handleAppError(err, appOrParcel);\n          return appOrParcel;\n        }\n      })\n  })\n}\n", "import { UNMOUNTING, NOT_MOUNTED, MOUNTED, SKIP_BECAUSE_BROKEN } from '../applications/app.helpers.js';\nimport { handleAppError, transformErr } from '../applications/app-errors.js';\nimport { reasonableTime } from '../applications/timeouts.js';\nimport { getProps } from './prop.helpers.js';\n\nexport function toUnmountPromise(appOrParcel, hardFail = false) {\n  return Promise.resolve().then(() => {\n    if (appOrParcel.status !== MOUNTED) {\n      return appOrParcel;\n    }\n    appOrParcel.status = UNMOUNTING;\n\n    const unmountChildrenParcels = Object.keys(appOrParcel.parcels)\n      .map(parcelId => appOrParcel.parcels[parcelId].unmountThisParcel());\n\n    let parcelError;\n\n    return Promise.all(unmountChildrenParcels)\n      .then(\n        unmountAppOrParcel,\n        parcelError => {\n          // There is a parcel unmount error\n          return unmountAppOrParcel()\n            .then(() => {\n              // Unmounting the app/parcel succeeded, but unmounting its children parcels did not\n              const parentError = Error(parcelError.message)\n              if (hardFail) {\n                const transformedErr = transformErr(parentError, appOrParcel)\n                appOrParcel.status = SKIP_BECAUSE_BROKEN;\n                throw transformedErr\n              } else {\n                handleAppError(parentError, appOrParcel);\n                appOrParcel.status = SKIP_BECAUSE_BROKEN;\n              }\n            })\n        }\n      )\n      .then(() => appOrParcel)\n\n    function unmountAppOrParcel() {\n      // We always try to unmount the appOrParcel, even if the children parcels failed to unmount.\n      return reasonableTime(appOrParcel.unmount(getProps(appOrParcel)), `Unmounting application ${appOrParcel.name}'`, appOrParcel.timeouts.unmount)\n        .then(() => {\n          // The appOrParcel needs to stay in a broken status if its children parcels fail to unmount\n          if (!parcelError) {\n            appOrParcel.status = NOT_MOUNTED;\n          }\n        })\n        .catch(err => {\n          if (hardFail) {\n            const transformedErr = transformErr(err, appOrParcel);\n            appOrParcel.status = SKIP_BECAUSE_BROKEN;\n            throw transformedErr;\n          } else {\n            handleAppError(err, appOrParcel);\n            appOrParcel.status = SKIP_BECAUSE_BROKEN;\n          }\n        })\n    }\n  })\n}\n", "import { NOT_MOUNTED, MOUNTED, SKIP_BECAUSE_BROKEN } from '../applications/app.helpers.js';\nimport { handleAppError, transformErr } from '../applications/app-errors.js';\nimport { reasonableTime } from '../applications/timeouts.js';\nimport CustomEvent from 'custom-event';\nimport { getProps } from './prop.helpers.js';\nimport { toUnmountPromise } from './unmount.js';\n\nlet beforeFirstMountFired = false;\nlet firstMountFired = false;\n\nexport function toMountPromise(appOrParcel, hardFail = false) {\n  return Promise.resolve().then(() => {\n    if (appOrParcel.status !== NOT_MOUNTED) {\n      return appOrParcel;\n    }\n\n    if (!beforeFirstMountFired) {\n      window.dispatchEvent(new CustomEvent('single-spa:before-first-mount'));\n      beforeFirstMountFired = true;\n    }\n\n    return reasonableTime(appOrParcel.mount(getProps(appOrParcel)), `Mounting application '${appOrParcel.name}'`, appOrParcel.timeouts.mount)\n      .then(() => {\n        appOrParcel.status = MOUNTED;\n\n        if (!firstMountFired) {\n          window.dispatchEvent(new CustomEvent('single-spa:first-mount'));\n          firstMountFired = true;\n        }\n\n        return appOrParcel;\n      })\n      .catch(err => {\n        // If we fail to mount the appOrParcel, we should attempt to unmount it before putting in SKIP_BECAUSE_BROKEN\n        // We temporarily put the appOrParcel into MOUNTED status so that toUnmountPromise actually attempts to unmount it\n        // instead of just doing a no-op.\n        appOrParcel.status = MOUNTED\n        return toUnmountPromise(appOrParcel)\n          .then(setSkipBecauseBroken, setSkipBecauseBroken)\n\n        function setSkipBecauseBroken() {\n          if (!hardFail) {\n            handleAppError(err, appOrParcel);\n            appOrParcel.status = SKIP_BECAUSE_BROKEN;\n            return appOrParcel;\n          } else {\n            const transformedErr = transformErr(err, appOrParcel)\n            appOrParcel.status = SKIP_BECAUSE_BROKEN;\n            throw transformedErr\n          }\n        }\n      })\n  })\n}\n", "import { validLifecycleFn, flattenFnArray } from '../lifecycles/lifecycle.helpers.js';\nimport { NOT_BOOTSTRAPPED, NOT_MOUNTED, MOUNTED, UPDATING, LOADING_SOURCE_CODE, SKIP_BECAUSE_BROKEN } from '../applications/app.helpers.js';\nimport { toBootstrapPromise } from '../lifecycles/bootstrap.js';\nimport { toMountPromise } from '../lifecycles/mount.js';\nimport { toUpdatePromise } from '../lifecycles/update.js';\nimport { toUnmountPromise } from '../lifecycles/unmount.js';\nimport { ensureValidAppTimeouts } from '../applications/timeouts.js';\n\nlet parcelCount = 0;\nconst rootParcels = {parcels: {}};\n\n// This is a public api, exported to users of single-spa\nexport function mountRootParcel() {\n  return mountParcel.apply(rootParcels, arguments);\n}\n\nexport function mountParcel(config, customProps) {\n  const owningAppOrParcel = this;\n\n  // Validate inputs\n  if (!config || (typeof config !== 'object' && typeof config !== 'function')) {\n    throw Error('Cannot mount parcel without a config object or config loading function');\n  }\n\n  if (config.name && typeof config.name !== 'string') {\n    throw Error('Parcel name must be a string, if provided');\n  }\n\n  if (typeof customProps !== 'object') {\n    throw Error(`Parcel ${name} has invalid customProps -- must be an object`);\n  }\n\n  if (!customProps.domElement) {\n    throw Error(`Parcel ${name} cannot be mounted without a domElement provided as a prop`);\n  }\n\n  const id = parcelCount++;\n\n  const passedConfigLoadingFunction = typeof config === 'function'\n  const configLoadingFunction = passedConfigLoadingFunction ? config : () => Promise.resolve(config)\n\n  // Internal representation\n  const parcel = {\n    id,\n    parcels: {},\n    status: passedConfigLoadingFunction ? LOADING_SOURCE_CODE : NOT_BOOTSTRAPPED,\n    customProps,\n    parentName: owningAppOrParcel.name,\n    unmountThisParcel() {\n      if (parcel.status !== MOUNTED) {\n        throw Error(`Cannot unmount parcel '${name}' -- it is in a ${parcel.status} status`);\n      }\n\n      return toUnmountPromise(parcel, true)\n        .then(value => {\n          if (parcel.parentName) {\n            delete owningAppOrParcel.parcels[parcel.id];\n          }\n\n          return value;\n        })\n        .then(value => {\n          resolveUnmount(value);\n          return value;\n        })\n        .catch(err => {\n          parcel.status = SKIP_BECAUSE_BROKEN;\n          rejectUnmount(err);\n          throw err;\n        });\n    }\n  };\n\n  // We return an external representation\n  let externalRepresentation\n\n  // Add to owning app or parcel\n  owningAppOrParcel.parcels[id] = parcel;\n\n  let loadPromise = configLoadingFunction()\n\n  if (!loadPromise || typeof loadPromise.then !== 'function') {\n    throw Error(`When mounting a parcel, the config loading function must return a promise that resolves with the parcel config`)\n  }\n\n  loadPromise = loadPromise.then(config => {\n    if (!config) {\n      throw Error(`When mounting a parcel, the config loading function returned a promise that did not resolve with a parcel config`)\n    }\n\n    const name = config.name || `parcel-${id}`;\n\n    if (!validLifecycleFn(config.bootstrap)) {\n      throw Error(`Parcel ${name} must have a valid bootstrap function`);\n    }\n\n    if (!validLifecycleFn(config.mount)) {\n      throw Error(`Parcel ${name} must have a valid mount function`);\n    }\n\n    if (!validLifecycleFn(config.unmount)) {\n      throw Error(`Parcel ${name} must have a valid unmount function`);\n    }\n\n    if (config.update && !validLifecycleFn(config.update)) {\n      throw Error(`Parcel ${name} provided an invalid update function`);\n    }\n\n    const bootstrap = flattenFnArray(config.bootstrap);\n    const mount = flattenFnArray(config.mount);\n    const unmount = flattenFnArray(config.unmount);\n\n    parcel.status = NOT_BOOTSTRAPPED;\n    parcel.name = name;\n    parcel.bootstrap = bootstrap;\n    parcel.mount = mount;\n    parcel.unmount = unmount;\n    parcel.timeouts = ensureValidAppTimeouts(config.timeouts);\n\n    if (config.update) {\n      parcel.update = flattenFnArray(config.update);\n      externalRepresentation.update = function(customProps) {\n        parcel.customProps = customProps;\n\n        return promiseWithoutReturnValue(toUpdatePromise(parcel));\n      }\n    }\n  })\n\n  // Start bootstrapping and mounting\n  // The .then() causes the work to be put on the event loop instead of happening immediately\n  const bootstrapPromise = loadPromise.then(() => toBootstrapPromise(parcel, true));\n  const mountPromise = bootstrapPromise.then(() => toMountPromise(parcel, true));\n\n  let resolveUnmount, rejectUnmount;\n\n  const unmountPromise = new Promise((resolve, reject) => {\n    resolveUnmount = resolve;\n    rejectUnmount = reject;\n  });\n\n  externalRepresentation = {\n    mount() {\n      return promiseWithoutReturnValue(\n        Promise\n        .resolve()\n        .then(() => {\n          if (parcel.status !== NOT_MOUNTED) {\n            throw Error(`Cannot mount parcel '${name}' -- it is in a ${parcel.status} status`);\n          }\n\n          // Add to owning app or parcel\n          owningAppOrParcel.parcels[id] = parcel;\n\n          return toMountPromise(parcel);\n        })\n      )\n    },\n    unmount() {\n      return promiseWithoutReturnValue(\n        parcel.unmountThisParcel()\n      );\n    },\n    getStatus() {\n      return parcel.status;\n    },\n    loadPromise: promiseWithoutReturnValue(loadPromise),\n    bootstrapPromise: promiseWithoutReturnValue(bootstrapPromise),\n    mountPromise: promiseWithoutReturnValue(mountPromise),\n    unmountPromise: promiseWithoutReturnValue(unmountPromise),\n  };\n\n  return externalRepresentation\n}\n\nfunction promiseWithoutReturnValue(promise) {\n  return promise.then(() => null);\n}\n", "import { UPDATING, MOUNTED, SKIP_BECAUSE_BROKEN } from '../applications/app.helpers.js';\nimport { transformErr } from '../applications/app-errors.js';\nimport { reasonableTime } from '../applications/timeouts.js';\nimport { getProps } from './prop.helpers.js';\n\nexport function toUpdatePromise(parcel) {\n  return Promise.resolve().then(() => {\n    if (parcel.status !== MOUNTED) {\n      throw Error(`Cannot update parcel '${parcel.name}' because it is not mounted`)\n    }\n\n    parcel.status = UPDATING;\n\n    return reasonableTime(parcel.update(getProps(parcel)), `Updating parcel '${parcel.name}'`, parcel.timeouts.mount)\n      .then(() => {\n        parcel.status = MOUNTED;\n        return parcel;\n      })\n      .catch(err => {\n        const transformedErr = transformErr(err, parcel)\n        parcel.status = SKIP_BECAUSE_BROKEN;\n        throw transformedErr;\n      })\n  })\n}\n\n", "import * as singleSpa from '../single-spa.js'\nimport { mountParcel } from '../parcels/mount-parcel.js';\n\nexport function getProps(appOrParcel) {\n  const result = {\n    ...appOrParcel.customProps,\n    name: appOrParcel.name,\n    mountParcel: mountParcel.bind(appOrParcel),\n    singleSpa\n  };\n\n  if (appOrParcel.unmountThisParcel) {\n    result.unmountSelf = appOrParcel.unmountThisParcel;\n  }\n\n  return result;\n}\n", "import { NOT_BOOTSTRAPPED, LOADING_SOURCE_CODE, SKIP_BECAUSE_BROKEN, NOT_LOADED } from '../applications/app.helpers.js';\nimport { ensureValidAppTimeouts } from '../applications/timeouts.js';\nimport { handleAppError } from '../applications/app-errors.js';\nimport { flattenFnArray, smellsLikeAPromise, validLifecycleFn } from './lifecycle.helpers.js';\nimport { getProps } from './prop.helpers.js';\n\nexport function toLoadPromise(app) {\n  return Promise.resolve().then(() => {\n    if (app.status !== NOT_LOADED) {\n      return app;\n    }\n\n    app.status = LOADING_SOURCE_CODE;\n\n    let appOpts;\n\n    return Promise.resolve().then(() => {\n      const loadPromise = app.loadImpl(getProps(app));\n      if (!smellsLikeAPromise(loadPromise)) {\n        // The name of the app will be prepended to this error message inside of the handleAppError function\n        throw Error(`single-spa loading function did not return a promise. Check the second argument to registerApplication('${app.name}', loadingFunction, activityFunction)`);\n      }\n      return loadPromise.then(val => {\n        appOpts = val;\n\n        let validationErrMessage;\n\n        if (typeof appOpts !== 'object') {\n          validationErrMessage = `does not export anything`;\n        }\n\n        if (!validLifecycleFn(appOpts.bootstrap)) {\n          validationErrMessage = `does not export a bootstrap function or array of functions`;\n        }\n\n        if (!validLifecycleFn(appOpts.mount)) {\n          validationErrMessage = `does not export a mount function or array of functions`;\n        }\n\n        if (!validLifecycleFn(appOpts.unmount)) {\n          validationErrMessage = `does not export an unmount function or array of functions`;\n        }\n\n        if (validationErrMessage) {\n          console.error(`The loading function for single-spa application '${app.name}' resolved with the following, which does not have bootstrap, mount, and unmount functions`, appOpts)\n          handleAppError(validationErrMessage, app);\n          app.status = SKIP_BECAUSE_BROKEN;\n          return app;\n        }\n\n        if (appOpts.devtools && appOpts.devtools.overlays) {\n          app.devtools.overlays = {...app.devtools.overlays, ...appOpts.devtools.overlays}\n        }\n\n        app.status = NOT_BOOTSTRAPPED;\n        app.bootstrap = flattenFnArray(appOpts.bootstrap, `App '${app.name}' bootstrap function`);\n        app.mount = flattenFnArray(appOpts.mount, `App '${app.name}' mount function`);\n        app.unmount = flattenFnArray(appOpts.unmount, `App '${app.name}' unmount function`);\n        app.unload = flattenFnArray(appOpts.unload || [], `App '${app.name}' unload function`);\n        app.timeouts = ensureValidAppTimeouts(appOpts.timeouts);\n\n        return app;\n      })\n    })\n    .catch(err => {\n      handleAppError(err, app);\n      app.status = SKIP_BECAUSE_BROKEN;\n      return app;\n    })\n  })\n}\n", "import { reroute } from './reroute.js';\nimport { find } from '../utils/find.js';\n\n/* We capture navigation event listeners so that we can make sure\n * that application navigation listeners are not called until\n * single-spa has ensured that the correct applications are\n * unmounted and mounted.\n */\nconst capturedEventListeners = {\n  hashchange: [],\n  popstate: [],\n};\n\nexport const routingEventsListeningTo = ['hashchange', 'popstate'];\n\nexport function navigateToUrl(obj, opts={}) {\n  let url;\n  if (typeof obj === 'string') {\n    url = obj ;\n  } else if (this && this.href) {\n    url = this.href;\n  } else if (obj && obj.currentTarget && obj.currentTarget.href && obj.preventDefault) {\n    url = obj.currentTarget.href;\n    obj.preventDefault();\n  } else {\n    throw Error(`singleSpaNavigate must be either called with a string url, with an <a> tag as its context, or with an event whose currentTarget is an <a> tag`);\n  }\n\n  const current = parseUri(window.location.href);\n  const destination = parseUri(url);\n\n  if (url.indexOf('#') === 0) {\n    window.location.hash = '#' + destination.anchor;\n  } else if (current.host !== destination.host && destination.host) {\n    if (opts.isTestingEnv) {\n      return {wouldHaveReloadedThePage: true};\n    } else {\n      window.location.href = url;\n    }\n  } else if (!isSamePath(destination.path + \"?\" + destination.query, current.path + \"?\" + current.query)) {\n    // different path, host, or query params\n    window.history.pushState(null, null, url);\n  } else {\n    window.location.hash = '#' + destination.anchor;\n  }\n\n  function isSamePath(destination, current) {\n    // if the destination has a path but no domain, it doesn't include the root '/'\n    return current === destination || current === '/' + destination;\n  }\n}\n\nexport function callCapturedEventListeners(eventArguments) {\n  if (eventArguments) {\n    const eventType = eventArguments[0].type;\n    if (routingEventsListeningTo.indexOf(eventType) >= 0) {\n      capturedEventListeners[eventType].forEach(listener => {\n        listener.apply(this, eventArguments);\n      });\n    }\n  }\n}\n\nfunction urlReroute() {\n  reroute([], arguments)\n}\n\n\n// We will trigger an app change for any routing events.\nwindow.addEventListener('hashchange', urlReroute);\nwindow.addEventListener('popstate', urlReroute);\n\n// Monkeypatch addEventListener so that we can ensure correct timing\nconst originalAddEventListener = window.addEventListener;\nconst originalRemoveEventListener = window.removeEventListener;\nwindow.addEventListener = function(eventName, fn) {\n  if (typeof fn === 'function') {\n    if (routingEventsListeningTo.indexOf(eventName) >= 0 && !find(capturedEventListeners[eventName], listener => listener === fn)) {\n      capturedEventListeners[eventName].push(fn);\n      return;\n    }\n  }\n\n  return originalAddEventListener.apply(this, arguments);\n}\n\nwindow.removeEventListener = function(eventName, listenerFn) {\n  if (typeof listenerFn === 'function') {\n    if (routingEventsListeningTo.indexOf(eventName) >= 0) {\n      capturedEventListeners[eventName] = capturedEventListeners[eventName].filter(fn => fn !== listenerFn);\n      return;\n    }\n  }\n\n  return originalRemoveEventListener.apply(this, arguments);\n}\n\nconst originalPushState = window.history.pushState;\nwindow.history.pushState = function(state) {\n  const result = originalPushState.apply(this, arguments);\n\n  urlReroute(createPopStateEvent(state));\n  \n  return result;\n}\n\nconst originalReplaceState = window.history.replaceState;\nwindow.history.replaceState = function(state) {\n  const result = originalReplaceState.apply(this, arguments);\n  urlReroute(createPopStateEvent(state));\n  return result;\n}\n\nfunction createPopStateEvent(state) {\n  // https://github.com/CanopyTax/single-spa/issues/224 and https://github.com/CanopyTax/single-spa-angular/issues/49\n  // We need a popstate event even though the browser doesn't do one by default when you call replaceState, so that\n  // all the applications can reroute.\n  try {\n    return new PopStateEvent('popstate', {state});\n  } catch (err) {\n    // IE 11 compatibility https://github.com/CanopyTax/single-spa/issues/299\n    // https://docs.microsoft.com/en-us/openspecs/ie_standards/ms-html5e/bd560f47-b349-4d2c-baa8-f1560fb489dd\n    const evt = document.createEvent('PopStateEvent');\n    evt.initPopStateEvent('popstate', false, false, state);\n    return evt;\n  }\n}\n\n/* For convenience in `onclick` attributes, we expose a global function for navigating to\n * whatever an <a> tag's href is.\n */\nwindow.singleSpaNavigate = navigateToUrl;\n\nfunction parseUri(str) {\n  // parseUri 1.2.2\n  // (c) Steven Levithan <stevenlevithan.com>\n  // MIT License\n  // http://blog.stevenlevithan.com/archives/parseuri\n  const parseOptions = {\n    strictMode: true,\n    key: [\"source\",\"protocol\",\"authority\",\"userInfo\",\"user\",\"password\",\"host\",\"port\",\"relative\",\"path\",\"directory\",\"file\",\"query\",\"anchor\"],\n    q:   {\n      name:   \"queryKey\",\n      parser: /(?:^|&)([^&=]*)=?([^&]*)/g\n    },\n    parser: {\n      strict: /^(?:([^:\\/?#]+):)?(?:\\/\\/((?:(([^:@]*)(?::([^:@]*))?)?@)?([^:\\/?#]*)(?::(\\d*))?))?((((?:[^?#\\/]*\\/)*)([^?#]*))(?:\\?([^#]*))?(?:#(.*))?)/,\n      loose:  /^(?:(?![^:@]+:[^:@\\/]*@)([^:\\/?#.]+):)?(?:\\/\\/)?((?:(([^:@]*)(?::([^:@]*))?)?@)?([^:\\/?#]*)(?::(\\d*))?)(((\\/(?:[^?#](?![^?#\\/]*\\.[^?#\\/.]+(?:[?#]|$)))*\\/?)?([^?#\\/]*))(?:\\?([^#]*))?(?:#(.*))?)/\n    }\n  };\n\n  let  o = parseOptions;\n  let m = o.parser[o.strictMode ? \"strict\" : \"loose\"].exec(str);\n  let uri = {};\n  let i = 14;\n\n  while (i--) uri[o.key[i]] = m[i] || \"\";\n\n  uri[o.q.name] = {};\n  uri[o.key[12]].replace(o.q.parser, function ($0, $1, $2) {\n    if ($1) uri[o.q.name][$1] = $2;\n  });\n\n  return uri;\n}\n", "import { routingEventsListeningTo } from './navigation/navigation-events.js';\n\nlet hasInitialized = false;\n\nexport function ensureJQuerySupport(jQuery = window.jQuery) {\n  if (!jQuery) {\n    if (window.$ && window.$.fn && window.$.fn.jquery) {\n      jQuery = window.$;\n    }\n  }\n\n  if (jQuery && !hasInitialized) {\n    const originalJQueryOn = jQuery.fn.on;\n    const originalJQueryOff = jQuery.fn.off;\n\n    jQuery.fn.on = function(eventString, fn) {\n      return captureRoutingEvents.call(this, originalJQueryOn, window.addEventListener, eventString, fn, arguments);\n    }\n\n    jQuery.fn.off = function(eventString, fn) {\n      return captureRoutingEvents.call(this, originalJQueryOff, window.removeEventListener, eventString, fn, arguments);\n    }\n\n    hasInitialized = true;\n  }\n}\n\nfunction captureRoutingEvents(originalJQueryFunction, nativeFunctionToCall, eventString, fn, originalArgs) {\n  if (typeof eventString !== 'string') {\n    return originalJQueryFunction.apply(this, originalArgs);\n  }\n\n  const eventNames = eventString.split(/\\s+/);\n  eventNames.forEach(eventName => {\n    if (routingEventsListeningTo.indexOf(eventName) >= 0) {\n      nativeFunctionToCall(eventName, fn);\n      eventString = eventString.replace(eventName, '');\n    }\n  });\n\n  if (eventString.trim() === '') {\n    return this;\n  } else {\n    return originalJQueryFunction.apply(this, originalArgs);\n  }\n}\n", "import { NOT_MOUNTED, UNLOADING, NOT_LOADED, SKIP_BECAUSE_BROKEN, isntActive } from '../applications/app.helpers.js';\nimport { handleAppError } from '../applications/app-errors.js';\nimport { reasonableTime } from '../applications/timeouts.js';\nimport { getProps } from './prop.helpers.js';\n\nconst appsToUnload = {};\n\nexport function toUnloadPromise(app) {\n  return Promise.resolve().then(() => {\n    const unloadInfo = appsToUnload[app.name];\n\n    if (!unloadInfo) {\n      /* No one has called unloadApplication for this app,\n      */\n      return app;\n    }\n\n    if (app.status === NOT_LOADED) {\n      /* This app is already unloaded. We just need to clean up\n       * anything that still thinks we need to unload the app.\n       */\n      finishUnloadingApp(app, unloadInfo);\n      return app;\n    }\n\n    if (app.status === UNLOADING) {\n      /* Both unloadApplication and reroute want to unload this app.\n       * It only needs to be done once, though.\n       */\n      return unloadInfo.promise.then(() => app);\n    }\n\n    if (app.status !== NOT_MOUNTED) {\n      /* The app cannot be unloaded until it is unmounted.\n      */\n      return app;\n    }\n\n    app.status = UNLOADING;\n    return reasonableTime(app.unload(getProps(app)), `Unloading application '${app.name}'`, app.timeouts.unload)\n      .then(() => {\n        finishUnloadingApp(app, unloadInfo);\n        return app;\n      })\n      .catch(err => {\n        errorUnloadingApp(app, unloadInfo, err);\n        return app;\n      })\n  })\n}\n\nfunction finishUnloadingApp(app, unloadInfo) {\n  delete appsToUnload[app.name];\n\n  // Unloaded apps don't have lifecycles\n  delete app.bootstrap;\n  delete app.mount;\n  delete app.unmount;\n  delete app.unload;\n\n  app.status = NOT_LOADED;\n\n  /* resolve the promise of whoever called unloadApplication.\n   * This should be done after all other cleanup/bookkeeping\n   */\n  unloadInfo.resolve();\n}\n\nfunction errorUnloadingApp(app, unloadInfo, err) {\n  delete appsToUnload[app.name];\n\n  // Unloaded apps don't have lifecycles\n  delete app.bootstrap;\n  delete app.mount;\n  delete app.unmount;\n  delete app.unload;\n\n  handleAppError(err, app);\n  app.status = SKIP_BECAUSE_BROKEN;\n  unloadInfo.reject(err);\n}\n\nexport function addAppToUnload(app, promiseGetter, resolve, reject) {\n  appsToUnload[app.name] = {app, resolve, reject};\n  Object.defineProperty(appsToUnload[app.name], 'promise', {get: promiseGetter});\n}\n\nexport function getAppUnloadInfo(appName) {\n  return appsToUnload[appName];\n}\n\nexport function getAppsToUnload() {\n  return Object.keys(appsToUnload)\n    .map(appName => appsToUnload[appName].app)\n    .filter(isntActive)\n}\n", "import { ensureJQuerySupport } from '../jquery-support.js';\nimport { isActive, isLoaded, isntLoaded, toName, NOT_LOADED, shouldBeActive, shouldntBeActive, isntActive, notSkipped } from './app.helpers.js';\nimport { reroute } from '../navigation/reroute.js';\nimport { find } from '../utils/find.js';\nimport { toUnmountPromise } from '../lifecycles/unmount.js';\nimport { toUnloadPromise, getAppUnloadInfo, addAppToUnload } from '../lifecycles/unload.js';\n\nconst apps = [];\n\nexport function getMountedApps() {\n  return apps.filter(isActive).map(toName);\n}\n\nexport function getAppNames() {\n  return apps.map(toName);\n}\n\n// used in devtools, not (currently) exposed as a single-spa API\nexport function getRawAppData() {\n  return [...apps];\n}\n\nexport function getAppStatus(appName) {\n  const app = find(apps, app => app.name === appName);\n  return app ? app.status : null;\n}\n\nexport function declareChildApplication(appName, arg1, arg2) {\n  console.warn('declareChildApplication is deprecated and will be removed in the next major version, use \"registerApplication\" instead')\n  return registerApplication(appName, arg1, arg2)\n}\n\nexport function registerApplication(appName, applicationOrLoadingFn, activityFn, customProps = {}) {\n  if (typeof appName !== 'string' || appName.length === 0)\n    throw Error(`The first argument must be a non-empty string 'appName'`);\n  if (getAppNames().indexOf(appName) !== -1)\n    throw Error(`There is already an app declared with name ${appName}`);\n  if (typeof customProps !== 'object' || Array.isArray(customProps))\n    throw Error('customProps must be an object');\n\n  if (!applicationOrLoadingFn)\n    throw Error(`The application or loading function is required`);\n\n  let loadImpl;\n  if (typeof applicationOrLoadingFn !== 'function') {\n    // applicationOrLoadingFn is an application\n    loadImpl = () => Promise.resolve(applicationOrLoadingFn);\n  } else {\n    // applicationOrLoadingFn is a loadingFn\n    loadImpl = applicationOrLoadingFn;\n  }\n\n  if (typeof activityFn !== 'function')\n    throw Error(`The activeWhen argument must be a function`);\n\n  apps.push({\n    name: appName,\n    loadImpl,\n    activeWhen: activityFn,\n    status: NOT_LOADED,\n    parcels: {},\n    devtools: {\n      overlays: {\n        options: {},\n        selectors: [],\n      }\n    },\n    customProps\n  });\n\n  ensureJQuerySupport();\n\n  reroute();\n}\n\nexport function checkActivityFunctions(location) {\n  const activeApps = []\n  for (let i = 0; i < apps.length; i++) {\n    if (apps[i].activeWhen(location)) {\n      activeApps.push(apps[i].name)\n    }\n  }\n  return activeApps\n}\n\nexport function getAppsToLoad() {\n  return apps\n    .filter(notSkipped)\n    .filter(isntLoaded)\n    .filter(shouldBeActive)\n}\n\nexport function getAppsToUnmount() {\n  return apps\n    .filter(notSkipped)\n    .filter(isActive)\n    .filter(shouldntBeActive)\n}\n\nexport function getAppsToMount() {\n  return apps\n    .filter(notSkipped)\n    .filter(isntActive)\n    .filter(isLoaded)\n    .filter(shouldBeActive)\n}\n\nexport function unregisterApplication(appName) {\n  if (!apps.find(app => app.name === appName)) {\n    throw Error(`Cannot unregister application '${appName}' because no such application has been registered`)\n  }\n\n  return unloadApplication(appName)\n    .then(() => {\n      const appIndex = apps.findIndex(app => app.name === appName)\n      apps.splice(appIndex, 1)\n    })\n}\n\nexport function unloadChildApplication(appName, opts) {\n  console.warn('unloadChildApplication is deprecated and will be removed in the next major version, use \"unloadApplication\" instead')\n  return unloadApplication(appName, opts)\n}\n\nexport function unloadApplication(appName, opts={waitForUnmount: false}) {\n  if (typeof appName !== 'string') {\n    throw Error(`unloadApplication requires a string 'appName'`);\n  }\n  const app = find(apps, App => App.name === appName);\n  if (!app) {\n    throw Error(`Could not unload application '${appName}' because no such application has been registered`);\n  }\n\n  const appUnloadInfo = getAppUnloadInfo(app.name);\n  if (opts && opts.waitForUnmount) {\n    // We need to wait for unmount before unloading the app\n\n    if (appUnloadInfo) {\n      // Someone else is already waiting for this, too\n      return appUnloadInfo.promise;\n    } else {\n      // We're the first ones wanting the app to be resolved.\n      const promise = new Promise((resolve, reject) => {\n        addAppToUnload(app, () => promise, resolve, reject);\n      });\n      return promise;\n    }\n  } else {\n    /* We should unmount the app, unload it, and remount it immediately.\n     */\n\n    let resultPromise;\n\n    if (appUnloadInfo) {\n      // Someone else is already waiting for this app to unload\n      resultPromise = appUnloadInfo.promise;\n      immediatelyUnloadApp(app, appUnloadInfo.resolve, appUnloadInfo.reject);\n    } else {\n      // We're the first ones wanting the app to be resolved.\n      resultPromise = new Promise((resolve, reject) => {\n        addAppToUnload(app, () => resultPromise, resolve, reject);\n        immediatelyUnloadApp(app, resolve, reject);\n      });\n    }\n\n    return resultPromise;\n  }\n}\n\nfunction immediatelyUnloadApp(app, resolve, reject) {\n  toUnmountPromise(app)\n    .then(toUnloadPromise)\n    .then(() => {\n      resolve()\n      setTimeout(() => {\n        // reroute, but the unload promise is done\n        reroute()\n      });\n    })\n    .catch(reject);\n}\n", "import CustomEvent from 'custom-event';\nimport { isStarted } from '../start.js';\nimport { toLoadPromise } from '../lifecycles/load.js';\nimport { toBootstrapPromise } from '../lifecycles/bootstrap.js';\nimport { toMountPromise } from '../lifecycles/mount.js';\nimport { toUnmountPromise } from '../lifecycles/unmount.js';\nimport { getMountedApps, getAppsToLoad, getAppsToUnmount, getAppsToMount } from '../applications/apps.js';\nimport { callCapturedEventListeners } from './navigation-events.js';\nimport { getAppsToUnload, toUnloadPromise } from '../lifecycles/unload.js';\n\nlet appChangeUnderway = false, peopleWaitingOnAppChange = [];\n\nexport function triggerAppChange() {\n  // Call reroute with no arguments, intentionally\n  return reroute()\n}\n\nexport function reroute(pendingPromises = [], eventArguments) {\n  if (appChangeUnderway) {\n    return new Promise((resolve, reject) => {\n      peopleWaitingOnAppChange.push({\n        resolve,\n        reject,\n        eventArguments,\n      });\n    });\n  }\n\n  appChangeUnderway = true;\n  let wasNoOp = true;\n\n  if (isStarted()) {\n    return performAppChanges();\n  } else {\n    return loadApps();\n  }\n\n  function loadApps() {\n    return Promise.resolve().then(() => {\n      const loadPromises = getAppsToLoad().map(toLoadPromise);\n\n      if (loadPromises.length > 0) {\n        wasNoOp = false;\n      }\n\n      return Promise\n        .all(loadPromises)\n        .then(finishUpAndReturn)\n        .catch(err => {\n          callAllEventListeners();\n          throw err;\n        })\n    })\n  }\n\n  function performAppChanges() {\n    return Promise.resolve().then(() => {\n      window.dispatchEvent(new CustomEvent(\"single-spa:before-routing-event\", getCustomEventDetail()));\n      const unloadPromises = getAppsToUnload().map(toUnloadPromise);\n\n      const unmountUnloadPromises = getAppsToUnmount()\n        .map(toUnmountPromise)\n        .map(unmountPromise => unmountPromise.then(toUnloadPromise));\n\n      const allUnmountPromises = unmountUnloadPromises.concat(unloadPromises);\n      if (allUnmountPromises.length > 0) {\n        wasNoOp = false;\n      }\n\n      const unmountAllPromise = Promise.all(allUnmountPromises);\n\n      const appsToLoad = getAppsToLoad();\n\n      /* We load and bootstrap apps while other apps are unmounting, but we\n       * wait to mount the app until all apps are finishing unmounting\n       */\n      const loadThenMountPromises = appsToLoad.map(app => {\n        return toLoadPromise(app)\n          .then(toBootstrapPromise)\n          .then(app => {\n            return unmountAllPromise\n              .then(() => toMountPromise(app))\n          })\n      })\n      if (loadThenMountPromises.length > 0) {\n        wasNoOp = false;\n      }\n\n      /* These are the apps that are already bootstrapped and just need\n       * to be mounted. They each wait for all unmounting apps to finish up\n       * before they mount.\n       */\n      const mountPromises = getAppsToMount()\n        .filter(appToMount => appsToLoad.indexOf(appToMount) < 0)\n        .map(appToMount => {\n          return toBootstrapPromise(appToMount)\n            .then(() => unmountAllPromise)\n            .then(() => toMountPromise(appToMount))\n        })\n      if (mountPromises.length > 0) {\n        wasNoOp = false;\n      }\n      return unmountAllPromise\n        .catch(err => {\n          callAllEventListeners();\n          throw err;\n        })\n        .then(() => {\n          /* Now that the apps that needed to be unmounted are unmounted, their DOM navigation\n           * events (like hashchange or popstate) should have been cleaned up. So it's safe\n           * to let the remaining captured event listeners to handle about the DOM event.\n           */\n          callAllEventListeners();\n\n          return Promise\n            .all(loadThenMountPromises.concat(mountPromises))\n            .catch(err => {\n              pendingPromises.forEach(promise => promise.reject(err));\n              throw err;\n            })\n            .then(() => finishUpAndReturn(false))\n        })\n\n    })\n  }\n\n  function finishUpAndReturn(callEventListeners=true) {\n    const returnValue = getMountedApps();\n\n    if (callEventListeners) {\n      callAllEventListeners();\n    }\n    pendingPromises.forEach(promise => promise.resolve(returnValue));\n\n    try {\n      const appChangeEventName = wasNoOp ? \"single-spa:no-app-change\": \"single-spa:app-change\";\n      window.dispatchEvent(new CustomEvent(appChangeEventName, getCustomEventDetail()));\n      window.dispatchEvent(new CustomEvent(\"single-spa:routing-event\", getCustomEventDetail()));\n    } catch (err) {\n      /* We use a setTimeout because if someone else's event handler throws an error, single-spa\n       * needs to carry on. If a listener to the event throws an error, it's their own fault, not\n       * single-spa's.\n       */\n      setTimeout(() => {\n        throw err;\n      });\n    }\n\n    /* Setting this allows for subsequent calls to reroute() to actually perform\n     * a reroute instead of just getting queued behind the current reroute call.\n     * We want to do this after the mounting/unmounting is done but before we\n     * resolve the promise for the `reroute` function.\n     */\n    appChangeUnderway = false;\n\n    if (peopleWaitingOnAppChange.length > 0) {\n      /* While we were rerouting, someone else triggered another reroute that got queued.\n       * So we need reroute again.\n       */\n      const nextPendingPromises = peopleWaitingOnAppChange;\n      peopleWaitingOnAppChange = [];\n      reroute(nextPendingPromises);\n    }\n\n    return returnValue;\n  }\n\n  /* We need to call all event listeners that have been delayed because they were\n   * waiting on single-spa. This includes haschange and popstate events for both\n   * the current run of performAppChanges(), but also all of the queued event listeners.\n   * We want to call the listeners in the same order as if they had not been delayed by\n   * single-spa, which means queued ones first and then the most recent one.\n   */\n  function callAllEventListeners() {\n    pendingPromises.forEach(pendingPromise => {\n      callCapturedEventListeners(pendingPromise.eventArguments);\n    });\n\n    callCapturedEventListeners(eventArguments);\n  }\n\n  function getCustomEventDetail() {\n    const result = {detail: {}}\n\n    if (eventArguments && eventArguments[0]) {\n      result.detail.originalEvent = eventArguments[0]\n    }\n\n    return result\n  }\n}\n", "import { reroute } from './navigation/reroute.js';\n\nlet started = false;\n\nexport function start() {\n  started = true;\n  reroute();\n}\n\nexport function isStarted() {\n  return started;\n}\n\nconst startWarningDelay = 5000;\n\nsetTimeout(() => {\n  if (!started) {\n    console.warn(`singleSpa.start() has not been called, ${startWarningDelay}ms after single-spa was loaded. Before start() is called, apps can be declared and loaded, but not bootstrapped or mounted. See https://github.com/CanopyTax/single-spa/blob/master/docs/single-spa-api.md#start`);\n  }\n}, startWarningDelay)\n", "import {getRawAppData, unregisterApplication} from '../applications/apps'\nimport {reroute} from '../navigation/reroute'\nimport {NOT_LOADED} from '../applications/app.helpers'\nimport {toLoadPromise} from '../lifecycles/load'\nimport {toBootstrapPromise} from '../lifecycles/bootstrap'\n\nexport default {\n  getRawAppData,\n  reroute,\n  NOT_LOADED,\n  toLoadPromise,\n  toBootstrapPromise,\n  unregisterApplication,\n}\n", "export { start } from './start.js';\nexport { ensureJQuerySupport } from './jquery-support.js';\nexport { setBootstrapMaxTime, setMountMaxTime, setUnmountMaxTime, setUnloadMaxTime } from './applications/timeouts.js';\nexport { registerApplication, getMountedApps, getAppStatus, unloadApplication, checkActivityFunctions, getAppNames, declareChildApplication, unloadChildApplication } from './applications/apps.js';\nexport { navigateToUrl } from './navigation/navigation-events.js';\nexport { triggerAppChange } from './navigation/reroute.js';\nexport { addErrorHandler, removeErrorHandler } from './applications/app-errors.js';\nexport { mountRootParcel } from './parcels/mount-parcel.js';\n\nexport {\n  NOT_LOADED,\n  LOADING_SOURCE_CODE,\n  NOT_BOOTSTRAPPED,\n  BOOTSTRAPPING,\n  NOT_MOUNTED,\n  MOUNTING,\n  UPDATING,\n  MOUNTED,\n  UNMOUNTING,\n  SKIP_BECAUSE_BROKEN,\n} from './applications/app.helpers.js';\n\nimport devtools from \"./devtools/devtools\"\nif(window && window.__SINGLE_SPA_DEVTOOLS__) {\n  window.__SINGLE_SPA_DEVTOOLS__.exposedMethods = devtools\n}\n"], "names": ["NativeCustomEvent", "CustomEvent", "p", "detail", "foo", "type", "e", "useNative", "document", "createEvent", "params", "initCustomEvent", "bubbles", "cancelable", "createEventObject", "Boolean", "errorHandlers", "handleAppError", "err", "app", "transformedErr", "transformErr", "length", "for<PERSON>ach", "handler", "setTimeout", "addError<PERSON><PERSON>ler", "Error", "push", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "removedSomething", "filter", "h", "<PERSON><PERSON><PERSON><PERSON>", "og<PERSON>rr", "appOrParcel", "result", "objectType", "unmountThisParcel", "errPrefix", "name", "status", "message", "console", "warn", "JSON", "stringify", "appName", "appOrParcelName", "NOT_LOADED", "LOADING_SOURCE_CODE", "NOT_BOOTSTRAPPED", "BOOTSTRAPPING", "NOT_MOUNTED", "MOUNTING", "MOUNTED", "UPDATING", "UNMOUNTING", "UNLOADING", "SKIP_BECAUSE_BROKEN", "isActive", "isntActive", "isLoaded", "isntLoaded", "shouldBeActive", "activeWhen", "window", "location", "shouldntBeActive", "notSkipped", "item", "to<PERSON>ame", "globalTimeoutConfig", "bootstrap", "millis", "dieOnTimeout", "mount", "unmount", "unload", "setBootstrapMaxTime", "time", "setMountMaxTime", "setUnmountMaxTime", "setUnloadMaxTime", "reasonableTime", "promise", "description", "timeoutConfig", "warningPeriod", "Promise", "resolve", "reject", "finished", "errored", "maybeTimingOut", "shouldError", "error", "numWarnings", "numMill<PERSON>", "then", "val", "catch", "ensureValidAppTimeouts", "timeouts", "find", "arr", "func", "i", "validLifecycleFn", "fn", "Array", "isArray", "flattenFnArray", "fns", "props", "waitForPromises", "index", "smellsLikeAPromise", "toBootstrapPromise", "hardFail", "getProps", "toUnmountPromise", "unmount<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Object", "keys", "parcels", "map", "parcelId", "all", "unmountAppOrParcel", "parcelError", "parentError", "beforeFirstMountFired", "firstMountFired", "toMountPromise", "dispatchEvent", "setSkipBecauseBroken", "parcelCount", "rootParcels", "mountRootParcel", "mountParcel", "apply", "arguments", "config", "customProps", "owningAppOrParcel", "this", "_typeof", "dom<PERSON>lement", "externalRepresentation", "id", "passedConfigLoadingFunction", "configLoadingFunction", "parcel", "parentName", "value", "resolveUnmount", "rejectUnmount", "loadPromise", "bootstrapPromise", "update", "promiseWithoutReturnValue", "toUpdatePromise", "mountPromise", "unmountPromise", "getStatus", "bind", "singleSpa", "unmountSelf", "toLoadPromise", "loadImpl", "validationErrMessage", "appOpts", "devtools", "overlays", "capturedEventListeners", "hashchange", "popstate", "routingEventsListeningTo", "navigateToUrl", "obj", "url", "opts", "href", "currentTarget", "preventDefault", "current", "parseUri", "destination", "indexOf", "hash", "anchor", "host", "isTestingEnv", "wouldHaveReloadedThePage", "isSamePath", "path", "query", "history", "pushState", "callCapturedEventListeners", "eventArguments", "eventType", "listener", "_this", "urlReroute", "reroute", "addEventListener", "originalAddEventListener", "originalRemoveEventListener", "removeEventListener", "eventName", "listenerFn", "originalPushState", "state", "createPopStateEvent", "originalReplaceState", "replaceState", "PopStateEvent", "evt", "initPopStateEvent", "str", "o", "strictMode", "key", "q", "parser", "strict", "loose", "m", "exec", "uri", "replace", "$0", "$1", "$2", "singleSpaNavigate", "hasInitialized", "ensureJQuerySupport", "j<PERSON><PERSON><PERSON>", "$", "j<PERSON>y", "originalJQueryOn", "on", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "off", "eventString", "captureRoutingEvents", "call", "originalJQueryFunction", "nativeFunctionToCall", "originalArgs", "split", "trim", "appsToUnload", "toUnloadPromise", "unloadInfo", "finishUnloadingApp", "errorUnloadingApp", "addAppToUnload", "promiseGetter", "defineProperty", "get", "apps", "getMountedApps", "getAppNames", "getAppStatus", "declareChildApplication", "arg1", "arg2", "registerApplication", "applicationOrLoadingFn", "activityFn", "options", "selectors", "checkActivityFunctions", "activeApps", "getAppsToLoad", "unloadChildApplication", "unloadApplication", "waitForUnmount", "App", "resultPromise", "appUnloadInfo", "getAppUnloadInfo", "immediatelyUnloadApp", "appChangeUnderway", "peopleWaitingOnAppChange", "triggerAppChange", "pendingPromises", "wasNoOp", "started", "getCustomEventDetail", "unloadPromises", "unmountUnloadPromises", "allUnmountPromises", "concat", "unmountAllPromise", "appsToLoad", "loadThenMountPromises", "mountPromises", "appToMount", "callAllEventListeners", "finishUpAndReturn", "loadPromises", "callEventListeners", "returnValue", "appChangeEventName", "nextPendingPromises", "pendingPromise", "originalEvent", "start", "getRawAppData", "unregisterApplication", "appIndex", "findIndex", "splice", "__SINGLE_SPA_DEVTOOLS__", "exposedMethods"], "mappings": "i4CACIA,mJAA2BC,YAmB/B,MAjBA,WACE,IACE,IAAIC,EAAI,IAAIF,EAAkB,MAAO,CAAEG,OAAQ,CAAEC,IAAK,SACtD,MAAQ,QAAUF,EAAEG,MAAQ,QAAUH,EAAEC,OAAOC,IAC/C,MAAOE,IAET,OAAO,EAWQC,GAAcP,EAG/B,oBAAuBQ,UAAY,mBAAsBA,SAASC,YAAc,SAAsBJ,EAAMK,GAC1G,IAAIJ,EAAIE,SAASC,YAAY,eAM7B,OALIC,EACFJ,EAAEK,gBAAgBN,EAAMK,EAAOE,QAASF,EAAOG,WAAYH,EAAOP,QAElEG,EAAEK,gBAAgBN,GAAM,GAAO,OAAO,GAEjCC,GAIT,SAAsBD,EAAMK,GAC1B,IAAIJ,EAAIE,SAASM,oBAWjB,OAVAR,EAAED,KAAOA,EACLK,GACFJ,EAAEM,QAAUG,QAAQL,EAAOE,SAC3BN,EAAEO,WAAaE,QAAQL,EAAOG,YAC9BP,EAAEH,OAASO,EAAOP,SAElBG,EAAEM,SAAU,EACZN,EAAEO,YAAa,EACfP,EAAEH,YAAS,GAENG,y4BC9CT,IAAIU,EAAgB,GAEb,SAASC,EAAeC,EAAKC,OAC5BC,EAAiBC,EAAaH,EAAKC,GAErCH,EAAcM,OAChBN,EAAcO,QAAQ,SAAAC,UAAWA,EAAQJ,KAEzCK,WAAW,iBACHL,IAKL,SAASM,EAAgBF,MACP,mBAAZA,QACHG,MAAM,iDAGdX,EAAcY,KAAKJ,GAGd,SAASK,EAAmBL,MACV,mBAAZA,QACHG,MAAM,qDAGVG,GAAmB,SACvBd,EAAgBA,EAAce,OAAO,SAAAC,OAC7BC,EAAYD,IAAMR,SACxBM,EAAmBA,GAAoBG,GAC/BA,IAGHH,EAGF,SAAST,EAAaa,EAAOC,OAI9BC,EAHEC,EAAaF,EAAYG,kBAAoB,SAAW,cACxDC,YAAeF,eAAeF,EAAYK,iCAAwBL,EAAYM,gBAIhFP,aAAiBP,MAAO,KAExBO,EAAMQ,QAAUH,EAAYL,EAAMQ,QAClC,MAAMxB,IAKRkB,EAASF,MACJ,CACLS,QAAQC,qBAAcT,EAAYM,qBAAYN,EAAYK,0HAExDJ,EAAST,MAAMY,EAAYM,KAAKC,UAAUZ,IAC1C,MAAMhB,GAENkB,EAASF,GAIbE,EAAOW,QAAUZ,EAAYK,KAC7BJ,EAAOY,gBAAkBb,EAAYK,SAEnCJ,EAAOI,KAAOL,EAAYK,KAC1B,MAAOtB,WAMFkB,MCrEIa,iBAAa,cACbC,0BAAsB,uBACtBC,uBAAmB,oBACnBC,oBAAgB,iBAChBC,kBAAc,eACdC,eAAW,YACXC,cAAU,WACVC,eAAW,YACXC,iBAAa,cACbC,EAAY,YACZC,0BAAsB,uBAE5B,SAASC,EAASzC,UAChBA,EAAIsB,SAAWc,EAGjB,SAASM,EAAW1C,UACjByC,EAASzC,GAGZ,SAAS2C,EAAS3C,UAChBA,EAAIsB,SAAWQ,GAAc9B,EAAIsB,SAAWS,EAG9C,SAASa,EAAW5C,UACjB2C,EAAS3C,GAGZ,SAAS6C,EAAe7C,cAEpBA,EAAI8C,WAAWC,OAAOC,UAC7B,MAAOjD,GACPD,EAAeC,EAAKC,GACpBA,EAAIsB,OAASkB,GAIV,SAASS,EAAiBjD,cAErBA,EAAI8C,WAAWC,OAAOC,UAC9B,MAAOjD,GACPD,EAAeC,EAAKC,GACpBA,EAAIsB,OAASkB,GAQV,SAASU,EAAWC,UAClBA,IAASX,KAAyBW,GAAQA,EAAK7B,SAAWkB,GAG5D,SAASY,EAAOpD,UACdA,EAAIqB,SC1DPgC,EAAsB,CAC1BC,UAAW,CACTC,OAAQ,IACRC,cAAc,GAEhBC,MAAO,CACLF,OAAQ,IACRC,cAAc,GAEhBE,QAAS,CACPH,OAAQ,IACRC,cAAc,GAEhBG,OAAQ,CACNJ,OAAQ,IACRC,cAAc,IAIX,SAASI,EAAoBC,OAAML,6DACpB,iBAATK,GAAqBA,GAAQ,QAChCrD,8EAGR6C,EAAoBC,UAAY,CAC9BC,OAAQM,EACRL,aAAAA,GAIG,SAASM,EAAgBD,OAAML,6DAChB,iBAATK,GAAqBA,GAAQ,QAChCrD,0EAGR6C,EAAoBI,MAAQ,CAC1BF,OAAQM,EACRL,aAAAA,GAIG,SAASO,EAAkBF,OAAML,6DAClB,iBAATK,GAAqBA,GAAQ,QAChCrD,4EAGR6C,EAAoBK,QAAU,CAC5BH,OAAQM,EACRL,aAAAA,GAIG,SAASQ,EAAiBH,OAAML,6DACjB,iBAATK,GAAqBA,GAAQ,QAChCrD,2EAGR6C,EAAoBM,OAAS,CAC3BJ,OAAQM,EACRL,aAAAA,GAIG,SAASS,EAAeC,EAASC,EAAaC,OAC7CC,EAAgB,WAEf,IAAIC,QAAQ,SAACC,EAASC,OACvBC,GAAW,EACXC,GAAU,WAeLC,EAAeC,OACjBH,MACiB,IAAhBG,EACFF,GAAU,EACNN,EAAcZ,aAChBgB,YAAUL,4CAA6CC,EAAcb,yBAErE/B,QAAQqD,gBAASV,4CAA6CC,EAAcb,6EAGzE,IAAKmB,EAAS,KACbI,EAAcF,EACdG,EAAYD,EAAcT,EAChC7C,QAAQC,eAAQ0C,+CAAgDY,oBAC5DA,EAAYV,EAAgBD,EAAcb,QAC5CjD,WAAW,kBAAMqE,EAAeG,EAAc,IAAIT,IA5B1DH,EACCc,KAAK,SAAAC,GACJR,GAAW,EACXF,EAAQU,KAETC,MAAM,SAAAD,GACLR,GAAW,EACXD,EAAOS,KAGT3E,WAAW,kBAAMqE,EAAe,IAAIN,GACpC/D,WAAW,kBAAMqE,GAAe,IAAOP,EAAcb,UAyBlD,SAAS4B,QAAuBC,yDAAW,eAE3C/B,KACA+B,GCzGA,SAASC,EAAKC,EAAKC,OACnB,IAAIC,EAAE,EAAGA,EAAEF,EAAInF,OAAQqF,OACtBD,EAAKD,EAAIE,WACJF,EAAIE,UAIR,KCTF,SAASC,EAAiBC,UACxBA,IAAqB,mBAAPA,IAECJ,EAFiCI,EAG9CC,MAAMC,QAAQN,KAASD,EAAKC,EAAK,SAAAnC,SAAwB,mBAATA,UADnCmC,EAKjB,SAASO,EAAeC,EAAK3B,UAEf,KADnB2B,EAAMH,MAAMC,QAAQE,GAAOA,EAAM,CAACA,IAC1B3F,SACN2F,EAAM,CAAC,kBAAMxB,QAAQC,aAGhB,SAASwB,UACP,IAAIzB,QAAQ,SAACC,EAASC,aAGlBwB,EAAgBC,OACjB/B,EAAU4B,EAAIG,GAAOF,GACtBG,EAAmBhC,GAGtBA,EACGc,KAAK,WACAiB,IAAUH,EAAI3F,OAAS,EACzBoE,IAEAyB,EAAgBC,EAAQ,KAG3Bf,MAAMV,GAVTA,YAAUL,uBAAwB8B,gCALtCD,CAAgB,MAsBf,SAASE,EAAmBhC,UAC1BA,GAAmC,mBAAjBA,EAAQc,MAAgD,mBAAlBd,EAAQgB,MCpClE,SAASiB,EAAmBnF,OAAaoF,iEACvC9B,QAAQC,UAAUS,KAAK,kBACxBhE,EAAYM,SAAWU,EAClBhB,GAGTA,EAAYM,OAASW,EAEdgC,EAAejD,EAAYsC,UAAU+C,EAASrF,yCAA6CA,EAAYK,UAASL,EAAYoE,SAAS9B,WACzI0B,KAAK,kBACJhE,EAAYM,OAASY,EACdlB,IAERkE,MAAM,SAAAnF,MACLiB,EAAYM,OAASkB,EACjB4D,QACqBlG,EAAaH,EAAKiB,UAGzClB,EAAeC,EAAKiB,GACbA,OCpBV,SAASsF,EAAiBtF,OAAaoF,iEACrC9B,QAAQC,UAAUS,KAAK,cACxBhE,EAAYM,SAAWc,SAClBpB,EAETA,EAAYM,OAASgB,MAEfiE,EAAyBC,OAAOC,KAAKzF,EAAY0F,SACpDC,IAAI,SAAAC,UAAY5F,EAAY0F,QAAQE,GAAUzF,6BAI1CmD,QAAQuC,IAAIN,GAChBvB,KACC8B,EACA,SAAAC,UAESD,IACJ9B,KAAK,eAEEgC,EAAcxG,MAAMuG,EAAYxF,YAClC6E,EAAU,KACNnG,EAAiBC,EAAa8G,EAAahG,SACjDA,EAAYM,OAASkB,EACfvC,EAENH,EAAekH,EAAahG,GAC5BA,EAAYM,OAASkB,MAK9BwC,KAAK,kBAAMhE,aAEL8F,WAEA7C,EAAejD,EAAY0C,QAAQ2C,EAASrF,qCAAyCA,EAAYK,UAASL,EAAYoE,SAAS1B,SACnIsB,KAAK,WAGFhE,EAAYM,OAASY,IAGxBgD,MAAM,SAAAnF,MACDqG,EAAU,KACNnG,EAAiBC,EAAaH,EAAKiB,SACzCA,EAAYM,OAASkB,EACfvC,EAENH,EAAeC,EAAKiB,GACpBA,EAAYM,OAASkB,OChDjC,IAAIyE,GAAwB,EACxBC,GAAkB,EAEf,SAASC,EAAenG,OAAaoF,iEACnC9B,QAAQC,UAAUS,KAAK,kBACxBhE,EAAYM,SAAWY,EAClBlB,GAGJiG,IACHlE,OAAOqE,cAAc,IAAItI,EAAY,kCACrCmI,GAAwB,GAGnBhD,EAAejD,EAAYyC,MAAM4C,EAASrF,oCAAwCA,EAAYK,UAASL,EAAYoE,SAAS3B,OAChIuB,KAAK,kBACJhE,EAAYM,OAASc,EAEhB8E,IACHnE,OAAOqE,cAAc,IAAItI,EAAY,2BACrCoI,GAAkB,GAGblG,IAERkE,MAAM,SAAAnF,UAILiB,EAAYM,OAASc,EACdkE,EAAiBtF,GACrBgE,KAAKqC,EAAsBA,YAErBA,OACFjB,EAIE,KACCnG,EAAiBC,EAAaH,EAAKiB,SACzCA,EAAYM,OAASkB,EACfvC,SANNH,EAAeC,EAAKiB,GACpBA,EAAYM,OAASkB,EACdxB,QCpCnB,IAAIsG,EAAc,EACZC,EAAc,CAACb,QAAS,IAGvB,SAASc,WACPC,EAAYC,MAAMH,EAAaI,WAGjC,SAASF,EAAYG,EAAQC,OAC5BC,EAAoBC,SAGrBH,GAA6B,WAAlBI,EAAOJ,IAAyC,mBAAXA,QAC7CpH,MAAM,6EAGVoH,EAAOvG,MAA+B,iBAAhBuG,EAAOvG,WACzBb,MAAM,gDAGa,WAAvBwH,EAAOH,SACHrH,uBAAgBa,2DAGnBwG,EAAYI,iBACTzH,uBAAgBa,wEAyCpB6G,EAtCEC,EAAKb,IAELc,EAAgD,mBAAXR,EACrCS,EAAwBD,EAA8BR,EAAS,kBAAMtD,QAAQC,QAAQqD,IAGrFU,EAAS,CACbH,GAAAA,EACAzB,QAAS,GACTpF,OAAQ8G,EAA8BrG,EAAsBC,EAC5D6F,YAAAA,EACAU,WAAYT,EAAkBzG,KAC9BF,gCACMmH,EAAOhH,SAAWc,QACd5B,uCAAgCa,gCAAuBiH,EAAOhH,0BAG/DgF,EAAiBgC,GAAQ,GAC7BtD,KAAK,SAAAwD,UACAF,EAAOC,mBACFT,EAAkBpB,QAAQ4B,EAAOH,IAGnCK,IAERxD,KAAK,SAAAwD,UACJC,EAAeD,GACRA,IAERtD,MAAM,SAAAnF,SACLuI,EAAOhH,OAASkB,EAChBkG,EAAc3I,GACRA,MASd+H,EAAkBpB,QAAQyB,GAAMG,MAE5BK,EAAcN,QAEbM,GAA2C,mBAArBA,EAAY3D,WAC/BxE,4HAoDJiI,EAAgBC,EAHdE,GA9CND,EAAcA,EAAY3D,KAAK,SAAA4C,OACxBA,QACGpH,8HAGFa,EAAOuG,EAAOvG,uBAAkB8G,OAEjC1C,EAAiBmC,EAAOtE,iBACrB9C,uBAAgBa,gDAGnBoE,EAAiBmC,EAAOnE,aACrBjD,uBAAgBa,4CAGnBoE,EAAiBmC,EAAOlE,eACrBlD,uBAAgBa,6CAGpBuG,EAAOiB,SAAWpD,EAAiBmC,EAAOiB,cACtCrI,uBAAgBa,+CAGlBiC,EAAYuC,EAAe+B,EAAOtE,WAClCG,EAAQoC,EAAe+B,EAAOnE,OAC9BC,EAAUmC,EAAe+B,EAAOlE,SAEtC4E,EAAOhH,OAASU,EAChBsG,EAAOjH,KAAOA,EACdiH,EAAOhF,UAAYA,EACnBgF,EAAO7E,MAAQA,EACf6E,EAAO5E,QAAUA,EACjB4E,EAAOlD,SAAWD,EAAuByC,EAAOxC,UAE5CwC,EAAOiB,SACTP,EAAOO,OAAShD,EAAe+B,EAAOiB,QACtCX,EAAuBW,OAAS,SAAShB,UACvCS,EAAOT,YAAcA,EAEdiB,ECvHR,SAAyBR,UACvBhE,QAAQC,UAAUS,KAAK,cACxBsD,EAAOhH,SAAWc,QACd5B,sCAA+B8H,EAAOjH,4CAG9CiH,EAAOhH,OAASe,EAET4B,EAAeqE,EAAOO,OAAOxC,EAASiC,+BAA8BA,EAAOjH,UAASiH,EAAOlD,SAAS3B,OACxGuB,KAAK,kBACJsD,EAAOhH,OAASc,EACTkG,IAERpD,MAAM,SAAAnF,OACCE,EAAiBC,EAAaH,EAAKuI,SACzCA,EAAOhH,OAASkB,EACVvC,MDuG2B8I,CAAgBT,SAOlBtD,KAAK,kBAAMmB,EAAmBmC,GAAQ,KACrEU,EAAeJ,EAAiB5D,KAAK,kBAAMmC,EAAemB,GAAQ,KAIlEW,EAAiB,IAAI3E,QAAQ,SAACC,EAASC,GAC3CiE,EAAiBlE,EACjBmE,EAAgBlE,WAGlB0D,EAAyB,CACvBzE,wBACSqF,EACLxE,QACCC,UACAS,KAAK,cACAsD,EAAOhH,SAAWY,QACd1B,qCAA8Ba,gCAAuBiH,EAAOhH,0BAIpEwG,EAAkBpB,QAAQyB,GAAMG,EAEzBnB,EAAemB,OAI5B5E,0BACSoF,EACLR,EAAOnH,sBAGX+H,4BACSZ,EAAOhH,QAEhBqH,YAAaG,EAA0BH,GACvCC,iBAAkBE,EAA0BF,GAC5CI,aAAcF,EAA0BE,GACxCC,eAAgBH,EAA0BG,IAM9C,SAASH,EAA0B5E,UAC1BA,EAAQc,KAAK,kBAAM,OE7KrB,SAASqB,EAASrF,OACjBC,OACDD,EAAY6G,aACfxG,KAAML,EAAYK,KAClBoG,YAAaA,EAAY0B,KAAKnI,GAC9BoI,UAAAA,WAGEpI,EAAYG,oBACdF,EAAOoI,YAAcrI,EAAYG,mBAG5BF,ECTF,SAASqI,GAActJ,UACrBsE,QAAQC,UAAUS,KAAK,kBACxBhF,EAAIsB,SAAWQ,EACV9B,GAGTA,EAAIsB,OAASS,EAINuC,QAAQC,UAAUS,KAAK,eACtB2D,EAAc3I,EAAIuJ,SAASlD,EAASrG,QACrCkG,EAAmByC,SAEhBnI,wHAAiHR,EAAIqB,sDAEtHsH,EAAY3D,KAAK,SAAAC,OAGlBuE,QAEmB,WAAnBxB,EAJJyB,EAAUxE,KAKRuE,8BAGG/D,EAAiBgE,EAAQnG,aAC5BkG,gEAGG/D,EAAiBgE,EAAQhG,SAC5B+F,4DAGG/D,EAAiBgE,EAAQ/F,WAC5B8F,+DAGEA,GACFhI,QAAQqD,iEAA0D7E,EAAIqB,mGAAkGoI,GACxK3J,EAAe0J,EAAsBxJ,GACrCA,EAAIsB,OAASkB,EACNxC,IAGLyJ,EAAQC,UAAYD,EAAQC,SAASC,WACvC3J,EAAI0J,SAASC,cAAe3J,EAAI0J,SAASC,YAAaF,EAAQC,SAASC,WAGzE3J,EAAIsB,OAASU,EACbhC,EAAIsD,UAAYuC,EAAe4D,EAAQnG,yBAAmBtD,EAAIqB,8BAC9DrB,EAAIyD,MAAQoC,EAAe4D,EAAQhG,qBAAezD,EAAIqB,0BACtDrB,EAAI0D,QAAUmC,EAAe4D,EAAQ/F,uBAAiB1D,EAAIqB,4BAC1DrB,EAAI2D,OAASkC,EAAe4D,EAAQ9F,QAAU,kBAAY3D,EAAIqB,2BAC9DrB,EAAIoF,SAAWD,EAAuBsE,EAAQrE,UAEvCpF,OAGVkF,MAAM,SAAAnF,UACLD,EAAeC,EAAKC,GACpBA,EAAIsB,OAASkB,EACNxC,SArDLyJ,ICNR,IAAMG,GAAyB,CAC7BC,WAAY,GACZC,SAAU,IAGCC,GAA2B,CAAC,aAAc,YAEhD,SAASC,GAAcC,OACxBC,EAD6BC,yDAAK,MAEnB,iBAARF,EACTC,EAAMD,OACD,GAAIlC,MAAQA,KAAKqC,KACtBF,EAAMnC,KAAKqC,SACN,CAAA,KAAIH,GAAOA,EAAII,eAAiBJ,EAAII,cAAcD,MAAQH,EAAIK,sBAI7D9J,uJAHN0J,EAAMD,EAAII,cAAcD,KACxBH,EAAIK,qBAKAC,EAAUC,GAASzH,OAAOC,SAASoH,MACnCK,EAAcD,GAASN,MAEJ,IAArBA,EAAIQ,QAAQ,KACd3H,OAAOC,SAAS2H,KAAO,IAAMF,EAAYG,YACpC,GAAIL,EAAQM,OAASJ,EAAYI,MAAQJ,EAAYI,KAAM,IAC5DV,EAAKW,mBACA,CAACC,0BAA0B,GAElChI,OAAOC,SAASoH,KAAOF,gBASPO,EAAaF,UAExBA,IAAYE,GAAeF,IAAY,IAAME,EAT1CO,CAAWP,EAAYQ,KAAO,IAAMR,EAAYS,MAAOX,EAAQU,KAAO,IAAMV,EAAQW,OAE9FnI,OAAOoI,QAAQC,UAAU,KAAM,KAAMlB,GAErCnH,OAAOC,SAAS2H,KAAO,IAAMF,EAAYG,OAStC,SAASS,GAA2BC,iBACrCA,EAAgB,KACZC,EAAYD,EAAe,GAAGpM,KAChC6K,GAAyBW,QAAQa,IAAc,GACjD3B,GAAuB2B,GAAWnL,QAAQ,SAAAoL,GACxCA,EAAS9D,MAAM+D,EAAMH,MAM7B,SAASI,KACPC,GAAQ,GAAIhE,WAKd5E,OAAO6I,iBAAiB,aAAcF,IACtC3I,OAAO6I,iBAAiB,WAAYF,IAGpC,IAAMG,GAA2B9I,OAAO6I,iBAClCE,GAA8B/I,OAAOgJ,oBAC3ChJ,OAAO6I,iBAAmB,SAASI,EAAWtG,QAC1B,mBAAPA,GACLqE,GAAyBW,QAAQsB,IAAc,IAAM3G,EAAKuE,GAAuBoC,GAAY,SAAAR,UAAYA,IAAa9F,WAMrHmG,GAAyBnE,MAAMK,KAAMJ,WALxCiC,GAAuBoC,GAAWvL,KAAKiF,IAQ7C3C,OAAOgJ,oBAAsB,SAASC,EAAWC,QACrB,mBAAfA,GACLlC,GAAyBW,QAAQsB,IAAc,UAM9CF,GAA4BpE,MAAMK,KAAMJ,WAL3CiC,GAAuBoC,GAAapC,GAAuBoC,GAAWpL,OAAO,SAAA8E,UAAMA,IAAOuG,KAQhG,IAAMC,GAAoBnJ,OAAOoI,QAAQC,UACzCrI,OAAOoI,QAAQC,UAAY,SAASe,OAC5BlL,EAASiL,GAAkBxE,MAAMK,KAAMJ,kBAE7C+D,GAAWU,GAAoBD,IAExBlL,GAGT,IAAMoL,GAAuBtJ,OAAOoI,QAAQmB,aAO5C,SAASF,GAAoBD,cAKlB,IAAII,cAAc,WAAY,CAACJ,MAAAA,IACtC,MAAOpM,OAGDyM,EAAMnN,SAASC,YAAY,wBACjCkN,EAAIC,kBAAkB,YAAY,GAAO,EAAON,GACzCK,GASX,SAAShC,GAASkC,WAkBXC,EAbgB,CACnBC,YAAY,EACZC,IAAK,CAAC,SAAS,WAAW,YAAY,WAAW,OAAO,WAAW,OAAO,OAAO,WAAW,OAAO,YAAY,OAAO,QAAQ,UAC9HC,EAAK,CACHzL,KAAQ,WACR0L,OAAQ,6BAEVA,OAAQ,CACNC,OAAQ,0IACRC,MAAQ,qMAKRC,EAAIP,EAAEI,cAA0CI,KAAKT,GACrDU,EAAM,GACN5H,EAAI,GAEDA,KAAK4H,EAAIT,EAAEE,IAAIrH,IAAM0H,EAAE1H,IAAM,UAEpC4H,EAAIT,EAAEG,EAAEzL,MAAQ,GAChB+L,EAAIT,EAAEE,IAAI,KAAKQ,QAAQV,EAAEG,EAAEC,OAAQ,SAAUO,EAAIC,EAAIC,GAC/CD,IAAIH,EAAIT,EAAEG,EAAEzL,MAAMkM,GAAMC,KAGvBJ,EAxDTrK,OAAOoI,QAAQmB,aAAe,SAASH,OAC/BlL,EAASoL,GAAqB3E,MAAMK,KAAMJ,kBAChD+D,GAAWU,GAAoBD,IACxBlL,GAqBT8B,OAAO0K,kBAAoBzD,GCjI3B,IAAI0D,IAAiB,EAEd,SAASC,SAAoBC,yDAAS7K,OAAO6K,UAC7CA,GACC7K,OAAO8K,GAAK9K,OAAO8K,EAAEnI,IAAM3C,OAAO8K,EAAEnI,GAAGoI,SACzCF,EAAS7K,OAAO8K,GAIhBD,IAAWF,GAAgB,KACvBK,EAAmBH,EAAOlI,GAAGsI,GAC7BC,EAAoBL,EAAOlI,GAAGwI,IAEpCN,EAAOlI,GAAGsI,GAAK,SAASG,EAAazI,UAC5B0I,GAAqBC,KAAKtG,KAAMgG,EAAkBhL,OAAO6I,iBAAkBuC,EAAazI,EAAIiC,YAGrGiG,EAAOlI,GAAGwI,IAAM,SAASC,EAAazI,UAC7B0I,GAAqBC,KAAKtG,KAAMkG,EAAmBlL,OAAOgJ,oBAAqBoC,EAAazI,EAAIiC,YAGzG+F,IAAiB,GAIrB,SAASU,GAAqBE,EAAwBC,EAAsBJ,EAAazI,EAAI8I,SAChE,iBAAhBL,EACFG,EAAuB5G,MAAMK,KAAMyG,IAGzBL,EAAYM,MAAM,OAC1BrO,QAAQ,SAAA4L,GACbjC,GAAyBW,QAAQsB,IAAc,IACjDuC,EAAqBvC,EAAWtG,GAChCyI,EAAcA,EAAYd,QAAQrB,EAAW,OAItB,KAAvBmC,EAAYO,OACP3G,KAEAuG,EAAuB5G,MAAMK,KAAMyG,ICtC9C,IAAMG,GAAe,GAEd,SAASC,GAAgB5O,UACvBsE,QAAQC,UAAUS,KAAK,eACtB6J,EAAaF,GAAa3O,EAAIqB,aAE/BwN,EAMD7O,EAAIsB,SAAWQ,GAIjBgN,GAAmB9O,EAAK6O,GACjB7O,GAGLA,EAAIsB,SAAWiB,EAIVsM,EAAW3K,QAAQc,KAAK,kBAAMhF,IAGnCA,EAAIsB,SAAWY,EAGVlC,GAGTA,EAAIsB,OAASiB,EACN0B,EAAejE,EAAI2D,OAAO0C,EAASrG,qCAAiCA,EAAIqB,UAASrB,EAAIoF,SAASzB,QAClGqB,KAAK,kBACJ8J,GAAmB9O,EAAK6O,GACjB7O,IAERkF,MAAM,SAAAnF,UAwBb,SAA2BC,EAAK6O,EAAY9O,UACnC4O,GAAa3O,EAAIqB,aAGjBrB,EAAIsD,iBACJtD,EAAIyD,aACJzD,EAAI0D,eACJ1D,EAAI2D,OAEX7D,EAAeC,EAAKC,GACpBA,EAAIsB,OAASkB,EACbqM,EAAWrK,OAAOzE,GAlCZgP,CAAkB/O,EAAK6O,EAAY9O,GAC5BC,KAhCFA,IAqCb,SAAS8O,GAAmB9O,EAAK6O,UACxBF,GAAa3O,EAAIqB,aAGjBrB,EAAIsD,iBACJtD,EAAIyD,aACJzD,EAAI0D,eACJ1D,EAAI2D,OAEX3D,EAAIsB,OAASQ,EAKb+M,EAAWtK,UAiBN,SAASyK,GAAehP,EAAKiP,EAAe1K,EAASC,GAC1DmK,GAAa3O,EAAIqB,MAAQ,CAACrB,IAAAA,EAAKuE,QAAAA,EAASC,OAAAA,GACxCgC,OAAO0I,eAAeP,GAAa3O,EAAIqB,MAAO,UAAW,CAAC8N,IAAKF,IC7EjE,IAAMG,GAAO,GAEN,SAASC,YACPD,GAAKxO,OAAO6B,GAAUkE,IAAIvD,GAG5B,SAASkM,YACPF,GAAKzI,IAAIvD,GAQX,SAASmM,GAAa3N,OACrB5B,EAAMqF,EAAK+J,GAAM,SAAApP,UAAOA,EAAIqB,OAASO,WACpC5B,EAAMA,EAAIsB,OAAS,KAGrB,SAASkO,GAAwB5N,EAAS6N,EAAMC,UACrDlO,QAAQC,KAAK,0HACNkO,GAAoB/N,EAAS6N,EAAMC,GAGrC,SAASC,GAAoB/N,EAASgO,EAAwBC,OAW/DtG,EAX2E1B,yDAAc,MACtE,iBAAZjG,GAA2C,IAAnBA,EAAQzB,OACzC,MAAMK,qEACgC,IAApC8O,KAAc5E,QAAQ9I,GACxB,MAAMpB,2DAAoDoB,OACjC,WAAvBoG,EAAOH,IAA4BlC,MAAMC,QAAQiC,GACnD,MAAMrH,MAAM,qCAEToP,EACH,MAAMpP,4DAKN+I,EAFoC,mBAA3BqG,EAEE,kBAAMtL,QAAQC,QAAQqL,IAGtBA,EAGa,mBAAfC,EACT,MAAMrP,oDAER4O,GAAK3O,KAAK,CACRY,KAAMO,EACN2H,SAAAA,EACAzG,WAAY+M,EACZvO,OAAQQ,EACR4E,QAAS,GACTgD,SAAU,CACRC,SAAU,CACRmG,QAAS,GACTC,UAAW,KAGflI,YAAAA,IAGF8F,KAEAhC,KAGK,SAASqE,GAAuBhN,WAC/BiN,EAAa,GACVzK,EAAI,EAAGA,EAAI4J,GAAKjP,OAAQqF,IAC3B4J,GAAK5J,GAAG1C,WAAWE,IACrBiN,EAAWxP,KAAK2O,GAAK5J,GAAGnE,aAGrB4O,EAGF,SAASC,YACPd,GACJxO,OAAOsC,GACPtC,OAAOgC,GACPhC,OAAOiC,GA8BL,SAASsN,GAAuBvO,EAASuI,UAC9C3I,QAAQC,KAAK,uHACN2O,GAAkBxO,EAASuI,GAG7B,SAASiG,GAAkBxO,OAASuI,yDAAK,CAACkG,gBAAgB,MACxC,iBAAZzO,QACHpB,2DAEFR,EAAMqF,EAAK+J,GAAM,SAAAkB,UAAOA,EAAIjP,OAASO,QACtC5B,QACGQ,8CAAuCoB,4DAqBzC2O,EAlBAC,ED9CD,SAA0B5O,UACxB+M,GAAa/M,GC6CE6O,CAAiBzQ,EAAIqB,SACvC8I,GAAQA,EAAKkG,eAAgB,IAG3BG,SAEKA,EAActM,YAGfA,EAAU,IAAII,QAAQ,SAACC,EAASC,GACpCwK,GAAehP,EAAK,kBAAMkE,GAASK,EAASC,YAEvCN,SAQLsM,GAEFD,EAAgBC,EAActM,QAC9BwM,GAAqB1Q,EAAKwQ,EAAcjM,QAASiM,EAAchM,SAG/D+L,EAAgB,IAAIjM,QAAQ,SAACC,EAASC,GACpCwK,GAAehP,EAAK,kBAAMuQ,GAAehM,EAASC,GAClDkM,GAAqB1Q,EAAKuE,EAASC,KAIhC+L,EAIX,SAASG,GAAqB1Q,EAAKuE,EAASC,GAC1C8B,EAAiBtG,GACdgF,KAAK4J,IACL5J,KAAK,WACJT,IACAjE,WAAW,WAETqL,SAGHzG,MAAMV,OCzKPmM,IAAoB,EAAOC,GAA2B,GAEnD,SAASC,YAEPlF,KAGF,SAASA,SAAQmF,yDAAkB,GAAIxF,4CACxCqF,UACK,IAAIrM,QAAQ,SAACC,EAASC,GAC3BoM,GAAyBnQ,KAAK,CAC5B8D,QAAAA,EACAC,OAAAA,EACA8G,eAAAA,MAKNqF,IAAoB,MAChBI,GAAU,SCnBPC,GD8CE1M,QAAQC,UAAUS,KAAK,WAC5BjC,OAAOqE,cAAc,IAAItI,EAAY,kCAAmCmS,UAClEC,EFkCH1K,OAAOC,KAAKkI,IAChBhI,IAAI,SAAA/E,UAAW+M,GAAa/M,GAAS5B,MACrCY,OAAO8B,GEpCmCiE,IAAIiI,IAEvCuC,EDiCH/B,GACJxO,OAAOsC,GACPtC,OAAO6B,GACP7B,OAAOqC,GCnCH0D,IAAIL,GACJK,IAAI,SAAAsC,UAAkBA,EAAejE,KAAK4J,MAEvCwC,EAAqBD,EAAsBE,OAAOH,GACpDE,EAAmBjR,OAAS,IAC9B4Q,GAAU,OAGNO,EAAoBhN,QAAQuC,IAAIuK,GAEhCG,EAAarB,KAKbsB,EAAwBD,EAAW5K,IAAI,SAAA3G,UACpCsJ,GAActJ,GAClBgF,KAAKmB,GACLnB,KAAK,SAAAhF,UACGsR,EACJtM,KAAK,kBAAMmC,EAAenH,SAG/BwR,EAAsBrR,OAAS,IACjC4Q,GAAU,OAONU,EDQHrC,GACJxO,OAAOsC,GACPtC,OAAO8B,GACP9B,OAAO+B,GACP/B,OAAOiC,GCXHjC,OAAO,SAAA8Q,UAAcH,EAAW7G,QAAQgH,GAAc,IACtD/K,IAAI,SAAA+K,UACIvL,EAAmBuL,GACvB1M,KAAK,kBAAMsM,IACXtM,KAAK,kBAAMmC,EAAeuK,cAE7BD,EAActR,OAAS,IACzB4Q,GAAU,GAELO,EACJpM,MAAM,SAAAnF,SACL4R,IACM5R,IAEPiF,KAAK,kBAKJ2M,IAEOrN,QACJuC,IAAI2K,EAAsBH,OAAOI,IACjCvM,MAAM,SAAAnF,SACL+Q,EAAgB1Q,QAAQ,SAAA8D,UAAWA,EAAQM,OAAOzE,KAC5CA,IAEPiF,KAAK,kBAAM4M,GAAkB,SAlF/BtN,QAAQC,UAAUS,KAAK,eACtB6M,EAAe3B,KAAgBvJ,IAAI2C,WAErCuI,EAAa1R,OAAS,IACxB4Q,GAAU,GAGLzM,QACJuC,IAAIgL,GACJ7M,KAAK4M,GACL1M,MAAM,SAAAnF,SACL4R,IACM5R,eA4EL6R,QAAkBE,6DACnBC,EAAc1C,KAEhByC,GACFH,IAEFb,EAAgB1Q,QAAQ,SAAA8D,UAAWA,EAAQK,QAAQwN,aAG3CC,EAAqBjB,EAAU,2BAA4B,wBACjEhO,OAAOqE,cAAc,IAAItI,EAAYkT,EAAoBf,MACzDlO,OAAOqE,cAAc,IAAItI,EAAY,2BAA4BmS,MACjE,MAAOlR,GAKPO,WAAW,iBACHP,OASV4Q,IAAoB,EAEhBC,GAAyBzQ,OAAS,EAAG,KAIjC8R,EAAsBrB,GAC5BA,GAA2B,GAC3BjF,GAAQsG,UAGHF,WASAJ,IACPb,EAAgB1Q,QAAQ,SAAA8R,GACtB7G,GAA2B6G,EAAe5G,kBAG5CD,GAA2BC,YAGpB2F,QACDhQ,EAAS,CAACjC,OAAQ,WAEpBsM,GAAkBA,EAAe,KACnCrK,EAAOjC,OAAOmT,cAAgB7G,EAAe,IAGxCrK,GC1LX,IAAI+P,IAAU,EAEP,SAASoB,KACdpB,IAAU,EACVrF,KASFrL,WAAW,WACJ0Q,IACHxP,QAAQC,sDAJc,0NAAA,YCPX,CACb4Q,cHWK,2BACMjD,KGXXzD,QAAAA,GACA7J,WAAAA,EACAwH,cAAAA,GACAnD,mBAAAA,EACAmM,sBH+FK,SAA+B1Q,OAC/BwN,GAAK/J,KAAK,SAAArF,UAAOA,EAAIqB,OAASO,UAC3BpB,+CAAwCoB,+DAGzCwO,GAAkBxO,GACtBoD,KAAK,eACEuN,EAAWnD,GAAKoD,UAAU,SAAAxS,UAAOA,EAAIqB,OAASO,IACpDwN,GAAKqD,OAAOF,EAAU,OI5FzBxP,QAAUA,OAAO2P,0BAClB3P,OAAO2P,wBAAwBC,eAAiBjJ"}