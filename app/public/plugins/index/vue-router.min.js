/*!
  * vue-router v3.0.7
  * (c) 2019 Evan You
  * @license MIT
  */
var t,e;t=this,e=function(){"use strict";function t(t){return Object.prototype.toString.call(t).indexOf("Error")>-1}function e(t,e){for(var r in e)t[r]=e[r];return t}var r={name:"RouterView",functional:!0,props:{name:{type:String,default:"default"}},render:function(t,r){var n=r.props,o=r.children,i=r.parent,a=r.data;a.routerView=!0;for(var u=i.$createElement,c=n.name,s=i.$route,p=i._routerViewCache||(i._routerViewCache={}),f=0,h=!1;i&&i._routerRoot!==i;){var l=i.$vnode&&i.$vnode.data;l&&(l.routerView&&f++,l.keepAlive&&i._inactive&&(h=!0)),i=i.$parent}if(a.routerViewDepth=f,h)return u(p[c],a,o);var d=s.matched[f];if(!d)return p[c]=null,u();var v=p[c]=d.components[c];a.registerRouteInstance=function(t,e){var r=d.instances[c];(e&&r!==t||!e&&r===t)&&(d.instances[c]=e)},(a.hook||(a.hook={})).prepatch=function(t,e){d.instances[c]=e.componentInstance},a.hook.init=function(t){t.data.keepAlive&&t.componentInstance&&t.componentInstance!==d.instances[c]&&(d.instances[c]=t.componentInstance)};var y=a.props=function(t,e){switch(typeof e){case"undefined":return;case"object":return e;case"function":return e(t);case"boolean":return e?t.params:void 0}}(s,d.props&&d.props[c]);if(y){y=a.props=e({},y);var m=a.attrs=a.attrs||{};for(var g in y)v.props&&g in v.props||(m[g]=y[g],delete y[g])}return u(v,a,o)}},n=/[!'()*]/g,o=function(t){return"%"+t.charCodeAt(0).toString(16)},i=/%2C/g,a=function(t){return encodeURIComponent(t).replace(n,o).replace(i,",")},u=decodeURIComponent;function c(t){var e={};return(t=t.trim().replace(/^(\?|#|&)/,""))?(t.split("&").forEach(function(t){var r=t.replace(/\+/g," ").split("="),n=u(r.shift()),o=r.length>0?u(r.join("=")):null;void 0===e[n]?e[n]=o:Array.isArray(e[n])?e[n].push(o):e[n]=[e[n],o]}),e):e}function s(t){var e=t?Object.keys(t).map(function(e){var r=t[e];if(void 0===r)return"";if(null===r)return a(e);if(Array.isArray(r)){var n=[];return r.forEach(function(t){void 0!==t&&(null===t?n.push(a(e)):n.push(a(e)+"="+a(t)))}),n.join("&")}return a(e)+"="+a(r)}).filter(function(t){return t.length>0}).join("&"):null;return e?"?"+e:""}var p=/\/?$/;function f(t,e,r,n){var o=n&&n.options.stringifyQuery,i=e.query||{};try{i=h(i)}catch(t){}var a={name:e.name||t&&t.name,meta:t&&t.meta||{},path:e.path||"/",hash:e.hash||"",query:i,params:e.params||{},fullPath:v(e,o),matched:t?d(t):[]};return r&&(a.redirectedFrom=v(r,o)),Object.freeze(a)}function h(t){if(Array.isArray(t))return t.map(h);if(t&&"object"==typeof t){var e={};for(var r in t)e[r]=h(t[r]);return e}return t}var l=f(null,{path:"/"});function d(t){for(var e=[];t;)e.unshift(t),t=t.parent;return e}function v(t,e){var r=t.path,n=t.query;void 0===n&&(n={});var o=t.hash;return void 0===o&&(o=""),(r||"/")+(e||s)(n)+o}function y(t,e){return e===l?t===e:!!e&&(t.path&&e.path?t.path.replace(p,"")===e.path.replace(p,"")&&t.hash===e.hash&&m(t.query,e.query):!(!t.name||!e.name)&&(t.name===e.name&&t.hash===e.hash&&m(t.query,e.query)&&m(t.params,e.params)))}function m(t,e){if(void 0===t&&(t={}),void 0===e&&(e={}),!t||!e)return t===e;var r=Object.keys(t),n=Object.keys(e);return r.length===n.length&&r.every(function(r){var n=t[r],o=e[r];return"object"==typeof n&&"object"==typeof o?m(n,o):String(n)===String(o)})}var g,b=[String,Object],w=[String,Array],x={name:"RouterLink",props:{to:{type:b,required:!0},tag:{type:String,default:"a"},exact:Boolean,append:Boolean,replace:Boolean,activeClass:String,exactActiveClass:String,event:{type:w,default:"click"}},render:function(t){var r=this,n=this.$router,o=this.$route,i=n.resolve(this.to,o,this.append),a=i.location,u=i.route,c=i.href,s={},h=n.options.linkActiveClass,l=n.options.linkExactActiveClass,d=null==h?"router-link-active":h,v=null==l?"router-link-exact-active":l,m=null==this.activeClass?d:this.activeClass,g=null==this.exactActiveClass?v:this.exactActiveClass,b=a.path?f(null,a,null,n):u;s[g]=y(o,b),s[m]=this.exact?s[g]:function(t,e){return 0===t.path.replace(p,"/").indexOf(e.path.replace(p,"/"))&&(!e.hash||t.hash===e.hash)&&function(t,e){for(var r in e)if(!(r in t))return!1;return!0}(t.query,e.query)}(o,b);var w=function(t){k(t)&&(r.replace?n.replace(a):n.push(a))},x={click:k};Array.isArray(this.event)?this.event.forEach(function(t){x[t]=w}):x[this.event]=w;var R={class:s};if("a"===this.tag)R.on=x,R.attrs={href:c};else{var E=function t(e){if(e)for(var r,n=0;n<e.length;n++){if("a"===(r=e[n]).tag)return r;if(r.children&&(r=t(r.children)))return r}}(this.$slots.default);if(E)E.isStatic=!1,(E.data=e({},E.data)).on=x,(E.data.attrs=e({},E.data.attrs)).href=c;else R.on=x}return t(this.tag,R,this.$slots.default)}};function k(t){if(!(t.metaKey||t.altKey||t.ctrlKey||t.shiftKey||t.defaultPrevented||void 0!==t.button&&0!==t.button)){if(t.currentTarget&&t.currentTarget.getAttribute){var e=t.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(e))return}return t.preventDefault&&t.preventDefault(),!0}}var R="undefined"!=typeof window;function E(t,e,r){var n=t.charAt(0);if("/"===n)return t;if("?"===n||"#"===n)return e+t;var o=e.split("/");r&&o[o.length-1]||o.pop();for(var i=t.replace(/^\//,"").split("/"),a=0;a<i.length;a++){var u=i[a];".."===u?o.pop():"."!==u&&o.push(u)}return""!==o[0]&&o.unshift(""),o.join("/")}function O(t){return t.replace(/\/\//g,"/")}var A=Array.isArray||function(t){return"[object Array]"==Object.prototype.toString.call(t)},C=H,j=L,_=function(t,e){return U(L(t,e))},T=U,S=B,$=new RegExp(["(\\\\.)","([\\/.])?(?:(?:\\:(\\w+)(?:\\(((?:\\\\.|[^\\\\()])+)\\))?|\\(((?:\\\\.|[^\\\\()])+)\\))([+*?])?|(\\*))"].join("|"),"g");function L(t,e){for(var r,n=[],o=0,i=0,a="",u=e&&e.delimiter||"/";null!=(r=$.exec(t));){var c=r[0],s=r[1],p=r.index;if(a+=t.slice(i,p),i=p+c.length,s)a+=s[1];else{var f=t[i],h=r[2],l=r[3],d=r[4],v=r[5],y=r[6],m=r[7];a&&(n.push(a),a="");var g=null!=h&&null!=f&&f!==h,b="+"===y||"*"===y,w="?"===y||"*"===y,x=r[2]||u,k=d||v;n.push({name:l||o++,prefix:h||"",delimiter:x,optional:w,repeat:b,partial:g,asterisk:!!m,pattern:k?I(k):m?".*":"[^"+P(x)+"]+?"})}}return i<t.length&&(a+=t.substr(i)),a&&n.push(a),n}function q(t){return encodeURI(t).replace(/[\/?#]/g,function(t){return"%"+t.charCodeAt(0).toString(16).toUpperCase()})}function U(t){for(var e=new Array(t.length),r=0;r<t.length;r++)"object"==typeof t[r]&&(e[r]=new RegExp("^(?:"+t[r].pattern+")$"));return function(r,n){for(var o="",i=r||{},a=(n||{}).pretty?q:encodeURIComponent,u=0;u<t.length;u++){var c=t[u];if("string"!=typeof c){var s,p=i[c.name];if(null==p){if(c.optional){c.partial&&(o+=c.prefix);continue}throw new TypeError('Expected "'+c.name+'" to be defined')}if(A(p)){if(!c.repeat)throw new TypeError('Expected "'+c.name+'" to not repeat, but received `'+JSON.stringify(p)+"`");if(0===p.length){if(c.optional)continue;throw new TypeError('Expected "'+c.name+'" to not be empty')}for(var f=0;f<p.length;f++){if(s=a(p[f]),!e[u].test(s))throw new TypeError('Expected all "'+c.name+'" to match "'+c.pattern+'", but received `'+JSON.stringify(s)+"`");o+=(0===f?c.prefix:c.delimiter)+s}}else{if(s=c.asterisk?encodeURI(p).replace(/[?#]/g,function(t){return"%"+t.charCodeAt(0).toString(16).toUpperCase()}):a(p),!e[u].test(s))throw new TypeError('Expected "'+c.name+'" to match "'+c.pattern+'", but received "'+s+'"');o+=c.prefix+s}}else o+=c}return o}}function P(t){return t.replace(/([.+*?=^!:${}()[\]|\/\\])/g,"\\$1")}function I(t){return t.replace(/([=!:$\/()])/g,"\\$1")}function M(t,e){return t.keys=e,t}function V(t){return t.sensitive?"":"i"}function B(t,e,r){A(e)||(r=e||r,e=[]);for(var n=(r=r||{}).strict,o=!1!==r.end,i="",a=0;a<t.length;a++){var u=t[a];if("string"==typeof u)i+=P(u);else{var c=P(u.prefix),s="(?:"+u.pattern+")";e.push(u),u.repeat&&(s+="(?:"+c+s+")*"),i+=s=u.optional?u.partial?c+"("+s+")?":"(?:"+c+"("+s+"))?":c+"("+s+")"}}var p=P(r.delimiter||"/"),f=i.slice(-p.length)===p;return n||(i=(f?i.slice(0,-p.length):i)+"(?:"+p+"(?=$))?"),i+=o?"$":n&&f?"":"(?="+p+"|$)",M(new RegExp("^"+i,V(r)),e)}function H(t,e,r){return A(e)||(r=e||r,e=[]),r=r||{},t instanceof RegExp?function(t,e){var r=t.source.match(/\((?!\?)/g);if(r)for(var n=0;n<r.length;n++)e.push({name:n,prefix:null,delimiter:null,optional:!1,repeat:!1,partial:!1,asterisk:!1,pattern:null});return M(t,e)}(t,e):A(t)?function(t,e,r){for(var n=[],o=0;o<t.length;o++)n.push(H(t[o],e,r).source);return M(new RegExp("(?:"+n.join("|")+")",V(r)),e)}(t,e,r):function(t,e,r){return B(L(t,r),e,r)}(t,e,r)}C.parse=j,C.compile=_,C.tokensToFunction=T,C.tokensToRegExp=S;var z=Object.create(null);function D(t,e,r){e=e||{};try{var n=z[t]||(z[t]=C.compile(t));return e.pathMatch&&(e[0]=e.pathMatch),n(e,{pretty:!0})}catch(t){return""}finally{delete e[0]}}function F(t,e,r,n){var o=e||[],i=r||Object.create(null),a=n||Object.create(null);t.forEach(function(t){!function t(e,r,n,o,i,a){var u=o.path;var c=o.name;var s=o.pathToRegexpOptions||{};var p=function(t,e,r){r||(t=t.replace(/\/$/,""));if("/"===t[0])return t;if(null==e)return t;return O(e.path+"/"+t)}(u,i,s.strict);"boolean"==typeof o.caseSensitive&&(s.sensitive=o.caseSensitive);var f={path:p,regex:K(p,s),components:o.components||{default:o.component},instances:{},name:c,parent:i,matchAs:a,redirect:o.redirect,beforeEnter:o.beforeEnter,meta:o.meta||{},props:null==o.props?{}:o.components?o.props:{default:o.props}};o.children&&o.children.forEach(function(o){var i=a?O(a+"/"+o.path):void 0;t(e,r,n,o,f,i)});if(void 0!==o.alias){var h=Array.isArray(o.alias)?o.alias:[o.alias];h.forEach(function(a){var u={path:a,children:o.children};t(e,r,n,u,i,f.path||"/")})}r[f.path]||(e.push(f.path),r[f.path]=f);c&&(n[c]||(n[c]=f))}(o,i,a,t)});for(var u=0,c=o.length;u<c;u++)"*"===o[u]&&(o.push(o.splice(u,1)[0]),c--,u--);return{pathList:o,pathMap:i,nameMap:a}}function K(t,e){return C(t,[],e)}function J(t,r,n,o){var i="string"==typeof t?{path:t}:t;if(i._normalized)return i;if(i.name)return e({},t);if(!i.path&&i.params&&r){(i=e({},i))._normalized=!0;var a=e(e({},r.params),i.params);if(r.name)i.name=r.name,i.params=a;else if(r.matched.length){var u=r.matched[r.matched.length-1].path;i.path=D(u,a,r.path)}return i}var s=function(t){var e="",r="",n=t.indexOf("#");n>=0&&(e=t.slice(n),t=t.slice(0,n));var o=t.indexOf("?");return o>=0&&(r=t.slice(o+1),t=t.slice(0,o)),{path:t,query:r,hash:e}}(i.path||""),p=r&&r.path||"/",f=s.path?E(s.path,p,n||i.append):p,h=function(t,e,r){void 0===e&&(e={});var n,o=r||c;try{n=o(t||"")}catch(t){n={}}for(var i in e)n[i]=e[i];return n}(s.query,i.query,o&&o.options.parseQuery),l=i.hash||s.hash;return l&&"#"!==l.charAt(0)&&(l="#"+l),{_normalized:!0,path:f,query:h,hash:l}}function N(t,e){var r=F(t),n=r.pathList,o=r.pathMap,i=r.nameMap;function a(t,r,a){var u=J(t,r,!1,e),s=u.name;if(s){var p=i[s];if(!p)return c(null,u);var f=p.regex.keys.filter(function(t){return!t.optional}).map(function(t){return t.name});if("object"!=typeof u.params&&(u.params={}),r&&"object"==typeof r.params)for(var h in r.params)!(h in u.params)&&f.indexOf(h)>-1&&(u.params[h]=r.params[h]);return u.path=D(p.path,u.params),c(p,u,a)}if(u.path){u.params={};for(var l=0;l<n.length;l++){var d=n[l],v=o[d];if(Q(v.regex,u.path,u.params))return c(v,u,a)}}return c(null,u)}function u(t,r){var n=t.redirect,o="function"==typeof n?n(f(t,r,null,e)):n;if("string"==typeof o&&(o={path:o}),!o||"object"!=typeof o)return c(null,r);var u=o,s=u.name,p=u.path,h=r.query,l=r.hash,d=r.params;if(h=u.hasOwnProperty("query")?u.query:h,l=u.hasOwnProperty("hash")?u.hash:l,d=u.hasOwnProperty("params")?u.params:d,s){i[s];return a({_normalized:!0,name:s,query:h,hash:l,params:d},void 0,r)}if(p){var v=function(t,e){return E(t,e.parent?e.parent.path:"/",!0)}(p,t);return a({_normalized:!0,path:D(v,d),query:h,hash:l},void 0,r)}return c(null,r)}function c(t,r,n){return t&&t.redirect?u(t,n||r):t&&t.matchAs?function(t,e,r){var n=a({_normalized:!0,path:D(r,e.params)});if(n){var o=n.matched,i=o[o.length-1];return e.params=n.params,c(i,e)}return c(null,e)}(0,r,t.matchAs):f(t,r,n,e)}return{match:a,addRoutes:function(t){F(t,n,o,i)}}}function Q(t,e,r){var n=e.match(t);if(!n)return!1;if(!r)return!0;for(var o=1,i=n.length;o<i;++o){var a=t.keys[o-1],u="string"==typeof n[o]?decodeURIComponent(n[o]):n[o];a&&(r[a.name||"pathMatch"]=u)}return!0}var X=Object.create(null);function Y(){var t=window.location.protocol+"//"+window.location.host,e=window.location.href.replace(t,"");window.history.replaceState({key:ct()},"",e),window.addEventListener("popstate",function(t){var e;G(),t.state&&t.state.key&&(e=t.state.key,at=e)})}function W(t,e,r,n){if(t.app){var o=t.options.scrollBehavior;o&&t.app.$nextTick(function(){var i=function(){var t=ct();if(t)return X[t]}(),a=o.call(t,e,r,n?i:null);a&&("function"==typeof a.then?a.then(function(t){rt(t,i)}).catch(function(t){}):rt(a,i))})}}function G(){var t=ct();t&&(X[t]={x:window.pageXOffset,y:window.pageYOffset})}function Z(t){return et(t.x)||et(t.y)}function tt(t){return{x:et(t.x)?t.x:window.pageXOffset,y:et(t.y)?t.y:window.pageYOffset}}function et(t){return"number"==typeof t}function rt(t,e){var r,n="object"==typeof t;if(n&&"string"==typeof t.selector){var o=document.querySelector(t.selector);if(o){var i=t.offset&&"object"==typeof t.offset?t.offset:{};e=function(t,e){var r=document.documentElement.getBoundingClientRect(),n=t.getBoundingClientRect();return{x:n.left-r.left-e.x,y:n.top-r.top-e.y}}(o,i={x:et((r=i).x)?r.x:0,y:et(r.y)?r.y:0})}else Z(t)&&(e=tt(t))}else n&&Z(t)&&(e=tt(t));e&&window.scrollTo(e.x,e.y)}var nt,ot=R&&((-1===(nt=window.navigator.userAgent).indexOf("Android 2.")&&-1===nt.indexOf("Android 4.0")||-1===nt.indexOf("Mobile Safari")||-1!==nt.indexOf("Chrome")||-1!==nt.indexOf("Windows Phone"))&&window.history&&"pushState"in window.history),it=R&&window.performance&&window.performance.now?window.performance:Date,at=ut();function ut(){return it.now().toFixed(3)}function ct(){return at}function st(t,e){G();var r=window.history;try{e?r.replaceState({key:at},"",t):(at=ut(),r.pushState({key:at},"",t))}catch(r){window.location[e?"replace":"assign"](t)}}function pt(t){st(t,!0)}function ft(t,e,r){var n=function(o){o>=t.length?r():t[o]?e(t[o],function(){n(o+1)}):n(o+1)};n(0)}function ht(e){return function(r,n,o){var i=!1,a=0,u=null;lt(e,function(e,r,n,c){if("function"==typeof e&&void 0===e.cid){i=!0,a++;var s,p=yt(function(t){var r;((r=t).__esModule||vt&&"Module"===r[Symbol.toStringTag])&&(t=t.default),e.resolved="function"==typeof t?t:g.extend(t),n.components[c]=t,--a<=0&&o()}),f=yt(function(e){var r="Failed to resolve async component "+c+": "+e;u||(u=t(e)?e:new Error(r),o(u))});try{s=e(p,f)}catch(t){f(t)}if(s)if("function"==typeof s.then)s.then(p,f);else{var h=s.component;h&&"function"==typeof h.then&&h.then(p,f)}}}),i||o()}}function lt(t,e){return dt(t.map(function(t){return Object.keys(t.components).map(function(r){return e(t.components[r],t.instances[r],t,r)})}))}function dt(t){return Array.prototype.concat.apply([],t)}var vt="function"==typeof Symbol&&"symbol"==typeof Symbol.toStringTag;function yt(t){var e=!1;return function(){for(var r=[],n=arguments.length;n--;)r[n]=arguments[n];if(!e)return e=!0,t.apply(this,r)}}var mt=function(t,e){this.router=t,this.base=function(t){if(!t)if(R){var e=document.querySelector("base");t=(t=e&&e.getAttribute("href")||"/").replace(/^https?:\/\/[^\/]+/,"")}else t="/";"/"!==t.charAt(0)&&(t="/"+t);return t.replace(/\/$/,"")}(e),this.current=l,this.pending=null,this.ready=!1,this.readyCbs=[],this.readyErrorCbs=[],this.errorCbs=[]};function gt(t,e,r,n){var o=lt(t,function(t,n,o,i){var a=function(t,e){"function"!=typeof t&&(t=g.extend(t));return t.options[e]}(t,e);if(a)return Array.isArray(a)?a.map(function(t){return r(t,n,o,i)}):r(a,n,o,i)});return dt(n?o.reverse():o)}function bt(t,e){if(e)return function(){return t.apply(e,arguments)}}mt.prototype.listen=function(t){this.cb=t},mt.prototype.onReady=function(t,e){this.ready?t():(this.readyCbs.push(t),e&&this.readyErrorCbs.push(e))},mt.prototype.onError=function(t){this.errorCbs.push(t)},mt.prototype.transitionTo=function(t,e,r){var n=this,o=this.router.match(t,this.current);this.confirmTransition(o,function(){n.updateRoute(o),e&&e(o),n.ensureURL(),n.ready||(n.ready=!0,n.readyCbs.forEach(function(t){t(o)}))},function(t){r&&r(t),t&&!n.ready&&(n.ready=!0,n.readyErrorCbs.forEach(function(e){e(t)}))})},mt.prototype.confirmTransition=function(e,r,n){var o=this,i=this.current,a=function(e){t(e)&&(o.errorCbs.length?o.errorCbs.forEach(function(t){t(e)}):console.error(e)),n&&n(e)};if(y(e,i)&&e.matched.length===i.matched.length)return this.ensureURL(),a();var u=function(t,e){var r,n=Math.max(t.length,e.length);for(r=0;r<n&&t[r]===e[r];r++);return{updated:e.slice(0,r),activated:e.slice(r),deactivated:t.slice(r)}}(this.current.matched,e.matched),c=u.updated,s=u.deactivated,p=u.activated,f=[].concat(function(t){return gt(t,"beforeRouteLeave",bt,!0)}(s),this.router.beforeHooks,function(t){return gt(t,"beforeRouteUpdate",bt)}(c),p.map(function(t){return t.beforeEnter}),ht(p));this.pending=e;var h=function(r,n){if(o.pending!==e)return a();try{r(e,i,function(e){!1===e||t(e)?(o.ensureURL(!0),a(e)):"string"==typeof e||"object"==typeof e&&("string"==typeof e.path||"string"==typeof e.name)?(a(),"object"==typeof e&&e.replace?o.replace(e):o.push(e)):n(e)})}catch(t){a(t)}};ft(f,h,function(){var t=[];ft(function(t,e,r){return gt(t,"beforeRouteEnter",function(t,n,o,i){return function(t,e,r,n,o){return function(i,a,u){return t(i,a,function(t){"function"==typeof t&&n.push(function(){!function t(e,r,n,o){r[n]&&!r[n]._isBeingDestroyed?e(r[n]):o()&&setTimeout(function(){t(e,r,n,o)},16)}(t,e.instances,r,o)}),u(t)})}}(t,o,i,e,r)})}(p,t,function(){return o.current===e}).concat(o.router.resolveHooks),h,function(){if(o.pending!==e)return a();o.pending=null,r(e),o.router.app&&o.router.app.$nextTick(function(){t.forEach(function(t){t()})})})})},mt.prototype.updateRoute=function(t){var e=this.current;this.current=t,this.cb&&this.cb(t),this.router.afterHooks.forEach(function(r){r&&r(t,e)})};var wt=function(t){function e(e,r){var n=this;t.call(this,e,r);var o=e.options.scrollBehavior,i=ot&&o;i&&Y();var a=xt(this.base);window.addEventListener("popstate",function(t){var r=n.current,o=xt(n.base);n.current===l&&o===a||n.transitionTo(o,function(t){i&&W(e,t,r,!0)})})}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype.go=function(t){window.history.go(t)},e.prototype.push=function(t,e,r){var n=this,o=this.current;this.transitionTo(t,function(t){st(O(n.base+t.fullPath)),W(n.router,t,o,!1),e&&e(t)},r)},e.prototype.replace=function(t,e,r){var n=this,o=this.current;this.transitionTo(t,function(t){pt(O(n.base+t.fullPath)),W(n.router,t,o,!1),e&&e(t)},r)},e.prototype.ensureURL=function(t){if(xt(this.base)!==this.current.fullPath){var e=O(this.base+this.current.fullPath);t?st(e):pt(e)}},e.prototype.getCurrentLocation=function(){return xt(this.base)},e}(mt);function xt(t){var e=decodeURI(window.location.pathname);return t&&0===e.indexOf(t)&&(e=e.slice(t.length)),(e||"/")+window.location.search+window.location.hash}var kt=function(t){function e(e,r,n){t.call(this,e,r),n&&function(t){var e=xt(t);if(!/^\/#/.test(e))return window.location.replace(O(t+"/#"+e)),!0}(this.base)||Rt()}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype.setupListeners=function(){var t=this,e=this.router.options.scrollBehavior,r=ot&&e;r&&Y(),window.addEventListener(ot?"popstate":"hashchange",function(){var e=t.current;Rt()&&t.transitionTo(Et(),function(n){r&&W(t.router,n,e,!0),ot||Ct(n.fullPath)})})},e.prototype.push=function(t,e,r){var n=this,o=this.current;this.transitionTo(t,function(t){At(t.fullPath),W(n.router,t,o,!1),e&&e(t)},r)},e.prototype.replace=function(t,e,r){var n=this,o=this.current;this.transitionTo(t,function(t){Ct(t.fullPath),W(n.router,t,o,!1),e&&e(t)},r)},e.prototype.go=function(t){window.history.go(t)},e.prototype.ensureURL=function(t){var e=this.current.fullPath;Et()!==e&&(t?At(e):Ct(e))},e.prototype.getCurrentLocation=function(){return Et()},e}(mt);function Rt(){var t=Et();return"/"===t.charAt(0)||(Ct("/"+t),!1)}function Et(){var t=window.location.href,e=t.indexOf("#");if(e<0)return"";var r=(t=t.slice(e+1)).indexOf("?");if(r<0){var n=t.indexOf("#");t=n>-1?decodeURI(t.slice(0,n))+t.slice(n):decodeURI(t)}else r>-1&&(t=decodeURI(t.slice(0,r))+t.slice(r));return t}function Ot(t){var e=window.location.href,r=e.indexOf("#");return(r>=0?e.slice(0,r):e)+"#"+t}function At(t){ot?st(Ot(t)):window.location.hash=t}function Ct(t){ot?pt(Ot(t)):window.location.replace(Ot(t))}var jt=function(t){function e(e,r){t.call(this,e,r),this.stack=[],this.index=-1}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype.push=function(t,e,r){var n=this;this.transitionTo(t,function(t){n.stack=n.stack.slice(0,n.index+1).concat(t),n.index++,e&&e(t)},r)},e.prototype.replace=function(t,e,r){var n=this;this.transitionTo(t,function(t){n.stack=n.stack.slice(0,n.index).concat(t),e&&e(t)},r)},e.prototype.go=function(t){var e=this,r=this.index+t;if(!(r<0||r>=this.stack.length)){var n=this.stack[r];this.confirmTransition(n,function(){e.index=r,e.updateRoute(n)})}},e.prototype.getCurrentLocation=function(){var t=this.stack[this.stack.length-1];return t?t.fullPath:"/"},e.prototype.ensureURL=function(){},e}(mt),_t=function(t){void 0===t&&(t={}),this.app=null,this.apps=[],this.options=t,this.beforeHooks=[],this.resolveHooks=[],this.afterHooks=[],this.matcher=N(t.routes||[],this);var e=t.mode||"hash";switch(this.fallback="history"===e&&!ot&&!1!==t.fallback,this.fallback&&(e="hash"),R||(e="abstract"),this.mode=e,e){case"history":this.history=new wt(this,t.base);break;case"hash":this.history=new kt(this,t.base,this.fallback);break;case"abstract":this.history=new jt(this,t.base)}},Tt={currentRoute:{configurable:!0}};function St(t,e){return t.push(e),function(){var r=t.indexOf(e);r>-1&&t.splice(r,1)}}return _t.prototype.match=function(t,e,r){return this.matcher.match(t,e,r)},Tt.currentRoute.get=function(){return this.history&&this.history.current},_t.prototype.init=function(t){var e=this;if(this.apps.push(t),t.$once("hook:destroyed",function(){var r=e.apps.indexOf(t);r>-1&&e.apps.splice(r,1),e.app===t&&(e.app=e.apps[0]||null)}),!this.app){this.app=t;var r=this.history;if(r instanceof wt)r.transitionTo(r.getCurrentLocation());else if(r instanceof kt){var n=function(){r.setupListeners()};r.transitionTo(r.getCurrentLocation(),n,n)}r.listen(function(t){e.apps.forEach(function(e){e._route=t})})}},_t.prototype.beforeEach=function(t){return St(this.beforeHooks,t)},_t.prototype.beforeResolve=function(t){return St(this.resolveHooks,t)},_t.prototype.afterEach=function(t){return St(this.afterHooks,t)},_t.prototype.onReady=function(t,e){this.history.onReady(t,e)},_t.prototype.onError=function(t){this.history.onError(t)},_t.prototype.push=function(t,e,r){this.history.push(t,e,r)},_t.prototype.replace=function(t,e,r){this.history.replace(t,e,r)},_t.prototype.go=function(t){this.history.go(t)},_t.prototype.back=function(){this.go(-1)},_t.prototype.forward=function(){this.go(1)},_t.prototype.getMatchedComponents=function(t){var e=t?t.matched?t:this.resolve(t).route:this.currentRoute;return e?[].concat.apply([],e.matched.map(function(t){return Object.keys(t.components).map(function(e){return t.components[e]})})):[]},_t.prototype.resolve=function(t,e,r){var n=J(t,e=e||this.history.current,r,this),o=this.match(n,e),i=o.redirectedFrom||o.fullPath;return{location:n,route:o,href:function(t,e,r){var n="hash"===r?"#"+e:e;return t?O(t+"/"+n):n}(this.history.base,i,this.mode),normalizedTo:n,resolved:o}},_t.prototype.addRoutes=function(t){this.matcher.addRoutes(t),this.history.current!==l&&this.history.transitionTo(this.history.getCurrentLocation())},Object.defineProperties(_t.prototype,Tt),_t.install=function t(e){if(!t.installed||g!==e){t.installed=!0,g=e;var n=function(t){return void 0!==t},o=function(t,e){var r=t.$options._parentVnode;n(r)&&n(r=r.data)&&n(r=r.registerRouteInstance)&&r(t,e)};e.mixin({beforeCreate:function(){n(this.$options.router)?(this._routerRoot=this,this._router=this.$options.router,this._router.init(this),e.util.defineReactive(this,"_route",this._router.history.current)):this._routerRoot=this.$parent&&this.$parent._routerRoot||this,o(this,this)},destroyed:function(){o(this)}}),Object.defineProperty(e.prototype,"$router",{get:function(){return this._routerRoot._router}}),Object.defineProperty(e.prototype,"$route",{get:function(){return this._routerRoot._route}}),e.component("RouterView",r),e.component("RouterLink",x);var i=e.config.optionMergeStrategies;i.beforeRouteEnter=i.beforeRouteLeave=i.beforeRouteUpdate=i.created}},_t.version="3.0.7",R&&window.Vue&&window.Vue.use(_t),_t},"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):t.VueRouter=e();