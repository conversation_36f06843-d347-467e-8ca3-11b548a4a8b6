!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):e.avalon=t()}(this,function(){"use strict";function e(e){this.size=0,this.limit=e,this.head=this.tail=void 0,this._keymap={}}function t(e,t){return nr[e]&&r.warn(e,"directive have defined! "),nr[e]=t,t.update||(t.update=function(){}),t.delay&&(tr[e]=1),t}function n(e){for(var t in tr)if("ms-"+t in e)return!0}function r(e){return new r.init(e)}function i(e,t){for(var n in t)e[n]=t[n];return e}function o(e,t){"string"==typeof e&&(e=e.match(ir)||[]);for(var n={},r=void 0!==t?t:1,i=0,o=e.length;i<o;i++)n[e[i]]=r;return n}function a(e){return r._quote(e)}function s(){lr&&r.config.debug&&Function.apply.call(console.log,console,arguments)}function c(){if(lr&&r.config.debug){var e=console.warn||console.log;Function.apply.call(e,console,arguments)}}function u(e,t){throw(t||Error)(e)}function l(){}function d(e){return null!==e&&"object"==typeof e}function f(e,t,n){n||(n=1),null==t&&(t=e||0,e=0);for(var r=-1,i=Math.max(0,Math.ceil((t-e)/n)),o=new Array(i);++r<i;)o[r]=e,e+=n;return o}function h(e){return e.replace(dr,"$1-$2").toLowerCase()}function p(e){return!e||e.indexOf("-")<0&&e.indexOf("_")<0?e:e.replace(fr,function(e){return e.charAt(1).toUpperCase()})}function v(e,t,n){return hr.call(e,t,n)}function m(e){return e=e||"avalon",String(Math.random()+Math.random()).replace(pr,e)}function g(e){return e.uuid||(e.uuid=m("e"))}function y(e){return e.uuid||(e.uuid="_"+ ++vr)}function b(e){return(e+"").replace(mr,"\\$&")}function w(){return Xn.createDocumentFragment()}function x(e){for(var t in e){var n=e[t];"function"==typeof x.plugins[t]?x.plugins[t](n):x[t]=n}return this}function _(e){return Xn.createComment(e)}function A(e){return/\[native code\]/.test(e)}function k(e,t,n){var r="for(var "+e+"i=0,n = this.length; i < n; i++){"+t.replace("_","((i in this) && fn.call(scope,this[i],i,this))")+"}"+n;return Function("fn,scope",r)}function C(e){return!!e&&(e==e.document&&e.document!=e)}function T(e){return Mr.test(sr.call(e))}function $(e,t){if(!e||"object"!==r.type(e)||e.nodeType||r.isWindow(e))return!1;try{if(e.constructor&&!cr.call(e,"constructor")&&!cr.call(e.constructor.prototype,"isPrototypeOf"))return!1;e.$vbthis}catch(e){return!1}if(Vr)for(t in e)return cr.call(e,t);for(t in e);return t===Dr||cr.call(e,t)}function N(e){return"[object Object]"===sr.call(e)&&Object.getPrototypeOf(e)===Object.prototype}function E(e,t){for(var n,i,o,a=t[0],s=1,c=t.length;s<c;s++){var u=t[s],l=Array.isArray(u);for(o in u)if(!l||u.hasOwnProperty(o)){try{var d=a[o],f=u[o]}catch(e){continue}a!==f&&(e&&f&&(r.isPlainObject(f)||(n=Array.isArray(f)))?(n?(n=!1,i=d&&Array.isArray(d)?d:[]):i=d&&r.isPlainObject(d)?d:{},a[o]=E(e,[i,f])):f!==Dr&&(a[o]=f))}}return a}function O(e){if(!e)return!1;var t=e.length;if(t===t>>>0){var n=sr.call(e);if(Fr.test(n))return!0;if("[object Object]"!==n)return!1;try{return!1!=={}.propertyIsEnumerable.call(e,"length")||Or.test(e.item||e.callee)}catch(t){return!e.window}}return!1}function M(e,t){var n=Math.pow(10,t);return""+(Math.round(e*n)/n).toFixed(t)}function S(e,t,n,r){e=(e+"").replace(/[^0-9+\-Ee.]/g,"");var i=isFinite(+e)?+e:0,o=isFinite(+t)?Math.abs(t):3,a="string"==typeof r?r:",",s=n||".",c="";return c=(o?M(i,o):""+Math.round(i)).split("."),c[0].length>3&&(c[0]=c[0].replace(/\B(?=(?:\d{3})+(?!\d))/g,a)),c.join(s)}function V(e){return e.replace(Lr,"").replace(Ir,function(e,t){var n=e.toLowerCase().match(/<(\w+)\s/);if(n){var r=Hr[n[1]];r&&(e=e.replace(r,function(e,t,n){var r=n.charAt(0);return t+"="+r+"javascript:void(0)"+r}))}return e.replace(Pr," ").replace(/\s+/g," ")})}function j(e){return parseInt(e,10)||0}function D(e,t,n){var r="";for(e<0&&(r="-",e=-e),e=""+e;e.length<t;)e="0"+e;return n&&(e=e.substr(e.length-t)),r+e}function F(e,t,n,r){return function(i){var o=i["get"+e]();return(n>0||o>-n)&&(o+=n),0===o&&-12===n&&(o=12),D(o,t,r)}}function L(e,t){return function(n,r){var i=n["get"+e]();return r[(t?"SHORT"+e:e).toUpperCase()][i]}}function P(e){var t=-1*e.getTimezoneOffset(),n=t>=0?"+":"";return n+=D(Math[t>0?"floor":"ceil"](t/60),2)+D(Math.abs(t%60),2)}function I(e,t){return e.getHours()<12?t.AMPMS[0]:t.AMPMS[1]}function H(e,t){var n,r,i=H.locate,o="",a=[];if(t=t||"mediumDate",t=i[t]||t,"string"==typeof e)if(/^\d+$/.test(e))e=j(e);else if(qr.test(e))e=+RegExp.$1;else{var s=e.trim(),c=[0,0,0,0,0,0,0],u=new Date(0);s=s.replace(/^(\d+)\D(\d+)\D(\d+)/,function(e,t,n,r){var i=4===r.length?[r,t,n]:[t,n,r];return c[0]=j(i[0]),c[1]=j(i[1])-1,c[2]=j(i[2]),""});var l=u.setFullYear,d=u.setHours;s=s.replace(/[T\s](\d+):(\d+):?(\d+)?\.?(\d)?/,function(e,t,n,r,i){return c[3]=j(t),c[4]=j(n),c[5]=j(r),i&&(c[6]=Math.round(1e3*parseFloat("0."+i))),""});var f=0,h=0;s=s.replace(/Z|([+-])(\d\d):?(\d\d)/,function(e,t,n,r){return l=u.setUTCFullYear,d=u.setUTCHours,t&&(f=j(t+n),h=j(t+r)),""}),c[3]-=f,c[4]-=h,l.apply(u,c.slice(0,3)),d.apply(u,c.slice(3)),e=u}for("number"==typeof e&&(e=new Date(e));t;)r=Br.exec(t),r?(a=a.concat(r.slice(1)),t=a.pop()):(a.push(t),t=null);return a.forEach(function(t){n=Rr[t],o+=n?n(e,i):t.replace(/(^'|'$)/g,"").replace(/''/g,"'")}),o}function R(e,t,n){var i=r.type(e);if("array"!==i&&"object"!==i)throw"orderBy只能处理对象或数组";var o="string"==typeof t?function(e){return e&&e[t]}:"function"==typeof t?t:function(e){return e},a={},s=[];B(e,Array.isArray(e),function(t){var n=e[t],r=o(n,t);r in a?a[r].push(t):a[r]=[t],s.push(r)}),s.sort(),n<0&&s.reverse();var c="array"===i,u=c?[]:{};return Y(u,s,function(t){var n=a[t].shift();c?u.push(e[n]):u[n]=e[n]})}function B(e,t,n){if(t)e.forEach(function(e,t){n(t)});else if("string"==typeof e.$track)e.$track.replace(/[^☥]+/g,function(e){n(e)});else for(var r in e)e.hasOwnProperty(r)&&n(r)}function q(e,t){var n=r.type(e);if("array"!==n&&"object"!==n)throw"filterBy只能处理对象或数组";var i=r.slice(arguments,2),o=r.type(t);if("function"===o)var a=t._orig||t;else{if("string"!==o&&"number"!==o)return e;if(""===t)return e;var s=new RegExp(r.escapeRegExp(t),"i");a=function(e){return s.test(e)}}var c="array"===n,u=c?[]:{};return B(e,c,function(t){var n=e[t];a.apply({key:t},[n,t].concat(i))&&(c?u.push(n):u[t]=n)}),u}function z(e,t,n){if(r.isObject(e)&&!Array.isArray(e)){var i=[];return Y(i,t,function(t){i.push(e.hasOwnProperty(t)?e[t]:n?n[t]:"")})}return e}function W(e,t,n){var i=r.type(e);if("array"!==i&&"object"!==i)throw"limitBy只能处理对象或数组";if("number"!=typeof t)return e;if(t!==t)return e;"object"===i&&(e=U(e,!1));var o=e.length;t=Math.floor(Math.min(o,t)),(n="number"==typeof n?n:0)<0&&(n=Math.max(0,o+n));for(var a=[],s=n;s<o&&a.length!==t;s++)a.push(e[s]);if("array"===i)return a;var c={};return Y(c,a,function(e){c[e.key]=e.value})}function Y(e,t,n){for(var r=0,i=t.length;r<i;r++)n(t[r]);return e}function U(e,t){var n=[],r=0;return B(e,t,function(t){n[r]={oldIndex:r,value:e[t],key:t},r++}),n}function J(e){return null==e?"":String(e).replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#39;")}function G(e,t){if(1===e.nodeType){var n=e.nodeName.toLowerCase();if("script"===n)e.text!==t.text&&(e.type="noexec",e.text=t.text,e.type=t.type||"");else if("object"===n){var i=t.childNodes;if(e.childNodes.length!==i.length){r.clearHTML(e);for(var o,a=0;o=i[a++];)e.appendChild(o.cloneNode(!0))}}else"input"===n&&Xr.test(t.nodeName)?(e.defaultChecked=e.checked=t.checked,e.value!==t.value&&(e.value=t.value)):"option"===n?e.defaultSelected=e.selected=t.defaultSelected:"input"!==n&&"textarea"!==n||(e.defaultValue=t.defaultValue)}}function X(e){return void 0!==e.getElementsByTagName?e.getElementsByTagName("*"):void 0!==e.querySelectorAll?e.querySelectorAll("*"):[]}function Z(e){for(var t=e.cloneNode(!0),n=X(t),r=X(e),i=0;i<r.length;i++)G(n[i],r[i]);return t}function K(e,t){try{for(;t=t.parentNode;)if(t===e)return!0}catch(e){}return!1}function Q(e){this.node=e}function ee(e){return"classList"in e||(e.classList=new Q(e)),e.classList}function te(e){var t=e.nodeName;return t.toLowerCase()===t&&!!e.scopeName&&""===e.outerText}function ne(e){if("string"==typeof e){if((e=e.trim())&&Kr.test(e.replace(ei,"@").replace(ti,"]").replace(Qr,"")))return new Function("return "+e)();throw TypeError("Invalid JSON: ["+e+"]")}return e}function re(e,t){for(var n in t)try{var i=t[n];if(0===n.indexOf("data-")||ni.test(e))e.setAttribute(n,i);else{var o=Zr[n]||n;if("boolean"==typeof e[o]&&("checked"===o&&(e.defaultChecked=!!i),e[o]=!!i),!1===i){e.removeAttribute(o);continue}if(r.msie<8&&/[A-Z]/.test(o)){e[o]=i+"";continue}!(r.modern||!te(e))||ie(e.nodeName,n)?("href"!==n&&"src"!==n||r.msie<8&&(i=String(i).replace(ri,"&")),e[o]=i+""):e.setAttribute(n,i)}}catch(e){r.log(String(e.message).trim(),n,i)}}function ie(e,t){var n=e+":"+t;return n in ii?ii[n]:ii[n]=t in Xn.createElement(e)}function oe(e,t){if(e.offsetWidth<=0){if(ci.test(wr["@:get"](e,"display"))){var n={node:e};for(var r in si)n[r]=e.style[r],e.style[r]=si[r];t.push(n)}var i=e.parentNode;i&&1===i.nodeType&&oe(i,t)}}function ae(e){return e.window||e.defaultView||e.parentWindow||!1}function se(e){var t=e.tagName.toLowerCase();return"input"===t?Xr.test(e.type)?"checked":e.type:t}function ce(e){if(e.hasAttribute&&e.hasAttribute("value"))return e.getAttribute("value");var t=e.getAttributeNode("value");return t&&t.specified?t.value:e.innerHTML.trim()}function ue(e){return o(e+",template,#document-fragment,#comment")}function le(e){return de(e)}function de(e){var t=e,n=_i.get(t);if(n)return r.mix(!0,[],n);var i=fe(e,!1);return _i.put(t,r.mix(!0,[],i)),i}function fe(e,t){t=void 0===t||!0===t;var n=he(e,t);return t?"string"==typeof n[0]?n[1]:n[0]:n}function he(e,t){function n(e){var t=i.last();t&&t.children?t.children.push(e):c.push(e)}var r=9990,i=[],o=e,a=e.length;i.last=function(){return i[i.length-1]};var s,c=[];do{if(0==--r)break;var u=ge(e);if(u){e=e.replace(u[0],"");var l=i.pop();if(!l)throw"是不是有属性值没有用引号括起";if("option"===l.nodeName?l.children=[{nodeName:"#text",nodeValue:be(l)}]:"table"===l.nodeName&&me(l.children),s=null,t&&1===c.length&&!i.length)return[o.slice(0,a-e.length),c[0]]}else{var u=ye(e);if(u){e=e.replace(u[0],"");var d=u[1];n(d);var f=!(!d.isVoidTag&&!yi[d.nodeName]);if(f||i.push(d),t&&f&&!i.length)return[o.slice(0,a-e.length),d];s=d}else{var h="";do{if(0!==e.indexOf("<"))break;h+=e.slice(0,1),e=e.slice(1)}while(e.length);var p=e.indexOf("<");if(-1===p)h=e,e="";else{var v=e.indexOf(x.openTag);if(-1!==v&&v<p){0!==v&&(h+=e.slice(0,v));var m=e.slice(v),g=ve(m);h+=g,e=m.slice(g.length)}else h+=e.slice(0,p),e=e.slice(p)}var y=pe(s,h,n);y&&(s=y)}}}while(e.length);return c}function pe(e,t,n){if(wi.test(t))return e&&"#text"===e.nodeName?(e.nodeValue+=t,e):(e={nodeName:"#text",nodeValue:t},n(e),e)}function ve(e){for(var t,n,r=x.closeTag,i=x.openTag,o=r.charAt(0),a=r.length,s="code",c=i.length,u=e.length;c<u;c++){var l=e.charAt(c);switch(s){case"code":if('"'===l||"'"===l)s="string",t=l;else if(l===o&&e.substr(c,a)===r)return e.slice(0,c+a);break;case"string":"\\"===l&&/"'/.test(e.charAt(c+1))&&(n=!n),l!==t||n||(s="code")}}throw"找不到界定符"+r}function me(e){for(var t=!1,n=0,r=e.length;n<r;n++){var i=e[n];Ai.test(i.nodeName)?t=!1:"tr"===i.nodeName?t?(e.splice(n,1),t.children.push(i),r--,n--):(t={nodeName:"tbody",props:{},children:[i]},e.splice(n,1,t)):t&&(e.splice(n,1),t.children.push(i),r--,n--)}}function ge(e){if(0===e.indexOf("</")){var t=e.match(/\<\/(\w+[^\s\/\>]*)>/);if(t){var n=t[1];return e=e.slice(3+n.length),[t[0],{nodeName:n}]}}return null}function ye(e){if(0===e.indexOf("<")){if(0===e.indexOf("\x3c!--")){var t=e.indexOf("--\x3e");-1===t&&thow("注释节点没有闭合 "+e.slice(0,100));var n={nodeName:"#comment",nodeValue:e.slice(4,t)};return[e.slice(0,t+3),n]}var r=e.match(ki);if(r){var i=r[0],o=r[1],n={nodeName:o,props:{},children:[]};e=e.replace(i,"");try{var a=we(e)}catch(e){}if(a&&(n.props=a[1],e=e.replace(a[0],""),i+=a[0]),">"===e.charAt(0)?(i+=">",e=e.slice(1),vi[n.nodeName]&&(n.isVoidTag=!0)):"/>"===e.slice(0,2)&&(i+="/>",e=e.slice(2),n.isVoidTag=!0),!n.isVoidTag&&yi[o]){var s="</"+o+">",c=e.indexOf(s),u=e.slice(0,c);i+=u+s,n.children.push({nodeName:"#text",nodeValue:u}),"textarea"===o&&(n.props.type=o,n.props.value=u)}return[i,n]}}}function be(e){var t="";return e.children.forEach(function(e){"#text"===e.nodeName?t+=e.nodeValue:e.children&&!bi[e.nodeName]&&(t+=be(e))}),t}function we(e){for(var t,n,r="AttrName",i="",o="",a={},s=0,c=e.length;s<c;s++){var u=e.charAt(s);switch(r){case"AttrName":if("/"===u&&">"===e.charAt(s+1)||">"===u)return i&&(a[i]=i),[e.slice(0,s),a];if(xi.test(u))i&&(r="AttrEqual");else if("="===u){if(!i)throw"必须指定属性名";r="AttrQuote"}else i+=u;break;case"AttrEqual":"="===u?r="AttrQuote":wi.test(u)&&(a[i]=i,i=u,r="AttrName");break;case"AttrQuote":'"'!==u&&"'"!==u||(t=u,r="AttrValue",n=!1);break;case"AttrValue":if("\\"===u&&/"'/.test(e.charAt(s+1))&&(n=!n),"\n"===u)break;u!==t?o+=u:u!==t||n||(a[i]=o,i=o="",r="AttrName")}}throw"必须关闭标签"}function xe(e,t){e.setAttribute("avalon-events",t)}function _e(e,t,n){var i=e.getAttribute("avalon-events");if(i&&(!0!==e.disabled||"click"!==t)){var o=[],a=Ei[t]||(Ei[t]=new RegExp("\\b"+t+"\\:([^,\\s]+)","g"));i.replace(a,function(e,t){return o.push(t),e}),o.length&&n.push({elem:e,uuids:o})}e=e.parentNode;var s=r.gestureEvents||{};e&&e.getAttribute&&($i[t]||s[t])&&_e(e,t,n)}function Ae(e){e=new Ce(e);var t=e.type,n=e.target,i=[];_e(n,t,i);for(var o,a,s,c=0;(s=i[c++])&&!e.cancelBubble;){e.currentTarget=s.elem;for(o=0;(a=s.uuids[o++])&&!e.stopImmediate;){var u=r.eventListeners[a];if(u){var l=Oi.test(a)?s.elem._ms_context_:0;if(l&&!1===l.$hashcode)return r.unbind(n,t,u);!1===u.call(l||n,e)&&(e.preventDefault(),e.stopPropagation())}}}}function ke(e){var t=Zn.getAttribute("delegate-events")||"";if(-1===t.indexOf(e)){var n=t.match(r.rword)||[];n.push(e),Zn.setAttribute("delegate-events",n.join(",")),r._nativeBind(Zn,e,Ae,!!Mi[e])}}function Ce(e){if(e.originalEvent)return e;for(var t in e)Si[t]||(this[t]=e[t]);this.target||(this.target=e.srcElement);this.target;this.fixEvent(),this.timeStamp=new Date-0,this.originalEvent=e}function Te(e){for(r.isReady=!0;e=Fi.shift();)e(r)}function $e(e,t,n){switch(t){case"style":case"script":case"noscript":case"template":case"xmp":e.children=[{nodeName:"#text",nodeValue:n}];break;case"textarea":var r=e.props;r.type=t,r.value=n,e.children=[{nodeName:"#text",nodeValue:n}];break;case"option":e.children=[{nodeName:"#text",nodeValue:Ne(n)}]}}function Ne(e){return String(e).replace(Pi,"").trim()}function Ee(e){return[Oe(e)]}function Oe(e){var t=e.nodeName.toLowerCase();switch(t){case"#text":case"#comment":return{nodeName:t,dom:e,nodeValue:e.nodeValue};default:var n=Me(e,e.attributes||[]),r={nodeName:t,dom:e,isVoidTag:!!vi[t],props:n};if("option"===t&&(n.selected=e.selected),Li[t]||"option"===t)$e(r,t,e.text||e.innerHTML),1===e.childNodes.length&&(r.children[0].dom=e.firstChild);else if(!r.isVoidTag){r.children=[];for(var i,o=0;i=e.childNodes[o++];){var a=Oe(i);/\S/.test(a.nodeValue)&&r.children.push(a)}}return r}}function Me(e,t){for(var n={},r=0,i=t.length;r<i;r++){var o=t[r];o.specified&&(n[o.name.toLowerCase()]=o.value)}if(Ii.test(e.nodeName)){n.type=e.type;var a=e.getAttributeNode("value");a&&/\S/.test(a.value)&&(n.value=a.value)}var s=e.style.cssText;return s&&(n.style=s),"select-one"===n.type&&(n.selectedIndex=e.selectedIndex),n}function Se(e){this.nodeName="#text",this.nodeValue=e}function Ve(e){this.nodeName="#comment",this.nodeValue=e}function je(e,t,n,r){this.nodeName=e,this.props=t,this.children=n,this.isVoidTag=r}function De(e,t,n){switch(t){case"style":e.setAttribute("type","text/css"),e.styleSheet.cssText=n;break;case"xmp":case"noscript":e.textContent=n}}function Fe(e){return!1!==e&&Object(e)!==e}function Le(e){return Xn.createElementNS("http://www.w3.org/2000/svg",e)}function Pe(e){Xn.styleSheets.length<31?Xn.createStyleSheet().addRule(".rvml","behavior:url(#default#VML)"):Xn.styleSheets[0].addRule(".rvml","behavior:url(#default#VML)");var t=e.split(":");1===t.length&&t.unshift("v");var n=t[1],r=t[0];return Xn.namespaces[r]||Xn.namespaces.add(r,"urn:schemas-microsoft-com:vml"),Xn.createElement("<"+r+":"+n+' class="rvml">')}function Ie(e,t,n,r){this.nodeName="#document-fragment",this.children=e,this.key=t,this.val=n,this.index=r,this.props={}}function He(){if(!(!0===r.isRunningActions||r.inTransaction>0)){r.isRunningActions=!0;for(var e,t=r.pendingActions.splice(0,r.pendingActions.length),n=0;e=t[n++];)e.update(),delete r.uniqActions[e.uuid];r.isRunningActions=!1}}function Re(e){for(var t,n=e.observers,r=0;t=n[r++];)t.schedule()}function Be(e){var t=r.trackingAction||null;null!==t&&(r.track("征收到",e.expr),t.mapIDs[e.uuid]=e)}function qe(e,t){if(e.observers){var n=r.trackingAction;n&&Wi.push(n),r.trackingAction=e,r.track("【action】",e.type,e.expr,"开始征收依赖项"),e.mapIDs={};var i,o=!0;try{i=t.call(e),o=!1}finally{if(o)r.warn("collectDeps fail",t+""),e.mapIDs={},r.trackingAction=n;else{r.trackingAction=Wi.pop();try{ze(e)}catch(e){r.warn(e)}}return i}}}function ze(e){var t=e.observers,n=[],i={},o=[];for(var a in e.mapIDs){var s=e.mapIDs[a];if(!s.isAction){if(!s.observers){delete e.mapIDs[a];continue}if(o.push(s.uuid),n.push(s),i[s.uuid]=1,s.lastAccessedBy===e.uuid)continue;s.lastAccessedBy=e.uuid,r.Array.ensure(s.observers,e)}}var o=o.sort().join(",");if(o!==e.ids){if(e.ids=o,e.isComputed){e.depsCount=n.length,e.deps=r.mix({},e.mapIDs),e.depsVersion={};for(var c in e.mapIDs){var u=e.mapIDs[c];e.depsVersion[u.uuid]=u.version}}else e.observers=n;for(var l,d=0;l=t[d++];)i[l.uuid]||r.Array.remove(l.observers,e)}}function We(e,t,n){n=n||[];var r="transaction "+(e.name||e.displayName||"noop");Ye(r);var i=e.apply(t,n);return Ue(r),i}function Ye(e){r.inTransaction+=1}function Ue(e){0==--r.inTransaction&&(r.isRunningActions=!1,He())}function Je(e){var t="??"+Yi++;return Ui.map[t]=e,t+" "}function Ge(e){return Ui.map[e]}function Xe(e){for(var t=Ze(e),n=0,r=t.length;n<r;n++)e=e.replace(t[n],Je);return e}function Ze(e,t,n){var r=!1,i=0,t=t||0;n=n||[];for(var o=e.length;t<o;t++){var a=e.charAt(t);r?a===r&&(n.push(e.slice(i,t+1)),r=!1):"'"===a?(r="'",i=t):'"'===a&&(r='"',i=t)}return!1!==r?Ze(e,i+1,n):n}function Ke(e){return e.replace(ro,Je).replace(ao,function(e){return Xi[e]?e:"__vmodel__."+e})}function Qe(e,t){var n=e+":"+t,i=so.get(n);if(i)return i.slice(0);Ui.map={};var o=e.replace(no,function(e,t){return t+Je(e.slice(t.length))});o=Xe(o),o=o.replace(Qi,Je).replace(Ki,"$1").replace(io,function(e,t,n){return t+Je(n)+":"}).replace(Zi,"$1__vmodel__.").replace(oo,function(e,t){return"|"+Je(t)}),o=Ke(o);var a=o.split(to),s=a.shift().replace(Ji,Ge).trim();return/\?\?\d/.test(s)&&(s=s.replace(Ji,Ge)),a.length?(a=a.map(function(e){var t="";return e=e.replace(eo,function(e,n){return/\S/.test(n)&&(t+=","+n),""}),"["+r.quote(e.trim())+t+"]"}),a="avalon.composeFilters("+a+")(__value__)",a=a.replace(Ji,Ge)):a="",so.put(n,[s,a])}function et(e){return co.test(e)&&(e+="($event)"),Qn<9&&(e=e.replace(uo,function(e,t,n){return"__vmodel__."+t+".call(__vmodel__"+(/\S/.test(n)?","+n:"")+")"})),e}function tt(e,t){var n,i=Qe(e,t);n=i[1]?i[1].replace(/__value__\)$/,i[0]+")"):i[0];try{return new Function("__vmodel__","return "+n+";")}catch(t){return r.log("parse getter: [",e,n,"]error"),r.noop}}function nt(e,t){var n=Qe(e,t),i="try{ "+n[0]+' = __value__}catch(e){avalon.log(e, "in on dir")}';try{return new Function("__vmodel__","__value__",i+";")}catch(t){return r.log("parse setter: ",e," error"),r.noop}}function rt(e,t,n){for(var r in t)1!==fo[r]&&(this[r]=t[r]);this.vm=e,this.observers=[],this.callback=n,this.uuid=++lo,this.ids="",this.mapIDs={},this.isAction=!0;var i=this.expr;"function"!=typeof this.getter&&(this.getter=tt(i,this.type)),"duplex"===this.type&&(this.setter=nt(i,this.type)),this.value=NaN,this.node||(this.value=this.get())}function it(e){if(e&&"object"==typeof e){if(e&&e.$events)return e.$model;if(Array.isArray(e)){for(var t=[],n=0,r=e.length;n<r;n++)t.push(it(e[n]));return t}var i={};for(var o in e)i[o]=it(e[o]);return i}return e}function ot(e,t,n){if(this.expr=e,t){var r=or.createProxy(t,this);r&&(t=r)}this.value=t,this.vm=n;try{n.$mutations[e]=this}catch(e){}this.uuid=++ho,this.updateVersion(),this.mapIDs={},this.observers=[]}function at(e){var t=e.toString();return t.substring(t.indexOf("{}")+1,t.lastIndexOf("}"))}function st(e){var t=function(){};return t.prototype=e,new t}function ct(e,t){if("function"==typeof t){(e.prototype=st(t.prototype)).constructor=e}}function ut(e,t){r.mix(this,e),r.mix(this,Wr),this.$hashcode=r.makeHashCode("$"),this.$id=this.$id||this.$hashcode,this.$events={__dep__:t||new ot(this.$id)},r.config.inProxyMode?(delete this.$mutations,this.$accessors={},this.$computed={},this.$track=""):this.$accessors={$model:go},void 0===t?(this.$watch=or.watchFactory(this.$events),this.$fire=or.fireFactory(this.$events)):(delete this.$watch,delete this.$fire)}function lt(e,t,n){return!(e in Wr)&&("$"===e.charAt(0)?!!n&&(mo[e]||(mo[e]=1,r.warn("ms-for中的变量"+e+"不再建议以$为前缀")),!0):null==t?(r.warn("定义vmodel时"+e+"的属性值不能为null undefine"),!0):!/error|date|function|regexp/.test(r.type(t))&&!(t&&t.nodeName&&t.nodeType))}function dt(e,t){if(e&&e.$events)return e;var n;return Array.isArray(e)?n=or.listFactory(e,!1,t):d(e)&&(n=or.modelFactory(e,t)),n}function ft(e,t,n){var r=null,i=n?vo:ot;return{get:function(){return r||(r=new i(e,t,this)),r.get()},set:function(n){r||(r=new i(e,t,this)),r.set(n)},enumerable:!0,configurable:!0}}function ht(e){var t=r.type(e);if("array"===t){for(var n=[],i=0;i<e.length;i++)n[i]=ht(e[i]);return n}if("object"===t&&"string"==typeof e.$track){var o={};return(e.$track.match(/[^☥]+/g)||[]).forEach(function(t){var n=e[t];o[t]=n&&n.$events?ht(n):n}),o}return e}function pt(e){for(var t in bo)or.hideProperty(e,t,bo[t])}function vt(e,t,n){t||(pt(e),er&&Object.defineProperty(e,"$model",or.modelAccessor),or.hideProperty(e,"$hashcode",r.makeHashCode("$")),or.hideProperty(e,"$events",{__dep__:n||new ot}));for(var i=e.$events&&e.$events.__dep__,o=0,a=e.length;o<a;o++){var s=e[o];d(s)&&(e[o]=or.createProxy(s,i))}return e}function mt(e,t,n){wo?Object.defineProperty(e,t,{value:n,writable:!0,enumerable:!1,configurable:!0}):xo[t]||(e[t]=n)}function gt(e){return function(t,n,i){var o=new rt(e.__proxy__,{deep:i,type:"user",expr:t},n);return e[t]?e[t].push(o):e[t]=[o],function(){o.dispose(),r.Array.remove(e[t],o),0===e[t].length&&delete e[t]}}}function yt(e){return function(t,n){var r=e[t];if(Array.isArray(r))for(var i,o=0;i=r[o++];)i.callback.call(i.vm,n,i.value,i.expr)}}function bt(e){return"☥"+e+"☥"}function wt(e,t,n,i){function o(t){return bt(e.$track).indexOf(bt(t))>-1}var a=e.$accessors;for(var s in Wr)r.msie<9&&void 0===t[s]||mt(e,s,t[s]);for(var c=0;c<n.length;c++){var u=n[c];if(!(u in a)){var l=t[u];if(i&&"function"==typeof l){e[u]=l.bind(e),e[u]._orig=l;continue}e[u]=l}}e.$track=n.join("☥"),r.msie<9&&(e.hasOwnProperty=o),e.$events.__proxy__=e}function xt(e){var t=[];for(var n in e)t.push(n);return t}function _t(e,t,n){if(0===n)return e===t;if(null===e&&null===t)return!0;if(void 0===e&&void 0===t)return!0;var r=Array.isArray(e);return r===Array.isArray(t)&&(r?At(e,t,n):"object"==typeof e&&"object"==typeof t?kt(e,t,n):e===t)}function At(e,t,n){if(e.length!==t.length)return!1;for(var r=e.length-1;r>=0;r--)try{if(!_t(e[r],t[r],n-1))return!1}catch(e){return!1}return!0}function kt(e,t,n){if(null===e||null===t)return!1;if(xt(e).length!==xt(t).length)return!1;for(var r in e){if(!(r in t))return!1;try{if(!_t(e[r],t[r],n-1))return!1}catch(e){return!1}}return!0}function Ct(){var e=Ro[0];e&&e()}function Tt(e,t,n){e[t]||(e[t]=n)}function $t(e,t,n){for(var r,i=[].concat(e[t]),o=0;r=i[o++];)"function"==typeof r&&r(n)}function Nt(e){var t=e.toLowerCase();return function(n){var i=this.dom,o=r(i),a=isFinite(n.stagger)?1e3*n.stagger:0;if(a&&n.staggerKey){var s=qo.get(n.staggerKey)||qo.put(n.staggerKey,{count:0,items:0});s.count++,s.items++}var c,u=s&&s.count||0,l=function(t){var o=!1!==t;0==--i.__ms_effect_&&(r.unbind(i,Po),r.unbind(i,Lo)),clearTimeout(c),$t(n,"on"+e+(o?"Done":"Abort"),i),s&&0==--s.items&&(s.count=0),n.queue&&(Ro.shift(),Ct())};$t(n,"onBefore"+e,i),n[t]?n[t](i,function(e){l(!1!==e)}):Vo&&(o.addClass(n[t+"Class"]),o.removeClass(Ot(n,t)),i.__ms_effect_?i.__ms_effect_++:(o.bind(Po,l),o.bind(Lo,l),i.__ms_effect_=1),setTimeout(function(){var e=NaN===r.root.offsetWidth;o.addClass(n[t+"ActiveClass"]),e=St(i),0===!e?l(!1):a||(c=setTimeout(function(){l(!1)},e+32))},17+a*u))}}function Et(e){if(!e.action)return e.action=e.hook.replace(/^on/,"").replace(/Done$/,"").toLowerCase()}function Ot(e,t){var t="leave"===t?"enter":"leave";return Array(t+"Class",t+"ActiveClass").map(function(t){return e[t]}).join(" ")}function Mt(e){var t=Yo.test(e)?1e3:1;return parseFloat(e)*t}function St(e){var t=rr.getComputedStyle(e,null),n=t[zo],r=t[Wo];return Mt(n)||Mt(r)}function Vt(e,t){var n=e.ownerDocument,i=e.nodeName,o="_"+i;if(!Vt[o]){var a=n.body.appendChild(n.createElement(i));t=r.css(a,"display"),n.body.removeChild(a),t===Uo&&(t="block"),Vt[o]=t}return Vt[o]}function jt(e,t){var n=e.innerRender=r.scan(e.fragment,e.vm);r.shadowCopy(t,n.root),delete t.nodeValue}function Dt(e){var t=typeof e;return e&&"object"===t?e.$hashcode:t+":"+e}function Ft(e,t){if(d(t)){var n=Array.isArray(t),i=[],o=[],a=0;return e.isArray=n,e.fragments?(e.preFragments=e.fragments,r.each(t,function(e,t){var r=n?Dt(t):e;o.push({key:r,val:t,index:a++}),i.push(r)}),e.fragments=o):(r.each(t,function(e,t){if(!(e in Wr)){var r=n?Dt(t):e;o.push(new Ie([],r,t,a++)),i.push(r)}}),e.fragments=o),i.join(";;")}return NaN}function Lt(e){var t=e.fragments.map(function(t,n){return Rt(t,e,n),qt(e.cache,t),t}),n=e.parentChildren,r=n.indexOf(e.begin);n.splice.apply(n,[r+1,0].concat(t))}function Pt(e){var t=e.cache,n={},r=[],i=e.preFragments;i.forEach(function(e){e._dispose=!0}),e.fragments.forEach(function(i,o){var a=Bt(t,i.key);a?(delete a._dispose,a.oldIndex=a.index,a.index=o,It(a.vm,e.keyName),a.vm[e.valName]=i.val,a.vm[e.keyName]=e.isArray?o:a.key,qt(n,a)):r.push(i)}),r.forEach(function(r){var o=zt(t,r.key);if(o){o.oldIndex=o.index,o.key=r.key;var a=o.val=r.val,s=o.index=r.index;o.vm[e.valName]=a,o.vm[e.keyName]=e.isArray?s:o.key,delete o._dispose}else r=new Ie([],r.key,r.val,r.index),o=Rt(r,e,r.index),i.push(o);qt(n,o)}),e.fragments=i,i.sort(function(e,t){return e.index-t.index}),e.cache=n}function It(e,t,n){r.config.inProxyMode?e.$accessors[t].value=NaN:e.$accessors[t].set(NaN)}function Ht(e){for(var t,n=e.begin.dom,r=n.parentNode,i=e.fragments,o=e.end.dom,a=0;t=i[a];a++)if(t._dispose)i.splice(a,1),a--,t.dispose();else{if(t.oldIndex!==t.index){var s=t.toFragment(),c=null===n.nextSibling;r.insertBefore(s,n.nextSibling),c&&!r.contains(o)&&r.insertBefore(o,n.nextSibling)}n=t.split}var u=e.parentChildren,l=u.indexOf(e.begin),d=u.indexOf(e.end);i.splice.apply(u,[l+1,d-l].concat(i))}function Rt(e,t,n){var i={};i[t.keyName]=t.isArray?n:e.key,i[t.valName]=e.val,t.asName&&(i[t.asName]=t.value);var o=e.vm=or.itemFactory(t.vm,{data:i});return t.isArray?o.$watch(t.valName,function(e){t.value&&t.value.set&&t.value.set(o[t.keyName],e)}):o.$watch(t.valName,function(n){t.value[e.key]=n}),e.index=n,e.innerRender=r.scan(t.fragment,o,function(){var t=this.root;ur.push.apply(e.children,t.children),this.root=e}),e}function Bt(e,t){var n=e[t];if(n){var r=n.arr;if(r){var i=r.pop();return r.length||(n.arr=0),i}return delete e[t],n}}function qt(e,t){var n=t.key;if(e[n]){var r=e[n];(r.arr||(r.arr=[])).push(t)}else e[n]=t}function zt(e){var t;for(var n in e){var t=n;break}if(t)return Bt(e,t)}function Wt(){for(var e=[],t=0;t<arguments.length;t++){var n=arguments[t],r=typeof n;if("string"===r||"number"===r||!0===n)e.push(n);else if(Array.isArray(n))e.push(Wt.apply(null,n));else if("object"===r)for(var i in n)n.hasOwnProperty(i)&&n[i]&&e.push(i)}return e.join(" ")}function Yt(e){var t=e.target;r(t).addClass(t.getAttribute(Xo[e.type])||"")}function Ut(e){var t=e.target,n=Xo[e.type];r(t).removeClass(t.getAttribute(n)||""),"change-active"!==n&&r(t).removeClass(t.getAttribute("change-active")||"")}function Jt(e,t){var n=e.getAttribute("change-class");n!==t&&(r(e).removeClass(n).addClass(t),e.setAttribute("change-class",t))}function Gt(e,t){e.children&&e.children.forEach(function(e){"option"===e.nodeName?Xt(e,t):Gt(e,t)})}function Xt(e,t){var n=e.props;if(!("disabled"in n)){var r=Zt(e,n);if(r=String(r||"").trim(),n.selected=-1!==t.indexOf(r),e.dom){e.dom.selected=n.selected;e.dom.selected}}}function Zt(e,t){if(t&&"value"in t)return t.value+"";var n=[];return e.children.forEach(function(e){"#text"===e.nodeName?n.push(e.nodeValue):"#document-fragment"===e.nodeName&&n.push(Zt(e))}),n.join("")}function Kt(e,t){return e.children.forEach(function(e){"option"===e.nodeName?!0===e.props.selected&&t.push(Zt(e,e.props)):e.children&&Kt(e,t)}),t}function Qt(e){e.userCb&&e.userCb.call(e.vm,{type:"changed",target:e.dom})}function en(e){var t=this,n=t._ms_duplex_;if(!t.composing&&t.value!==n.value){if(t.caret)try{var r=n.getCaret(t);n.pos=r}catch(e){}if(n.debounceTime>4){var i=new Date,o=i-n.time||0;n.time=i,o>=n.debounceTime?Zo[n.dtype].call(n):(clearTimeout(n.debounceID),n.debounceID=setTimeout(function(){Zo[n.dtype].call(n)},o))}else n.isChanged?setTimeout(function(){Zo[n.dtype].call(n)},4):Zo[n.dtype].call(n)}}function tn(){var e=this.expr;Ko.test(e)&&(this.isChanged=!0,e=e.replace(Ko,""));var t=e.match(Qo);t&&(e=e.replace(Qo,""),this.isChanged||(this.debounceTime=parseInt(t[1],10)||300)),this.expr=e}function nn(){var e=(this.expr,this.node),t=e.props.type;this.parseValue=an;var n,r=this.param,i=!1;r=r?r.split("-").map(function(e){return"checked"===e&&(i=!0),e}):[],e.duplex=this,Xr.test(t)&&i&&(r=[],n="radio",this.isChecked=i),this.parsers=r,/input|textarea|select/.test(e.nodeName)?n||(n="select"===e.nodeName?"select":"checkbox"===t?"checkbox":"radio"===t?"radio":"input"):"contenteditable"in e.props&&(n="contenteditable"),this.dtype=n,"input"!==n&&"contenteditable"!==n?(delete this.isChanged,delete this.debounceTime):this.isChecked||(this.isString=!0);var o=e.props["data-duplex-changed"];if(o){var a=Qe(o,"xx"),s=et(a[0]);this.userCb=new Function("$event","var __vmodel__ = this\nreturn "+s)}}function rn(e,t){if(Array.isArray(e)){if(e+""!==this.compareVal)return this.compareVal=e+"",!0}else if(e=this.parseValue(e),this.isChecked||(this.value=e+=""),e!==this.compareVal)return this.compareVal=e,!0}function on(e,t){var n=e.dom;this.dom=n,this.vdom=e,this.duplexCb=en,n._ms_duplex_=this,t(n,this)}function an(e){for(var t,n=0;t=this.parsers[n++];){var i=r.parsers[t];i&&(e=i.call(this,e))}return e}function sn(e,t){var n={};switch(t.dtype){case"radio":case"checkbox":n.click=en;break;case"select":n.change=en;break;case"contenteditable":t.isChanged?n.blur=en:r.modern?(rr.webkitURL?n.webkitEditableContentChanged=en:rr.MutationEvent&&(n.DOMCharacterDataModified=en),n.input=en):(n.keydown=pn,n.paste=un,n.cut=un,n.focus=hn,n.blur=fn);break;case"input":t.isChanged?n.change=en:Qn<10?(n.propertychange=cn,n.paste=un,n.cut=un,n.keyup=pn):(n.input=en,n.compositionstart=fn,n.compositionend=hn,/\[native code\]/.test(rr.Int8Array)||(n.keydown=pn,n.paste=un,n.cut=un,rr.netscape&&(n.DOMAutoComplete=en)))}/password|text/.test(e.type)&&(n.focus=ln,n.blur=dn,t.getCaret=mn,t.setCaret=vn);for(var i in n)r.bind(e,i,n[i])}function cn(e){"value"===e.propertyName&&en.call(this,e)}function un(e){var t=this;setTimeout(function(){en.call(t,e)},0)}function ln(){this.caret=!0}function dn(){this.caret=!1}function fn(){this.composing=!0}function hn(e){this.composing=!1,un.call(this,e)}function pn(e){var t=e.keyCode;91===t||15<t&&t<19||37<=t&&t<=40||en.call(this,e)}function vn(e,t){var n;e.createTextRange?sa(function(){e.focus(),n=e.createTextRange(),n.collapse(!0),n.moveEnd("character",t),n.moveStart("character",t),n.select()}):(e.focus(),void 0!==e.selectionStart&&e.setSelectionRange(t,t))}function mn(e){var t,n,r,i,o,a=0
;return e.selectionStart+e.selectionEnd>-1?a=e.selectionStart:(n=Xn.selection.createRange())&&n.parentElement()===e&&(i=e.value.length,t=e.value.replace(/\r\n/g,"\n"),r=e.createTextRange(),r.moveToBookmark(n.getBookmark()),o=e.createTextRange(),o.collapse(!1),r.compareEndPoints("StartToEnd",o)>-1?a=i:(a=-r.moveStart("character",-i),a+=t.slice(0,a).split("\n").length-1)),a}function gn(e,t){var n=this.dom;if(this.isString&&t&&!e&&!n.valueHijack){n.valueHijack=en;var i=setInterval(function(){r.contains(r.root,n)?n.valueHijack({type:"poll"}):clearInterval(i)},30);return i}}function yn(e){return"regexp"===r.type(e)}function bn(e){if("string"==typeof e&&e){var t=e.split("-");if(3===t.length&&4===t[0].length){var n=~~t[0],r=~~t[1]-1,i=~~t[2],o=new Date(n,r,i);return o.getFullYear()===n&&o.getMonth()===r&&o.getDate()===i}}return!1}function wn(e){for(;"FORM"!==e.tagName;)e=e.parentNode;return e._ms_validate_}function xn(e){e.preventDefault();var t=wn(e.target);t&&t.onManual&&t.onManual()}function _n(e,t,n){for(var r,i=0;r=e[i++];){var o=r.rules&&r.duplex;o?(t.push(o),Cn(o,n)):r.children?_n(r.children,t,n):Array.isArray(r)&&_n(r,t,n)}}function An(e){var t=e.target,n=t._ms_duplex_,i=(n||{}).vdom;if(n&&i.rules&&!n.validator){var o=wn(t);o&&r.Array.ensure(o.fields,n)&&Cn(n,o)}}function kn(e){var t=e.target,n=t._ms_duplex_,r=wn(e.target);r&&r.validate(n,0,e)}function Cn(e,t){var n=e.dom;e.validator||(e.validator=t,!t.validateInKeyup||e.isChanged||e.debounceTime||r.bind(n,"keyup",kn),t.validateInBlur&&r.bind(n,"blur",kn),t.resetInFocus&&r.bind(n,"focus",function(e){var t=e.target,n=t._ms_duplex_,r=wn(e.target);r&&r.onReset.call(t,e,n)}))}function Tn(){var e=this.data||{};return this.message.replace(fa,function(t,n){return null==e[n]?"":e[n]})}function $n(e,t,n,i){var o=t.type,a=r.directives[o];if(Gn){var s=r.vdom(n,"toDOM");1===s.nodeType&&s.removeAttribute(t.attrName),n.dom=s}var c=a.update?function(e){!i.mount&&/css|visible|duplex/.test(o)?i.callbacks.push(function(){a.update.call(l,l.node,e)}):a.update.call(l,l.node,e)}:r.noop;for(var u in a)t[u]=a[u];t.node=n;var l=new rt(e,t,c);return l.init&&l.init(),l.update(),l}function Nn(e,t){var n=t[0],i={},o=[],a=!1;for(var s in e){var c=e[s],u=s.split("-");if(s in n.props)var l=s;else l=":"+s.slice(3);ha[u[1]]&&u.splice(1,0,"on"),"on"===u[1]&&(u[3]=parseFloat(u[3])||0);var d=u[1];if("controller"!==d&&"important"!==d&&nr[d]){var f={type:d,param:u[2],attrName:l,name:u.join("-"),expr:c,priority:nr[d].priority||100*d.charCodeAt(0)};if("if"===d&&(a=!0),"on"===d&&(f.priority+=u[3]),!i[f.name]&&(i[f.name]=c,o.push(f),"for"===d))return[r.mix(f,t[3])]}}if(o.sort(En),a)for(var h,p=[],v=0;h=o[v++];)if(p.push(h),"if"===h.type)return p;return o}function En(e,t){return e.priority-t.priority}function On(e){var t=e.nodeValue.trim().replace(/\n\r?/g,""),n=[];do{var i=t.indexOf(x.openTag);i=-1===i?t.length:i;var o=t.slice(0,i);if(/\S/.test(o)&&n.push(r.quote(r._decode(o))),t=t.slice(i+x.openTag.length)){i=t.indexOf(x.closeTag);var o=t.slice(0,i),a=r.unescapeHTML(o);if(/\|\s*\w/.test(a)){var s=Qe(a,"expr");s[1]&&(a=s[1].replace(va,s[0]+")"))}pa&&(a="("+a+")"),n.push(a),t=t.slice(i+x.closeTag.length)}}while(t.length);return[{expr:n.join("+"),name:"expr",type:"expr"}]}function Mn(e){for(var t,n=0,r=0;t=e[r++];)"#document-fragment"===t.nodeName?n+=Mn(t.children):n+=1;return n}function Sn(e,t){t&&t.forEach(function(t){if(t){var n=t.children&&Mn(t.children);if("#document-fragment"===t.nodeName)var i=w();else{i=r.vdom(t,"toDOM");var o=i.childNodes&&i.childNodes.length;o&&n&&o>n&&(ma[i.nodeName]||r.clearHTML(i))}if(n&&(Sn(i,t.children),"select"===t.nodeName)){var a=[];Kt(t,a),Gt(t,a)}try{ma[e.nodeName]||e.appendChild(i)}catch(e){}}})}function Vn(e){for(var t;t=e.firstChild;)1===t.nodeType&&Vn(t),e.removeChild(t)}function jn(e,t){var n,r=e.indexOf(t)+1,i=1,o=[];for(o.start=r;t=e[r++];)if(o.push(t),"#comment"===t.nodeName)if(Dn(t.nodeValue,"ms-for:"))i++;else if("ms-for-end:"===t.nodeValue&&0===--i){n=t,o.pop();break}return o.end=n,o}function Dn(e,t){return 0===e.indexOf(t)}function Fn(e,t,n){this.root=e,this.vm=t,this.beforeReady=n,this.bindings=[],this.callbacks=[],this.directives=[],this.init()}function Ln(){var e=this.beforeUpdate(),t=this.value=this.get();if(this.callback&&this.diff(t,e)){this.callback(this.node,this.value);var n=this.vm,i=n.$render,o=n.$events.onViewChange;o&&i&&i.root&&!r.viewChanging&&(ga&&(clearTimeout(ga),ga=null),ga=setTimeout(function(){o.forEach(function(e){e.callback.call(n,{type:"viewchange",target:i.root,vmodel:n})})}))}this._isScheduled=!1}function Pn(e){var e=or.toJson(e);if(Array.isArray(e)){var t={};return e.forEach(function(e){e&&r.shadowCopy(t,e)}),t}return e}function In(e,t){e.innerRender=t;var n=t.root,r=e.node,i=r.props.slot;for(var o in n)r[o]=n[o];r.props&&i&&(r.props.slot=i),t.root=r,t.vnodes[0]=r}function Hn(e,t,n){var r=e.$events["on"+n];r&&r.forEach(function(r){setTimeout(function(){r.callback.call(e,{type:n.toLowerCase(),target:t.dom,vmodel:e})},0)})}function Rn(e,t,n){var i=[],o=e.defaults;Bn(o,i),Bn(t,i);var a={};for(var s in o){var c=t[s];a[s]=null==c?o[s]:c}a.$id=t.id||t.$id||r.makeHashCode(n),delete a.id;var u=r.mix(!0,{},a),l=r.define(u);return i.forEach(function(e){l.$watch(e.type,e.cb)}),l}function Bn(e,t){for(var n in e)ya[n]&&"function"==typeof e[n]&&0===n.indexOf("on")&&t.unshift({type:n,cb:e[n]})}function qn(e,t){var n=t&&t[0]&&t[0].forDir;n&&(n.parentChildren=e)}function zn(e,t){for(var n,r=0;n=e[r];r++){if("slot"===n.nodeName){qn(e,t),e.splice.apply(e,[r,1].concat(t));break}n.children&&zn(n.children,t)}}function Wn(e,t){for(var n,r=0;n=e[r];r++)if("slot"!==n.nodeName)n.children&&Wn(n.children,t);else{var i=n.props.name;qn(e,t[i]),e.splice.apply(e,[r,1].concat(t[i]))}}function Yn(e,t){r.components[e]=t;for(var n,i=0;n=ba[i];i++)n.is===e&&(ba.splice(i,1),n.reInit=!0,delete n.value,n.update(),i--);return t}function Un(e){var t=e.displayName;delete e.displayName;var n={defaults:r.mix(!0,{},this.defaults,e.defaults)};return e.soleSlot&&(n.soleSlot=e.soleSlot),n.template=e.template||this.template,r.component(t,n)}var Jn="object"==typeof window?window:"object"==typeof global?global:{},Gn=!!Jn.location&&Jn.navigator,Xn=Gn?Jn.document:{createElement:Object,createElementNS:Object,documentElement:"xx",contains:Boolean},Zn=Gn?Xn.documentElement:{outerHTML:"x"},Kn={objectobject:7,objectundefined:6,undefinedfunction:NaN,undefinedobject:NaN},Qn=Xn.documentMode||Kn[typeof Xn.all+typeof XMLHttpRequest],er=/NaN|undefined/.test(Qn)||Qn>8;e.prototype={put:function(e,t){var n={key:e,value:t};return this._keymap[e]=n,this.tail?(this.tail.newer=n,n.older=this.tail):this.head=n,this.tail=n,this.size===this.limit?this.shift():this.size++,t},shift:function(){var e=this.head;e&&(this.head=this.head.newer,this.head.older=e.newer=e.older=this._keymap[e.key]=void 0,delete this._keymap[e.key],this.size--)},get:function(e){var t=this._keymap[e];if(void 0!==t)return t===this.tail?t.value:(t.newer&&(t===this.head&&(this.head=t.newer),t.newer.older=t.older),t.older&&(t.older.newer=t.newer),t.newer=void 0,t.older=this.tail,this.tail&&(this.tail.newer=t),this.tail=t,t.value)}};var tr={},nr={},rr=Jn;r.init=function(e){this[0]=this.element=e},r.fn=r.prototype=r.init.prototype;var ir=/[^, ]+/g,or={},ar=Object.prototype,sr=ar.toString,cr=ar.hasOwnProperty,ur=Array.prototype,lr="object"==typeof console;r.config={debug:!0};var dr=/([a-z\d])([A-Z]+)/g,fr=/[-_][^-_]/g,hr=ur.slice,pr=/\d\.\d{4}/,vr=1,mr=/[-.*+?^${}()|[\]\/\\]/g,gr={},yr={},br={},wr={};rr.avalon=r;var xr=/&[a-z0-9#]{2,10};/,_r=Xn.createElement("div");i(r,{Array:{merge:function(e,t){e.push.apply(e,t)},ensure:function(e,t){if(-1===e.indexOf(t))return e.push(t)},removeAt:function(e,t){return!!e.splice(t,1).length},remove:function(e,t){var n=e.indexOf(t);return!!~n&&r.Array.removeAt(e,n)}},evaluatorPool:new e(888),parsers:{number:function(e){return""===e?"":parseFloat(e)||0},string:function(e){return null===e||void 0===e?"":e+""},boolean:function(e){return""===e?e:"true"===e||"1"===e}},_decode:function(e){return xr.test(e)?(_r.innerHTML=e,_r.innerText||_r.textContent):e}});var Ar={interpolate:function(e){var t=e[0],n=e[1];if(t===n)throw new SyntaxError("interpolate openTag cannot equal to closeTag");if(/[<>]/.test(t+"test"+n))throw new SyntaxError('interpolate cannot contains "<" or ">"');x.openTag=t,x.closeTag=n;var r=b(t),i=b(n);x.rtext=new RegExp(r+"(.+?)"+i,"g"),x.rexpr=new RegExp(r+"([\\s\\S]*)"+i)}};if(x.plugins=Ar,x({interpolate:["{{","}}"],debug:!0}),i(r,{shadowCopy:i,oneObject:o,inspect:sr,ohasOwn:cr,rword:ir,version:"2.2.6",vmodels:{},directives:nr,directive:t,eventHooks:gr,eventListeners:yr,validators:br,cssHooks:wr,log:s,noop:l,warn:c,error:u,config:x,modern:er,msie:Qn,root:Zn,document:Xn,window:rr,inBrowser:Gn,isObject:d,range:f,slice:v,hyphen:h,camelize:p,escapeRegExp:b,quote:a,makeHashCode:m}),!A("司徒正美".trim)){String.prototype.trim=function(){return this.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"")}}Object.create||(Object.create=function(){function e(){}return function(t){if(1!=arguments.length)throw new Error("Object.create implementation only accepts one parameter.");return e.prototype=t,new e}}());var kr=!{toString:null}.propertyIsEnumerable("toString"),Cr=function(){}.propertyIsEnumerable("prototype"),Tr=["toString","toLocaleString","valueOf","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","constructor"],$r=Tr.length;A(Object.keys)||(Object.keys=function(e){var t=[],n=Cr&&"function"==typeof e;if("string"==typeof e||e&&e.callee)for(var r=0;r<e.length;++r)t.push(String(r));else for(var i in e)n&&"prototype"===i||!cr.call(e,i)||t.push(String(i));if(kr)for(var o=e.constructor,a=o&&o.prototype===e,s=0;s<$r;s++){var c=Tr[s];a&&"constructor"===c||!cr.call(e,c)||t.push(c)}return t}),A(Array.isArray)||(Array.isArray=function(e){return"[object Array]"===Object.prototype.toString.call(e)}),A(A.bind)||(Function.prototype.bind=function(e){if(arguments.length<2&&void 0===e)return this;var t=this,n=arguments;return function(){var r,i=[];for(r=1;r<n.length;r++)i.push(n[r]);for(r=0;r<arguments.length;r++)i.push(arguments[r]);return t.apply(e,i)}});try{hr.call(r.document.documentElement)}catch(e){ur.slice=function(e,t){if(t=void 0!==t?t:this.length,Array.isArray(this))return hr.call(this,e,t);var n,r,i=[],o=this.length,a=e||0;a=a>=0?a:o+a;var s=t||o;if(t<0&&(s=o+t),(r=s-a)>0)if(i=new Array(r),this.charAt)for(n=0;n<r;n++)i[n]=this.charAt(a+n);else for(n=0;n<r;n++)i[n]=this[a+n];return i}}A(ur.map)||r.shadowCopy(ur,{indexOf:function(e,t){var n=this.length,r=~~t;for(r<0&&(r+=n);r<n;r++)if(this[r]===e)return r;return-1},lastIndexOf:function(e,t){var n=this.length,r=null==t?n-1:t;for(r<0&&(r=Math.max(0,n+r));r>=0;r--)if(this[r]===e)return r;return-1},forEach:k("","_",""),filter:k("r=[],j=0,","if(_)r[j++]=this[i]","return r"),map:k("r=[],","r[i]=_","return r"),some:k("","if(_)return true","return false"),every:k("","if(!_)return false","return true")});var Nr=function(){var e={92:"\\\\",34:'\\"',8:"\\b",12:"\\f",10:"\\n",13:"\\r",9:"\\t"},t=function(e,t){return("000000"+(t||0)).slice(-e)},n=function(n){var r=n.charCodeAt(0),i=e[r];return i||"\\u00"+t(2,r.toString(16))},r=/[\x00-\x1f\x22\x5c]/g;return function(e){return r.lastIndex=0,'"'+(r.test(e)?String(e).replace(r,n):e)+'"'}}();try{r._quote=JSON.stringify}catch(e){r._quote=Nr}var Er={};"Boolean Number String Function Array Date RegExp Object Error".replace(r.rword,function(e){Er["[object "+e+"]"]=e.toLowerCase()}),r.type=function(e){return null==e?String(e):"object"==typeof e||"function"==typeof e?Er[sr.call(e)]||"object":typeof e};var Or=/^\s*\bfunction\b/;r.isFunction="object"==typeof alert?function(e){try{return Or.test(e+"")}catch(e){return!1}}:function(e){return"[object Function]"===sr.call(e)};var Mr=/^\[object (?:Window|DOMWindow|global)\]$/;r.isWindow=T(r.window)?T:C;var Sr,Vr;for(Sr in r({}))break;Vr="0"!==Sr,r.isPlainObject=/\[native code\]/.test(Object.getPrototypeOf)?N:$;var jr=/object|function/;r.mix=r.fn.mix=function(){var e=arguments.length,t=!1,n=0,r=[];for(!0===arguments[0]&&(t=!0,n=1);n<e;n++){var i=arguments[n];i=i&&jr.test(typeof i)?i:{},r.push(i)}return 1===r.length&&r.unshift(this),E(t,r)};var Dr,Fr=/(Array|List|Collection|Map|Arguments)\]$/;r.each=function(e,t){if(e){var n=0;if(O(e))for(var r=e.length;n<r&&!1!==t(n,e[n]);n++);else for(n in e)if(e.hasOwnProperty(n)&&!1===t(n,e[n]))break}},function(){var e=["%cavalon.js %c"+r.version+" %cin debug mode, %cmore...","color: rgb(114, 157, 52); font-weight: normal;","color: rgb(85, 85, 85); font-weight: normal;","color: rgb(85, 85, 85); font-weight: normal;","color: rgb(82, 140, 224); font-weight: normal; text-decoration: underline;"];if("object"==typeof console){var t=console,n=t.groupCollapsed||t.log;Function.apply.call(n,t,e),t.log("You're running avalon in debug mode - messages will be printed to the console to help you fix problems and optimise your application.\n\nTo disable debug mode, add this line at the start of your app:\n\n  avalon.config({debug: false});\n\nDebug mode also automatically shut down amicably when your app is minified.\n\nGet help and support:\n  https://segmentfault.com/t/avalon\n  http://avalonjs.coding.me/\n  http://www.baidu-x.com/?q=avalonjs\n  http://www.avalon.org.cn/\n\nFound a bug? Raise an issue:\n  https://github.com/RubyLouvre/avalon/issues\n\n"),n!==console.log&&t.groupEnd(e)}}();var Lr=/<script[^>]*>([\S\s]*?)<\/script\s*>/gim,Pr=/\s+(on[^=\s]+)(?:=("[^"]*"|'[^']*'|[^\s>]+))?/g,Ir=/<\w+\b(?:(["'])[^"]*?(\1)|[^>])*>/gi,Hr={a:/\b(href)\=("javascript[^"]*"|'javascript[^']*')/gi,img:/\b(src)\=("javascript[^"]*"|'javascript[^']*')/gi,form:/\b(action)\=("javascript[^"]*"|'javascript[^']*')/gi},Rr={yyyy:F("FullYear",4),yy:F("FullYear",2,0,!0),y:F("FullYear",1),MMMM:L("Month"),MMM:L("Month",!0),MM:F("Month",2,1),M:F("Month",1,1),dd:F("Date",2),d:F("Date",1),HH:F("Hours",2),H:F("Hours",1),hh:F("Hours",2,-12),h:F("Hours",1,-12),mm:F("Minutes",2),m:F("Minutes",1),ss:F("Seconds",2),s:F("Seconds",1),sss:F("Milliseconds",3),EEEE:L("Day"),EEE:L("Day",!0),a:I,Z:P},Br=/((?:[^yMdHhmsaZE']+)|(?:'(?:[^']|'')*')|(?:E+|y+|M+|d+|H+|h+|m+|s+|a|Z))(.*)/,qr=/^\/Date\((\d+)\)\/$/,zr={AMPMS:{0:"上午",1:"下午"},DAY:{0:"星期日",1:"星期一",2:"星期二",3:"星期三",4:"星期四",5:"星期五",6:"星期六"},MONTH:{0:"1月",1:"2月",2:"3月",3:"4月",4:"5月",5:"6月",6:"7月",7:"8月",8:"9月",9:"10月",10:"11月",11:"12月"},SHORTDAY:{0:"周日",1:"周一",2:"周二",3:"周三",4:"周四",5:"周五",6:"周六"},fullDate:"y年M月d日EEEE",longDate:"y年M月d日",medium:"yyyy-M-d H:mm:ss",mediumDate:"yyyy-M-d",mediumTime:"H:mm:ss",short:"yy-M-d ah:mm",shortDate:"yy-M-d",shortTime:"ah:mm"};zr.SHORTMONTH=zr.MONTH,H.locate=zr;var Wr={$id:void 0,$render:void 0,$track:void 0,$element:void 0,$computed:void 0,$watch:void 0,$fire:void 0,$events:void 0,$accessors:void 0,$hashcode:void 0,$mutations:void 0,$vbthis:void 0,$vbsetter:void 0},Yr={stop:function(e){return e.stopPropagation(),e},prevent:function(e){return e.preventDefault(),e}},Ur={esc:27,tab:9,enter:13,space:32,del:46,up:38,left:37,right:39,down:40};for(var Jr in Ur)!function(e,t){Yr[e]=function(e){return e.which!==t&&(e.$return=!0),e}}(Jr,Ur[Jr]);var Gr=r.filters={};r.composeFilters=function(){var e=arguments;return function(t){for(var n,i=0;n=e[i++];){var o=n[0],a=r.filters[o];if("function"==typeof a){n[0]=t;try{t=a.apply(0,n)}catch(e){}}}return t}},r.escapeHtml=J,r.mix(Gr,{uppercase:function(e){return String(e).toUpperCase()},lowercase:function(e){return String(e).toLowerCase()},truncate:function(e,t,n){return e?(e=String(e),isNaN(t)&&(t=30),n="string"==typeof n?n:"...",e.length>t?e.slice(0,t-n.length)+n:e):""},camelize:r.camelize,date:H,escape:J,sanitize:V,number:S,currency:function(e,t,n){return(t||"¥")+S(e,isFinite(n)?n:2)}},{filterBy:q,orderBy:R,selectBy:z,limitBy:W},Yr);var Xr=/^(?:checkbox|radio)$/;r.contains=K,r.cloneNode=function(e){return e.cloneNode(!0)},Gn&&function(){function e(e,t){e in Zn||!HTMLElement.prototype.__defineGetter__||HTMLElement.prototype.__defineGetter__(e,t)}Qn<10&&(r.cloneNode=Z),Xn.contains||(Xn.contains=function(e){return K(Xn,e)}),r.modern&&(Xn.createTextNode("x").contains||(Node.prototype.contains=function(e){return K(this,e)})),e("outerHTML",function(){var e=Xn.createElement("div");return e.appendChild(this),e.innerHTML}),e("children",function(){for(var e,t=[],n=0;e=this.childNodes[n++];)1===e.nodeType&&t.push(e);return t}),e("innerText",function(){return this.textContent})}(),Q.prototype={toString:function(){var e=this.node,t=e.className,n="string"==typeof t?t:t.baseVal,r=n.match(/\S+/g);return r?r.join(" "):""},contains:function(e){return(" "+this+" ").indexOf(" "+e+" ")>-1},add:function(e){this.contains(e)||this.set(this+" "+e)},remove:function(e){this.set((" "+this+" ").replace(" "+e+" "," "))},set:function(e){e=e.trim();var t=this.node;"object"==typeof t.className?t.setAttribute("class",e):t.className=e,e||t.removeAttribute("class")}},"add,remove".replace(ir,function(e){r.fn[e+"Class"]=function(t){var n=this[0]||{};return t&&"string"==typeof t&&1===n.nodeType&&t.replace(/\S+/g,function(t){ee(n)[e](t)}),this}}),r.shadowCopy(r.fn,{hasClass:function(e){var t=this[0]||{};return 1===t.nodeType&&ee(t).contains(e)},toggleClass:function(e,t){var n="boolean"==typeof t,r=this;return String(e).replace(/\S+/g,function(e){var i=n?t:!r.hasClass(e);r[i?"addClass":"removeClass"](e)}),this}});var Zr={};"accept-charset,acceptCharset|char,ch|charoff,chOff|class,className|for,htmlFor|http-equiv,httpEquiv".replace(/[^\|]+/g,function(e){var t=e.split(",");Zr[t[0]]=t[1]}),["autofocus,autoplay,async,allowTransparency,checked,controls","declare,disabled,defer,defaultChecked,defaultSelected,","isMap,loop,multiple,noHref,noResize,noShade","open,readOnly,selected"].join(",").replace(/\w+/g,function(e){Zr[e.toLowerCase()]=e}),["accessKey,bgColor,cellPadding,cellSpacing,codeBase,codeType,colSpan","dateTime,defaultValue,contentEditable,frameBorder,longDesc,maxLength,marginWidth,marginHeight,rowSpan,tabIndex,useMap,vSpace,valueType,vAlign"].join(",").replace(/\w+/g,function(e){Zr[e.toLowerCase()]=e});var Kr=/^[\],:{}\s]*$/,Qr=/(?:^|:|,)(?:\s*\[)+/g,ei=/\\(?:["\\\/bfnrt]|u[\da-fA-F]{4})/g,ti=/"[^"\\\r\n]*"|true|false|null|-?(?:\d+\.|)\d+(?:[eE][+-]?\d+|)/g,ni=/^\[object SVG\w*Element\]$/,ri=/&amp;/g,ii={};try{r.parseJSON=JSON.parse}catch(e){r.parseJSON=ne}r.fn.attr=function(e,t){return 2===arguments.length?(this[0].setAttribute(e,t),this):this[0].getAttribute(e)};var oi=o("float","cssFloat");r.cssNumber=o("animationIterationCount,columnCount,order,flex,flexGrow,flexShrink,fillOpacity,fontWeight,lineHeight,opacity,orphans,widows,zIndex,zoom");var ai=["","-webkit-","-o-","-moz-","-ms-"];r.cssName=function(e,t,n){if(oi[e])return oi[e];t=t||r.root.style||{};for(var i=0,o=ai.length;i<o;i++)if((n=r.camelize(ai[i]+e))in t)return oi[e]=n;return null},r.css=function(e,t,n,i){if(e instanceof r&&(e=e[0]),1===e.nodeType){var o=r.camelize(t);if(t=r.cssName(o)||o,void 0===n||"boolean"==typeof n){i=wr[o+":get"]||wr["@:get"],"background"===t&&(t="backgroundColor");var a=i(e,t);return!0===n?parseFloat(a)||0:a}if(""===n)e.style[t]="";else{if(null==n||n!==n)return;isFinite(n)&&!r.cssNumber[o]&&(n+="px"),i=wr[o+":set"]||wr["@:set"],i(e,t,n)}}},r.fn.css=function(e,t){if(r.isPlainObject(e))for(var n in e)r.css(this,n,e[n]);else var i=r.css(this,e,t);return void 0!==i?i:this},r.fn.position=function(){var e,t,n=this[0],i={top:0,left:0};return n?("fixed"===this.css("position")?t=n.getBoundingClientRect():(e=this.offsetParent(),t=this.offset(),"HTML"!==e[0].tagName&&(i=e.offset()),i.top+=r.css(e[0],"borderTopWidth",!0),i.left+=r.css(e[0],"borderLeftWidth",!0),i.top-=e.scrollTop(),i.left-=e.scrollLeft()),{top:t.top-i.top-r.css(n,"marginTop",!0),left:t.left-i.left-r.css(n,"marginLeft",!0)}):i},r.fn.offsetParent=function(){for(var e=this[0].offsetParent;e&&"static"===r.css(e,"position");)e=e.offsetParent;return r(e||r.root)},wr["@:set"]=function(e,t,n){try{e.style[t]=n}catch(e){}},wr["@:get"]=function(e,t){if(!e||!e.style)throw new Error("getComputedStyle要求传入一个节点 "+e);var n,r=rr.getComputedStyle(e,null);return r&&""===(n="filter"===t?r.getPropertyValue(t):r[t])&&(n=e.style[t]),n},wr["opacity:get"]=function(e){var t=wr["@:get"](e,"opacity");return""===t?"1":t},"top,left".replace(r.rword,function(e){wr[e+":get"]=function(t){var n=wr["@:get"](t,e);return/px$/.test(n)?n:r(t).position()[e]+"px"}});var si={position:"absolute",visibility:"hidden",display:"block"},ci=/^(none|table(?!-c[ea]).+)/;if(r.each({Width:"width",Height:"height"},function(e,t){var n="client"+e,i="scroll"+e,o="offset"+e;wr[t+":get"]=function(t,n,i){var a=-4;"number"==typeof i&&(a=i),n="Width"===e?["Left","Right"]:["Top","Bottom"];var s=t[o];return 2===a?s+r.css(t,"margin"+n[0],!0)+r.css(t,"margin"+n[1],!0):(a<0&&(s=s-r.css(t,"border"+n[0]+"Width",!0)-r.css(t,"border"+n[1]+"Width",!0)),-4===a&&(s=s-r.css(t,"padding"+n[0],!0)-r.css(t,"padding"+n[1],!0)),s)},wr[t+"&get"]=function(e){var n=[];oe(e,n);for(var r,i=wr[t+":get"](e),o=0;r=n[o++];){e=r.node;for(var a in r)"string"==typeof r[a]&&(e.style[a]=r[a])}return i},r.fn[t]=function(r){var a=this[0];if(0===arguments.length){if(a.setTimeout)return a["inner"+e]||a.document.documentElement[n]||a.document.body[n];if(9===a.nodeType){var s=a.documentElement;return Math.max(a.body[i],s[i],a.body[o],s[o],s[n])}return wr[t+"&get"](a)}return this.css(t,r)},r.fn["inner"+e]=function(){return wr[t+":get"](this[0],void 0,-2)},r.fn["outer"+e]=function(e){return wr[t+":get"](this[0],void 0,!0===e?2:0)}}),Qn<9){r.shadowCopy(oi,o("float","styleFloat"));var ui=/^-?(?:\d*\.)?\d+(?!px)[^\d\s]+$/i,li=/^(top|right|bottom|left)$/,di=/alpha\([^)]+\)/i,fi=8===Qn,hi={thin:fi?"1px":"2px",medium:fi?"3px":"4px",thick:fi?"5px":"6px"};wr["@:get"]=function(e,t){var n=e.currentStyle,r=n[t];if(ui.test(r)&&!li.test(r)){var i=e.style,o=i.left,a=e.runtimeStyle.left;e.runtimeStyle.left=n.left,i.left="fontSize"===t?"1em":r||0,r=i.pixelLeft+"px",i.left=o,e.runtimeStyle.left=a}return"medium"===r&&(t=t.replace("Width","Style"),"none"===n[t]&&(r="0px")),""===r?"auto":hi[r]||r},wr["opacity:set"]=function(e,t,n){var r=e.style,i=Number(n)<=1?"alpha(opacity="+100*n+")":"",o=r.filter||"";r.zoom=1,r.filter=(di.test(o)?o.replace(di,i):o+" "+i).trim(),r.filter||r.removeAttribute("filter")},wr["opacity:get"]=function(e){for(var t,n=e.style.filter.match(/(opacity|\d(\d|\.)*)/g)||[],r=!1,i=0;t=n[i++];)if("opacity"===t)r=!0;else if(r)return t/100+"";return"1"}}r.fn.offset=function(){var e=this[0],t={left:0,top:0};if(!e||!e.tagName||!e.ownerDocument)return t;var n=e.ownerDocument,i=n.body,o=n.documentElement,a=n.defaultView||n.parentWindow;if(!r.contains(o,e))return t;e.getBoundingClientRect&&(t=e.getBoundingClientRect());var s=o.clientTop||i.clientTop,c=o.clientLeft||i.clientLeft,u=Math.max(a.pageYOffset||0,o.scrollTop,i.scrollTop),l=Math.max(a.pageXOffset||0,o.scrollLeft,i.scrollLeft);return{top:t.top+u-s,left:t.left+l-c}},r.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},function(e,t){r.fn[e]=function(n){var i=this[0]||{},o=ae(i),a=r.root,s="scrollTop"===e;if(!arguments.length)return o?t in o?o[t]:a[e]:i[e];o?o.scrollTo(s?r(o).scrollLeft():n,s?n:r(o).scrollTop()):i[e]=n}});var pi={"option:get":Qn?ce:function(e){return e.value},"select:get":function(e,t){for(var n,r=e.options,i=e.selectedIndex,o=pi["option:get"],a="select-one"===e.type||i<0,s=a?null:[],c=a?i+1:r.length,u=i<0?c:a?i:0;u<c;u++)if(n=r[u],(n.selected||u===i)&&!n.disabled&&(!n.parentNode.disabled||"OPTGROUP"!==n.parentNode.tagName)){if(t=o(n),a)return t;s.push(t)}return s},"select:set":function(e,t,n){t=[].concat(t);for(var r,i=pi["option:get"],o=0;r=e.options[o++];)(r.selected=t.indexOf(i(r))>-1)&&(n=!0);n||(e.selectedIndex=-1)}};r.fn.val=function(e){var t=this[0];if(t&&1===t.nodeType){var n=0===arguments.length,r=n?":get":":set",i=pi[se(t)+r];if(i)var o=i(t,e);else{if(n)return(t.value||"").replace(/\r/g,"");t.value=e}}return n?o:this};var vi={area:1,base:1,basefont:1,bgsound:1,br:1,col:1,command:1,embed:1,frame:1,hr:1,img:1,input:1,keygen:1,link:1,meta:1,param:1,source:1,track:1,wbr:1},mi=o("div,ul,ol,dl,table,h1,h2,h3,h4,h5,h6,form,fieldset"),gi=ue("tr,style,script"),yi=(ue("option,optgroup,#text"),ue("option,#text"),ue("#text"),ue("th,td,style,script"),ue("col"),ue("base,basefont,bgsound,link,style,script,meta,title,noscript,noframes"),o("head,body"),{xmp:1,style:1,script:1,noscript:1,textarea:1,"#comment":1,template:1}),bi={style:1,script:1,noscript:1,template:1},wi=/\S/,xi=/\s/;r.lexer=le;var _i=new e(100),Ai=/^(tbody|thead|tfoot)$/,ki=/\<(\w[^\s\/\>]*)/,Ci=/<|&#?\w+;/,Ti=new e(128);r.parseHTML=function(e){var t=w();if("string"!=typeof e)return t;if(!Ci.test(e))return Xn.createTextNode(e);e=e.replace(/<(?!area|br|col|embed|hr|img|input|link|meta|param)(([\w:]+)[^>]*)\/>/gi,"<$1></$2>").trim();var n=Ti.get(e);if(n)return r.cloneNode(n);for(var i,o=le(e),a=0;i=o[a++];){var s=r.vdom(i,"toDOM");t.appendChild(s)}return e.length<1024&&Ti.put(e,t),t},r.innerHTML=function(e,t){var n=r.parseHTML(t);this.clearHTML(e),e.appendChild(n)},r.unescapeHTML=function(e){return String(e).replace(/&quot;/g,'"').replace(/&#39;/g,"'").replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&amp;/g,"&")},r.clearHTML=function(e){for(;e.lastChild;)e.removeChild(e.lastChild);return e};var $i={click:!0,dblclick:!0,keydown:!0,keypress:!0,keyup:!0,mousedown:!0,mousemove:!0,mouseup:!0,mouseover:!0,mouseout:!0,wheel:!0,mousewheel:!0,input:!0,change:!0,beforeinput:!0,compositionstart:!0,compositionupdate:!0,compositionend:!0,select:!0,cut:!0,copy:!0,paste:!0,beforecut:!0,beforecopy:!0,beforepaste:!0,focusin:!0,focusout:!0,DOMFocusIn:!0,DOMFocusOut:!0,DOMActivate:!0,dragend:!0,datasetchanged:!0},Ni=r.modern&&Xn.ontouchstart;r.fn.bind=function(e,t,n){if(this[0])return r.bind(this[0],e,t,n)},r.fn.unbind=function(e,t,n){if(this[0]){var i=hr.call(arguments);i.unshift(this[0]),r.unbind.apply(0,i)}return this},r.bind=function(e,t,n){if(1===e.nodeType){var i=e.getAttribute("avalon-events")||"",o=y(n),a=gr[t];"click"===t&&Ni&&e.addEventListener("click",r.noop),a&&(t=a.type||t,a.fix&&(n=a.fix(e,n),n.uuid=o));var s=t+":"+o;r.eventListeners[n.uuid]=n,-1===i.indexOf(t+":")&&($i[t]||r.modern&&Mi[t]?ke(t):r._nativeBind(e,t,Ae));var c=i.split(",");return""===c[0]&&c.shift(),-1===c.indexOf(s)&&(c.push(s),xe(e,c.join(","))),n}var u=function(t){n.call(e,new Ce(t))};return r._nativeBind(e,t,u),u},r.unbind=function(e,t,n){if(1===e.nodeType){var i=e.getAttribute("avalon-events")||"";switch(arguments.length){case 1:r._nativeUnBind(e,t,Ae),e.removeAttribute("avalon-events");break;case 2:i=i.split(",").filter(function(e){return-1===e.indexOf(t+":")}).join(","),xe(e,i);break;default:var o=t+":"+n.uuid;i=i.split(",").filter(function(e){return e!==o}).join(","),xe(e,i),delete r.eventListeners[n.uuid]}}else r._nativeUnBind(e,t,n)};var Ei={},Oi=/^e/,Mi={focus:!0,blur:!0},Si={webkitMovementY:1,webkitMovementX:1,keyLocation:1,fixEvent:function(){},preventDefault:function(){var e=this.originalEvent||{};e.returnValue=this.returnValue=!1,er&&e.preventDefault&&e.preventDefault()},stopPropagation:function(){var e=this.originalEvent||{};e.cancelBubble=this.cancelBubble=!0,er&&e.stopPropagation&&e.stopPropagation()},stopImmediatePropagation:function(){this.stopPropagation(),this.stopImmediate=!0},toString:function(){return"[object Event]"}};if(Ce.prototype=Si,"onmouseenter"in Zn||r.each({mouseenter:"mouseover",mouseleave:"mouseout"},function(e,t){gr[e]={type:t,fix:function(t,n){return function(r){var i=r.relatedTarget;if(!i||i!==t&&!(16&t.compareDocumentPosition(i)))return delete r.type,r.type=e,n.apply(this,arguments)}}}}),r.each({AnimationEvent:"animationend",WebKitAnimationEvent:"webkitAnimationEnd"},function(e,t){rr[e]&&!gr.animationend&&(gr.animationend={type:t})}),!("onmousewheel"in Xn)){var Vi=void 0!==Xn.onwheel?"wheel":"DOMMouseScroll",ji="wheel"===Vi?"deltaY":"detail";gr.mousewheel={type:Vi,fix:function(e,t){return function(n){var r=n[ji]>0?-120:120;return n.wheelDelta=~~e._ms_wheel_+r,e._ms_wheel_=n.wheelDeltaY=n.wheelDelta,n.wheelDeltaX=0,Object.defineProperty&&Object.defineProperty(n,"type",{value:"mousewheel"}),t.apply(this,arguments)}}}}er||(delete $i.change,delete $i.select),r._nativeBind=er?function(e,t,n,r){e.addEventListener(t,n,!!r)}:function(e,t,n){e.attachEvent("on"+t,n)},r._nativeUnBind=er?function(e,t,n,r){e.removeEventListener(t,n,!!r)}:function(e,t,n){e.detachEvent("on"+t,n)},r.fireDom=function(e,t,n){if(Xn.createEvent){var i=Xn.createEvent("Events");i.initEvent(t,!0,!0,n),r.shadowCopy(i,n),e.dispatchEvent(i)}else if(Zn.contains(e)){i=Xn.createEventObject(),n&&r.shadowCopy(i,n);try{e.fireEvent("on"+t,i)}catch(e){r.log("fireDom",t,"args error")}}};var Di=/^(?:mouse|contextmenu|drag)|click/;Ce.prototype.fixEvent=function(){var e=this;if(null==e.which&&0===e.type.indexOf("key")&&(e.which=null!=e.charCode?e.charCode:e.keyCode),Di.test(e.type)&&!("pageX"in e)){var t=e.target.ownerDocument||Xn,n="BackCompat"===t.compatMode?t.body:t.documentElement;e.pageX=e.clientX+(n.scrollLeft>>0)-(n.clientLeft>>0),e.pageY=e.clientY+(n.scrollTop>>0)-(n.clientTop>>0),e.wheelDeltaY=~~e.wheelDelta,e.wheelDeltaX=0}},"oninput"in Xn.createElement("input")||(gr.input={type:"propertychange",fix:function(e,t){return function(e){if("value"===e.propertyName)return e.type="input",t.apply(this,arguments)}}});var Fi=[];r.ready=function(e){Fi.push(e),r.isReady&&Te()},r.ready(function(){r.scan&&r.scan(Xn.body)}),Gn&&function(){function e(){try{Zn.doScroll("left"),Te()}catch(t){setTimeout(e)}}if("complete"===Xn.readyState)setTimeout(Te);else if(Xn.addEventListener)Xn.addEventListener("DOMContentLoaded",Te,!1);else if(Xn.attachEvent){Xn.attachEvent("onreadystatechange",function(){"complete"===Xn.readyState&&Te()});try{var t=null===rr.frameElement}catch(e){}Zn.doScroll&&t&&rr.external&&e()}r.bind(rr,"load",Te)}();var Li={script:1,style:1,textarea:1,xmp:1,noscript:1,template:1},Pi=/<\w+(\s+("[^"]*"|'[^']*'|[^>])+)?>|<\/\w+>/gi,Ii=/input|textarea|select/i;Se.prototype={constructor:Se,toDOM:function(){if(this.dom)return this.dom;var e=r._decode(this.nodeValue);return this.dom=Xn.createTextNode(e)},toHTML:function(){return this.nodeValue}},Ve.prototype={constructor:Ve,toDOM:function(){return this.dom?this.dom:this.dom=Xn.createComment(this.nodeValue)},toHTML:function(){return"\x3c!--"+this.nodeValue+"--\x3e"}},je.prototype={constructor:je,toDOM:function(){if(this.dom)return this.dom;var e,t=this.nodeName;e=r.modern&&Ri[t]?Le(t):r.modern||!qi[t]&&!Bi.test(t)?Xn.createElement(t):Pe(t);var n=this.props||{};for(var i in n){var o=n[i];Fe(o)&&(Hi[i]&&r.msie<8?Hi[i](e,o):e.setAttribute(i,o+""))}var a=this.children||[],s=a[0]?a[0].nodeValue:"";switch(this.nodeName){case"script":e.type="noexec",e.text=s;try{e.innerHTML=s}catch(e){}e.type=n.type||"";break;case"noscript":e.textContent=s;case"style":case"xmp":case"template":try{e.innerHTML=s}catch(t){De(e,this.nodeName,s)}break;case"option":Qn<9&&(e.text=s);default:!this.isVoidTag&&this.children&&this.children.forEach(function(t){return a&&e.appendChild(r.vdom(a,"toDOM"))})}return this.dom=e},toHTML:function(){var e=[],t=this.props||{};for(var n in t){Fe(t[n])&&e.push(n+"="+r.quote(t[n]+""))}e=e.length?" "+e.join(" "):"";var i="<"+this.nodeName+e;return this.isVoidTag?i+"/>":(i+=">",this.children&&(i+=this.children.map(function(e){return e?r.vdom(e,"toHTML"):""}).join("")),i+"</"+this.nodeName+">")}};var Hi={class:function(e,t){e.className=t},style:function(e,t){e.style.cssText=t},type:function(e,t){try{e.type=t}catch(e){}},for:function(e,t){e.setAttribute("for",t),e.htmlFor=t}},Ri=r.oneObject("circle,defs,ellipse,image,line,path,polygon,polyline,rect,symbol,text,use,g,svg"),Bi=/^\w+\:\w+/,qi=r.oneObject("shape,line,polyline,rect,roundrect,oval,arc,curve,background,image,shapetype,group,fill,stroke,shadow, extrusion, textbox, imagedata, textpath");Ie.prototype={constructor:Ie,toDOM:function(){if(this.dom)return this.dom;var e=this.toFragment();return this.split=e.lastChild,this.dom=e},dispose:function(){this.toFragment(),
this.innerRender&&this.innerRender.dispose();for(var e in this)this[e]=null},toFragment:function(){var e=w();return this.children.forEach(function(t){return e.appendChild(r.vdom(t,"toDOM"))}),e},toHTML:function(){return this.children.map(function(e){return r.vdom(e,"toHTML")}).join("")}},r.mix(r,{VText:Se,VComment:Ve,VElement:je,VFragment:Ie});var zi={"#text":"VText","#document-fragment":"VFragment","#comment":"VComment"};r.vdomAdaptor=r.vdom=function(e,t){if(!e)return"toHTML"===t?"":w();var n=e.nodeName;return n?r[zi[n]||"VElement"].prototype[t].call(e):new r.VFragment(e)[t]()};r.domize=function(e){return r.vdom(e,"toDOM")},r.pendingActions=[],r.uniqActions={},r.inTransaction=0,x.trackDeps=!1,r.track=function(){x.trackDeps&&r.log.apply(r,arguments)};var Wi=[];r.transaction=We;var Yi=0,Ui={map:{}},Ji=/\?\?\d+/g,Gi=r.oneObject("break,case,catch,continue,debugger,default,delete,do,else,false,finally,for,function,if,in,instanceof,new,null,return,switch,this,throw,true,try,typeof,var,void,while,with,abstract,boolean,byte,char,class,const,double,enum,export,extends,final,float,goto,implements,import,int,interface,long,native,package,private,protected,public,short,static,super,synchronized,throws,transient,volatile,arguments"),Xi=r.mix({Math:1,Date:1,$event:1,window:1,__vmodel__:1,avalon:1},Gi),Zi=/(^|[^\w\u00c0-\uFFFF_])(@|##)(?=[$\w])/g,Ki=/\s*(\.|\|)\s*/g,Qi=/\|\|/g,eo=/\(([^)]*)\)/,to=/\|(?=\?\?)/,no=/(^|[^\/])\/(?!\/)(\[.+?]|\\.|[^\/\\\r\n])+\/[gimyu]{0,5}(?=\s*($|[\r\n,.;})]))/g,ro=/\.[\w\.\$]+/g,io=/(\{|\,)\s*([\$\w]+)\s*:/g,oo=/\|(\w+)/g,ao=/[$a-zA-Z_][$a-zA-Z0-9_]*/g,so=new e(300),co=/^__vmodel__\.[$\w\.]+$/,uo=/__vmodel__\.([^(]+)\(([^)]*)\)/,lo=1;rt.prototype={getValue:function(){var e=this.vm;try{return this.getter.call(e,e)}catch(e){r.log(this.getter+" exec error")}},setValue:function(e){var t=this.vm;this.setter&&this.setter.call(t,t,e)},get:function(e){this.type;this.deep&&(r.deepCollect=!0);var t=qe(this,this.getValue);return this.deep&&r.deepCollect&&(r.deepCollect=!1),t},beforeUpdate:function(){return this.oldValue=it(this.value)},update:function(e,t){var n=this.beforeUpdate(),r=this.value=this.get(),i=this.callback;i&&this.diff(r,n,e)&&i.call(this.vm,this.value,n,this.expr),this._isScheduled=!1},schedule:function(){this._isScheduled||(this._isScheduled=!0,r.uniqActions[this.uuid]||(r.uniqActions[this.uuid]=1,r.pendingActions.push(this)),He())},removeDepends:function(){var e=this;this.observers.forEach(function(t){r.Array.remove(t.observers,e)})},diff:function(e,t){return e!==t},dispose:function(){this.value=null,this.removeDepends(),this.beforeDispose&&this.beforeDispose();for(var e in this)delete this[e]}};var fo={vm:1,callback:1,observers:1,oldValue:1,value:1,getValue:1,setValue:1,get:1,removeDepends:1,beforeUpdate:1,update:1,dispose:1},ho=1;ot.prototype={get:function(){if(r.trackingAction){this.collect();var e=this.value;if(e&&e.$events)if(Array.isArray(e))e.forEach(function(e){e&&e.$events&&e.$events.__dep__.collect()});else if(r.deepCollect)for(var t in e)if(e.hasOwnProperty(t)){e[t]}}return this.value},collect:function(){r.track(name,"被收集"),Be(this)},updateVersion:function(){this.version=Math.random()+Math.random()},notify:function(){Ye(),Re(this),Ue()},set:function(e){var t=this.value;if(e!==t){if(r.isObject(e)){var n=t&&t.$hashcode,i=or.createProxy(e,this);i&&(n&&(i.$hashcode=n),e=i)}this.value=e,this.updateVersion(),this.notify()}}};var po=/(\?|if\b|\(.+\))/,vo=function(e){function t(t,n,i){e.call(this,t,void 0,i),delete n.get,delete n.set,r.mix(this,n),this.deps={},this.type="computed",this.depsVersion={},this.isComputed=!0,this.trackAndCompute(),"isStable"in this||(this.isStable=!po.test(at(this.getter)))}ct(t,e);var n=t.prototype;return n.trackAndCompute=function(){this.isStable&&this.depsCount>0?this.getValue():qe(this,this.getValue.bind(this))},n.getValue=function(){return this.value=this.getter.call(this.vm)},n.schedule=function(){for(var e=this.observers,t=e.length;t--;){var n=e[t];n.schedule&&n.schedule()}},n.shouldCompute=function(){if(this.isStable){var e=!1;for(var t in this.deps)this.deps[t].version!==this.depsVersion[t]&&(e=!0,this.deps[t].version=this.depsVersion[t]);return e}return!0},n.set=function(){this.setter&&r.transaction(this.setter,this.vm,arguments)},n.get=function(){return this.collect(),this.shouldCompute()&&(this.trackAndCompute(),this.updateVersion()),this.value},t}(ot);r.define=function(e){var t=e.$id;t||r.error("vm.$id must be specified"),r.vmodels[t]&&r.warn("error:["+t+"] had defined!");var n=or.modelFactory(e);return r.vmodels[t]=n},or.modelFactory=function(e,t){var n=e.$computed||{};delete e.$computed;var i=new ut(e,t),o=i.$accessors,a=[];or.hideProperty(i,"$mutations",{});for(var s in e)if(!(s in Wr)){var c=e[s];a.push(s),lt(s,c)&&(o[s]=ft(s,c))}for(var u in n)if(!(u in Wr)){var c=n[u];"function"==typeof c&&(c={get:c}),c&&c.get&&(c.getter=c.get,c.setter=c.set,r.Array.ensure(a,u),o[u]=ft(u,c,!0))}var l=or.createViewModel(i,o,i);return or.afterCreate(l,i,a,!t),l};var mo={};or.createProxy=dt,or.itemFactory=function(e,t){var n=e.$model,i=new ut(n),o=r.shadowCopy(i.$accessors,e.$accessors),a=t.data;for(var s in a){var c=n[s]=i[s]=a[s];o[s]=ft(s,c)}var u=Object.keys(n),l=or.createViewModel(i,o,i);return or.afterCreate(l,i,u),l},or.fuseFactory=function(e,t){var n=r.mix(e.$model,t.$model),i=new ut(r.mix(n,{$id:e.$id+t.$id})),o=r.mix(i.$accessors,e.$accessors,t.$accessors),a=Object.keys(n),s=or.createViewModel(i,o,i);return or.afterCreate(s,i,a,!1),s};var go={get:function(){return ht(this)},set:r.noop,enumerable:!1,configurable:!0};or.toJson=ht,or.modelAccessor=go;var yo=ur.splice,bo={set:function(e,t){if(e>>>0===e&&this[e]!==t){if(e>this.length)throw Error(e+"set方法的第一个参数不能大于原数组长度");this.splice(e,1,t)}},toJSON:function(){return this.$model=or.toJson(this)},contains:function(e){return-1!==this.indexOf(e)},ensure:function(e){return!this.contains(e)&&(this.push(e),!0)},pushArray:function(e){return this.push.apply(this,e)},remove:function(e){return this.removeAt(this.indexOf(e))},removeAt:function(e){return e>>>0===e?this.splice(e,1):[]},clear:function(){return this.removeAll(),this},removeAll:function(e){var t=(this.length,Array.isArray(e)?function(t){return-1!==e.indexOf(t)}:"function"==typeof e&&e);if(t)for(var n=this.length-1;n>=0;n--)t(this[n],n)&&yo.call(this,n,1);else yo.call(this,0,this.length);this.toJSON(),this.$events.__dep__.notify()}};["push","pop","shift","unshift","splice","sort","reverse"].forEach(function(e){var t=ur[e];bo[e]=function(){var n=this.$events,r=or.listFactory(arguments,!0,n.__dep__),i=t.apply(this,r);return this.toJSON(),n.__dep__.notify(e),i}}),or.listFactory=vt;var wo=!0;try{Object.defineProperty({},"_",{value:"x"}),delete Wr.$vbsetter,delete Wr.$vbthis}catch(e){wo=!1}var xo={$vbthis:1,$vbsetter:1};or.hideProperty=mt,or.fireFactory=yt,or.watchFactory=gt,or.afterCreate=wt;var _o,Ao=Object.defineProperties,ko=new Date-0;if(!wo&&("__defineGetter__"in r&&(_o=function(e,t,n){return"value"in n&&(e[t]=n.value),"get"in n&&e.__defineGetter__(t,n.get),"set"in n&&e.__defineSetter__(t,n.set),e},Ao=function(e,t){for(var n in t)t.hasOwnProperty(n)&&_o(e,n,t[n]);return e}),Qn<9)){var Co={};window.execScript(["Function parseVB(code)","\tExecuteGlobal(code)","End Function"].join("\n"),"VBScript");var To=function(e,t,n,r){var i=t[n];if(4!==arguments.length)return i.get.call(e);i.set.call(e,r)};Ao=function(e,t,n){var i=[];i.push("\tPrivate [$vbsetter]","\tPublic  [$accessors]","\tPublic Default Function [$vbthis](ac"+ko+", s"+ko+")","\t\tSet  [$accessors] = ac"+ko+": set [$vbsetter] = s"+ko,"\t\tSet  [$vbthis]    = Me","\tEnd Function");var o={$vbthis:!0,$vbsetter:!0,$accessors:!0};for(e in Wr)o[e]||(i.push("\tPublic ["+e+"]"),o[e]=!0);for(e in t)o[e]||(o[e]=!0,i.push("\tPublic Property Let ["+e+"](val"+ko+")",'\t\tCall [$vbsetter](Me, [$accessors], "'+e+'", val'+ko+")","\tEnd Property","\tPublic Property Set ["+e+"](val"+ko+")",'\t\tCall [$vbsetter](Me, [$accessors], "'+e+'", val'+ko+")","\tEnd Property","\tPublic Property Get ["+e+"]","\tOn Error Resume Next","\t\tSet["+e+'] = [$vbsetter](Me, [$accessors],"'+e+'")',"\tIf Err.Number <> 0 Then","\t\t["+e+'] = [$vbsetter](Me, [$accessors],"'+e+'")',"\tEnd If","\tOn Error Goto 0","\tEnd Property"));for(e in n)o[e]||(o[e]=!0,i.push("\tPublic ["+e+"]"));i.push("\tPublic [hasOwnProperty]"),i.push("End Class");var a=i.join("\r\n"),s=Co[a];return s||(s=r.makeHashCode("VBClass"),window.parseVB("Class "+s+a),window.parseVB(["Function "+s+"Factory(acc, vbm)","\tDim o","\tSet o = (New "+s+")(acc, vbm)","\tSet "+s+"Factory = o","End Function"].join("\r\n")),Co[a]=s),window[s+"Factory"](t,To)}}or.createViewModel=Ao;var $o=r.directive("important",{priority:1,getScope:function(e,t){var n=r.vmodels[e];if(n)return n;throw"error! no vmodel called "+e},update:function(e,t,n){if(r.inBrowser){var i=r.vdom(e,"toDOM");1===i.nodeType&&(i.removeAttribute(t),r(i).removeClass("ms-controller"));var o=r.vmodels[n];o&&(o.$element=i,o.$render=this,o.$fire("onReady"),delete o.$events.onReady)}}}),No=$o.update;r.directive("controller",{priority:2,getScope:function(e,t){var n=r.vmodels[e];return n?(n.$render=this,t&&t!==n?or.fuseFactory(t,n):n):t},update:No}),r.directive("skip",{delay:!0});var Eo={},Oo=r.directive("css",{diff:function(e,t){if(Object(e)===e){if(e=or.toJson(e),Array.isArray(e)){var n={};e.forEach(function(e){e&&r.shadowCopy(n,e)}),e=n,Eo[this.type]||(r.warn("ms-"+this.type+"指令的值不建议使用数组形式了！"),Eo[this.type]=1)}var i=!1,o={};if(t){if(this.deep){"number"==typeof this.deep&&this.deep;for(var a in e){if(!_t(e[a],t[a],4))return this.value=e,!0;o[a]=e[a]}}else for(var s in e)e[s]!==t[s]&&(i=!0),o[s]=e[s];for(var c in t)c in o||(i=!0,o[c]="")}else o=e,i=!0;if(i)return this.value=o,!0}return!1},update:function(e,t){var n=e.dom;if(n&&1===n.nodeType){var i=r(n);for(var o in t)i.css(o,t[o])}}}),Mo=Oo.diff,So={TransitionEvent:"transitionend",WebKitTransitionEvent:"webkitTransitionEnd",OTransitionEvent:"oTransitionEnd",otransitionEvent:"otransitionEnd"},Vo=void 0,jo=void 0,Do=void 0,Fo=void 0,Lo=void 0,Po=void 0;for(Fo in So){if(rr[Fo]){jo=So[Fo];break}try{document.createEvent(Fo);jo=So[Fo];break}catch(e){}}"string"==typeof jo&&(Vo=!0,Po=jo),So={AnimationEvent:"animationend",WebKitAnimationEvent:"webkitAnimationEnd"};for(Fo in So)if(rr[Fo]){Do=So[Fo];break}"string"==typeof Do&&(Vo=!0,Lo=Do);var Io=r.directive("effect",{priority:5,diff:function(e){var t=this.node;"string"==typeof e&&(this.value=e={is:e},r.warn("ms-effect的指令值不再支持字符串,必须是一个对象")),this.value=t.effect=e;var n=this;return!!Mo.call(this,e,this.oldValue)&&(setTimeout(function(){t.animating=!0,Io.update.call(n,t,t.effect)}),t.animating=!1,!0)},update:function(e,t,n){var i=e.dom;if(i&&1===i.nodeType){var o=t||n,a=o.is,s=r.effects[a];if(!s)return void r.warn(a+" effect is undefined");var c={},u=Ho[o.action];if("function"!=typeof Bo.prototype[u])return void r.warn("action is undefined");var l=new r.Effect(i);return r.mix(c,s,o,{action:u}),c.queue?(Ro.push(function(){l[u](c)}),Ct()):l[u](c),!0}}}),Ho={true:"enter",false:"leave",enter:"enter",leave:"leave",move:"move",undefined:"enter"},Ro=[];r.effects={},r.effect=function(e,t){var n=r.effects[e]=t||{};return Vo&&!1!==n.css&&(Tt(n,"enterClass",e+"-enter"),Tt(n,"enterActiveClass",n.enterClass+"-active"),Tt(n,"leaveClass",e+"-leave"),Tt(n,"leaveActiveClass",n.leaveClass+"-active")),n};var Bo=function(e){this.dom=e};r.Effect=Bo,Bo.prototype={enter:Nt("Enter"),leave:Nt("Leave"),move:Nt("Move")};var qo=new e(128);r.applyEffect=function(e,t,n){var i=n.cb,o=t.effect;if(o&&e&&1===e.nodeType){var a=n.hook,s=o[a];i&&(Array.isArray(s)?s.push(i):o[a]=s?[s,i]:[i]),Et(n),r.directives.effect.update(t,o,r.shadowCopy({},n))}else i&&i(e)};var zo=r.cssName("transition-duration"),Wo=r.cssName("animation-duration"),Yo=/\d+s$/,Uo="none";r.parseDisplay=Vt,r.directive("visible",{diff:function(e,t){var n=!!e;if(void 0===t||n!==t)return this.value=n,!0},ready:!0,update:function(e,t){var n=e.dom;if(n&&1===n.nodeType){var i,o=n.style.display;t?(o===Uo&&((i=e.displayValue)||(n.style.display="",""===n.style.cssText&&n.removeAttribute("style"))),""===n.style.display&&r(n).css("display")===Uo&&r.contains(n.ownerDocument,n)&&(i=Vt(n))):o!==Uo&&(i=Uo,e.displayValue=o);var a=function(){void 0!==i&&(n.style.display=i)};r.applyEffect(n,e,{hook:t?"onEnterDone":"onLeaveDone",cb:a})}}}),r.directive("text",{delay:!0,init:function(){var e=this.node;e.isVoidTag&&r.error("自闭合元素不能使用ms-text");var t={nodeName:"#text",nodeValue:this.getValue()};e.children.splice(0,e.children.length,t),Gn&&(r.clearHTML(e.dom),e.dom.appendChild(r.vdom(t,"toDOM"))),this.node=t;this.type=this.name="expr";var n=r.directives.expr,i=this;this.callback=function(e){n.update.call(i,i.node,e)}}}),r.directive("expr",{update:function(e,t){t=null==t||""===t?"​":t,e.nodeValue=t,e.dom&&(e.dom.data=t)}}),r.directive("attr",{diff:Mo,update:function(e,t){var n=e.props;for(var r in t)!1==!!t[r]?delete n[r]:n[r]=t[r];var i=e.dom;i&&1===i.nodeType&&re(i,t)}}),r.directive("html",{update:function(e,t){this.beforeDispose(),this.innerRender=r.scan('<div class="ms-html-container">'+t+"</div>",this.vm,function(){var t=this.root;e.children&&(e.children.length=0),e.children=t.children,this.root=e,e.dom&&r.clearHTML(e.dom)})},beforeDispose:function(){this.innerRender&&this.innerRender.dispose()},delay:!0}),r.directive("if",{delay:!0,priority:5,init:function(){this.placeholder=_("if");var e=this.node.props;delete e["ms-if"],delete e[":if"],this.fragment=r.vdom(this.node,"toHTML")},diff:function(e,t){var n=!!e;if(void 0===t||n!==t)return this.value=n,!0},update:function(e,t){if(void 0===this.isShow&&t)return void jt(this,e);this.isShow=t;var n=this.placeholder;if(t){var r=n.parentNode;jt(this,e),r&&r.replaceChild(e.dom,n)}else{this.beforeDispose(),e.nodeValue="if",e.nodeName="#comment",delete e.children;var i=e.dom,r=i&&i.parentNode;e.dom=n,r&&r.replaceChild(n,i)}},beforeDispose:function(){this.innerRender&&this.innerRender.dispose()}}),r.directive("on",{beforeInit:function(){this.getter=r.noop},init:function(){var e=this.node,t=this.name.replace("ms-on-","e").replace("-","_"),n=t+"_"+this.expr.replace(/\s/g,"").replace(/[^$a-z]/gi,function(e){return e.charCodeAt(0)}),i=r.eventListeners[n];if(!i){var o=Qe(this.expr),a=o[0],s=o[1];a=et(a),s&&(s=s.replace(/__value__/g,"$event"),s+="\nif($event.$return){\n\treturn;\n}");var c=["try{","\tvar __vmodel__ = this;","\t"+s,"\treturn "+a,'}catch(e){avalon.log(e, "in on dir")}'].filter(function(e){return/\S/.test(e)});i=new Function("$event",c.join("\n")),i.uuid=n,r.eventListeners[n]=i}var u=r.vdom(e,"toDOM");u._ms_context_=this.vm,this.eventType=this.param.replace(/\-(\d)$/,""),delete this.param,r(u).bind(this.eventType,i)},beforeDispose:function(){r(this.node.dom).unbind(this.eventType)}});var Jo=/^[$a-zA-Z_][$a-zA-Z0-9_]*$/,Go=/^(null|undefined|NaN|window|this|\$index|\$id)$/;r.directive("for",{delay:!0,priority:3,beforeInit:function(){var e,t=this.expr;t=t.replace(/\s+as\s+([$\w]+)/,function(t,n){return!Jo.test(n)||Go.test(n)?r.error("alias "+n+" is invalid --- must be a valid JS identifier which is not a reserved name."):e=n,""});var n=t.split(" in "),i=n[0].match(/[$\w_]+/g);1===i.length&&i.unshift("$key"),this.expr=n[1],this.keyName=i[0],this.valName=i[1],this.signature=r.makeHashCode("for"),e&&(this.asName=e),delete this.param},init:function(){var e=this.userCb;if("string"==typeof e&&e){var t=Qe(e,"for"),n=et(t[0]);this.userCb=new Function("$event","var __vmodel__ = this\nreturn "+n)}this.node.forDir=this,this.fragment=["<div>",this.fragment,"\x3c!--",this.signature,"--\x3e</div>"].join(""),this.cache={}},diff:function(e,t){if(!this.updating){this.updating=!0;var n=Ft(this,e);return void 0===this.oldTrackIds||(this.oldTrackIds!==n?(this.oldTrackIds=n,!0):void 0)}},update:function(){if(this.preFragments?(Pt(this),Ht(this)):(this.fragments=this.fragments||[],Lt(this)),this.userCb){var e=this;setTimeout(function(){e.userCb.call(e.vm,{type:"rendered",target:e.begin.dom,signature:e.signature})},0)}delete this.updating},beforeDispose:function(){this.fragments.forEach(function(e){e.dispose()})}}),r.directive("class",{diff:function(e,t){var n=this.type,r=this.node,i=r.classEvent||{};"hover"===n?(i.mouseenter=Yt,i.mouseleave=Ut):"active"===n&&(i.tabIndex=r.props.tabindex||-1,i.mousedown=Yt,i.mouseup=Ut,i.mouseleave=Ut),r.classEvent=i;var o=Wt(e);if(void 0===typeof t||t!==o)return this.value=o,r["change-"+n]=o,!0},update:function(e,t){var n=e.dom;if(n&&1==n.nodeType){var i=this.type,o="change-"+i,a=e.classEvent;if(a){for(var s in a)"tabIndex"===s?n[s]=a[s]:r.bind(n,s,a[s]);e.classEvent={}}["class","hover","active"].forEach(function(e){if(i===e)if("class"===e)n&&Jt(n,t);else{var a=n.getAttribute(o);a&&r(n).removeClass(a);var s="change-"+e;n.setAttribute(s,t)}})}}}),nr.active=nr.hover=nr.class;var Xo={mouseenter:"change-hover",mouseleave:"change-hover",mousedown:"change-active",mouseup:"change-active"};g(Yt),g(Ut);var Zo={input:function(e){var t=this;e=e||"value";var n=t.dom,r=n[e],i=t.parseValue(r);t.value=r,t.setValue(i),Qt(t);var o=t.pos;n.caret&&t.setCaret(n,o)},radio:function(){var e=this;if(e.isChecked){var t=!e.value;e.setValue(t),Qt(e)}else Zo.input.call(e),e.value=NaN},checkbox:function(){var e=this,t=e.value;Array.isArray(t)||(r.warn("ms-duplex应用于checkbox上要对应一个数组"),t=[t]);var n=e.dom.checked?"ensure":"remove";if(t[n]){var i=e.parseValue(e.dom.value);t[n](i),Qt(e)}this.__test__=t},select:function(){var e=this,t=r(e.dom).val();t+""!=this.value+""&&(t=Array.isArray(t)?t.map(function(t){return e.parseValue(t)}):e.parseValue(t),e.setValue(t),Qt(e))},contenteditable:function(){Zo.input.call(this,"innerHTML")}},Ko=/\|\s*change\b/,Qo=/\|\s*debounce(?:\(([^)]+)\))?/,ea=!0;try{var ta={},na=HTMLInputElement.prototype,ra=HTMLTextAreaElement.prototype,ia=function(e){ta[this.tagName].call(this,e);var t=this._ms_duplex_;!this.caret&&t&&t.isString&&t.duplexCb.call(this,{type:"setter"})},oa=HTMLInputElement.prototype;Object.getOwnPropertyNames(oa),ta.INPUT=Object.getOwnPropertyDescriptor(na,"value").set,Object.defineProperty(na,"value",{set:ia}),ta.TEXTAREA=Object.getOwnPropertyDescriptor(ra,"value").set,Object.defineProperty(ra,"value",{set:ia}),ea=!1}catch(e){}var aa={input:function(){var e=this.node,t=this.value+"";e.dom.value=e.props.value=t},updateChecked:function(e,t){e.dom&&(e.dom.defaultChecked=e.dom.checked=t)},radio:function(){var e,t=this.node,n=t.props.value;e=this.isChecked?!!this.value:this.value+""===n,t.props.checked=e,aa.updateChecked(t,e)},checkbox:function(){var e=this.node,t=e.props,n=t.value+"",r=[].concat(this.value),i=r.some(function(e){return e+""===n});t.defaultChecked=t.checked=i,aa.updateChecked(e,i)},select:function(){var e=Array.isArray(this.value)?this.value.map(String):this.value+"";Gt(this.node,e)},contenteditable:function(){for(var e,t=le(this.value),n=w(),i=0;e=t[i++];){var o=r.vdom(e,"toDOM");n.appendChild(o)}r.clearHTML(this.dom).appendChild(n);var a=this.node.children;a.length=0,Array.prototype.push.apply(a,t),this.duplexCb.call(this.dom)}};y(ln),y(dn),y(fn),y(hn),y(en),y(cn),y(un),y(pn);var sa=function(e){setTimeout(e,0)};if(r.directive("duplex",{priority:9999999,beforeInit:tn,init:nn,diff:rn,update:function(e,t){this.dom||on.call(this,e,sn),gn.call(this,r.msie,ea),aa[this.dtype].call(this)}}),r.__pollValue=gn,r.msie<8){var ca=aa.updateChecked;aa.updateChecked=function(e,t){var n=e.dom;n&&setTimeout(function(){ca(e,t),n.firstCheckedIt=1},n.firstCheckedIt?31:16)}}r.directive("rules",{diff:function(e){if(d(e)){return this.node.rules=or.toJson(e),!0}}});var ua=/^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/i,la=/^(ftp|http|https):\/\/(\w+:{0,1}\w*@)?(\S+)(:[0-9]+)?(\/|\/([\w#!:.?+=&%@!\-\/]))?$/;r.shadowCopy(r.validators,{pattern:{message:"必须匹配{{pattern}}这样的格式",get:function(e,t,n){var r=t.dom,i=t.data;if(!yn(i.pattern)){var o=r.getAttribute("pattern");i.pattern=new RegExp("^(?:"+o+")$")}return n(i.pattern.test(e)),e}},digits:{message:"必须整数",get:function(e,t,n){return n(/^\-?\d+$/.test(e)),e}},number:{message:"必须数字",get:function(e,t,n){return n(!!e&&isFinite(e)),e}},norequired:{message:"",get:function(e,t,n){return n(!0),e}},required:{message:"必须填写",get:function(e,t,n){return n(""!==e),e}},equalto:{message:"密码输入不一致",get:function(e,t,n){var i=String(t.data.equalto);return n(e===(r(document.getElementById(i)).val()||"")),e}},date:{message:"日期格式不正确",get:function(e,t,n){var r=t.data;return n(yn(r.date)?r.date.test(e):bn(e)),e}},url:{message:"URL格式不正确",get:function(e,t,n){return n(la.test(e)),e}},email:{message:"email格式不正确",get:function(e,t,n){return n(ua.test(e)),e}},minlength:{message:"最少输入{{minlength}}个字",get:function(e,t,n){var r=parseInt(t.data.minlength,10);return n(e.length>=r),e}},maxlength:{message:"最多输入{{maxlength}}个字",get:function(e,t,n){var r=parseInt(t.data.maxlength,10);return n(e.length<=r),e}},min:{message:"输入值不能小于{{min}}",get:function(e,t,n){var r=parseInt(t.data.min,10);return n(parseFloat(e)>=r),e}},max:{message:"输入值不能大于{{max}}",get:function(e,t,n){var r=parseInt(t.data.max,10);return n(parseFloat(e)<=r),e}},chs:{message:"必须是中文字符",get:function(e,t,n){return n(/^[\u4e00-\u9fa5]+$/.test(e)),e}}});var da=r.directive("validate",{diff:function(e){var t=this.node;if(!t.validator&&d(e)){t.validator=e,e=or.toJson(e),e.vdom=t,e.dom=t.dom;for(var n in da.defaults)e.hasOwnProperty(n)||(e[n]=da.defaults[n]);return e.fields=e.fields||[],t.vmValidator=e,!0}},update:function(e){function t(){var e=this;e&&da.validateAll.call(e,e.onValidateAll)}var n=e.vmValidator,i=e.validator,o=e.dom;o._ms_validate_=n,_n(e.children,n.fields,n);var a=window.netscape?"keypress":"focusin";r.bind(document,a,An);try{var s=n.onManual=t.bind(n);i.onManual=s}catch(e){r.warn("要想使用onManual方法，必须在validate对象预定义一个空的onManual函数")}delete e.vmValidator,o.setAttribute("novalidate","novalidate"),n.validateAllInSubmit&&r.bind(o,"submit",xn)},validateAll:function(e){var t=this,n=this.vdom,r=t.fields=[];_n(n.children,r,t);var i="function"==typeof e?e:t.onValidateAll,o=t.fields.filter(function(e){var n=e.dom;return n&&!n.disabled&&t.dom.contains(n)}).map(function(e){return da.validate(e,!0)}),a={};return Promise.all(o).then(function(e){var r=e.concat.apply([],e);t.deduplicateInValidateAll&&(r=r.filter(function(e){var t=e.element,n=t.uniqueID||(t.uniqueID=setTimeout("1"));return!a[n]&&(a[n]=!0)})),i.call(n.dom,r)})},validate:function(e,t,n){var i=[],o=e.value,a=e.dom;if("function"!=typeof Promise&&r.warn("浏览器不支持原生Promise,请下载并<script src=url>引入\nhttps://github.com/RubyLouvre/avalon/blob/master/test/promise.js"),!a.disabled){var s=e.vdom.rules,c=[],u=!0;if(!s.norequired||""!==o)for(var l in s){var d=s[l];if(!1!==d){var f,h=r.validators[l];i.push(new Promise(function(e,t){f=e}));var p=function(t){var n={element:a,data:e.data,message:a.getAttribute("data-"+l+"-message")||a.getAttribute("data-message")||h.message,validateRule:l,getMessage:Tn};t?f(!0):(u=!1,c.push(n),f(!1))};e.data={},e.data[l]=d,h.get(o,e,p)}}return Promise.all(i).then(function(r){if(!t){var i=e.validator;u?i.onSuccess.call(a,[{data:e.data,element:a}],n):i.onError.call(a,c,n),i.onComplete.call(a,c,n)}return c})}}}),fa=/\\?{{([^{}]+)\}}/gm;da.defaults={validate:da.validate,onError:r.noop,onSuccess:r.noop,onComplete:r.noop,onManual:r.noop,onReset:r.noop,onValidateAll:r.noop,validateInBlur:!0,validateInKeyup:!0,validateAllInSubmit:!0,resetInFocus:!0,deduplicateInValidateAll:!1};var ha=r.oneObject("animationend,blur,change,input,click,dblclick,focus,keydown,keypress,keyup,mousedown,mouseenter,mouseleave,mousemove,mouseout,mouseover,mouseup,scan,scroll,submit","on"),pa=/[+-\?]/,va=/__value__\)$/,ma={"#text":1,"#comment":1,script:1,style:1,noscript:1};r.scan=function(e,t,n){return new Fn(e,t,n||r.noop)},Fn.prototype={init:function(){var e;if(this.root&&this.root.nodeType>0)e=Ee(this.root),Vn(this.root);else{if("string"!=typeof this.root)return r.warn("avalon.scan first argument must element or HTML string");e=le(this.root)}this.root=e[0],this.vnodes=e,this.scanChildren(e,this.vm,!0)},scanChildren:function(e,t,n){for(var r=0;r<e.length;r++){var i=e[r];switch(i.nodeName){case"#text":t&&this.scanText(i,t);break;case"#comment":t&&this.scanComment(i,t,e);break;case"#document-fragment":this.scanChildren(i.children,t,!1);break;default:this.scanTag(i,t,e,!1)}}n&&this.complete()},scanText:function(e,t){x.rexpr.test(e.nodeValue)&&this.bindings.push([e,t,{nodeValue:e.nodeValue}])},scanComment:function(e,t,n){Dn(e.nodeValue,"ms-for:")&&this.getForBinding(e,t,n)},scanTag:function(e,t,i,o){var a,s,c={},u=e.props;for(var l in u){var d=u[l],f=l;if(":"===l.charAt(0)&&(l="ms-"+l.slice(1)),Dn(l,"ms-")){c[l]=d;var h=l.match(/\w+/g)[1];h=ha[h]||h,nr[h]||r.warn(l+" has not registered!"),a=!0}"ms-for"===l&&(s=d,delete u[f])}var p=c["ms-important"]||c["ms-controller"];if(p){var v=r.serverTemplates,m=v&&v[p];if(m){r.log("前端再次渲染后端传过来的模板");var g=le(m)[0];for(var y in g)e[y]=g[y];return delete v[p],void this.scanTag(e,t,i,o)}var h=c["ms-important"]===p?"important":"controller",b="ms-"+h in u?"ms-"+h:":"+h;Gn&&delete u[b];var w=nr[h];if(!(t=w.getScope.call(this,p,t)))return;var x=u.class;x&&(u.class=(" "+x+" ").replace(" ms-controller ","").trim());var _=this;t.$render=_,this.callbacks.push(function(){w.update.call(_,e,b,p)})}if(s)return e.dom&&e.dom.removeAttribute(f),this.getForBindingByElement(e,t,i,s);/^ms\-/.test(e.nodeName)&&(u.is=e.nodeName),u.is&&(c["ms-widget"]||(c["ms-widget"]="{}"),a=!0),a&&this.bindings.push([e,t,c]);var A=e.children;!Li[e.nodeName]&&A&&A.length&&!n(c)&&this.scanChildren(A,t,!1)},complete:function(){if(this.yieldDirectives(),this.beforeReady(),Gn){var e=this.root;if(Gn){Sn(r.vdom(e,"toDOM"),e.children)}}this.mount=!0;for(var t;t=this.callbacks.pop();)t();this.optimizeDirectives()},yieldDirectives:function(){for(var e;e=this.bindings.shift();){var t=e[0],n=e[1],r=e[2],i=[];"nodeValue"in r?i=On(r):"ms-skip"in r||(i=Nn(r,e));for(var o,a=0;o=i[a++];){var s=nr[o.type];if(Gn||!/on|duplex|active|hover/.test(o.type)){s.beforeInit&&s.beforeInit.call(o);var c=new $n(n,o,t,this);this.directives.push(c)}}}},optimizeDirectives:function(){for(var e,t=0;e=this.directives[t++];)e.callback=nr[e.type].update,e.update=Ln,e._isScheduled=!1},update:function(){for(var e,t=0;e=this.directives[t++];)e.update()},dispose:function(){for(var e,t=this.directives||[],n=0;e=t[n++];)e.dispose();for(var r in this)"dispose"!==r&&delete this[r]},getForBinding:function(e,t,n,i){var o=e.nodeValue.replace("ms-for:","").trim();e.nodeValue="ms-for:"+o;var a=jn(n,e),s=a.end,c=r.vdom(a,"toHTML");n.splice(a.start,a.length),e.props={},this.bindings.push([e,t,{"ms-for":o},{begin:e,end:s,expr:o,userCb:i,fragment:c,parentChildren:n}])},getForBindingByElement:function(e,t,n,r){var i=n.indexOf(e),o=e.props,a={nodeName:"#comment",nodeValue:"ms-for:"+r};o.slot&&(a.slot=o.slot,delete o.slot);var s={nodeName:"#comment",nodeValue:"ms-for-end:"};n.splice(i,1,a,e,s),this.getForBinding(a,t,n,o["data-for-rendered"])}};var ga,ya=r.oneObject("onInit,onReady,onViewChange,onDispose,onEnter,onLeave"),ba=[];return r.directive("widget",{delay:!0,priority:4,deep:!0,init:function(){var e=this.node;if(this.cacheVm=!!e.props.cached,e.dom&&"#comment"===e.nodeName)var t=e.dom;var n=this.getValue(),i=Pn(n),o=e.props.is||i.is;this.is=o;var a=r.components[o];if(!("fragment"in this))if(e.isVoidTag)this.fragment=!1;else{var s=e.children[0];s&&s.nodeValue?this.fragment=s.nodeValue:this.fragment=r.vdom(e.children,"toHTML")}if(!a)return this.readyState=0,e.nodeName="#comment",e.nodeValue="unresolved component placeholder",delete e.dom,void r.Array.ensure(ba,this);var c=i.id||i.$id,u=r.vmodels[c],l=!1;if(u)d=u,this.comVm=d,In(this,d.$render),l=!0;else{"function"==typeof a&&(a=new a(i));var d=Rn(a,i,o);this.readyState=1,Hn(d,e,"Init"),this.comVm=d;var f=r.scan(a.template,d);d.$render=f,In(this,f);var h=[],p=[];if(this.fragment||a.soleSlot){var v=this.fragment?this.vm:d,m=this.fragment||"{{##"+a.soleSlot+"}}",g=r.scan("<div>"+m+"</div>",v,function(){h=this.root.children});p=g.directives,this.childBoss=g;for(var y in g)delete g[y]}Array.prototype.push.apply(f.directives,p);var b=[],w={};a.soleSlot?b=h:h.forEach(function(e,t){if(e.slot){var n=jn(h,e);n.push(n.end),n.unshift(e),w[e.slot]=n}else if(e.props){var r=e.props.slot;r&&(delete e.props.slot,Array.isArray(w[r])?w[r].push(e):w[r]=[e])}}),a.soleSlot?zn(f.vnodes,b):Wn(f.vnodes,w)}if(t){var x=r.vdom(e,"toDOM");t.parentNode.replaceChild(x,t),d.$element=f.root.dom=x,delete this.reInit}Vn(e.dom),d.$element=e.dom,Sn(e.dom,e.children),l?Hn(d,e,"Enter"):Hn(d,e,"Ready")},diff:function(e,t){if(Mo.call(this,e,t))return!0},update:function(e,t){switch(this.readyState){case 0:this.reInit&&(this.init(),this.readyState++);break;case 1:this.readyState++;break;default:this.readyState++;var n=this.comVm;r.viewChanging=!0,r.transaction(function(){for(var e in t)n.hasOwnProperty(e)&&(Array.isArray(t[e])?n[e]=t[e].concat():n[e]=t[e])}),Hn(n,e,"ViewChange"),delete r.viewChanging}this.value=r.mix(!0,{},t)},beforeDispose:function(){var e=this.comVm;this.cacheVm?Hn(e,this.node,"Leave"):(Hn(e,this.node,"Dispose"),e.$hashcode=!1,delete r.vmodels[e.$id],this.innerRender&&this.innerRender.dispose())}}),r.components={},r.component=function(e,t){return t.extend=Un,Yn(e,t)},r});
//# sourceMappingURL=avalon.min.js.map