module.exports = {
  PJSamplingPlanApprove331: {
    default_form_component_values: [
      {
        name: '评价依据是否全面、有效',
        value: '是',
      },
      {
        name: '评价范围与内容是否全面，评价单元划分是否合理',
        value: '是',
      },
      {
        name: '评价方法是否合理',
        value: '是',
      },
      {
        name: '职业病危害因素识别与接触分析是否准确',
        value: '是',
      },
      {
        name: '重点评价的职业病危害因素筛选是否正确',
        value: '是',
      },
      {
        name: '职业卫生调查、现场采样与测量计划是否全面',
        value: '是',
      },
      {
        name: '工作组织及进度安排是否合理',
        value: '是',
      },
    ],
  },
  JCSamplingPlanApprove331: {
    default_form_component_values: [
      {
        name: '采样与测量计划要素的是否全面性',
        value: '是',
      },
      {
        name: '检测工作场所、岗位（工种）及职业病危害因素是否全面性',
        value: '是',
      },
      {
        name: '仪器设备、空气收集器和现场采样与测量需求是否匹配',
        value: '是',
      },
      {
        name: '采样与测量地点(对象或点位)、时机、方式、时间以及样品数量是否合理',
        value: '是',
      },
    ],
  },
  // 检测报告审核记录
  JCReportReviewRecord331: {
    default_form_component_values: [
      {
        name: '检测依据的全面性和有效性；',
        value: '是',
      },
      {
        name: '检测范围的全面性;',
        value: '是',
      },
      {
        name: '用人单位情况调查要素的全面性，与实际情况的一致性；',
        value: '是',
      },
      {
        name: '现场采样与测量的全面性；',
        value: '是',
      },
      {
        name: '检测结果及接触水平评价的准确性；',
        value: '是',
      },
      {
        name: '超标岗位原因分析的准确性；',
        value: '是',
      },
      {
        name: '检测结论的准确性；',
        value: '是',
      },
      {
        name: '建议的合理性和可行性',
        value: '是',
      },
    ],
  },
};
