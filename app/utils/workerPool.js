'use strict';

const { Worker } = require('worker_threads');
const path = require('path');

/**
 * Worker线程池管理器
 * 用于管理多个worker线程并发处理福州数据文件
 */
class WorkerPool {
  constructor(options = {}) {
    this.poolSize = options.poolSize || require('os').cpus().length;
    this.workerScript = options.workerScript || path.join(__dirname, '../workers/fzDataWorker.js');
    this.workers = [];
    this.activeJobs = new Map();
    this.jobQueue = [];
    this.jobIdCounter = 0;
    this.isInitialized = false;
  }

  /**
   * 初始化线程池
   */
  async initialize() {
    if (this.isInitialized) return;

    for (let i = 0; i < this.poolSize; i++) {
      const worker = new Worker(this.workerScript);
      worker.isAvailable = true;
      worker.workerId = i;

      worker.on('message', result => {
        this.handleWorkerMessage(worker, result);
      });

      worker.on('error', error => {
        this.handleWorkerError(worker, error);
      });

      worker.on('exit', code => {
        if (code !== 0) {
          console.error(`Worker ${worker.workerId} 异常退出，退出码: ${code}`);
          this.restartWorker(worker);
        }
      });

      this.workers.push(worker);
    }

    this.isInitialized = true;
    console.log(`Worker线程池已初始化，池大小: ${this.poolSize}`);
  }

  /**
   * 重启worker
   * @param oldWorker
   */
  async restartWorker(oldWorker) {
    const workerId = oldWorker.workerId;
    const index = this.workers.indexOf(oldWorker);

    if (index !== -1) {
      const newWorker = new Worker(this.workerScript);
      newWorker.isAvailable = true;
      newWorker.workerId = workerId;

      newWorker.on('message', result => {
        this.handleWorkerMessage(newWorker, result);
      });

      newWorker.on('error', error => {
        this.handleWorkerError(newWorker, error);
      });

      this.workers[index] = newWorker;
      console.log(`Worker ${workerId} 已重启`);
    }
  }

  /**
   * 处理worker消息
   * @param worker
   * @param result
   */
  handleWorkerMessage(worker, result) {
    const job = this.activeJobs.get(worker);
    if (job) {
      worker.isAvailable = true;
      this.activeJobs.delete(worker);

      if (result.success) {
        job.resolve(result.result);
      } else {
        job.reject(new Error(result.error));
      }

      // 处理队列中的下一个任务
      this.processNextJob();
    }
  }

  /**
   * 处理worker错误
   * @param worker
   * @param error
   */
  handleWorkerError(worker, error) {
    const job = this.activeJobs.get(worker);
    if (job) {
      worker.isAvailable = true;
      this.activeJobs.delete(worker);
      job.reject(error);
      this.processNextJob();
    }
  }

  /**
   * 执行任务
   * @param task
   */
  async execute(task) {
    return new Promise((resolve, reject) => {
      const jobId = ++this.jobIdCounter;
      const job = {
        id: jobId,
        task,
        resolve,
        reject,
        createdAt: Date.now(),
      };

      this.jobQueue.push(job);
      this.processNextJob();
    });
  }

  /**
   * 处理下一个任务
   */
  processNextJob() {
    if (this.jobQueue.length === 0) return;

    const availableWorker = this.workers.find(w => w.isAvailable);
    if (!availableWorker) return;

    const job = this.jobQueue.shift();
    availableWorker.isAvailable = false;
    this.activeJobs.set(availableWorker, job);

    availableWorker.postMessage(job.task);
  }

  /**
   * 获取池状态
   */
  getStatus() {
    const available = this.workers.filter(w => w.isAvailable).length;
    const busy = this.workers.length - available;

    return {
      poolSize: this.poolSize,
      available,
      busy,
      queueLength: this.jobQueue.length,
      activeJobs: this.activeJobs.size,
    };
  }

  /**
   * 销毁线程池
   */
  async destroy() {
    // 等待所有活跃任务完成
    while (this.activeJobs.size > 0) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    // 终止所有worker
    await Promise.all(this.workers.map(worker => worker.terminate()));
    this.workers = [];
    this.activeJobs.clear();
    this.jobQueue = [];
    this.isInitialized = false;

    console.log('Worker线程池已销毁');
  }
}

module.exports = WorkerPool;
