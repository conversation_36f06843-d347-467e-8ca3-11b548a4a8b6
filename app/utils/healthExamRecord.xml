<?xml version="1.0" encoding="utf-8"?>
<!--数据交换格式如下-->
<DataExchange>
<!--头部开始-->
<Header>
<!--文档标识 ID-->
<DocumentId>机构编码-业务类别代码-当前时间（yyyyMMddHHmmssSSS）-5 位随机码</DocumentId>
<!--数据操作类型 Add,Update,Delete-->
<OperateType>Add</OperateType>
<!--业务活动标识 职业病系统-->
<BusinessActivityIdentification>NEWOMAR</BusinessActivityIdentification>
<!--上报机构代码-->
<ReportOrgCode>120102001</ReportOrgCode>
<!--上报机构授权码-->
<License>XXXXXXXXXXXXXX</License>
<!--上报地区代码-->
<ReportZoneCode>120102000</ReportZoneCode>
</Header>
<!--头部结束-->

<!--主体开始-->
<EventBody>
<HEALTH_EXAM_RECORD_LIST>
<!-- 可重复多个 -->
<HEALTH_EXAM_RECORD ID="数据唯一标识">
<!--用人单位信息开始-->
<ENTERPRISE_INFO>
<!--用人单位名称-->
<ENTERPRISE_NAME>xxx</ENTERPRISE_NAME>
<!--统一社会信用代码(18 位)-->
<CREDIT_CODE>11111111111111111</CREDIT_CODE>
<!-- 企业类型代码 -->
<ECONOMIC_TYPE_CODE>0101</ECONOMIC_TYPE_CODE>
<!--行业类别代码-->
<INDUSTRY_CATEGORY_CODE>01</INDUSTRY_CATEGORY_CODE>
<!--企业规模代码-->
<BUSINESS_SCALE_CODE>01</BUSINESS_SCALE_CODE>
<!--所属地区代码-->
<ADDRESS_CODE>10000000</ADDRESS_CODE>
<!--用人单位详细地址-->
<ADDRESS_DETAIL>xx 路 xx 号</ADDRESS_DETAIL>
<!--用人单位地址邮政代码-->
<ADDRESS_ZIP_CODE>100000</ADDRESS_ZIP_CODE>
<!--用人单位联系人名称-->
<ENTERPRISE_CONTACT>张 xx</ENTERPRISE_CONTACT>
<!--用人单位联系人电话-->
<CONTACT_TELPHONE>131xxxxxxxx</CONTACT_TELPHONE>
<!--用人单位所在区全名称（如：北京市市辖区海淀区）-->
<ALL_NAME>北京市市辖区海淀区</ALL_NAME>
</ENTERPRISE_INFO>
<!--用人单位信息结束-->

<!--用工单位信息开始-->
<ENTERPRISE_INFO_EMPLOYER>
<!-- 用工单位名称 -->
<ENTERPRISE_NAME_EMPLOYER>xxx</ENTERPRISE_NAME_EMPLOYER>
<!--用工单位统一社会信用代码(18 位)-->
<CREDIT_CODE_EMPLOYER>11111111111111111</CREDIT_CODE_EMPLOYER>
<!-- 用工单位企业类型代码 -->
<ECONOMIC_TYPE_CODE_EMPLOYER>0101</ECONOMIC_TYPE_CODE_EMPLOYER>
<!--用工单位行业类别代码-->
<INDUSTRY_CATEGORY_CODE_EMPLOYER>01</INDUSTRY_CATEGORY_CODE_EMPLOYER>
<!--用工单位企业规模代码-->
<BUSINESS_SCALE_CODE_EMPLOYER>01</BUSINESS_SCALE_CODE_EMPLOYER>
<!--用工单位所属地区代码-->
<ADDRESS_CODE_EMPLOYER>10000000</ADDRESS_CODE_EMPLOYER>
<!--用工单位所在区全名称（如：北京市市辖区海淀区）-->
<ALL_NAME_EMPLOYER>北京市市辖区海淀区</ALL_NAME_EMPLOYER>
</ENTERPRISE_INFO_EMPLOYER>
<!--用工单位信息结束-->

<!--劳动者信息开始-->
<WORKER_INFO>
<!-- 姓名 -->
<WORKER_NAME>王某</WORKER_NAME>
<!-- 身份证件类型代码 -->
<ID_CARD_TYPE_CODE>01</ID_CARD_TYPE_CODE>
<!-- 身份证件号码 -->
<ID_CARD>410xxxxxxxxxxxxxxxxxx</ID_CARD>
<!-- 性别代码-->
<GENDER_CODE>1</GENDER_CODE>
<!-- 出生日期-->
<BIRTH_DATE>19900101</BIRTH_DATE>
<!--联系电话-->
<WORKER_TELPHONE>131xxxxxxxx</WORKER_TELPHONE>
</WORKER_INFO>
<!--劳动者信息结束-->
<!--个人生活史开始-->
<PERSONAL_HISTORY>
<!--目前吸烟情况-->
<SMOKING_STATUS>1</SMOKING_STATUS>
<!--吸烟史-年-->
<PERSONAL_HISTORY_YEAR>2</PERSONAL_HISTORY_YEAR>
<!--吸烟史-月-->
<PERSONAL_HISTORY_MONTH>1</PERSONAL_HISTORY_MONTH>
<!--平均每天吸烟量-->
<DAILY_SMOKING_VOLUME>10</DAILY_SMOKING_VOLUME>
</PERSONAL_HISTORY>
<!--个人生活史结束-->

<!--监测类型代码-->
<JC_TYPE>01</JC_TYPE>
<!--体检类型代码-->
<EXAM_TYPE_CODE>01</EXAM_TYPE_CODE>
<!--体检日期-->
<EXAM_DATE>20200101</EXAM_DATE>
<!--接触的职业病危害因素代码-->
<CONTACT_FACTOR_CODE>1001,3008</CONTACT_FACTOR_CODE>
<!--接触的其他职业病危害因素-->
<CONTACT_FACTOR_OTHER>xxxx</CONTACT_FACTOR_OTHER>
<!-- 体检危害因素代码 -->
<FACTOR_CODE>1001,3008</FACTOR_CODE>
<!--其他危害因素具体名称（如果危害因素选择其他，需要在这里填写具体的危害因素名称）-->
<FACTOR_OTHER>xxxx</FACTOR_OTHER>
<!--工种代码-->
<WORK_TYPE_CODE>00-13</WORK_TYPE_CODE>
<!--其他工种名称（工种选择其他时，需要在这里保存具体工种的名字）-->
<OTHER_WORK_TYPE>xxxx</OTHER_WORK_TYPE>
<!--是否复查（0 否、1 是）-->
<IS_REVIEW>0</IS_REVIEW>
<!--创建地区代码-->
<AREA_CODE>100000000</AREA_CODE>
<!--创建机构代码-->
<ORG_CODE>100000001</ORG_CODE>

<!--体检结论信息开始-->
<EXAM_CONCLUSION_LIST>
<!--每个危害因素对应一个结论，各类粉尘只需填写一个大类粉尘的结论即可 -->
<EXAM_CONCLUSION>
<!-- 职业病危害因素代码 -->
<ITAM_CODE>1</ITAM_CODE>
<!--危害因素名称-->
<ITAM_NAME>xxxx</ITAM_NAME>
<!-- 体检结论代码 -->
<EXAM_CONCLUSION_CODE>01</EXAM_CONCLUSION_CODE>
<!--疑似职业病代码（见字典职业病类型，当体检结论位疑似职业病时，需填写该字段）-->
<YSZYB_CODE>01</YSZYB_CODE>
<!--接触相应职业病危害因素的用人单位名称（当体检类型是上岗前，体检结论是疑似职业病时，需填写该字段）-->
<ENTERPRISE_NAME>xxx</ENTERPRISE_NAME>
<!--职业禁忌证名称（当体检结论是职业禁忌证时，需填写该字段）-->
<ZYJJZ_NAME>xxxxxx</ZYJJZ_NAME>
<!--其他疾病或异常描述（当体检结论是其他疾病或异常时，需填写该字段）-->
<QTJB_NAME>xxxxxx</QTJB_NAME>
<!--开始接害日期（当体检类型是上岗前时非必填；当体检的危害因素是混合接触时，开始接害日期和接害工龄要分别按危害因素填写，各类粉尘只需填写一次）-->
<HARM_START_DATE>20200101</HARM_START_DATE>
<!-- 实际接害工龄-年（同上） -->
<HARM_AGE_YEAR>20</HARM_AGE_YEAR>
<!-- 实际接害工龄-月（同上） -->
<HARM_AGE_MONTH>10</HARM_AGE_MONTH>
</EXAM_CONCLUSION>
</EXAM_CONCLUSION_LIST>
<!--体检结论信息结束-->

<!--检查项目信息开始-->
<EXAM_ITEM_RESULT_LIST>
<EXAM_ITEM_RESULT>
<!--检查项目父级名称（检查指标名称一级节点名称）-->
<EXAM_ITEM_PNAME>xxxx</EXAM_ITEM_PNAME>
<!--检查项目名称-->
<EXAM_ITEM_NAME>xxxxxx</EXAM_ITEM_NAME>
<!--检查项目代码-->
<EXAM_ITEM_CODE>1001</EXAM_ITEM_CODE>
<!--检查结果类型代码-->
<EXAM_RESULT_TYPE>01</EXAM_RESULT_TYPE>
<!-- 符号代码（小于或者等于） -->
<DATA_VERSION>0</DATA_VERSION>
<!--检查结果-->
<EXAM_RESULT>xxxx</EXAM_RESULT>
<!--检查项目计量单位及参考值范围（只有检查结果为数字类型的需要填写，没有时可不填写-）-->
<EXAM_ITEM_UNIT_CODE>xxx</EXAM_ITEM_UNIT_CODE>
<!--参考值范围小值（只有检查结果为数字类型的需要填写，没有时可不填写-）-->
<REFERENCE_RANGE_MIN>1</REFERENCE_RANGE_MIN>
<!--参考值范围大值（只有检查结果为数字类型的需要填写，没有时可不填写-）-->
<REFERENCE_RANGE_MAX>10</REFERENCE_RANGE_MAX>
<!--是否异常（0 正常、1 异常、3 未检查；当检查项目是胸片时，见胸片检查结果代码表）-->
<ABNORMAL>0</ABNORMAL>
</EXAM_ITEM_RESULT>
</EXAM_ITEM_RESULT_LIST>
<!--检查项目信息结束-->

<!--填表人姓名-->
<WRITE_PERSON>xxx</WRITE_PERSON>
<!--填表人电话-->
<WRITE_PERSON_TELPHONE>131xxxxxxxx</WRITE_PERSON_TELPHONE>
<!-- 报告出具日期 -->
<WRITE_DATE>20200101</WRITE_DATE>
<!-- 检查单位名称 -->
<REPORT_ORGAN_CREDIT_CODE>xxxx</REPORT_ORGAN_CREDIT_CODE>
<!--报告人姓名-->
<REPORT_PERSON>xxx</REPORT_PERSON>
<!--报告人联系电话-->
<REPORT_PERSON_TEL>131xxxxxxxx</REPORT_PERSON_TEL>
<!--报告日期-->
<REPORT_DATE>20200101</REPORT_DATE>
<!--报告单位名称-->
<REPORT_UNIT>xxxx</REPORT_UNIT>
<!--备注-->
<REMARK>xxxx</REMARK>

<!-- 审核信息开始 -->
<AUDIT_INFO>
<!-- 审核状态代码（当上传方式为通过系统 xml 上传时，审核状态不能为 07 或 09；当上传方式为通过国家数据交换平台上传时，审核状态只能为 07）-->
<AUDITSTATUS>07</AUDITSTATUS>
<!-- 审核意见 -->
<AUDITINFO>xxx</AUDITINFO>
<!-- 审核时间 -->
<AUDITDATE>20200101</AUDITDATE>
<!-- 审核人姓名 -->
<AUDITOR_NAME>张三</AUDITOR_NAME>
<!-- 审核机构 -->
<AUDIT_ORG>xxx</AUDIT_ORG>
</AUDIT_INFO>
<!-- 审核信息结束 -->
</HEALTH_EXAM_RECORD>
</HEALTH_EXAM_RECORD_LIST>
</EventBody>
<!--主体结束-->
</DataExchange>