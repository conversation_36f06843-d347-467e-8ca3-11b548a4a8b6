'use strict';

const axios = require('axios');
const moment = require('moment');
const fs = require('fs').promises; // 使用 fs.promises
const path = require('path');

class whPortalTaskUtil {
  constructor(ctx) {
    this.ctx = ctx;
    this.baseUrl = `${ctx.app.config.whRequest.host}/RESTAdapter/118/CommonData`; // 生产环境 REST 接口地址
    this.config = ctx.app.config;
  }

  /**
   * 构造待办/已办/申请的 REST 请求报文
   * @param {Object} taskData - 任务数据
   * @param {string} sourceId - 来源系统标识，如 HSE
   * @param {number} size - 发送的 DATAROW 数量
   * @return {Object} REST 请求报文
   */
  buildTaskPayload(taskData, sourceId = 'OHS', size = 1) {
    const currentTime = moment().format('YYYY-MM-DD HH:mm:ss');
    return {
      arg0: {
        HEADER: {
          SOURCEID: sourceId,
          DESTINATIONID: 'PORTAL',
          ACTION: 'CREATE',
          SIZE: size.toString(),
          DATE: currentTime,
          BO: 'TASK',
        },
        REQUEST: {
          DATAROW: Array.isArray(taskData) ? taskData : [ taskData ],
        },
      },
    };
  }

  /**
   * 发送请求到 Portal 接口
   * @param {Object} payload - 请求报文
   * @return {Promise<Object>} 接口响应
   */
  async sendTaskRequest(payload) {
    try {
      if (!this.config || !this.config.whRequest) {
        throw new Error('whRequest configuration is missing');
      }

      // 将 payload 保存到日志目录
      const logDir = path.join(this.ctx.app.baseDir, 'logs');
      const logFile = path.join(logDir, `payload_${moment().format('YYYY-MM-DD_HH-mm-ss')}.json`);

      try {
        // 确保日志目录存在
        await fs.mkdir(logDir, { recursive: true });
        // 写入 payload
        await fs.writeFile(logFile, JSON.stringify(payload, null, 2));
      } catch (writeError) {
        this.ctx.logger.error('Failed to write payload log:', writeError);
      }
      console.log(payload, 'payload');
      const response = await axios.post(this.baseUrl, payload, {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Basic ${Buffer.from(
            `${this.config.whRequest.username}:${this.config.whRequest.password}`
          ).toString('base64')}`,
        },
      });
      return {
        success: true,
        data: response.data,
      };
    } catch (error) {
      this.ctx.logger.error('Failed to send task request to Portal:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * 构造多语言字段
   * @param {string} zh - 中文描述
   * @param {string} en - 英文描述 (可选)
   * @param {string} hu - 匈文描述 (可选)
   * @return {string} 多语言格式字符串
   */
  buildMultiLangField(zh, en = '', hu = '') {
    return `ZH@!${zh}_@!@_EN@!${en}_@!@_HU@!${hu}`;
  }
}

module.exports = whPortalTaskUtil;
