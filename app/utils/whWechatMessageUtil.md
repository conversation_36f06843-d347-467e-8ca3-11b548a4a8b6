# whWechatMessageUtil 使用说明

## 概述
whWechatMessageUtil 是微信公众号《万华流程审批》消息推送工具类，用于向微信公众号发送流程审批相关消息。

## 配置设置

首先需要在 `config/config.default.js` 中添加配置：

```javascript
// 微信公众号消息推送配置

  testUrl: 'https://apigwqas.whchem.com:643/moa_api_msg', // 测试环境地址
  prodUrl: '', // 生产环境地址（上线前提供）
  apiKey: '7ea6dcfbdb7f43eea6a0cd6b6bc2a5c8', // X-Ca-API-Key，每个系统唯一 
```

## 基本使用

### 1. 引入工具类

```javascript
const whWechatMessageUtil = require('../utils/whWechatMessageUtil');
```

### 2. 发送单条消息

```javascript
async function sendSingleMessage(ctx) {
  const messageUtil = new whWechatMessageUtil(ctx);
  
  const messageData = {
    taskid: 'https://example.com/task/12345',
    bpdname: '安全检查流程@Safety Check Process',
    activityname: '部门主管审核@Department Manager Review',
    fromid: 'zhangsan',
    fromname: '张三',
    toid: 'lisi',
    toname: '李四',
    action: '1', // 1=提交, 2=驳回, 3=加签, 4=转办, 99=未知
    ctime: '2023-12-01 14:30:00',
    // 可选字段
    bpdid: 'SAFETY_001',
    activityid: 'APPROVE_001',
    tstaskid: 'TASK_001',
    tspiid: 'PROC_001',
    mpappid: '', // 小程序appid
    mppagepath: '', // 小程序页面路径
  };
  
  const result = await messageUtil.sendMessage('HSE', messageData);
  
  if (result.success) {
    console.log('消息发送成功:', result.data);
  } else {
    console.log('消息发送失败:', result.error);
  }
}
```

### 3. 发送多条消息

```javascript
async function sendMultipleMessages(ctx) {
  const messageUtil = new whWechatMessageUtil(ctx);
  
  const messageList = [
    {
      taskid: 'https://example.com/task/12345',
      bpdname: '安全检查流程@Safety Check Process',
      activityname: '部门主管审核@Department Manager Review',
      fromid: 'zhangsan',
      fromname: '张三',
      toid: 'lisi',
      toname: '李四',
      action: '1',
    },
    {
      taskid: 'https://example.com/task/12346',
      bpdname: '设备维修申请@Equipment Repair Request',
      activityname: '技术部审核@Technical Review',
      fromid: 'wangwu',
      fromname: '王五',
      toid: 'zhaoliu',
      toname: '赵六',
      action: '2',
    },
  ];
  
  const result = await messageUtil.sendMessage('HSE', messageList);
  console.log('批量发送结果:', result);
}
```

### 4. 批量发送（分批处理）

```javascript
async function sendBatchMessages(ctx) {
  const messageUtil = new whWechatMessageUtil(ctx);
  
  // 大量消息数据
  const largeMessageList = [
    // ... 100条消息
  ];
  
  // 分批发送，每批10条
  const result = await messageUtil.sendBatchMessages('HSE', largeMessageList, 10);
  
  console.log(`批量发送完成: 总数${result.total}, 成功${result.successCount}, 失败${result.failCount}`);
  console.log('详细结果:', result.results);
}
```

## 工具方法

### 1. 构造双语描述

```javascript
const messageUtil = new whWechatMessageUtil(ctx);

// 自动截断超长文本并构造双语格式
const bilingualName = messageUtil.buildBilingualName(
  '这是一个很长的中文名称可能超过二十个字符',
  'This is a very long English name that might exceed twenty characters'
);
console.log(bilingualName); // "这是一个很长的中文名称可能超过二十@This is a very long E"
```

### 2. 格式化时间

```javascript
const messageUtil = new whWechatMessageUtil(ctx);

// 使用当前时间
const currentTime = messageUtil.formatDateTime();
console.log(currentTime); // "2023-12-1 14:30:25"

// 使用指定时间
const specifiedTime = messageUtil.formatDateTime(new Date('2023-12-01T14:30:00'));
console.log(specifiedTime); // "2023-12-1 14:30:00"
```

## 在 Service 中使用

```javascript
// app/service/wechatMessage.js
'use strict';

const Service = require('egg').Service;
const whWechatMessageUtil = require('../utils/whWechatMessageUtil');

class WechatMessageService extends Service {
  
  /**
   * 发送流程审批消息
   * @param {Object} processData - 流程数据
   */
  async sendProcessMessage(processData) {
    const messageUtil = new whWechatMessageUtil(this.ctx);
    
    const messageData = {
      taskid: `${this.config.domain}/process/handle/${processData.id}`,
      bpdname: messageUtil.buildBilingualName(processData.processName, processData.processNameEn),
      activityname: messageUtil.buildBilingualName(processData.activityName, processData.activityNameEn),
      fromid: processData.fromUserId,
      fromname: processData.fromUserName,
      toid: processData.toUserId,
      toname: processData.toUserName,
      action: processData.action || '1',
      ctime: messageUtil.formatDateTime(processData.createTime),
    };
    
    const result = await messageUtil.sendMessage('HSE', messageData);
    
    if (!result.success) {
      this.ctx.logger.error('微信消息发送失败', result);
      throw new Error(`微信消息发送失败: ${result.error}`);
    }
    
    return result;
  }
}

module.exports = WechatMessageService;
```

## 在 Controller 中使用

```javascript
// app/controller/api/wechatMessage.js
'use strict';

const Controller = require('egg').Controller;

class WechatMessageController extends Controller {
  
  /**
   * 发送审批消息
   */
  async sendApprovalMessage() {
    const { ctx } = this;
    
    try {
      const result = await ctx.service.wechatMessage.sendProcessMessage(ctx.request.body);
      
      ctx.body = {
        code: 200,
        message: '消息发送成功',
        data: result,
      };
    } catch (error) {
      ctx.body = {
        code: 500,
        message: error.message,
      };
    }
  }
}

module.exports = WechatMessageController;
```

## 错误处理

工具类内置了完善的错误处理机制：

1. **配置验证**：自动检查配置是否完整
2. **参数验证**：验证必填字段和双语格式
3. **网络异常**：处理网络请求失败
4. **日志记录**：详细记录请求和响应日志

## 注意事项

1. **双语格式**：`bpdname` 和 `activityname` 必须为 `中文@英文` 格式，且各部分不超过20字符
2. **API Key**：每个系统的 API Key 是唯一的，需要联系相关同事获取
3. **网络访问**：待办处理页面需要在公网可以访问
4. **异常处理**：务必做好异常处理和重试机制
5. **日志备份**：请求日志会自动保存到 `logs/wechat-message/` 目录

## Action 类型说明

- `1`: 提交
- `2`: 驳回  
- `3`: 加签
- `4`: 转办
- `99`: 未知

## 系统标识(sid)说明

- `HSE`: HSE系统
- `OHS`: OHS系统
- `TE`: 移动报销系统
- 其他系统标识需要与相关同事协商确定 