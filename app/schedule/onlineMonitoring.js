// 处理在线监测统计的问题， 处理每天的数据


module.exports = app => {
  return {
    schedule: {
      interval: 60000, // 每1 分钟 执行一次
      disable: app.config.disabledOnlineMonitor,
      immediate: false, // 启动服务时就立即执行一次任务
      type: 'worker', // 指定每次只有一个 随机的worker 执行
    },
    // 定时执行的任务
    async task(ctx) {
      // 1、获取所有已经开启的设备ID
      const allDevices = await ctx.model.Devices.find({ connectionStatus: true, status: true }, { deviceID: 1 });
      const deviceIDs = allDevices.map(ele => ele.deviceID);
      // 2、获取前一分钟的所有设备数据
      const curDate = new Date();
      const query = {
        deviceID: { $in: deviceIDs },
        dateTime: {
          $lt: curDate,
          $gte: new Date(curDate.getTime() - 60000),
        },
      };
      const allDevicesData = await ctx.model.DevicesData.find(query, { data: 1, deviceID: 1, deviceType: 1 });
      // 按不同设备分类数据 （不用管道查询是因为有些设备可能没有数据，以及数据不全的情况）
      // console.log(11111111111, allDevicesData);
      const targetDataObj = {};
      const len = deviceIDs.length;
      for (let i = 0; i < len; i++) {
        const curDeviceID = deviceIDs[i];
        targetDataObj[curDeviceID] = allDevicesData.filter(ele => ele.deviceID === curDeviceID);
      }
      let toBeDeletedIds = [];
      // console.log(33333, targetDataObj);
      // 整理好数据之后再分别处理每个设备上一分钟的数据
      for (const deviceID in targetDataObj) {
        let value = 0; // 没有数据就认为是 0
        const times = targetDataObj[deviceID].length;
        if (times) {
          toBeDeletedIds = toBeDeletedIds.concat(targetDataObj[deviceID].map(ele => ele._id).slice(0, times - 1));
          if (targetDataObj[deviceID][0].deviceType === 1 || targetDataObj[deviceID][0].data.LASp) {
            // 噪声
            let integral = 0;
            for (let j = 0; j < times; j++) {
              const curVal = targetDataObj[deviceID][j].data.LASp;
              integral += Math.pow(10, 0.1 * curVal);
            }
            value = 10 * (Math.log((1 / times) * integral) / Math.log(10));
          } else if (targetDataObj[deviceID][0].deviceType === 2) {
            // 粉尘
            const allData = targetDataObj[deviceID].filter(ele => ele.data.status === '正常检测状态').map(ele => +ele.data.value);
            const sum = allData.reduce((a, b) => a + b, 0);
            value = sum / allData.length;
          }
        }
        await ctx.model.Devices.updateOne(
          { deviceID },
          { $push: { minuteData: { value, dateTime: new Date(curDate.getTime() - 30000) } } }
        );
      }
      // 删除已经处理过的秒数据
      await ctx.model.DevicesData.deleteMany(
        { _id: { $in: toBeDeletedIds } }
      );
    },
  };
};
