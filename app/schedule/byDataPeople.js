// 定时任务 同步福州数据
module.exports = app => {
  class byDataPeople extends app.Subscription {
    static get schedule() {
      return {
        // 每隔30分钟执行一次
        cron: '0 0/30 * * * *',
        disable: !app.config.isGetByData,
        immediate: true, // 启动服务时就立即执行一次任务
        type: 'worker', // 指定每次只有一个 随机的worker 执行
      };
    } // 定时执行的任务
    async subscribe() {
      console.log('BYDATA定时任务开始');
      this.ctx.auditLog('BYDATA定时任务开始', 'BYDATA定时任务开始', 'info');
      console.time('BYDATA定时任务2耗时');
      // 人员拿洗
      console.time('人员拿洗');
      this.ctx.auditLog('BYDATA岗位拿取', 'BYDATA岗位拿取', 'info');

      const params = {};
      if (app.config.getByDataInitializationTime !== 'all') {
        params.dt = app.config.getByDataInitializationTime
          ? app.config.getByDataInitializationTime
          : new Date();
      }
      try {
        await this.ctx.service.byData.incrementPeople(params);
      } catch (error) {
        this.ctx.auditLog('BYDATA岗位拿取失败', JSON.stringify(error), 'error');
      }
      this.ctx.auditLog('BYDATA岗位拿取结束', 'BYDATA岗位拿取结束', 'info');
      console.timeEnd('人员拿洗');
      // 人员拿洗
      console.time('BYDATA工作记录拿取');
      this.ctx.auditLog('BYDATA工作记录拿取', 'BYDATA工作记录拿取', 'info');
      try {
        await this.ctx.service.byData.incermentPeopleWork({});
      } catch (error) {
        this.ctx.auditLog('BYDATA工作记录拿取失败', JSON.stringify(error), 'error');
      }
      this.ctx.auditLog('BYDATA工作记录拿取结束', 'BYDATA工作记录拿取结束', 'info');
      console.timeEnd('BYDATA工作记录拿取');
      console.timeEnd('BYDATA定时任务2耗时');
    } catch(error) {
      this.ctx.auditLog('同步北元数据失败', JSON.stringify(error), 'error');
    }
  }
  return byDataPeople;
};
