// 定时任务 同步福州数据
module.exports = app => {
  class ByDataTOne extends app.Subscription {
    static get schedule() {
      return {
        // 每天凌晨4点执行一次
        cron: '0 0  * * *',
        disable: !app.config.isGetByData,
        immediate: true, // 启动服务时就立即执行一次任务
        type: 'worker', // 指定每次只有一个 随机的worker 执行
      };
    } // 定时执行的任务
    async subscribe() {
      try {
        this.ctx.auditLog('BYDATA定时任务开始', 'BYDATA定时任务开始', 'info');
        console.time('BYDATA定时任务1耗时');
        // 组织拿洗
        console.time('BYDATA组织拿取');
        this.ctx.auditLog('BYDATA组织拿取', 'BYDATA组织拿取', 'info');
        const params = {};
        if (app.config.getByDataInitializationTime !== 'all') {
          params.dt = app.config.getByDataInitializationTime
            ? app.config.getByDataInitializationTime
            : new Date();
        }
        try {
          await this.ctx.service.byData.incrementOrgs(params);
        } catch (error) {
          this.ctx.auditLog(
            'BYDATA组织拿取失败',
            JSON.stringify(error),
            'error'
          );
        }
        this.ctx.auditLog('BYDATA组织拿取结束', 'BYDATA组织拿取结束', 'info');
        console.timeEnd('BYDATA组织拿取');
        // 部门拿洗
        console.time('BYDATA部门拿取');
        console.log('BYDATA部门拿取');
        try {
          await this.ctx.service.byData.incrementDepart(params);
        } catch (error) {
          this.ctx.auditLog(
            'BYDATA部门拿取失败',
            JSON.stringify(error),
            'error'
          );
        }
        this.ctx.auditLog('BYDATA部门拿取结束', 'BYDATA部门拿取结束', 'info');
        console.timeEnd('BYDATA部门拿取');
        // 岗位拿洗
        console.time('BYDATA岗位拿取');
        this.ctx.auditLog('BYDATA岗位拿取', 'BYDATA岗位拿取', 'info');
        try {
          await this.ctx.service.byData.incrementPost(params);
        } catch (error) {
          this.ctx.auditLog(
            'BYDATA岗位拿取失败',
            JSON.stringify(error),
            'error'
          );
        }
        this.ctx.auditLog('BYDATA岗位拿取结束', 'BYDATA岗位拿取结束', 'info');
        console.timeEnd('BYDATA岗位拿取');
        // 双预防对接
        console.time('BYDATA双预防对接拿取');
        this.ctx.auditLog(
          'BYDATA双预防对接拿取',
          'BYDATA双预防对接拿取',
          'info'
        );
        try {
          const sfxParams = {};
          if (app.config.getByDataInitializationTime !== 'all') {
            sfxParams.gxsj = app.config.getByDataInitializationTime
              ? app.config.getByDataInitializationTime
              : new Date();
          }
          await this.ctx.service.byData.incermentHiddenDanger(sfxParams);
        } catch (error) {
          this.ctx.auditLog(
            'BYDATA双预防拿取失败',
            JSON.stringify(error),
            'error'
          );
        }
        this.ctx.auditLog(
          'BYDATA岗位拿取结束',
          'BYDATA双预防对接拿取结束',
          'info'
        );
        console.timeEnd('BYDATA双预防对接拿取');

        // 培训数据对接
        // 1、
        try {
          const trainPLanList = await this.ctx.service.byData.getTrainPlanList({});
          await this.ctx.service.byData.clearTrainList(trainPLanList);
        } catch (error) {
          this.ctx.auditLog(
            'BYDATA培训班对接失败',
            JSON.stringify(error),
            'error'
          );
        }

        // 2、忘了，好像是加人的
        try {
          const details = await this.ctx.service.byData.getTrainPlanDetails({});
          await this.ctx.service.byData.clearTrainDetailList(details);
        } catch (error) {
          this.ctx.auditLog(
            'BYDATA培训人的记录接失败',
            JSON.stringify(error),
            'error'
          );
        }

        //   // details
        // );
        // 3、personing
        try {
          const progresList =
            await this.ctx.service.byData.getTrainPersonProgress();
          await this.ctx.service.byData.clearTrainPersonProgress(progresList);
        } catch (error) {
          this.ctx.auditLog(
            'BYDATA培训人的记录接失败',
            JSON.stringify(error),
            'error'
          );
        }

        try {
          const testList = await this.ctx.service.byData.getTrainKsxx();
          const tesjcjList = await this.ctx.service.byData.getTrainKscj();
          await this.ctx.service.byData.clearTrainKsList(tesjcjList, testList);
        } catch (error) {
          this.ctx.auditLog(
            'BYDATA培训考试信息接失败',
            JSON.stringify(error),
            'error'
          );
        }

        console.timeEnd('BYDATA定时任务1耗时');
      } catch (error) {
        this.ctx.auditLog('同步北元数据失败', JSON.stringify(error), 'error');
      }
    }
  }
  return ByDataTOne;
};
