// const moment = require('moment');
module.exports = app => {
  return {
    schedule: app.config.whBaseData,

    async task(ctx) {

      try {

        ctx.auditLog(
          '万华mdg未处理数据定时任务开始',
          '',
          'info'
        );
        await ctx.service.whData.processNoMdgData();

        ctx.auditLog(
          '万华mdg未处理数据定时任务处理完成',
          '',
          'info');
      } catch (error) {
        ctx.auditLog(
          '万华mdg未处理数据定时任务处理失败',
          error.message,
          'error'
        );
        throw error;
      }
    },
  };
};
