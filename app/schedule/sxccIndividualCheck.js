// 定时任务 山西焦煤轮询体检系统的体检结果和签到状态
module.exports = app => {
  class SxccIndividualCheck extends app.Subscription {
    static get schedule() {
      return app.config.sxccIndividualCheckCron;
    }

    async subscribe() {
      const { ctx } = this;
      console.log('山西焦煤轮询体检系统的体检结果和签到状态定时任务开始');
      ctx.auditLog('山西焦煤轮询体检系统的体检结果和签到状态定时任务开始', '山西焦煤轮询体检系统的体检结果和签到状态定时任务开始', 'info');
      console.time('山西焦煤轮询体检系统的体检结果和签到状态定时任务耗时');

      try {
        // 推送体检计划列表
        await ctx.service.sxccPhysicalAppointment.pushPhysicalPlanList();
        // 推送体检计划详情
        await ctx.service.sxccPhysicalAppointment.pushPhysicalPlanDetail();
        // 推送个人体检预约详情
        await ctx.service.sxccPhysicalAppointment.pushIndividualAppointment();
        // // 获取体检人员现场签到
        // await ctx.service.sxccPhysicalAppointment.queryIndividualCheckIn();
        // 获取职业健康档案-json-国家标准
        await ctx.service.sxccPhysicalAppointment.queryHealthExamRecord();
        ctx.auditLog('山西焦煤轮询体检系统的体检结果和签到状态定时任务成功', '山西焦煤轮询体检系统的体检结果和签到状态定时任务成功', 'info');
      } catch (error) {
        const errorMessage = error instanceof Error ? error.stack : JSON.stringify(error);
        ctx.auditLog('山西焦煤轮询体检系统的体检结果和签到状态定时任务失败', errorMessage, 'error');
      }

      ctx.auditLog('山西焦煤轮询体检系统的体检结果和签到状态定时任务结束', '山西焦煤轮询体检系统的体检结果和签到状态定时任务结束', 'info');
      console.timeEnd('山西焦煤轮询体检系统的体检结果和签到状态定时任务耗时');

      console.log('山西焦煤轮询体检系统的体检结果和签到状态定时任务结束');
    }
  }
  return SxccIndividualCheck;
};
