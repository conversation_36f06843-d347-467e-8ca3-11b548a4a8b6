#!/usr/bin/env node
'use strict';

/**
 * 福州数据处理子进程
 * 独立的Node.js进程，用于CPU密集型的文件解密和数据分析
 * 修复版本：支持完整的数据库操作，直接调用现有服务
 */

const fs = require('fs');
const path = require('path');

// 初始化EggJS应用上下文
let app;
let ctx;
let isInitialized = false;

// 加载加密工具和JSZip
let EncryptUtil;
let JSZip;
try {
  EncryptUtil = require('../utils/encryptUtil');
  JSZip = require('jszip');
  console.log(`[${process.env.PROCESS_ID}] 成功加载加密工具和JSZip`);
} catch (error) {
  console.error(`[${process.env.PROCESS_ID}] 加载模块失败:`, error.message);
  // 尝试备用路径
  try {
    JSZip = require('jszip');
    console.log(`[${process.env.PROCESS_ID}] JSZip加载成功，但EncryptUtil加载失败`);
    EncryptUtil = null;
  } catch (zipError) {
    console.error(`[${process.env.PROCESS_ID}] JSZip也加载失败:`, zipError.message);
    EncryptUtil = null;
    JSZip = null;
  }
}

// 加密密钥
const encryptionKey = 'chiscdc@bhkdownload#$%^';

/**
 * 初始化EggJS应用上下文
 */
async function initializeEggApplication() {
  if (isInitialized) {
    return true;
  }

  try {
    console.log(`[${process.env.PROCESS_ID}] 初始化EggJS应用上下文...`);

    // 加载EggJS应用
    const egg = require('egg');

    // 获取应用根目录
    const appRoot = path.resolve(__dirname, '..');

    // 创建EggJS应用实例
    app = egg(appRoot, {
      env: process.env.NODE_ENV || 'development',
      workers: 1,
      typescript: false,
    });

    // 等待应用启动
    await app.ready();

    // 创建上下文
    ctx = app.createAnonymousContext();

    console.log(`[${process.env.PROCESS_ID}] EggJS应用初始化完成`);

    isInitialized = true;
    return true;

  } catch (error) {
    console.error(`[${process.env.PROCESS_ID}] EggJS应用初始化失败:`, error);

    // 如果EggJS初始化失败，尝试简化的初始化方式
    try {
      console.log(`[${process.env.PROCESS_ID}] 尝试简化初始化...`);

      // 加载必要的依赖
      const mongoose = require('mongoose');

      // 加载配置文件
      const configPath = path.resolve(__dirname, '../config/config.local.js');
      let config;
      try {
        const configModule = require(configPath);
        // 如果是函数则调用
        config = typeof configModule === 'function' ? configModule({ baseDir: path.resolve(__dirname, '..') }) : configModule;
      } catch (configError) {
        console.error(`[${process.env.PROCESS_ID}] 加载配置文件失败:`, configError);
        // 使用默认配置
        config = {
          mongoose: {
            client: {
              url: 'mongodb://127.0.0.1:27017/fujian',
              options: { useUnifiedTopology: true },
            },
          },
          groupID: {
            adminGroupID: '5d67d3e5ed55bc15688c2e21',
          },
        };
      }

      // 建立数据库连接
      console.log(`[${process.env.PROCESS_ID}] 连接数据库...`);
      await mongoose.connect(config.mongoose.client.url, config.mongoose.client.options);
      console.log(`[${process.env.PROCESS_ID}] 数据库连接成功`);

      // 手动加载并注册模型
      console.log(`[${process.env.PROCESS_ID}] 加载数据库模型...`);

      // 为mongoose添加必要的Schema和模型
      const modelSchemas = {};
      // 加载AdminUser模型
      try {
        const AdminUserModel = require('../model/adminUser')({ mongoose, config });
        modelSchemas.AdminUser = AdminUserModel;
      } catch (err) {
        console.error(`[${process.env.PROCESS_ID}] 加载AdminUser模型失败:`, err.message);
      }

      // 加载User模型
      try {
        const UserModel = require('../model/user')({ mongoose, config });
        modelSchemas.User = UserModel;
      } catch (err) {
        console.error(`[${process.env.PROCESS_ID}] 加载User模型失败:`, err.message);
      }

      // 加载Employee模型
      try {
        const EmployeeModel = require('../model/employee')({ mongoose, config });
        modelSchemas.Employee = EmployeeModel;
      } catch (err) {
        console.error(`[${process.env.PROCESS_ID}] 加载Employee模型失败:`, err.message);
      }

      // 加载Adminorg模型（来自插件）
      try {
        const AdminorgModel = require('../lib/plugin/egg-jk-adminorg/app/model/adminorg')({ mongoose });
        modelSchemas.Adminorg = AdminorgModel;
      } catch (err) {
        console.error(`[${process.env.PROCESS_ID}] 加载Adminorg模型失败:`, err.message);
      }

      // 加载Healthcheck模型（来自插件）
      try {
        const HealthcheckModel = require('../lib/plugin/egg-jk-physicalExamOrg/app/model/healthcheck')({ mongoose });
        modelSchemas.Healthcheck = HealthcheckModel;
      } catch (err) {
        console.error(`[${process.env.PROCESS_ID}] 加载Healthcheck模型失败:`, err.message);
      }

      // 加载Suspect模型（来自插件）
      try {
        const SuspectModel = require('../lib/plugin/egg-jk-physicalExamOrg/app/model/suspect')({ mongoose, config });
        modelSchemas.Suspect = SuspectModel;
      } catch (err) {
        console.error(`[${process.env.PROCESS_ID}] 加载Suspect模型失败:`, err.message);
      }

      console.log(`[${process.env.PROCESS_ID}] 已加载模型:`, Object.keys(modelSchemas));

      // 直接引入必要的服务
      const FzDataService = require('../service/fzData');

      // 创建模拟的应用和上下文
      app = {
        mongoose,
        config,
      };

      ctx = {
        app,
        logger: {
          info: (...args) => console.log(`[${process.env.PROCESS_ID}]`, ...args),
          error: (...args) => console.error(`[${process.env.PROCESS_ID}]`, ...args),
          warn: (...args) => console.warn(`[${process.env.PROCESS_ID}]`, ...args),
        },
        auditLog: (level, message, type = 'info') => {
          console.log(`[${process.env.PROCESS_ID}] [AUDIT] ${level}: ${message} (${type})`);
        },
        model: modelSchemas, // 提供model访问接口
        service: {},
      };

      // 初始化fzData服务
      ctx.service.fzData = new FzDataService(ctx);
      ctx.service.fzData.ctx = ctx;
      ctx.service.fzData.app = app;

      console.log(`[${process.env.PROCESS_ID}] 简化初始化完成，包含模型: ${Object.keys(modelSchemas).join(', ')}`);

      isInitialized = true;
      return true;

    } catch (fallbackError) {
      console.error(`[${process.env.PROCESS_ID}] 简化初始化也失败:`, fallbackError);
      isInitialized = false;
      return false;
    }
  }
}

/**
 * 从zip文件中获取并解密数据
 * @param {string} filePath - zip文件的完整路径
 * @return {Promise<Object|null>} 解析后的JSON内容，失败时返回null
 */
async function getOldDataFromZip(filePath) {
  try {
    if (!fs.existsSync(filePath)) {
      throw new Error(`文件不存在: ${filePath}`);
    }

    if (!JSZip) {
      throw new Error('JSZip模块未加载');
    }

    const zipContent = fs.readFileSync(filePath);
    const zip = new JSZip();
    const zipData = await zip.loadAsync(zipContent);

    const data = zipData.files['data.json'];
    if (!data) {
      throw new Error(`zip文件中不存在data.json: ${filePath}`);
    }

    const content = await data.async('string');

    // 尝试解密
    let decryptContent;
    if (EncryptUtil && EncryptUtil.decrypt) {
      decryptContent = EncryptUtil.decrypt(content, encryptionKey);
    } else {
      console.warn(`[${process.env.PROCESS_ID}] EncryptUtil不可用，尝试直接解析`);
      decryptContent = content;
    }

    const jsonContent = JSON.parse(decryptContent);
    return jsonContent;
  } catch (error) {
    throw new Error(`从zip文件获取数据失败: ${filePath}, 错误: ${error.message}`);
  }
}

/**
 * 处理单个文件
 * @param {string} filePath - 文件路径
 * @param {string} taskType - 任务类型
 * @return {Promise<Object>} 处理结果
 */
async function processFile(filePath, taskType) {
  const startTime = Date.now();
  const fileName = path.basename(filePath);

  try {
    console.log(`[${process.env.PROCESS_ID}] 开始处理文件: ${fileName}`);

    // 确保应用已初始化
    if (!isInitialized) {
      const initResult = await initializeEggApplication();
      if (!initResult) {
        throw new Error('应用初始化失败，无法处理数据');
      }
    }

    // 从ZIP文件中解密数据
    const jsonData = await getOldDataFromZip(filePath);
    console.log(`[${process.env.PROCESS_ID}] 文件解密完成: ${fileName}`);

    if (!jsonData) {
      throw new Error('解密后的数据为空');
    }

    // 根据任务类型处理数据
    let processResult;
    switch (taskType) {
      case 'company':
        processResult = await processCompanyData(jsonData);
        break;
      case 'healthCheck':
        processResult = await processHealthCheckData(jsonData);
        break;
      case 'diagnosis':
        processResult = await processDiagnosisData(jsonData);
        break;
      default:
        throw new Error(`未知的任务类型: ${taskType}`);
    }

    const duration = Date.now() - startTime;

    const isSkipped = processResult.skippedCount > 0;
    const isSuccess = !isSkipped && processResult.processedCount > 0;

    // 确定处理状态
    let status;
    let message;
    if (isSkipped) {
      status = 'skipped';
      message = `跳过处理: ${processResult.skipReason || '特殊响应'}`;
    } else if (isSuccess) {
      status = 'success';
      message = `处理成功: ${processResult.processedCount} 条记录`;
    } else {
      status = 'failed';
      message = '没有数据需要处理';
    }

    console.log(`[${process.env.PROCESS_ID}] 文件处理完成: ${fileName}, 耗时: ${duration}ms, 状态: ${status} - ${message}`);

    return {
      fileName,
      filePath,
      status, // 明确的状态字段：'success', 'failed', 'skipped'
      success: isSuccess, // 保留向后兼容性
      skipped: isSkipped, // 保留向后兼容性
      skipReason: processResult.skipReason || null,
      message,
      duration,
      processedCount: processResult.processedCount || 0,
      errorCount: processResult.errorCount || 0,
      skippedCount: processResult.skippedCount || 0,
      details: processResult.details || {},
      workerId: process.env.PROCESS_ID,
      // 添加解密数据和分析结果，用于存储到 metadata.additionalInfo
      decryptedData: jsonData,
      dataAnalysis: {
        taskType,
        dataType: Array.isArray(jsonData) ? 'array' : 'object',
        keys: Array.isArray(jsonData) ? [`数组长度: ${jsonData.length}`] : Object.keys(jsonData),
        processedAt: new Date().toISOString(),
        dataSize: JSON.stringify(jsonData).length,
      },
      performance: {
        duration,
        fileSize: require('fs').statSync(filePath).size,
        processingSpeed: Math.round((processResult.processedCount || 0) / (duration / 1000)),
      },
    };

  } catch (error) {
    const duration = Date.now() - startTime;
    console.error(`[${process.env.PROCESS_ID}] 文件处理失败: ${fileName}, 错误: ${error.message}`);

    return {
      fileName,
      filePath,
      status: 'failed', // 明确的失败状态
      success: false, // 保留向后兼容性
      message: `处理失败: ${error.message}`,
      duration,
      error: error.message,
      workerId: process.env.PROCESS_ID,
    };
  }
}

/**
 * 处理企业数据 - 使用现有的fzData服务
 * @param {Object} jsonData - JSON数据
 * @return {Promise<Object>} 处理结果
 */
async function processCompanyData(jsonData) {
  console.log(`[${process.env.PROCESS_ID}] 开始处理企业数据...`);

  // 检查特殊响应格式
  if (jsonData && typeof jsonData === 'object') {
    if (jsonData.mess === '未查询到相关数据！' && jsonData.type === '03') {
      console.log(`[${process.env.PROCESS_ID}] 企业数据类型为03，未查询到相关数据`);
      return {
        processedCount: 0,
        errorCount: 0,
        skippedCount: 1,
        skipReason: 'no_data',
        details: { message: '未查询到相关数据', type: jsonData.type },
      };
    }

    if (jsonData.mess === '认证TokenId无效，请重新获取TokenId！' && jsonData.type === '02') {
      console.log(`[${process.env.PROCESS_ID}] 企业数据TokenId无效`);
      return {
        processedCount: 0,
        errorCount: 0,
        skippedCount: 1,
        skipReason: 'invalid_token',
        details: { message: '认证TokenId无效', type: jsonData.type },
      };
    }

    // 检查企业数据列表（支持多种格式）
    let companyList = null;
    if (jsonData.crptList && Array.isArray(jsonData.crptList)) {
      companyList = jsonData.crptList;
    } else if (jsonData.ZoneCompanyList && Array.isArray(jsonData.ZoneCompanyList)) {
      companyList = jsonData.ZoneCompanyList;
    }

    if (companyList && companyList.length > 0) {
      console.log(`[${process.env.PROCESS_ID}] 发现企业数据列表，开始处理 ${companyList.length} 条记录`);

      try {
        // 调用现有的企业数据清洗方法
        await ctx.service.fzData.clearOurCompanyData(companyList);

        console.log(`[${process.env.PROCESS_ID}] 企业数据处理完成: ${companyList.length} 条记录`);

        return {
          processedCount: companyList.length,
          errorCount: 0,
          details: {
            totalRecords: companyList.length,
            processedRecords: companyList.length,
            failedRecords: 0,
          },
        };

      } catch (error) {
        console.error(`[${process.env.PROCESS_ID}] 企业数据处理异常:`, error);
        throw new Error(`企业数据处理失败: ${error.message}`);
      }
    }
  }

  throw new Error('企业数据格式无效：缺少crptList或ZoneCompanyList数组');
}

/**
 * 处理体检数据 - 使用现有的fzData服务
 * @param {Object} jsonData - JSON数据
 * @return {Promise<Object>} 处理结果
 */
async function processHealthCheckData(jsonData) {
  console.log(`[${process.env.PROCESS_ID}] 开始处理体检数据...`);

  // 检查特殊响应格式
  if (jsonData && typeof jsonData === 'object') {
    if (jsonData.type === '03') {
      console.log(`[${process.env.PROCESS_ID}] 体检数据类型为03，未查询到相关数据`);
      return {
        processedCount: 0,
        errorCount: 0,
        skippedCount: 1,
        skipReason: 'no_data',
        details: { message: '未查询到相关数据', type: jsonData.type },
      };
    }

    if (jsonData.mess === '认证TokenId无效，请重新获取TokenId！' && jsonData.type === '02') {
      console.log(`[${process.env.PROCESS_ID}] 体检数据TokenId无效`);
      return {
        processedCount: 0,
        errorCount: 0,
        skippedCount: 1,
        skipReason: 'invalid_token',
        details: { message: '认证TokenId无效', type: jsonData.type },
      };
    }

    // 检查是否有bhkList数组
    if (jsonData.bhkList && Array.isArray(jsonData.bhkList)) {
      console.log(`[${process.env.PROCESS_ID}] 发现体检数据列表，开始处理 ${jsonData.bhkList.length} 条记录`);

      try {
        // 调用现有的体检数据清洗方法
        await ctx.service.fzData.clearHealthyCheckData(jsonData.bhkList);

        console.log(`[${process.env.PROCESS_ID}] 体检数据处理完成: ${jsonData.bhkList.length} 条记录`);

        return {
          processedCount: jsonData.bhkList.length,
          errorCount: 0,
          details: {
            totalRecords: jsonData.bhkList.length,
            processedRecords: jsonData.bhkList.length,
            failedRecords: 0,
          },
        };

      } catch (error) {
        console.error(`[${process.env.PROCESS_ID}] 体检数据处理异常:`, error);
        throw new Error(`体检数据处理失败: ${error.message}`);
      }
    }
  }

  throw new Error('体检数据格式无效：缺少bhkList数组或数据格式错误');
}

/**
 * 处理诊断数据 - 使用现有的fzData服务
 * @param {Object} jsonData - JSON数据
 * @return {Promise<Object>} 处理结果
 */
async function processDiagnosisData(jsonData) {
  console.log(`[${process.env.PROCESS_ID}] 开始处理诊断数据...`);

  // 检查特殊响应格式
  if (jsonData && typeof jsonData === 'object') {
    if (jsonData.mess === '未查询到相关数据！' && jsonData.type === '03') {
      console.log(`[${process.env.PROCESS_ID}] 诊断数据类型为03，未查询到相关数据`);
      return {
        processedCount: 0,
        errorCount: 0,
        skippedCount: 1,
        skipReason: 'no_data',
        details: { message: '未查询到相关数据', type: jsonData.type },
      };
    }

    if (jsonData.mess === '认证TokenId无效，请重新获取TokenId！' && jsonData.type === '02') {
      console.log(`[${process.env.PROCESS_ID}] 诊断数据TokenId无效`);
      return {
        processedCount: 0,
        errorCount: 0,
        skippedCount: 1,
        skipReason: 'invalid_token',
        details: { message: '认证TokenId无效', type: jsonData.type },
      };
    }

    // 检查诊断数据type，除非为"00"，其他都按跳过处理
    if (jsonData.type && jsonData.type !== '00') {
      console.log(`[${process.env.PROCESS_ID}] 诊断数据type不为"00"，已跳过处理，实际类型: ${jsonData.type}`);
      return {
        processedCount: 0,
        errorCount: 0,
        skippedCount: 1,
        skipReason: 'type_not_00',
        details: { message: 'type不为00的诊断数据已跳过', type: jsonData.type, actualType: jsonData.type },
      };
    }

    // 检查是否有occdiscaseList数组
    if (jsonData.occdiscaseList && Array.isArray(jsonData.occdiscaseList)) {
      console.log(`[${process.env.PROCESS_ID}] 发现诊断数据列表，开始处理 ${jsonData.occdiscaseList.length} 条记录`);

      try {
        // 调用现有的诊断数据清洗方法 (假设存在类似方法)
        // 注意：这里需要确认是否有对应的诊断数据处理方法
        if (ctx.service.fzData.createDiagnosis) {
          await ctx.service.fzData.createDiagnosis(jsonData.occdiscaseList);
        } else {
          // 如果没有专门的诊断数据处理方法，可以记录日志但不处理
          console.warn(`[${process.env.PROCESS_ID}] 诊断数据处理方法未找到，跳过处理`);
          return {
            processedCount: 0,
            errorCount: 0,
            skippedCount: jsonData.occdiscaseList.length,
            skipReason: 'method_not_found',
            details: { message: '诊断数据处理方法未找到', totalRecords: jsonData.occdiscaseList.length },
          };
        }

        console.log(`[${process.env.PROCESS_ID}] 诊断数据处理完成: ${jsonData.occdiscaseList.length} 条记录`);

        return {
          processedCount: jsonData.occdiscaseList.length,
          errorCount: 0,
          details: {
            totalRecords: jsonData.occdiscaseList.length,
            processedRecords: jsonData.occdiscaseList.length,
            failedRecords: 0,
          },
        };

      } catch (error) {
        console.error(`[${process.env.PROCESS_ID}] 诊断数据处理异常:`, error);
        throw new Error(`诊断数据处理失败: ${error.message}`);
      }
    }
  }

  throw new Error('诊断数据格式无效：缺少occdiscaseList数组或数据格式错误');
}

/**
 * 处理批次任务
 * @param {Array} batch - 文件批次
 * @param {string} taskType - 任务类型
 * @return {Promise<Array>} 处理结果数组
 */
async function processBatch(batch, taskType) {
  console.log(`[${process.env.PROCESS_ID}] 开始处理批次: ${batch.length} 个文件`);

  const results = [];
  for (const filePath of batch) {
    const result = await processFile(filePath, taskType);
    results.push(result);
  }

  console.log(`[${process.env.PROCESS_ID}] 批次处理完成`);
  return results;
}

// 进程间通信处理
process.on('message', async message => {
  if (message.type === 'task') {
    try {
      const { batch, taskType, processId } = message;
      console.log(`[${processId}] 收到任务: 处理 ${batch.length} 个文件，类型: ${taskType}`);

      const results = await processBatch(batch, taskType);

      // 发送结果回主进程
      process.send({
        type: 'result',
        results,
        processId,
        summary: {
          total: results.length,
          success: results.filter(r => r.status === 'success').length,
          failed: results.filter(r => r.status === 'failed').length,
          skipped: results.filter(r => r.status === 'skipped').length,
        },
      });

      // 处理完成后退出进程
      console.log(`[${processId}] 任务完成，正常退出`);
      process.exit(0);

    } catch (error) {
      const errorDetails = {
        message: error.message,
        stack: error.stack,
        processId: process.env.PROCESS_ID,
        timestamp: new Date().toISOString(),
      };

      console.error(`[${process.env.PROCESS_ID}] 任务处理失败:`, errorDetails);

      // 发送详细错误结果
      try {
        process.send({
          type: 'result',
          results: [{
            status: 'failed',
            success: false,
            message: `批次处理失败: ${error.message}`,
            error: error.message,
            errorDetails,
            workerId: process.env.PROCESS_ID,
          }],
          processId: process.env.PROCESS_ID,
          error: errorDetails,
        });
      } catch (sendError) {
        console.error(`[${process.env.PROCESS_ID}] 发送错误结果失败:`, sendError);
      }

      console.log(`[${process.env.PROCESS_ID}] 错误处理完成，退出进程`);
      process.exit(1);
    }
  }
});

// 错误处理
process.on('uncaughtException', error => {
  const errorDetails = {
    type: 'uncaughtException',
    message: error.message,
    stack: error.stack,
    processId: process.env.PROCESS_ID,
    timestamp: new Date().toISOString(),
  };

  console.error(`[${process.env.PROCESS_ID}] 未捕获的异常:`, errorDetails);

  // 尝试发送错误信息
  try {
    process.send({
      type: 'error',
      error: errorDetails,
      processId: process.env.PROCESS_ID,
    });
  } catch (sendError) {
    console.error(`[${process.env.PROCESS_ID}] 发送异常信息失败:`, sendError);
  }

  // 延迟退出，确保错误信息能被发送
  setTimeout(() => {
    process.exit(1);
  }, 100);
});

process.on('unhandledRejection', reason => {
  const errorDetails = {
    type: 'unhandledRejection',
    reason: reason instanceof Error ? reason.message : String(reason),
    stack: reason instanceof Error ? reason.stack : null,
    processId: process.env.PROCESS_ID,
    timestamp: new Date().toISOString(),
  };

  console.error(`[${process.env.PROCESS_ID}] 未处理的Promise拒绝:`, errorDetails);

  // 尝试发送错误信息
  try {
    process.send({
      type: 'error',
      error: errorDetails,
      processId: process.env.PROCESS_ID,
    });
  } catch (sendError) {
    console.error(`[${process.env.PROCESS_ID}] 发送拒绝信息失败:`, sendError);
  }

  // 延迟退出，确保错误信息能被发送
  setTimeout(() => {
    process.exit(1);
  }, 100);
});

console.log(`[${process.env.PROCESS_ID}] 子进程已启动，等待任务...`);
