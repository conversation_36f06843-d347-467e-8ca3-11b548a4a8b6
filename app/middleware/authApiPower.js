const _ = require('lodash');
module.exports = (options, app) => {
  let routeWhiteList = [
    'heartbeat',
    'hzwy/physical/report',
    'hzwy/physical/reportFile',
    'report/createPhyFz',
    'byTest',
    'byCallback',
    'whcallback',
    'whh5',
    'healCheckResult',
    'healCheckFile',
    'testEncrypt',
    'wxWorkAuthAction',
    'validateUrlWxWorkAuthAction',
    'checkItems/getIndicator',
    'sxccPhysicalAppointment/handleCompletePhysicalExam',
    'whData/getPersonData',
    'whData/getOrgData',
    'whData/getAccountData',
    'whData/getPostData',
    'whData/getPostAndPersonData',
    'getSystemConfig',
  ];
  return async function checkApiPower(ctx, next) {
    try {
      // ctx.logger.info('Api鉴权：', JSON.parse(JSON.stringify(ctx)), 'info');
      const getPluginApiWhiteList = app.getExtendFontApiList();
      if (!_.isEmpty(getPluginApiWhiteList) && !_.isEmpty(getPluginApiWhiteList.fontApiWhiteList) && routeWhiteList.indexOf(getPluginApiWhiteList.fontApiWhiteList.join(',')) < 0) {
        routeWhiteList = routeWhiteList.concat(getPluginApiWhiteList.fontApiWhiteList);
      }
      const targetApi = (ctx.originalUrl).replace(/(\/api\/|\/chenkSystem\/|\/crm\/)/, '').split('?')[0];
      const targetResourceObj = await ctx.service.apiResource.find({ isPaging: '0' }, { query: { type: '1', api: targetApi, enable: true }, files: '_id api' });
      let hasPower = false,
        apiPower = {};
      (!_.isEmpty(ctx.session.api)) ? apiPower = await ctx.helper.getApiPower(ctx) : hasPower = false;
      (_.indexOf(routeWhiteList, targetApi) > -1) ? hasPower = true : targetApi;
      const targetResourceObjCount = targetResourceObj.length;
      for (let i = 0; i < targetResourceObjCount; i++) {
        const element = targetResourceObj[i];
        if (targetResourceObj && apiPower && (_.indexOf(apiPower, element._id) > -1)) {
          hasPower = true;
          break;
        }
      }
      hasPower ? ctx.logger.info('Api鉴权成功：' + ctx.originalUrl) : ctx.logger.info('鉴权失败：' + ctx.originalUrl);
      hasPower ? await next() : ctx.helper.renderFail(ctx, { message: '对不起，您暂无此权限' });
    } catch (error) {
      ctx.auditLog('Api权限中间件：', { error, ctx }, 'error');
      ctx.helper.renderFail(ctx, { message: '服务器繁忙，请稍后再试' });
    }
  };
};
