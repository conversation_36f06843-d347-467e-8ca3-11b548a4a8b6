const {
  authToken,
} = require('@utils');
const _ = require('lodash');
module.exports = (options, app) => {

  const routeWhiteList = [ 'training/courses/getClassification', 'training/getExpertList', 'training/expertTrainList', 'agent/getShareDetail', 'agent/getTrainSiteConfig', 'adminTraining/getCertificate' ];

  return async function authAdminToken(ctx, next) {
    const url = ctx.request.url;
    ctx.session.user = '';
    let userToken = ctx.get('Authorization') || ctx.cookies.get('admin_' + app.config.auth_cookie_name, {
      path: '/',
      maxAge: app.config.adminUserMaxAge,
      signed: true,
      httpOnly: false,
    });

    // 处理Bearer token格式
    if (userToken && userToken.startsWith('Bearer ')) {
      userToken = userToken.substring(7);
    }

    if (userToken) {
      const checkToken = await authToken.checkToken(userToken, app.config.encrypt_key, ctx);
      if (checkToken) {

        if (typeof checkToken === 'object') {
          const _id = checkToken._id;
          const targetUser = await ctx.service.user.item(ctx, {
            query: {
              _id,
            },
            populate: 'none',
            files: {
              password: 0,
              email: 0,
            },
          });
          if (!_.isEmpty(targetUser)) {
            // const { EnterpriseID } = await ctx.model.Employee.findOne({
            //   _id: targetUser._id,
            // }, 'EnterpriseID');
            const EnterpriseID = targetUser.companyId && targetUser.companyId.length > 0 ? targetUser.companyId[0] : '';
            const {
              userName,
              _id,
              group,
            } = targetUser;

            ctx.session.user = {
              userName,
              _id,
              group,
              EnterpriseID,
            };
            // 检索代号：#0001
            // 根据url动态设置后台根路径 可以用其他的，譬如jwt，这里临时使用session，ctx.state 无效
            if (url.indexOf('/admin') === 0) {
              ctx.session.basePath = app.config.admin_base_path;
            } else if (url.indexOf('/qy') === 0) {
              ctx.session.basePath = app.config.qy_base_path;
            } else if (url.indexOf('/user') === 0) {
              ctx.session.basePath = app.config.user_base_path;
            }
            await next();
          } else {
            ctx.helper.renderFail(ctx, {
              message: '哎呀！网页开小差了！重新登录试试？',
            });
          }
        } else {
          ctx.helper.renderFail(ctx, {
            message: '哎呀！网页开小差了！重新登录试试？',
          });
        }

      } else {
        ctx.helper.renderCustom(ctx, {
          status: 401,
          message: '哎呀！登录过期了，请重新登录。',
        });
      }
    } else {
      // 没有配置但是包含在白名单内的路由校验
      if (!_.isEmpty(routeWhiteList)) {
        const checkWhiteRouter = _.filter(routeWhiteList, item => {
          return ctx.originalUrl.indexOf(item) >= 0;
        });
        if (!_.isEmpty(checkWhiteRouter)) {
          await next();
        } else {
          ctx.helper.renderFail(ctx, {
            message: '哎呀！网页开小差了！重新登录试试？',
          });
        }
      } else {
        ctx.helper.renderFail(ctx, {
          message: '哎呀！网页开小差了！重新登录试试？',
        });
      }

    }

  };

};
