const {
  authToken,
} = require('@utils');
const _ = require('lodash');
module.exports = (options, app) => {

  const routeWhiteList = [];

  return async function authQlcToken(ctx, next) {
    ctx.session.superUser = '';
    const userToken = ctx.get('Authorization') || ctx.cookies.get('admin_' + app.config.auth_jcqlccookie_name, {
      path: '/',
      maxAge: app.config.adminUserMaxAge,
      signed: true,
      httpOnly: false,
    });
    if (userToken) {
      const checkToken = await authToken.checkToken(userToken, app.config.encrypt_key, ctx);
      if (checkToken) {
        if (typeof checkToken === 'object') {
          const _id = checkToken._id,
            type = checkToken.type;
          let user = null;

          if (type === 1) {
            // 下面四行是xx加的，暂时没有用到targetUser
            user = await ctx.model.ServiceUser.findOne({ _id }, { password: 0 }); // 返回结果不包含密码字段
            if (!_.isEmpty(user)) {
              ctx.session.jcqlcInfo = { ...user._doc };
            }
          } else {
            user = await ctx.service.jcUser.item(ctx, { query: { _id }, files: { password: 0 } }); // 返回结果不包含密码字段
            ctx.session.jcqlcInfo = {
              email: user.email,
              countryCode: user.countryCode,
              comments: user.comments,
              logo: user.logo,
              enable: user.enable,
              state: user.state,
              _id: user._id,
              serviceEmployeeID: user.serviceEmployeeID,
              org_id: user.EnterpriseID,
              name: user.name,
              userName: user.userName,
              phoneNum: user.phoneNum,
              group: user.group,
              date: user.date,
            };
          }

          if (!_.isEmpty(user)) {
            const {
              userName,
              _id,
              group,
            } = user;
            ctx.session.jcqlcUserInfo = {
              userName,
              _id,
              type, // 用于区别用户类型 0 为服务机构员工账户 1 为服务机构用户
              group,
              EnterpriseID: type === 1 ? checkToken.EnterpriseID || '' : user.EnterpriseID || '',
              regAddr: checkToken.regAddr || [],
            };
            await next();
          } else {
            ctx.helper.renderFail(ctx, {
              message: '哎呀！网页开小差了！重新登录试试？',
            });
          }
        } else {
          ctx.helper.renderFail(ctx, {
            message: '哎呀！网页开小差了！重新登录试试？',
          });
        }

      } else {
        ctx.helper.renderFail(ctx, {
          message: '哎呀！网页开小差了！重新登录试试？',
        });
      }
    } else {
      // 没有配置但是包含在白名单内的路由校验
      if (!_.isEmpty(routeWhiteList)) {
        const checkWhiteRouter = _.filter(routeWhiteList, item => {
          return ctx.originalUrl.indexOf(item) >= 0;
        });
        if (!_.isEmpty(checkWhiteRouter)) {
          await next();
        } else {
          ctx.helper.renderFail(ctx, {
            message: '哎呀！网页开小差了！重新登录试试？',
          });
        }
      } else {
        ctx.helper.renderFail(ctx, {
          message: '哎呀！网页开小差了！重新登录试试？',
        });
      }

    }

  };

};
