const {
  authToken,
} = require('@utils');
const _ = require('lodash');
module.exports = (options, app) => {

  const routeWhiteList = [];

  return async function authAllUserToken(ctx, next) {
    // console.log('中间件',ctx.cookies.get('admin_' + app.config.auth_toolscookie_name, {
    //   path: '/',
    //   maxAge: app.config.adminUserMaxAge,
    //   signed: false,
    //   httpOnly: false,
    // }))
    const url = ctx.request.url;
    ctx.session.toolsUser = {};
    const userToken = ctx.cookies.get('admin_' + app.config.auth_toolscookie_name) || ctx.get('Authorization');
    if (userToken) {
      const checkToken = await authToken.checkToken(userToken, app.config.encrypt_key, ctx);
      if (checkToken) {

        if (typeof checkToken === 'object') {
          const _id = checkToken._id;
          const targetUser = await ctx.service.allUser.getToolsTargetUser({ _id });
          // const targetUser = await ctx.service.user.item(ctx, {
          //   query: {
          //     _id,
          //   },
          //   populate: 'none',
          //   files: {
          //     password: 0,
          //     email: 0,
          //   },
          // });
          if (!_.isEmpty(targetUser.user)) {
            const {
              userName,
              _id,
              group,
            } = targetUser.user;

            ctx.session.toolsUser = {
              userName,
              _id,
              group,
              model: targetUser.model,
            };

            // 检索代号：#0001
            // 根据url动态设置后台根路径 可以用其他的，譬如jwt，这里临时使用session，ctx.state 无效
            if (url.indexOf('/admin') === 0) {
              ctx.session.basePath = app.config.admin_base_path;
            }
            await next();
          } else {
            console.log('走这第一个');
            ctx.helper.renderFail(ctx, {
              message: '哎呀！网页开小差了！重新登录试试？',
            });
          }
        } else {
          console.log('走这第二个');
          ctx.helper.renderFail(ctx, {
            message: '哎呀！网页开小差了！重新登录试试？',
          });
        }

      } else {
        console.log('走这第三个');
        ctx.helper.renderFail(ctx, {
          message: '哎呀！网页开小差了！重新登录试试？',
        });
      }
    } else {
      // 没有配置但是包含在白名单内的路由校验
      if (!_.isEmpty(routeWhiteList)) {
        const checkWhiteRouter = _.filter(routeWhiteList, item => {
          return ctx.originalUrl.indexOf(item) >= 0;
        });
        if (!_.isEmpty(checkWhiteRouter)) {
          await next();
        } else {
          ctx.helper.renderFail(ctx, {
            message: '哎呀！网页开小差了！重新登录试试？',
          });
        }
      } else {
        ctx.helper.renderFail(ctx, {
          message: '哎呀！网页开小差了！重新登录试试？',
        });
      }

    }

  };

};
