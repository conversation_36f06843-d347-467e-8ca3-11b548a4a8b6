module.exports = () => {
  return async function notFoundHandler(ctx, next) {
    await next();
    if (ctx.status === 404 && !ctx.body) {
      if (ctx.acceptJSON) {
        ctx.body = {
          error: 'Not Found',
        };
      } else {
        if (ctx.originalUrl.indexOf('/admin/') === 0) {
          ctx.redirect('/admin/login');
        } else {
          ctx.body = '<h1>Page Not Found 您的页面丢失了</h1>';
        }

      }
    }
  };
};
