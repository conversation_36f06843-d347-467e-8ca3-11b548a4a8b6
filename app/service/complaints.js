
const Service = require('egg').Service;


class UserService extends Service {
  async getSupervisionByCompanyId(companyId) {
    const company = await this.ctx.model.Adminorg.findOne(
      { _id: companyId },
      { districtRegAdd: true, workAddress: true }
    );
    console.log('企业注册地址：', company);
    if (!company) return '未找到相关企业信息';
    let surpervision = await this.ctx.model.SuperUser.findOne(
      { regAdd: { $all: company.districtRegAdd } },
      { cname: true, _id: true, regAdd: true }
    );
    if (!surpervision) {
      surpervision = await this.ctx.model.SuperUser.findOne(
        { regAdd: { $all: company.districtRegAdd.slice(0, 3) } },
        { cname: true, _id: true, regAdd: true }
      );
    }
    if (!surpervision) {
      surpervision = await this.ctx.model.SuperUser.findOne(
        { regAdd: { $all: company.districtRegAdd.slice(0, 2) } },
        { cname: true, _id: true, regAdd: true }
      );
    }
    if (!surpervision) {
      surpervision = await this.ctx.model.SuperUser.findOne(
        { userName: 'china' },
        { cname: true, _id: true, regAdd: true }
      );
    }
    // console.log('企业所在监管单位：', surpervision);
    return surpervision;
  }

  async create(params) {
    return await this.ctx.model.Complaints.create(
      params
    );
  }
  // 发送提示信息给企业
  async msgToCompany(company_id, msgContent) {
    const { ctx, app } = this;
    // pc端页面消息提醒
    const res = await ctx.service.messageNotification.sendMessage(
      '投诉举报信息提醒',
      msgContent,
      [{
        readerID: company_id,
        readerGroup: app.config.groupID.adminGroupID,
        isRead: 0,
      }],
      ctx.session.user._id,
      app.config.groupID.userGroupID
    );
    return res;
  }

  async update(_id, status, newComment, addStatus) {
    return await this.ctx.model.Complaints.update(
      { _id, status: { $ne: 4 } },
      {
        $push: { comments: newComment },
        status,
        addStatus,
      },
      { new: true }
    );
  }
  async delete(_id) {
    return await this.ctx.model.Complaints.update(
      { _id },
      {
        status: 4,
      }
    );
  }
  async findByUserId(userId, status) {
    const query = { userId };
    if (status) query.status = status;
    return await this.ctx.model.Complaints.find(query).sort({ time: -1 });
  }
  async findById(_id) {
    return await this.ctx.model.Complaints.findOne({ _id });
  }
  async scoring(_id, scoring) {
    return await this.ctx.model.Complaints.update(
      { _id, status: { $ne: 4 } },
      { scoring }
    );
  }

}

module.exports = UserService;
