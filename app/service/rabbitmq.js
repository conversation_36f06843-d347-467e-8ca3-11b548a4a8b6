/**
 * @description RabbitMQ Service for use
 * @class RabbitMQService
 * @augments {Service}
 * @example
 *  await service.rabbitmq.produce('myQueue', message);
 *  await service.rabbitmq.consume('myQueue', message => {
 *   console.log(message);
 *  });
 */
const amqp = require('amqplib');
const { Service } = require('egg');

class RabbitmqService extends Service {
  async produce({ message, exchange, routingKey }) {
    const { ctx, config } = this;
    const connection = await amqp.connect(config.rabbitMq.url);
    const channel = await connection.createChannel();
    // 声明一个交换机
    await channel.assertExchange(exchange, 'direct', {
      durable: true,
    });
    channel.publish(exchange, routingKey, Buffer.from(JSON.stringify(message)));

    // await channel.assertQueue(queue, { durable: true });

    // channel.sendToQueue(queue, Buffer.from(JSON.stringify(message)));

    ctx.auditLog(
      'rabbitmq produce',
      `GET message : ${JSON.stringify(message)}`
    );

    // setTimeout(() => {
    //   connection.close();
    // }, 500);
  }

  async consume({ queueExist, callback, exchange, routingKey }) {
    this.ctx.auditLog('rmq', '队列消费start: ' + queueExist, 'info');
    const { config } = this;
    const connection = await amqp.connect(config.rabbitMq.url);
    const channel = await connection.createChannel();
    await channel.assertExchange(exchange, 'direct', {
      durable: true,
    });
    // 添加一个队列
    const { queue } = await channel.assertQueue(queueExist, {
      durable: true,
    });
    await channel.bindQueue(queue, exchange, routingKey);

    // await channel.assertQueue(queue, { durable: true });

    channel.consume(
      queue,
      msg => {
        if (msg !== null) {
          callback(msg.content.toString());
          // channel.ack(msg);
        }
      },
      {
        noAck: true,
      }
    );
  }
}

module.exports = RabbitmqService;
