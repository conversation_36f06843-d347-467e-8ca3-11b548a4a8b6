
const Service = require('egg').Service;


class UserService extends Service {

  async create(params) {
    return await this.ctx.model.Comment.create(
      params
    );
  }
  async update(_id, newComment) {
    return await this.ctx.model.Comment.update(
      { _id },
      {
        $push: { comments: newComment },
        replyStatus: false,
      },
      { new: true }
    );
  }
  async findByUserId(userId, limit = 10, page = 1) {
    const skip = (page - 1) * limit;
    const res = await this.ctx.model.Comment.aggregate([
      { $match: { userId, status: true } },
      { $unwind: '$comments' },
      { $sort: { 'comments.time': -1 } },
      { $skip: skip },
      { $limit: limit },
      { $group: {
        _id: '$_id',
        comments: { $push: '$comments' },
      } },
    ]);
    return res ? res[0] : {};
    // return await this.ctx.model.Comment.findOne(
    //   { userId, status: true }
    // );
  }
  async findAll(query) {
    return await this.ctx.model.Comment.find(query);
  }

}

module.exports = UserService;
