// 法律法规
const Service = require('egg').Service;

class LearningService extends Service {
  async list(data) {
    const { ctx } = this;
    const query = {
      isDel: 0,
      draft: '0',
      state: '2',
      categories: data.categories,
    };
    if (data.isTop) query.isTop = data.isTop;
    const res = await ctx.model.Content.find(query)
      .populate('uAuthor', 'name userName logo')
      .sort({ updateDate: -1 })
      .skip((data.pageCurrent - 1) * data.size)
      .limit(data.size);

    return res;
  }

  // 首页中的全局搜索
  async globalSearch(data) {
    console.log(1111111, data);
    const { ctx } = this;
    const keyWords = new RegExp(data.keyWords, 'i');
    // 1、获取文章类的数据
    const articles = await ctx.model.Content.find({
      $or: [
        { title: { $regex: keyWords } },
        { stitle: { $regex: keyWords } },
        { simpleComments: { $regex: keyWords } },
      ],
      isDel: 0,
      draft: '0',
      state: '2',
    });
    // 2、获取公开课数据（包括公开课培训）
    const courses = await ctx.service.adminTraining.getList({
      trainingType: 2,
      keyWord: data.keyWords,
      pageCurrent: 1,
      size: 20,
      userId: data.userId,
    });
    // 3、 查找用户的培训记录
    let training;
    if (data.hasLogin && data.userId) { // 管理员培训
      if (data.isManager && data.companyId && data.employeeId) {
        training = await ctx.model.PersonalTraining.aggregate([
          { $match: { EnterpriseID: data.companyId, employeesId: data.employeeId, status: true } },
          { $lookup: {
            from: 'adminTraining',
            localField: 'adminTrainingId',
            foreignField: '_id',
            as: 'oldAdminTraining',
          } },
          { $addFields: { originTraining: { $arrayElemAt: [ '$oldAdminTraining', 0 ] } } },
          { $match: { 'originTraining.name': { $regex: data.keyWords } } },
          { $sort: { createdAt: -1 } },
        ]);
      } else { // 员工培训
        training = await ctx.model.PersonalTraining.aggregate([
          { $match: { userId: data.userId, trainingType: 3, status: true } },
          { $lookup: {
            from: 'employeesTrainingPlan',
            localField: 'employeesTrainingPlanId',
            foreignField: '_id',
            as: 'oldEmployeesTrainingPlan',
          } },
          { $addFields: { originTraining: { $arrayElemAt: [ '$oldEmployeesTrainingPlan', 0 ] } } },
          { $match: { 'originTraining.name': { $regex: data.keyWords } } },
          { $sort: { createdAt: -1 } },
        ]);
      }
    }

    return {
      articles,
      courses,
      training: training || [],
    };
  }

}
module.exports = LearningService;
