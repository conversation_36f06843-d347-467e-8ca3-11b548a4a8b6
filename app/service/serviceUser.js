// 机构端用户注册登录
const Service = require('egg').Service;
// const _ = require('lodash');

const {
  _list,
  _item,
  _count,
  _create,
  _update,
  _removes,
  _safeDelete,
} = require('./general');

class UserService extends Service {

  // 给用户信息中插入org_id
  async inserOrgToUser(params) {
    const user_id = this.ctx.session.jcqlcInfo ? this.ctx.session.jcqlcInfo._id : params._id;
    return new Promise((res, rej) => {
      this.ctx.model.ServiceUser.updateOne({ _id: user_id }, { org_id: params.org_id, org: params.org }, (err, result) => {
        if (err) {
          rej(err);
        } else {
          res(result);
        }
      });
    });
  }

  async find(payload, {
    query = {},
    searchKeys = [],
    populate = [],
    files = null,
  } = {}) {

    const listdata = _list(this.ctx.model.ServiceUser, payload, {
      query,
      searchKeys,
      populate,
      files,
    });
    return listdata;

  }

  // 个人用户注册
  async createServiceUser(params) {
    const { ctx } = this;
    // 2. 创建用户
    try {
      let result = await ctx.model.ServiceUser.findOne({ phoneNum: params.phoneNum });
      if (!result) {
        console.log(params);
        result = await ctx.model.ServiceUser.create(params);
        return { errCod: 500, message: '用户创建失败' };
      }
      return { errCod: 0, data: result };
    } catch (error) {
      console.log(error);
      ctx.body = {
        status: 500,
        msg: '用户创建失败',
      };
    }
  }

  async count(params = {}) {
    return _count(this.ctx.model.ServiceUser, params);
  }

  async create(payload) {
    return _create(this.ctx.model.ServiceUser, payload);
  }

  async removes(res, values, key = '_id') {
    return _removes(res, this.ctx.model.ServiceUser, values, key);
  }

  async safeDelete(res, values) {
    return _safeDelete(res, this.ctx.model.ServiceUser, values);
  }

  async update(res, _id, payload) {
    return _update(res, this.ctx.model.ServiceUser, _id, payload);
  }

  async item(res, params = {}) {
    return _item(res, this.ctx.model.ServiceUser, params);
  }
}

module.exports = UserService;
