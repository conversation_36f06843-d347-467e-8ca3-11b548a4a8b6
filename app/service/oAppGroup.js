const Service = require('egg').Service;
const path = require('path');

const {
  _unionQuery,
  _list,
  _item,
  _count,
  _create,
  _update,
  _removes,
  _removeAll,
} = require(path.join(process.cwd(), 'app/service/general'));


class OAppGroupService extends Service {

  async unionQuery(payload, {
    collections = [],
    unwindArray = [],
    query = {},
    searchKeys = [],
    files = null,
    sort = {},
    statisticsFiles = [],
  } = {}) {

    const listdata = _unionQuery(this.ctx.model.OAppGroup, payload, {
      collections,
      unwindArray,
      query,
      searchKeys,
      files,
      sort,
      statisticsFiles,
    });
    return listdata;
  }

  async find(payload, {
    query = {},
    searchKeys = [],
    populate = [],
    files = null,
    sort = {},
  } = {}) {

    const listdata = _list(this.ctx.model.OAppGroup, payload, {
      query,
      searchKeys,
      populate,
      files,
      sort,
    });
    return listdata;

  }


  async count(params = {}) {
    return _count(this.ctx.model.OAppGroup, params);
  }

  async create(payload) {
    return _create(this.ctx.model.OAppGroup, payload);
  }

  async removes(res, values, key = '_id') {
    return _removes(res, this.ctx.model.OAppGroup, values, key);
  }

  async removeAll() {
    return _removeAll(this.ctx.model.OAppGroup);
  }

  async update(res, _id, payload) {
    return _update(res, this.ctx.model.OAppGroup, _id, payload);
  }

  async item(res, params = {}) {
    return _item(res, this.ctx.model.OAppGroup, params);
  }

}

module.exports = OAppGroupService;
