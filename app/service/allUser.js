const Service = require('egg').Service;

const _ = require('lodash');
const path = require('path');
// general是一个公共库，可用可不用
const {
  _item,
} = require(path.join(process.cwd(), 'app/service/general'));

class allUserService extends Service {
  async getToolsTargetUser(params) {
    let queryField = '';
    if (params.phoneNum) {
      queryField = 'phoneNum';
    } else if (params.userName) {
      queryField = 'userName';
    } else if (params._id) {
      queryField = '_id';
    } else {

      return;
    }
    const queryUserObj = {
      $or: [{
        [queryField]: params[queryField],
      }, {
        [queryField]: '0' + params[queryField],
      }],
      // countryCode,
    };
    let model = '';
    console.log(model);
    const files = this.getAuthUserFields('login');
    console.log(files, queryUserObj);
    let user = await _item(this.ctx, this.ctx.model.User, {
      query: _.assign({
        state: '1',
      }, queryUserObj),
      files: files ? files : '-password',
    });
    if (user) {
      model = 'User';
    } else {
      user = await _item(this.ctx, this.ctx.model.AdminUser, {
        query: _.assign({
          state: '1',
        }, queryUserObj),
        files: files ? files : '-password',
      });
      if (user) {
        model = 'AdminUser';
      } else {
        user = await _item(this.ctx, this.ctx.model.ServiceUser, {
          query: _.assign({
            state: '1',
          }, queryUserObj),
          files: files ? files : '-password',
        });
        if (user) {
          model = 'ServiceUser';
        } else {
          user = await _item(this.ctx, this.ctx.model.JcUser, {
            query: _.assign({
              state: '1',
            }, queryUserObj),
            files: files ? files : '-password',
          });
          if (user) {
            model = 'JcUser';
          } else {
            user = await _item(this.ctx, this.ctx.model.OperateUser, {
              query: _.assign({
                state: '1',
              }, queryUserObj),
              files: files ? files : '-password',
            });
            if (user) {
              model = 'OperateUser';
            } else {
              user = await _item(this.ctx, this.ctx.model.PhysicalExamUser, {
                query: _.assign({
                  state: '1',
                }, queryUserObj),
                files: files ? files : '-password',
              });
              if (user) {
                model = 'PhysicalExamUser';
              }
            }
          }
        }

      }


    }
    console.log(model);
    console.log('logAllUer');
    return { model, user };

  }
  getAuthUserFields(type = '') {
    let fieldStr = 'id enable _id email userName logo phoneNum name companyId company idNo companyStatus employeeId';
    if (type === 'login') {
      fieldStr = 'employeeId enable password _id email userName logo phoneNum name companyId company idNo companyStatus employeeId';
    } else if (type === 'base') {
      fieldStr = 'id userName name password category group logo date enable state phoneNum idNo companyStatus employeeId company countryCode email watchers followers comments idNo favorites favoriteCommunityContent despises comments profession experience industry introduction birth gender';
    } else if (type === 'session') {
      fieldStr = 'id userName name password category group logo date enable state phoneNum companyStatus company employeeId countryCode watchers followers praiseContents praiseMessages praiseCommunityContent watchSpecials watchCommunity watchTags favorites favoriteCommunityContent despises despiseMessage despiseCommunityContent idNo position gender vip email comments';
    }
    return fieldStr;
  }

}

module.exports = allUserService;
