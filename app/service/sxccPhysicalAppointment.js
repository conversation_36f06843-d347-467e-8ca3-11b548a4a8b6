const Service = require('egg').Service;
const moment = require('moment');
const fs = require('fs');
const path = require('path');
const mkdirp = require('mkdirp');

class SxccPhysicalAppointmentService extends Service {
  // 获取山西焦煤体检预约列表
  async tjPlanListV1_0(physicalExamOrgId, query) {
    // this.ctx.auditLog('Service/tjPlanList山西焦煤体检预约列表', query);

    // 从query中解构参数
    const { startTime, endTime } = query;
    const page = parseInt(query.page) || 1;
    const limit = parseInt(query.limit) || 10;

    // 构建查询条件
    const queryCondition = {};
    // if (status) {
    //   queryCondition.status = status;
    // }
    if (startTime && endTime) {
      queryCondition.$and = [
        { checkStartDate: { $lte: new Date(endTime) } },
        { checkEndDate: { $gte: new Date(startTime) } },
      ];
    }
    queryCondition.physicalExaminationOrgID = physicalExamOrgId;

    // 设置分页
    const skip = (page - 1) * limit;

    try {
    // 查询 tjPlan 数据库
      const tjPlanList = await this.ctx.model.TjPlan.find(queryCondition)
        .skip(skip)
        .limit(limit);

      // this.ctx.auditLog('Service/tjPlanList山西焦煤体检预约列表666', tjPlanList);
      const list = [];
      for (let i = 0; i < tjPlanList.length; i++) {
        const tjPlan = tjPlanList[i];
        // 从tjPlan中解构参数
        // 获取体检人员id
        const employeeIds = tjPlan.employees.map(employee => employee.employeeId);
        // 根据tjPlan.EnterpriseID查询adminorgs数据库
        const adminorg = await this.ctx.model.Adminorg.findOne(
          { _id: tjPlan.EnterpriseID },
          {
            _id: 1,
            cname: 1,
            code: 1,
            regAdd: 1,
            phoneNum: 1,
            adminUserId: 1,
            companyScale: 1, // 企业规模
            industryCategory: 1, // 行业类别
            regType: 1, // 注册类型
            districtRegAdd: 1, // 注册地址
            workAddress: 1, // 工作场所
          }
        );
        // 根据tjPlan.adminUserId查询adminusers数据库
        let adminuser = {};
        if (adminorg && adminorg.adminUserId) {
          adminuser = await this.ctx.model.AdminUser.findOne({ _id: adminorg.adminUserId }, { name: 1, phoneNum: 1 });
        }
        let areaEmployer = {};
        if (adminorg && adminorg.districtRegAdd) {
          areaEmployer = await this.ctx.model.District.findOne({ level: '3', name: adminorg.districtRegAdd[adminorg.districtRegAdd.length - 1] }, { area_code: 1 });
        }
        const employerInfo = {
          _id: adminorg._id,
          EnterpriseID: tjPlan.EnterpriseID,
          creditCode: adminorg.code,
          employerName: adminorg.cname,
          contactPerson: adminuser.name || '',
          employerPhone: adminorg.phoneNum,
          enterpriseSizeCode: adminorg.companyScale || '',
          industryCategoryCode: adminorg.industryCategory || [],
          areaCodeEmployer: areaEmployer ? areaEmployer.area_code : '',
          economicTypeCode: adminorg.regType || '',
          address: adminorg.regAdd,
          workAddress: adminorg.workAddress || [],
        };
        const listInfo = {
          // isReview: tjPlan.reviewStatus,
          employeeIds,
          _id: tjPlan._id,
          EnterpriseCode: adminorg.code,
          checkType: tjPlan.checkType,
          peopleNum: tjPlan.employees.length,
          startTime: tjPlan.checkStartDate,
          endTime: tjPlan.checkEndDate,
          createdAt: tjPlan.createAt,
          updatedAt: tjPlan.updateAt,
          status: tjPlan.status || 1,
          employerInfo,
        };
        list.push(listInfo);
      }
      // 返回查询结果
      return list;
    } catch (error) {
      this.ctx.auditLog('查询数据库时出错:', error.message, 'error');
      throw error;
    }
  }

  // 获取山西焦煤体检预约详情列表
  async tjPlanListDetailsV1_0(physicalExamOrgId, query) {
    // this.ctx.auditLog('Service/tjPlanListDetails山西焦煤体检预约详情', query);
    const tjPlanId = query._id;
    try {
      // 查询 tjPlan 数据库
      const tjPlan = await this.ctx.model.TjPlan.findOne({
        _id: tjPlanId,
        physicalExaminationOrgID: physicalExamOrgId,
      });

      if (!tjPlan) {
        throw new Error('未查询到该体检计划');
      }
      // 使用聚合管道从 tjPlan 中提取 employeeIds
      const result = await this.ctx.model.TjPlan.aggregate([
        {
          $match: {
            _id: tjPlanId,
            physicalExaminationOrgID: physicalExamOrgId,
          },
        },
        {
          $project: {
            employeeIds: '$employees.employeeId',
          },
        },
      ]);

      if (result.length === 0 || !result[0].employeeIds) {
        throw new Error('未查询到体检计划中的员工信息');
      }
      // 获取体检人员的ID
      const employeeIds = result[0].employeeIds;
      const employeeInfoArr = await this.ctx.model.User.aggregate([
        {
          $match: {
            employeeId: { $in: employeeIds },
          },
        },
        {
          $lookup: {
            from: 'employees',
            localField: 'employeeId',
            foreignField: '_id',
            as: 'employeeInfo',
          },
        },
        {
          $unwind: '$employeeInfo',
        },
        {
          $lookup: {
            from: 'appointmentdetails',
            let: { employeeId: '$employeeId' },
            pipeline: [
              {
                $match: {
                  $expr: {
                    $and: [
                      { $eq: [ '$employeeId', '$$employeeId' ] },
                      { $eq: [ '$tjPlanId', tjPlanId ] },
                    ],
                  },
                },
              },
              {
                $project: { checkItems: 1, _id: 0 },
              },
            ],
            as: 'checkItems',
          },
        },
        {
          $unwind: {
            path: '$checkItems',
            preserveNullAndEmptyArrays: true, // 如果没有匹配项，仍保留文档
          },
        },
        {
          $project: {
            _id: 1,
            name: 1,
            idcardType: '$idType',
            idcardCode: '$idNo',
            sexCode: {
              $cond: { if: { $eq: [ '$gender', '1' ] }, then: '女', else: '男' },
            },
            birthday: {
              $dateToString: {
                format: '%Y-%m-%d',
                date: { $add: [ '$birth', 8 * 60 * 60 * 1000 ] }, // 加8小时以转换为北京时间
              },
            },
            telPhone: '$phoneNum',
            jobCode: '$employeeInfo.workType',
            checkItems: '$checkItems.checkItems',
          },
        },
      ]);
      // 根据tjPlan.EnterpriseID查询adminorgs数据库
      const adminorg = await this.ctx.model.Adminorg.findOne(
        { _id: tjPlan.EnterpriseID },
        { _id: 1, cname: 1, code: 1, regAdd: 1, phoneNum: 1, adminUserId: 1 }
      );
      // 根据tjPlan.adminUserId查询adminusers数据库
      let adminuser = {};
      if (adminorg && adminorg.adminUserId) {
        adminuser = await this.ctx.model.AdminUser.findOne(
          { _id: adminorg.adminUserId },
          { name: 1, phoneNum: 1 }
        );
      }
      const employerInfo = {
        _id: adminorg._id,
        EnterpriseID: tjPlan.EnterpriseID,
        creditCode: adminorg.code,
        employerName: adminorg.cname,
        contactPerson: adminuser.name || '',
        employerPhone: adminorg.phoneNum,
        address: adminorg.regAdd,
      };
      const listInfo = {
        // isReview: tjPlan.reviewStatus,
        employeeIds: employeeInfoArr,
        _id: tjPlan._id,
        EnterpriseCode: adminorg.code,
        checkType: tjPlan.checkType,
        peopleNum: tjPlan.employees.length,
        startTime: tjPlan.checkStartDate,
        endTime: tjPlan.checkEndDate,
        createdAt: tjPlan.createAt,
        updatedAt: tjPlan.updateAt,
        employerInfo,
        status: tjPlan.status || 1,
      };
      // 返回查询结果
      return listInfo;
    } catch (error) {
      this.ctx.auditLog('查询数据库时出错:', error.message, 'error');
      throw error;
    }
  }

  // 获取山西焦煤个人体检预约详情列表
  async IndividualAppointmentV1_0(physicalExamOrgId, query) {
    // this.ctx.auditLog('Service/IndividualAppointment山西焦煤体检预约详情', query);

    // 从query中解构参数
    const { startTime, endTime } = query;
    const page = parseInt(query.page) || 1;
    const limit = parseInt(query.limit) || 10;

    // 构建查询条件
    const queryCondition = {};
    // if (status) {
    //   queryCondition.status = status;
    // }
    if (startTime && endTime) {
      queryCondition.appointDate = { $gte: new Date(startTime), $lte: new Date(endTime) };
    } else if (startTime) {
      queryCondition.appointDate = { $gte: new Date(startTime) };
    } else if (endTime) {
      queryCondition.appointDate = { $lte: new Date(endTime) };
    }
    queryCondition.physicalExaminationOrgId = physicalExamOrgId;

    // 设置分页
    const skip = (page - 1) * limit;

    try {
    // 查询 appointmentDetails 数据库
      const appointments = await this.ctx.model.AppointmentDetails.find(queryCondition)
        .skip(skip)
        .limit(limit);

      // 查询 user 和 employee 数据库
      const appointmentsInfoArr = [];
      for (let i = 0; i < appointments.length; i++) {
        const tjPlanId = appointments[i].tjPlanId;
        const employeeId = appointments[i].employeeId;
        const apointmentDate = moment(appointments[i].appointDate).format('YYYY-MM-DD'); // 格式化日期
        const checkMenu = appointments[i].physicalExaminationName;
        const { filteredItems, totalBasePrice } = this.removeDuplicate(appointments[i].checkItems);
        const checkItems = filteredItems;
        const totalPrice = appointments[i].totalPrice;
        const enterprisePay = appointments[i].enterprisePay;
        const selectionQuota = appointments[i].selectionQuota;
        const selfPay = appointments[i].selfPay;
        const userInfo = await this.ctx.model.User.findOne({ employeeId }, { _id: 1, name: 1, idType: 1, idNo: 1, gender: 1, birth: 1, phoneNum: 1 });
        const employeeInfo = await this.ctx.model.Employee.findOne({ _id: employeeId }, { workType: 1 });
        const appointmentsInfo = {
          _id: userInfo._id,
          tjPlanId,
          name: userInfo.name,
          idcardType: userInfo.idType,
          idcardCode: userInfo.idNo,
          sexCode: userInfo.gender === '1' ? '女' : '男',
          birthday: userInfo.birth,
          telPhone: userInfo.phoneNum,
          jobCode: employeeInfo.workType,
          apointmentDate,
          checkMenu,
          checkItems,
          totalPrice,
          enterprisePay,
          selfPay,
          selectionQuota,
          totalBasePrice,
        };
        // this.ctx.auditLog('Service/IndividualAppointment山西焦煤体检预约详情列表666', appointmentsInfo);
        appointmentsInfoArr.push(appointmentsInfo);
      }
      // 返回查询结果
      return appointmentsInfoArr;
    } catch (error) {
      this.ctx.auditLog('查询数据库时出错:', error.message, 'error');
      throw error;
    }
  }

  // 山西焦煤体检人员现场签到
  async IndividualCheckInV1_0(physicalExamOrgId, query) {
    // this.ctx.auditLog('Service/IndividualCheckIn山西焦煤体检人员现场签到', query);
    const { name, idType, idNo, tjPlanId } = query;
    try {
      // 根据 name、idType、idNo 查询 employees 数据库
      const userInfo = await this.ctx.model.User.findOne({ name, idType, idNo }, { _id: 1 });
      if (!userInfo) {
        throw new Error('未查询到该员工信息');
      }

      // 根据 employeeId、EnterpriseID、physicalExaminationOrgId、tjPlanId 查询 appointmentDetails 数据库
      const appointmentDetails = await this.ctx.model.AppointmentDetails.findOne(
        {
          employeeId: userInfo._id,
          physicalExaminationOrgId: physicalExamOrgId,
          tjPlanId,
        },
        { _id: 1, appointDate: 1, physicalExaminationOrgName: 1, physicalExaminationName: 1 }
      );
      if (!appointmentDetails) {
        throw new Error('未查询到该员工的体检预约信息');
      }
      // 更新 appointmentDetails 数据库
      const result = await this.ctx.model.AppointmentDetails.updateOne(
        { tjPlanId, physicalExaminationOrgId: physicalExamOrgId, employeeId: userInfo._id },
        { $set: { status: '2', isCheckIn: '1' } }
      );
      // 更新 TjPlan 数据库
      const resultTjPlan = await this.ctx.model.TjPlan.updateOne(
        { _id: tjPlanId, physicalExaminationOrgID: physicalExamOrgId, 'employees.employeeId': userInfo._id },
        { $set: { 'employees.$.checkStatus': 2, 'employees.$.isCheckIn': '1' } }
      );
      if (result.nModified === 0 || resultTjPlan.nModified === 0) {
        throw new Error('更新体检预约信息失败');
      }

      // 返回查询结果
      return '签到成功';
    } catch (error) {
      this.ctx.auditLog('查询数据库时出错:', error.message, 'error');
      throw error;
    }
  }

  // 推送体检计划列表
  async pushPhysicalPlanList() {
    const { ctx } = this;
    const isFullPushXk = this.config.isFullPushXk;
    ctx.auditLog('山西焦煤推送体检计划列表定时任务开始', new Date(), 'info');
    // 查询当天的体检计划
    const startOfToday = moment().startOf('day').toISOString();
    const endOfToday = moment().endOf('day').toISOString();

    // 构建查询条件
    const queryCondition = {};
    // queryCondition.$and = [
    //   { checkStartDate: { $lte: new Date(endOfToday) } },
    //   { checkEndDate: { $gte: new Date(startOfToday) } },
    // ];
    queryCondition.updatedAt = {
      $gte: new Date(startOfToday),
      $lte: new Date(endOfToday),
    };
    if (isFullPushXk) {
      delete queryCondition.updatedAt;
    }

    try {
      const tjPlanList = await ctx.model.TjPlan.find(queryCondition);

      const list = [];
      for (let i = 0; i < tjPlanList.length; i++) {
        const tjPlan = tjPlanList[i];
        // 从tjPlan中解构参数
        // 获取体检人员id
        const employeeIds = tjPlan.employees.map(employee => employee.employeeId);
        // 根据tjPlan.EnterpriseID查询adminorgs数据库
        const adminorg = await this.ctx.model.Adminorg.findOne(
          { _id: tjPlan.EnterpriseID },
          {
            _id: 1,
            cname: 1,
            code: 1,
            regAdd: 1,
            phoneNum: 1,
            adminUserId: 1,
            companyScale: 1, // 企业规模
            industryCategory: 1, // 行业类别
            regType: 1, // 注册类型
            districtRegAdd: 1, // 注册地址
            workAddress: 1, // 工作场所
          }
        );
        // 根据tjPlan.adminUserId查询adminusers数据库
        let adminuser = {};
        if (adminorg && adminorg.adminUserId) {
          adminuser = await this.ctx.model.AdminUser.findOne({ _id: adminorg.adminUserId }, { name: 1, phoneNum: 1 });
        }
        let areaEmployer = {};
        if (adminorg && adminorg.districtRegAdd) {
          areaEmployer = await this.ctx.model.District.findOne({ level: '3', name: adminorg.districtRegAdd[adminorg.districtRegAdd.length - 1] }, { area_code: 1 });
        }
        const employerInfo = {
          _id: adminorg._id,
          EnterpriseID: tjPlan.EnterpriseID,
          creditCode: adminorg.code,
          employerName: adminorg.cname,
          contactPerson: adminuser.name || '',
          employerPhone: adminorg.phoneNum,
          enterpriseSizeCode: adminorg.companyScale || '',
          industryCategoryCode: adminorg.industryCategory || [],
          areaCodeEmployer: areaEmployer ? areaEmployer.area_code : '',
          economicTypeCode: adminorg.regType || '',
          address: adminorg.regAdd,
          workAddress: adminorg.workAddress || [],
        };
        const listInfo = {
          isReview: tjPlan.reviewStatus,
          employeeIds,
          _id: tjPlan._id,
          EnterpriseCode: adminorg.code,
          checkType: tjPlan.checkType,
          peopleNum: tjPlan.employees.length,
          startTime: tjPlan.checkStartDate,
          endTime: tjPlan.checkEndDate,
          createdAt: tjPlan.createAt,
          updatedAt: tjPlan.updateAt,
          status: tjPlan.status || 1,
          employerInfo,
        };
        list.push(listInfo);
      }
      // ctx.auditLog('推送体检计划列表:', JSON.stringify(list));
      ctx.auditLog('推送体检计划列表:', list.length, 'info');
      // 推送体检计划列表
      if (list.length > 0) {
        await ctx.curl(`${this.config.sxccXkHost}/manager/front/zwy/receiveTjPlanList`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          dataType: 'json',
          data: list,
        });
        // ctx.auditLog('推送体检计划列表结果:', res);
      } else {
        ctx.auditLog('无体检计划列表数据');
      }
    } catch (error) {
      ctx.auditLog('查询数据库时出错:', error.message, 'error');
      throw error;
    }
  }

  // 推送体检计划详情
  async pushPhysicalPlanDetail() {
    const { ctx } = this;
    const isFullPushXk = this.config.isFullPushXk;
    ctx.auditLog('山西焦煤推送体检计划详情定时任务开始', new Date(), 'info');
    // 查询当天的体检计划
    const startOfToday = moment().startOf('day').toISOString();
    const endOfToday = moment().endOf('day').toISOString();

    // 构建查询条件
    const queryCondition = {};
    // queryCondition.$and = [
    //   { checkStartDate: { $lte: new Date(endOfToday) } },
    //   { checkEndDate: { $gte: new Date(startOfToday) } },
    // ];
    queryCondition.updatedAt = {
      $gte: new Date(startOfToday),
      $lte: new Date(endOfToday),
    };

    if (isFullPushXk) {
      delete queryCondition.updatedAt;
    }

    try {
      const tjPlanList = await ctx.model.TjPlan.find(queryCondition);

      const tjPlanIdArr = [];
      for (let i = 0; i < tjPlanList.length; i++) {
        const tjPlan = tjPlanList[i];
        const tjPlanId = tjPlan._id;
        // 从tjPlan中解构参数
        // 使用聚合管道从 tjPlan 中提取 employeeIds
        const result = await this.ctx.model.TjPlan.aggregate([
          {
            $match: {
              _id: tjPlanId,
            },
          },
          {
            $project: {
              employeeIds: '$employees.employeeId',
            },
          },
        ]);

        if (result.length === 0 || !result[0].employeeIds) {
          throw new Error('未查询到体检计划中的员工信息');
        }
        // 获取体检人员的ID
        const employeeIds = result[0].employeeIds;
        const employeeInfoArr = await this.ctx.model.User.aggregate([
          {
            $match: {
              employeeId: { $in: employeeIds },
            },
          },
          {
            $lookup: {
              from: 'employees',
              localField: 'employeeId',
              foreignField: '_id',
              as: 'employeeInfo',
            },
          },
          {
            $unwind: '$employeeInfo',
          },
          {
            $lookup: {
              from: 'appointmentdetails',
              let: { employeeId: '$employeeId' },
              pipeline: [
                {
                  $match: {
                    $expr: {
                      $and: [
                        { $eq: [ '$employeeId', '$$employeeId' ] },
                        { $eq: [ '$tjPlanId', tjPlanId ] },
                      ],
                    },
                  },
                },
                {
                  $project: { checkItems: 1, _id: 0 },
                },
              ],
              as: 'checkItems',
            },
          },
          {
            $unwind: {
              path: '$checkItems',
              preserveNullAndEmptyArrays: true, // 如果没有匹配项，仍保留文档
            },
          },
          {
            $lookup: {
              from: 'tjPlan',
              let: { employeeId: '$employeeId', tjPlanId },
              pipeline: [
                {
                  $match: {
                    $expr: {
                      $eq: [ '$_id', '$$tjPlanId' ], // 使用 _id 匹配 tjPlanId
                    },
                  },
                },
                {
                  $project: {
                    employees: {
                      $filter: {
                        input: '$employees',
                        as: 'employee',
                        cond: { $eq: [ '$$employee.employeeId', '$$employeeId' ] }, // 在 employees 数组中匹配 employeeId
                      },
                    },
                  },
                },
              ],
              as: 'tjPlanInfo',
            },
          },
          {
            $unwind: {
              path: '$tjPlanInfo',
              preserveNullAndEmptyArrays: true,
            },
          },
          {
            $unwind: {
              path: '$tjPlanInfo.employees', // 展开 employees 数组，提取对应的员工信息
              preserveNullAndEmptyArrays: true,
            },
          },
          {
            $project: {
              _id: 1,
              name: 1,
              idcardType: '$idType',
              idcardCode: '$idNo',
              sexCode: {
                $cond: { if: { $eq: [ '$gender', '1' ] }, then: '女', else: '男' },
              },
              birthday: {
                $dateToString: {
                  format: '%Y-%m-%d',
                  date: { $add: [ '$birth', 8 * 60 * 60 * 1000 ] }, // 加8小时以转换为北京时间
                },
              },
              telPhone: '$phoneNum',
              jobCode: '$employeeInfo.workType',
              checkItems: '$checkItems.checkItems',
              departName: '$tjPlanInfo.employees.departName',
              appointmentStatus: '$tjPlanInfo.employees.appointmentStatus',
            },
          },
        ]);
        // 根据tjPlan.EnterpriseID查询adminorgs数据库
        const adminorg = await this.ctx.model.Adminorg.findOne(
          { _id: tjPlan.EnterpriseID },
          { _id: 1, cname: 1, code: 1, regAdd: 1, phoneNum: 1, adminUserId: 1 }
        );
        // 根据tjPlan.adminUserId查询adminusers数据库
        let adminuser = {};
        if (adminorg && adminorg.adminUserId) {
          adminuser = await this.ctx.model.AdminUser.findOne(
            { _id: adminorg.adminUserId },
            { name: 1, phoneNum: 1 }
          );
        }
        const employerInfo = {
          _id: adminorg._id,
          EnterpriseID: tjPlan.EnterpriseID,
          creditCode: adminorg.code,
          employerName: adminorg.cname,
          contactPerson: adminuser.name || '',
          employerPhone: adminorg.phoneNum,
          address: adminorg.regAdd,
        };
        const listInfo = {
          isReview: tjPlan.reviewStatus,
          tjPlanId,
          employeeIds: employeeInfoArr,
          _id: tjPlan._id,
          EnterpriseCode: adminorg.code,
          checkType: tjPlan.checkType,
          peopleNum: tjPlan.employees.length,
          startTime: tjPlan.checkStartDate,
          endTime: tjPlan.checkEndDate,
          createdAt: tjPlan.createAt,
          updatedAt: tjPlan.updateAt,
          employerInfo,
          status: tjPlan.status || 1,
        };

        // ctx.auditLog('推送体检计划详情:', JSON.stringify(listInfo));
        tjPlanIdArr.push(tjPlan._id);
        if (tjPlanId) {
          // 推送体检计划详情
          await ctx.curl(`${this.config.sxccXkHost}/manager/front/zwy/receiveTjPlanListDetails`, {
            method: 'POST',
            dataType: 'json',
            headers: {
              'Content-Type': 'application/json',
            },
            data: listInfo,
          });
          // ctx.auditLog('推送体检计划详情结果:', res);
        } else {
          ctx.auditLog('无体检计划详情数据');
        }
      }
      ctx.auditLog('推送体检计划详情计划ID:', tjPlanIdArr, 'info');
    } catch (error) {
      ctx.auditLog('查询数据库时出错:', error.message, 'error');
      throw error;
    }
  }

  // 推送个人体检预约详情
  async pushIndividualAppointment() {
    const { ctx } = this;
    const isFullPushXk = this.config.isFullPushXk;
    ctx.auditLog('山西焦煤推送个人体检预约详情定时任务开始', new Date(), 'info');
    // 查询当天的体检计划
    const startOfToday = moment().startOf('day').toISOString();
    const endOfToday = moment().endOf('day').toISOString();

    // 构建查询条件
    const queryCondition = {};
    queryCondition.status = '0';
    queryCondition.updatedAt = {
      $gte: new Date(startOfToday),
      $lte: new Date(endOfToday),
    };
    if (isFullPushXk) {
      delete queryCondition.status;
      delete queryCondition.updatedAt;
    }

    try {
      const appointments = await ctx.model.AppointmentDetails.find(queryCondition);

      // 查询 user 和 employee 数据库
      const appointmentsInfoArr = [];
      const employeeIdArr = [];
      for (let i = 0; i < appointments.length; i++) {
        const tjPlanId = appointments[i].tjPlanId;
        // ctx.auditLog('tjPlanId:', tjPlanId);
        const employeeId = appointments[i].employeeId;
        const apointmentDate = moment(appointments[i].appointDate).format('YYYY-MM-DD'); // 格式化日期
        const checkMenu = appointments[i].physicalExaminationName;
        const { filteredItems, totalBasePrice } = this.removeDuplicate(appointments[i].checkItems);
        const checkItems = filteredItems;
        const totalPrice = appointments[i].totalPrice;
        const enterprisePay = appointments[i].enterprisePay;
        const selectionQuota = appointments[i].selectionQuota;
        const selfPay = appointments[i].selfPay;
        const userInfo = await this.ctx.model.User.findOne({ employeeId }, { _id: 1, name: 1, idType: 1, idNo: 1, gender: 1, birth: 1, phoneNum: 1 });
        const employeeInfo = await this.ctx.model.Employee.findOne({ _id: employeeId }, { workType: 1 });
        const appointmentsInfo = {
          _id: userInfo._id,
          tjPlanId,
          name: userInfo.name,
          idcardType: userInfo.idType,
          idcardCode: userInfo.idNo,
          sexCode: userInfo.gender === '1' ? '女' : '男',
          birthday: userInfo.birth,
          telPhone: userInfo.phoneNum,
          jobCode: employeeInfo.workType,
          apointmentDate,
          checkMenu,
          checkItems,
          totalPrice,
          enterprisePay,
          selfPay,
          selectionQuota,
          totalBasePrice,
        };
        // ctx.auditLog('Service/IndividualAppointment山西焦煤体检预约详情列表666', appointmentsInfo);
        appointmentsInfoArr.push(appointmentsInfo);
        employeeIdArr.push(employeeId);
      }
      // ctx.auditLog('推送个人体检预约详情:', JSON.stringify(appointmentsInfoArr));
      ctx.auditLog('推送个人体检预约详情人员ID:', employeeIdArr, 'info');
      ctx.auditLog('推送个人体检预约详情:', appointmentsInfoArr.length, 'info');
      if (appointmentsInfoArr.length > 0) {
        // 推送个人体检预约详情
        await ctx.curl(`${this.config.sxccXkHost}/manager/front/zwy/receiveIndividualAppointment`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          dataType: 'json',
          data: appointmentsInfoArr,
        });
        // ctx.auditLog('推送个人体检预约详情结果:', res);
      } else {
        ctx.auditLog('无个人体检预约详情数据');
      }
    } catch (error) {
      ctx.auditLog('查询数据库时出错:', error.message, 'error');
      throw error;
    }
  }

  // 去除预约详情中重复项目
  removeDuplicate(checkItems) {
    if (!checkItems || checkItems.length === 0) {
      return {
        filteredItems: [],
        totalBasePrice: 0,
      };
    }
    let totalBasePrice = 0;
    const cnCodeSets = checkItems.map(
      item => {
        if (item.type === '0') {
          totalBasePrice += item.price || 0;
        }
        return new Set(item.comments.map(c => c.cnCode));
      }
    );

    // 判断 set1 是否完全包含 set2
    function isSubset(set1, set2) {
      for (const value of set2) {
        if (!set1.has(value)) {
          return false;
        }
      }
      return true;
    }

    // 过滤掉那些 cnCode 被完全包含在其他项中的项
    const filteredItems = checkItems.filter((item, index) => {
      const currentSet = cnCodeSets[index];
      return !cnCodeSets.some(
        (otherSet, otherIndex) =>
          otherIndex !== index && isSubset(otherSet, currentSet)
      );
    });
    return {
      filteredItems,
      totalBasePrice,
    };
  }

  // 山西焦煤轮询体检系统的签到状态
  async queryIndividualCheckIn() {
    this.ctx.auditLog('山西焦煤轮询体检系统的体检结果和签到状态定时任务开始', new Date(), 'info');

    try {
      const allCheckInData = [];
      const startData = moment().startOf('day').format('YYYY-MM-DD HH:mm:ss');
      const endData = moment().endOf('day').format('YYYY-MM-DD HH:mm:ss');
      let page = 1;
      let hasMoreData = true;

      while (hasMoreData) {
        const res = await this.ctx.curl(`${this.config.sxccXkHost}/manager/front/zwy/queryIndividualCheckIn`, {
          method: 'GET',
          dataType: 'json',
          data: {
            start_date: startData,
            end_date: endData,
            page,
          },
        });

        const checkInData = res.data;
        if (checkInData && checkInData.data && checkInData.data.list && checkInData.data.list.length > 0) {
          allCheckInData.push(...checkInData.data.list);
          page++;
          hasMoreData = checkInData.data.list.length === checkInData.data.page_size;
        } else {
          hasMoreData = false;
        }
      }

      // 处理所有签到信息
      // this.ctx.auditLog('所有签到信息:', JSON.stringify(allCheckInData));
      this.ctx.auditLog('获取签到信息:', allCheckInData.length, 'info');

      for (let i = 0; i < allCheckInData.length; i++) {
        const item = allCheckInData[i];
        // 查询体检计划
        const tjPlan = await this.ctx.model.TjPlan.findOne({ _id: item.plan_id });
        const tjPlanId = tjPlan._id;
        // 根据 item.idNo 查询 employees 数据库
        const employee = await this.ctx.model.Employee.findOne({ IDNum: item.idNo });
        const employeeId = employee._id;
        // 根据tjPlanId和employeeId更新tjPlan和appointmentDetails数据库
        await this.ctx.model.TjPlan.updateOne(
          { _id: tjPlanId, 'employees.employeeId': employeeId },
          {
            $set:
              { 'employees.$.isCheckIn': '1',
                'employees.$.checkStatus': 2,
              },
          }
        );
        await this.ctx.model.AppointmentDetails.updateOne(
          { tjPlanId, employeeId },
          {
            $set:
              { isCheckIn: '1',
                status: 2,
              },
          }
        );
      }

    } catch (error) {
      this.ctx.auditLog('查询签到信息时出错:', error.message, 'error');
      throw error;
    }
  }

  // 获取个人体检报告文件
  async queryHealthExamRecordFile(tjPlanId, employeeId) {
    // this.ctx.auditLog('山西焦煤获取个人体检报告文件定时任务开始', new Date(), 'info');

    try {
      const res = await this.ctx.curl(`${this.config.sxccXkHost}/manager/front/zwy/queryHealthExamRecordFile`, {
        method: 'POST',
        dataType: 'json',
        headers: {
          'Content-Type': 'application/json',
        },
        data: {
          _id: employeeId._id,
          tjPlanId,
        },
      });

      // const res = await this.ctx.curl('127.0.0.1:7009/api/sxccTestFile', {
      //   method: 'GET',
      //   dataType: 'json',
      //   headers: {
      //     'Content-Type': 'application/json',
      //   },
      //   data: {
      //   },
      // });

      if (res.status !== 200) {
        this.ctx.auditLog('查询体检报告文件信息失败', res, 'error');
      }

      // this.ctx.auditLog('获取个人体检报告文件信息结果:', tjPlanId, employeeId._id);

      // 查询suspect数据库中的caseCard字段是否有值
      const suspectInfo = await this.ctx.model.Suspect.findOne({ tjPlanId, employeeId: employeeId._id }, { caseCard: 1, EnterpriseID: 1 });
      const EnterpriseID = suspectInfo.EnterpriseID;
      if (suspectInfo && suspectInfo.caseCard && suspectInfo.caseCard.staticName) {
        // 删除oss上的文件
        await this.ctx.service.shaanxiData.deleteSourceFileCaseCard(suspectInfo.caseCard, EnterpriseID);
      }
      // // 根据res.data.data.code查询Adminorg数据库
      // const AdminorgInfo = await this.ctx.model.Adminorg.findOne({ code: res.data.data.code }, { _id: 1 });
      // 假设返回的data包含Base64字符串
      const base64Data = res.data.data.file; // 根据实际响应调整

      // 解码Base64数据
      const buf = Buffer.from(base64Data, 'base64');

      const timestamp = new Date().getTime();
      const randomCode = Math.floor(Math.random() * 1000000)
        .toString()
        .padStart(6, '0');
      // 生成文件名
      const fileName = timestamp + randomCode + employeeId._id;

      // 处理得到静态资源路径地址
      const filePath = path.join(this.ctx.app.config.upload_path, EnterpriseID);

      // 创建输出目录
      mkdirp.sync(filePath);

      const localFilePath = path.resolve(filePath, fileName + '.pdf');
      // 写入文件
      await fs.writeFileSync(path.resolve(filePath, fileName + '.pdf'), buf);

      const ossResult = await this.ctx.helper.pipe({
        readableStream: fs.createReadStream(
          path.resolve(filePath, fileName + '.pdf')
        ),
        target: path.resolve(filePath, fileName + '.pdf'),
      });

      if (ossResult.status === 200) {
        // 删除本地文件
        await fs.unlinkSync(path.resolve(localFilePath));
      }
      // 返回结果
      return {
        originName: fileName + '.pdf',
        staticName: fileName + '.pdf',
      };

    } catch (error) {
      this.ctx.auditLog('查询体检报告文件信息时出错:', error.message, 'error');
      throw error;
    }
  }

  // 获取职业健康档案-json-国家标准
  async queryHealthExamRecord() {
    // this.ctx.auditLog('山西焦煤获取职业健康档案定时任务开始', new Date());

    try {
      // 查询体检结果信息
      // 时间为今天
      // const startData = '2024-09-09 00:00:00';
      const startData = moment().startOf('day').format('YYYY-MM-DD HH:mm:ss');
      const endData = moment().endOf('day').format('YYYY-MM-DD HH:mm:ss');
      let page = 1;
      let hasMoreData = true;

      const employeeIdsArr = [];
      while (hasMoreData) {
        const res = await this.ctx.curl(`${this.config.sxccXkHost}/manager/front/zwy/queryHealthExamRecord`, {
          method: 'GET',
          dataType: 'json',
          headers: {
            'Content-Type': 'application/json',
          },
          data: {
            start_date: startData,
            end_date: endData,
            page,
          },
        });

        // const res = await this.ctx.curl('127.0.0.1:7009/api/sxccTest', {
        //   method: 'GET',
        //   dataType: 'json',
        //   headers: {
        //     'Content-Type': 'application/json',
        //   },
        //   data: {
        //   },
        // });

        // this.ctx.auditLog('查询体检结果信息结果:', res);

        if (res.status !== 200) {
          this.ctx.auditLog('查询体检结果信息失败', res, 'error');
        }

        // this.ctx.auditLog('查询体检结果信息结果:', JSON.stringify(res.data.data.list));

        for (const item of res.data.data.list) {
          // this.ctx.auditLog('获取职业健康档案:', item.HEALTH_EXAM_RECORD_LIST.ID, 'info');
          const tjPlanId = item.HEALTH_EXAM_RECORD_LIST.WORKER_INFO.PLAN_ID;
          // 查询体检计划
          const tjPlan = await this.ctx.model.TjPlan.findOne({ _id: tjPlanId });
          const physicalExamOrgId = tjPlan.physicalExaminationOrgID;
          // 上传职业健康档案
          await this.healthExamRecord(physicalExamOrgId, item.HEALTH_EXAM_RECORD_LIST);
          // 获取个人体检报告文件
          const IDNum = item.HEALTH_EXAM_RECORD_LIST.WORKER_INFO.ID_CARD;
          const employeeId = await this.ctx.model.Employee.findOne({ IDNum }, { _id: 1 });
          const fileInfo = await this.queryHealthExamRecordFile(tjPlanId, employeeId);
          if (!fileInfo) {
            throw new Error('未查询到体检报告文件信息');
          }
          // 根据tjPlanId和employeeId更新Suspect数据库中caseCard字段
          const caseCardUpdate = {
            originName: fileInfo.originName || '',
            staticName: fileInfo.staticName || '',
          };
          // 根据tjPlanId和employeeId更新Suspect数据库中caseCard字段
          await this.ctx.model.Suspect.updateOne(
            { tjPlanId, employeeId: employeeId._id },
            { $set: { caseCard: caseCardUpdate } }
          );
          // 根据tjPlanId和employeeId更新tjPlan数据库中employees数组字段
          await this.ctx.model.TjPlan.updateOne(
            { _id: tjPlanId, 'employees.employeeId': employeeId._id },
            { $set: { 'employees.$.checkStatus': 1 } }
          );
          // 根据tjPlanId和employeeId更新appointmentDetails数据库中status字段
          await this.ctx.model.AppointmentDetails.updateOne(
            { tjPlanId, employeeId: employeeId._id },
            { $set: { status: 1 } }
          );
          employeeIdsArr.push(employeeId._id);
        }

        page++;
        hasMoreData = res.data.data.list.length === res.data.data.page_size;
      }

      this.ctx.auditLog('获取职业健康档案:', employeeIdsArr, 'info');
    } catch (error) {
      this.ctx.auditLog('查询体检结果信息时出错:', error.message, 'error');
      throw error;
    }
  }

  // 上传职业健康档案(单个)
  async itemParameterVerification(item) {
    if (!item.WORKER_INFO || !item.WORKER_INFO.ID_CARD_TYPE_CODE) {
      throw new Error('WORKER_INFO和ID_CARD_TYPE_CODE不能为空');
    }
    if (!item.WORKER_INFO.PLAN_ID) {
      throw new Error('PLAN_ID不能为空');
    }
    if (!item.ENTERPRISE_INFO_EMPLOYER || !item.ENTERPRISE_INFO_EMPLOYER.CREDIT_CODE_EMPLOYER) {
      throw new Error('ENTERPRISE_INFO_EMPLOYER和CREDIT_CODE_EMPLOYER不能为空');
    }
    if (!item.EXAM_CONCLUSION_LIST || !item.EXAM_CONCLUSION_LIST.length) {
      throw new Error('EXAM_CONCLUSION_LIST不能为空');
    }
    if (!item.WORKER_INFO.WORKER_TELPHONE) {
      throw new Error('WORKER_TELPHONE不能为空');
    }
    if (!item.WORKER_INFO.BHK_CODE) {
      throw new Error('BHK_CODE不能为空');
    }
    if (!item.EXAM_DATE) {
      throw new Error('EXAM_DATE不能为空');
    }
    if (!item.REPORT_DATE) {
      throw new Error('REPORT_DATE不能为空');
    }
    if (!item.EXAM_TYPE_CODE) {
      throw new Error('EXAM_TYPE_CODE不能为空');
    }
    if ([ '01', '02', '03', '04', '05', '06' ].indexOf(item.EXAM_TYPE_CODE) === -1) {
      throw new Error('EXAM_TYPE_CODE参数错误');
    }
  }

  // 上传职业健康档案(国家标准)
  async healthExamRecord(physicalExamOrgId, item) {
    try {
      await this.itemParameterVerification(item);
      if (item.EXAM_DATE && item.EXAM_DATE.length === 8) {
        item.EXAM_DATE = this.getTrueTime(item.EXAM_DATE);
      }
      if (item.REPORT_DATE && item.REPORT_DATE.length === 8) {
        item.REPORT_DATE = this.getTrueTime(item.REPORT_DATE);
      }
      const { ctx } = this;
      const physicalExamOrg = await ctx.model.PhysicalExamOrg.findOne({ _id: physicalExamOrgId });
      if (item.ORG_CODE && item.ORG_CODE !== physicalExamOrg.organization) {
        throw new Error('您的token跟上传的创建机构代码不匹配');
      }
      // 1、通过用工单位统一信用代码判断是否预约体检
      const EnterpriseCode = item.ENTERPRISE_INFO_EMPLOYER.CREDIT_CODE_EMPLOYER;
      const checkType = item.EXAM_TYPE_CODE;
      // 2、处理用工单位信息
      const Enterprise = await this.handleEnterprise(EnterpriseCode, item.ENTERPRISE_INFO_EMPLOYER, physicalExamOrg);
      // // 3、处理用人单位信息
      // if (item.ENTERPRISE_INFO && item.ENTERPRISE_INFO.CREDIT_CODE) {
      //   await this.handleEnterprise(item.ENTERPRISE_INFO.CREDIT_CODE, item.ENTERPRISE_INFO, physicalExamOrg);
      // }
      // 4、人员信息
      const workerInfo = item.WORKER_INFO;
      const personInfo = await this.handlePersonInfo({
        EnterpriseID: Enterprise._id,
        name: workerInfo.WORKER_NAME,
        IDNum: workerInfo.ID_CARD,
        laborDispatching: !!(item.ENTERPRISE_INFO && item.ENTERPRISE_INFO.CREDIT_CODE),
        gender: workerInfo.GENDER_CODE ? String(workerInfo.GENDER_CODE - 1) : '',
        phoneNum: workerInfo.WORKER_TELPHONE,
        userId: '',
        source: 'oapi',
        status: [ '03', '05' ].includes(checkType) ? 0 : 1,
      });
      if (item.EXAM_TYPE_CODE === '06') {
        return {
          status: 200,
          message: '普通体检不创建体检项目, 企业信息和人员信息已创建成功',
        };
      }
      const tjPlanId = item.WORKER_INFO.PLAN_ID;
      // 5、体检项目
      const healthcheck = await this.handleHealthCheck(physicalExamOrg, item, Enterprise, tjPlanId);
      // 6、体检信息
      await this.handleSuspect(healthcheck, personInfo, item);
      // // 7、更新预约单
      // if (appointment) await this.updateAppointment(appointment, healthcheck._id, personInfo);
      // 8、更新体检项目中的人数以及企业表中的healcheckInfo
      await this.updateHealthCheck(healthcheck, Enterprise, item, tjPlanId);
      return {
        status: 200,
        tjPlanId: item.WORKER_INFO.PLAN_ID,
        message: `用工单位：${item.ENTERPRISE_INFO_EMPLOYER.ENTERPRISE_NAME_EMPLOYER || Enterprise.cname} - ${item.WORKER_INFO.WORKER_NAME}的职业健康档案上传成功`,
      };
    } catch (e) {
      this.ctx.auditLog('体检系统对接 上传职业健康档案失败', e.message, 'error');
      return {
        status: 500,
        message: (item.id || item.ID) + '上传失败：' + e.message,
      };
    }
  }

  // 时间转化
  getTrueTime(dateString) { // dateString = '20200101'
    if (!dateString || dateString.length !== 8) return '';
    const year = dateString.substring(0, 4);
    const month = dateString.substring(4, 6);
    const day = dateString.substring(6, 8);
    return new Date(year, month - 1, day);
  }

  // 处理企业信息(用工单位、用人单位)
  async handleEnterprise(EnterpriseCode) {
    const Enterprise = await this.ctx.model.Adminorg.findOne({ code: EnterpriseCode });
    if (!Enterprise) {
      throw new Error(`${EnterpriseCode}: 当前企业不存在`);
    }
    // if (!Enterprise) {
    //   // const { districtRegAdd = [], point = [] } = await this.ctx.service.district.getParentByCode(params.ADDRESS_CODE_EMPLOYER || params.ADDRESS_CODE);
    //   const industryCategory = params.INDUSTRY_CATEGORY_CODE_EMPLOYER || params.INDUSTRY_CATEGORY_CODE;
    //   const enterpriseData = {
    //     cname: params.ENTERPRISE_NAME_EMPLOYER || params.ENTERPRISE_NAME || '',
    //     code: EnterpriseCode,
    //     regType: params.ECONOMIC_TYPE_CODE_EMPLOYER || params.ECONOMIC_TYPE_CODE || '',
    //     industryCategory: industryCategory ? [ industryCategory ] : [],
    //     companyScale: params.BUSINESS_SCALE_CODE_EMPLOYER || params.BUSINESS_SCALE_CODE || '',
    //     // districtRegAdd,
    //     regAdd: params.ADDRESS_DETAIL || '',
    //     // workAddress: [{ districts: districtRegAdd, point, address: params.ADDRESS_DETAIL || '' }],
    //     workAddress: [{ address: params.ADDRESS_DETAIL || '' }],
    //     isactive: '1',
    //     leadIn: '3',
    //     physicalExaminationOrgID: [{
    //       _id: physicalExamOrg._id,
    //       ServiceContractName: physicalExamOrg.contract || '',
    //       ServicePhoneNum: physicalExamOrg.phoneNum || '',
    //     }],
    //   };
    //   Enterprise = await this.ctx.model.Adminorg.create(enterpriseData);
    // }
    return Enterprise;
  }

  // 创建/更新人员信息employee、user
  async handlePersonInfo(personInfo) {
    const { ctx } = this;
    const { EnterpriseID, IDNum } = personInfo;
    if (!IDNum) throw new Error('身份证号不能为空');
    const employeeInfo = await ctx.model.Employee.findOne({ EnterpriseID, IDNum });
    // if (!employeeInfo) employeeInfo = await ctx.model.Employee.findOne({ EnterpriseID, phoneNum });
    if (!employeeInfo) {
      // employeeInfo = await ctx.model.Employee.create(personInfo);
      // 直接跳过
      ctx.auditLog('体检系统对接 上传体检报告-人员信息不存在', personInfo, 'info');
      throw new Error(`${IDNum}:人员信息不存在`);
    }
    // await ctx.model.Employee.updateOne({ _id: employeeInfo._id }, { $set: personInfo });
    // ctx.auditLog('体检系统对接 上传体检报告-更新人员信息成功', personInfo, 'info');

    // if (phoneNum) {
    //   const flag = /^1[3-9][0-9]{9}$/.test(phoneNum); // 手机号校验
    //   if (!flag) throw new Error('手机号格式错误');
    //   let user = await ctx.model.User.findOne({ phoneNum });
    //   if (!user) {
    //     user = await ctx.model.User.create({ name: employeeInfo.name, phoneNum, employeeId: employeeInfo._id, idNo: IDNum, companyId: [ EnterpriseID ], companyStatus: 2, birth: personInfo.birthDate });
    //   }
    //   if (user && user._id) {
    //     await ctx.model.Employee.updateOne({ _id: employeeInfo._id }, { userId: user._id });
    //   }
    // }
    // if (employeeInfo.departs.length === 0) {
    //   let dingtrees = await ctx.model.Dingtree.findOne({ EnterpriseID });
    //   if (!dingtrees) {
    //     dingtrees = await ctx.model.Dingtree.create({ EnterpriseID, name: '企业部门', type: '1' });
    //   }
    //   await ctx.model.Employee.updateOne({ _id: employeeInfo._id }, { departs: [[ dingtrees._id ]] });
    //   await ctx.model.Dingtree.updateOne({ _id: dingtrees._id }, { $addToSet: { staff: employeeInfo._id } });
    // }
    return employeeInfo;
  }

  // 创建/更新体检项目(体检机构已存在)
  async handleHealthCheck(physicalExamOrgDetail, item, EnterpriseDetail, tjPlanId) {
    const { ctx } = this;
    // 查询体检计划
    const appointment = await ctx.model.TjPlan.findOne({ _id: tjPlanId });
    const physicalExaminationOrgID = physicalExamOrgDetail._id,
      EnterpriseID = EnterpriseDetail._id,
      year = new Date(item.EXAM_DATE).getFullYear() + '';
    const healthcheckeCode = {
      '01': '0',
      '02': '1',
      '03': '2',
      '04': '4',
      '05': '5',
    };
    const checkType = healthcheckeCode[item.EXAM_TYPE_CODE];
    // const recheck = !!(item.IS_REVIEW && item.IS_REVIEW === '1');
    // let healthchecke = await ctx.model.Healthcheck.findOne({ physicalExaminationOrgID, EnterpriseID, year, checkType, recheck });
    let healthchecke = await ctx.model.Healthcheck.findOne({ tjPlanId });
    if (!healthchecke) {
      const newData = {
        organization: item.REPORT_ORGAN_CREDIT_CODE || physicalExamOrgDetail.name, // 检查机构名称
        physicalExaminationOrgID,
        enterpriseName: item.ENTERPRISE_INFO_EMPLOYER.ENTERPRISE_NAME_EMPLOYER || EnterpriseDetail.cname,
        EnterpriseID,
        enterpriseContactsName: EnterpriseDetail.contract || '',
        enterpriseContactsPhonNumber: EnterpriseDetail.phoneNum || '',
        enterpriseAddr: EnterpriseDetail.districtRegAdd || [],
        workAddress: EnterpriseDetail.workAddress || [],
        year,
        projectNumber: 'WKZWY' + moment(item.EXAM_DATE).format('YYYYMMDD') + Math.random().toString().slice(2, 6),
        checkDate: appointment ? appointment.startTime : new Date(item.EXAM_DATE),
        checkEndDate: appointment ? appointment.endTime : new Date(item.EXAM_DATE),
        checkPlace: (physicalExamOrgDetail.regAddr ? physicalExamOrgDetail.regAddr.join('') : '') + physicalExamOrgDetail.address || '',
        approvalDate: new Date(item.REPORT_DATE),
        applyTime: new Date(),
        checkType,
        shouldCheckNum: appointment ? appointment.employees.length || appointment.peopleNum : 0,
        actuallNum: 0,
        recheck: !!(item.IS_REVIEW && item.IS_REVIEW === '1'),
        reportStatus: true,
      };
      healthchecke = await ctx.model.Healthcheck.create(newData);
    }
    // else {
    //   const updateData = {
    //     checkDate: new Date(item.EXAM_DATE) < healthchecke.checkDate ? new Date(item.EXAM_DATE) : healthchecke.checkDate,
    //     checkEndDate: new Date(item.EXAM_DATE) > healthchecke.checkEndDate ? new Date(item.EXAM_DATE) : healthchecke.checkEndDate,
    //   };
    //   await ctx.model.Healthcheck.updateOne({ _id: healthchecke._id }, updateData);
    // }
    return healthchecke;
  }

  // 创建/更新suspect
  async handleSuspect(healthcheck, personInfo, item) {
    const age = item.WORKER_INFO.BIRTH_DATE ? moment().diff(item.WORKER_INFO.BIRTH_DATE, 'years') : ''; // 根据出生日期计算年龄
    const workType = await this.getWorkTypeCode(item.WORK_TYPE_CODE || item.OTHER_WORK_TYPE);
    let suspect = await this.ctx.model.Suspect.findOne({ employeeId: personInfo._id, tjPlanId: item.WORKER_INFO.PLAN_ID });
    const { BHK_CODE } = item.WORKER_INFO;

    const newData = {
      name: personInfo.name,
      tjPlanId: item.WORKER_INFO.PLAN_ID,
      age,
      gender: item.WORKER_INFO.GENDER_CODE ? item.WORKER_INFO.GENDER_CODE - 1 + '' : '',
      workType,
      harmFactors: item.EXAM_CONCLUSION_LIST[0].ITAM_NAME || item.EXAM_CONCLUSION_LIST[0].ITAM_CODE || item.CONTACT_FACTOR_CODE,
      otherHarmFactors: item.CONTACT_FACTOR_OTHER || item.FACTOR_OTHER || '',
      opinion: item.EXAM_CONCLUSION_LIST.map(ele => ele.EXAM_CONCLUSION_CODE).join('；'), // 意见
      CwithO: this.getCwithO(item.EXAM_CONCLUSION_LIST, item.EXAM_ITEM_RESULT_LIST),
      dedicalAdvice: '',
      batch: healthcheck._id,
      employeeId: personInfo._id,
      IDCard: item.WORKER_INFO.ID_CARD,
      EnterpriseID: personInfo.EnterpriseID,
      checkType: healthcheck.checkType,
      checkDate: new Date(item.EXAM_DATE),
      recheck: item.IS_REVIEW === '1' ? '是' : '否',
      riskFactorsOfPhysicalExaminations: item.EXAM_CONCLUSION_LIST.map(ele => {
        return {
          harmFactor: ele.ITAM_NAME || '',
          examConclusion: ele.EXAM_CONCLUSION_CODE || '',
          suspectedOccupationalDisease: ele.YSZYB_CODE || '',
          occupationalContraindications: ele.ZYJJZ_NAME || '',
          otherOrDes: ele.QTJB_NAME || '',
        };
      }),
      bhkSubList: item.EXAM_ITEM_RESULT_LIST.map(ele => {
        return {
          itmcod: ele.EXAM_ITEM_PNAME || '',
          name: ele.EXAM_ITEM_NAME || '',
          classify: ele.EXAM_RESULT_TYPE || '',
          msrunt: ele.EXAM_ITEM_UNIT_CODE || '', // 计量单位
          itemStdValue: `${ele.REFERENCE_RANGE_MIN || ''} - ${ele.REFERENCE_RANGE_MAX || ''}`,
          result: ele.EXAM_RESULT || '',
          chkdat: new Date(item.EXAM_DATE),
          jdgptn: +ele.EXAM_RESULT_TYPE || 1,
          minVal: ele.REFERENCE_RANGE_MIN || '',
          maxVal: ele.REFERENCE_RANGE_MAX || '',
          diagRest: ele.EXAM_RESULT || '',
          rstFlag: ele.ABNORMAL || '',
          rgltag: ele.RGLTAG || 1,
        };
      }),
    };
    if (BHK_CODE) {
      newData.reportCode = BHK_CODE;
    }

    if (suspect) {
      await this.ctx.model.Suspect.updateOne({ _id: suspect._id }, newData);
    } else {
      suspect = await this.ctx.model.Suspect.create(newData);
    }
  }

  // 根据code获取工种代码名称
  async getWorkTypeCode(code) {
    if (!code) return '';
    const data = await this.ctx.model.ZjWorkTypeCode.findOne({ code });
    return data ? data.name : code;
  }

  // 获取体检结果中的CwithO
  getCwithO(EXAM_CONCLUSION_LIST = [], EXAM_ITEM_RESULT_LIST = []) {
    const data = EXAM_CONCLUSION_LIST[0];
    if (data.YSZYB_CODE) return '疑似职业病';
    if (data.ZYJJZ_NAME) return '禁忌证';
    if (data.QTJB_NAME) return '其他疾病或异常';
    if (EXAM_ITEM_RESULT_LIST[0] && EXAM_ITEM_RESULT_LIST[0].ABNORMAL === '1') return '目前未见异常';
    return EXAM_ITEM_RESULT_LIST[0] ? EXAM_ITEM_RESULT_LIST[0].EXAM_RESULT : '目前未见异常';
  }

  // 更新HealthCheck中的人数以及企业表中的体检信息
  async updateHealthCheck(healthcheck, Enterprise, item, tjPlanId) {
    // const appointment = appointmentId ? await this.ctx.model.HealthCheckAppointment.findOne({ _id: appointmentId }) : {};
    // 查询体检计划中已预约但未体检的人数
    const isNotTjPeopleNum = await this.ctx.model.AppointmentDetails.find({ tjPlanId }).count();
    // const appointmentPeopleNum = (appointment.employeeIds ? appointment.employeeIds.length : appointment.peopleNum) || 0;
    const suspectList = await this.ctx.model.Suspect.find({ tjPlanId });
    const updateData = {
      shouldCheckNum: isNotTjPeopleNum > suspectList.length ? isNotTjPeopleNum : suspectList.length,
      actuallNum: suspectList.length,
      recheckNum: suspectList.filter(ele => ele.recheck === '是').length,
      normal: suspectList.filter(ele => ele.CwithO === '目前未见异常').length,
      suspected: suspectList.filter(ele => ele.CwithO === '疑似职业病').length,
      forbid: suspectList.filter(ele => ele.CwithO === '禁忌证').length,
      otherDisease: suspectList.filter(ele => ele.CwithO === '其他疾病或异常').length,
      checkDate: new Date(item.EXAM_DATE) < healthcheck.checkDate ? new Date(item.EXAM_DATE) : healthcheck.checkDate,
      checkEndDate: new Date(item.EXAM_DATE) > healthcheck.checkEndDate ? new Date(item.EXAM_DATE) : healthcheck.checkEndDate,
    };
    await this.ctx.model.Healthcheck.updateOne({ _id: healthcheck._id }, updateData);
    await this.ctx.model.Adminorg.updateOne({ _id: Enterprise._id }, { $set: { healcheckInfo: {
      actuallNum: String(updateData.actuallNum), // 实检人数
      recheck: String(updateData.recheckNum), // 复查
      suspected: String(updateData.suspected), // 疑似
      forbid: String(updateData.forbid), // 禁忌证
      occupational: String(Enterprise.occupational || 0), // 职业病
      recentDay: healthcheck.checkDate, // 体检时间
    } } });
  }

  // 山西焦煤体检预约提醒
  async remind() {
    const { ctx } = this;
    // 从TjPlan表中获取体检开始时间字段checkStartDate为第二天的体检计划
    const startOfTomorrow = moment().add(1, 'days').startOf('day')
      .toISOString();
    const endOfTomorrow = moment().add(1, 'days').endOf('day')
      .toISOString();

    ctx.auditLog(`查询体检计划日期范围：${startOfTomorrow} - ${endOfTomorrow}`, '', 'info');

    const isQyWeChat = this.config.isQyWeChat;

    try {
      const TjPlanList = await ctx.model.TjPlan.aggregate([
        {
          $match: {
            $and: [
              { checkStartDate: { $lte: new Date(endOfTomorrow) } },
              { checkEndDate: { $gte: new Date(startOfTomorrow) } },
            ],
          },
        },
        {
          $unwind: '$employees', // 展开 employees 数组
        },
        {
          $match: {
            $and: [
              { 'employees.appointmentStatus': 1 },
              { 'employees.checkStatus': 0 },
            ],
          },
        },
        {
          $group: {
            _id: '$_id',
            EnterpriseID: { $first: '$EnterpriseID' },
            EnterpriseName: { $first: '$EnterpriseName' },
            physicalExaminationOrgName: { $first: '$physicalExaminationOrgName' },
            employees: { $push: '$employees' },
          },
        },
      ]);

      ctx.auditLog(`获取到的体检计划数量：${TjPlanList.length}`, '', 'info');
      // ctx.auditLog('获取到的体检计划列表：', JSON.stringify(TjPlanList));
      ctx.auditLog('获取到的体检计划列表：', TjPlanList, 'info');

      for (const TjPlan of TjPlanList) {
        const { _id, EnterpriseID, EnterpriseName, physicalExaminationOrgName, employees } = TjPlan;
        // 根据_id和employees中的employeeId获取appointmentDetails表中appointDate为明天的数据
        const appointmentDetails = await ctx.model.AppointmentDetails.find(
          {
            tjPlanId: _id,
            employeeId: { $in: employees.map(user => user.employeeId) },
            appointDate: { $gte: new Date(startOfTomorrow), $lte: new Date(endOfTomorrow) },
          },
          { _id: 1, appointDate: 1, employeeId: 1, physicalExaminationOrgName: 1, physicalExaminationName: 1 }
        );
        // ctx.auditLog('获取到的体检预约详情：', JSON.stringify(appointmentDetails));
        const checkStartDateStr = moment(startOfTomorrow).format('YYYY-MM-DD');
        // ctx.auditLog('体检计划开始日期：', checkStartDateStr);
        // 从Adminorg表中获取体检机构的adminUserId
        const adminUserId = await ctx.model.Adminorg.findOne(
          { _id: EnterpriseID },
          { adminUserId: 1 }
        );
          // 发送铃铛消息
        const readerIds = appointmentDetails.map(user => ({
          readerID: user.employeeId,
          readerGroup: '',
          isRead: 0,
        }));
          // const { _id } = await ctx.model.AdminUser.findOne(
          //   { newAddEnterpriseID: EnterpriseID },
          //   { _id: 1 }
          // );
          // readerIds.push({
          //   readerID: _id,
          //   readerGroup: '',
          //   isRead: 0,
          // });
        // ctx.auditLog(readerIds.length, readerIds, 'readerIdsreaderIds');
        const messageNotice = {
          title: '体检计划',
          message: `您预约${physicalExaminationOrgName}的体检将于明日${checkStartDateStr}开始，请提前做好准备。`,
          reader: readerIds,
          sendWay: 'systemMessage',
          templateCode: '',
          type: 1,
          SignName: '',
          files: [],
          authorID: adminUserId,
          authorGroup: '',
          informer: EnterpriseName || '', // 发通知的人名
        };
        await this.sendMessageToUser(messageNotice);

        if (isQyWeChat) {
          // 发送企业微信消息
          await ctx.curl(`${this.config.iServiceHost}/api/message/send`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            dataType: 'json',
            data: {
              channel: 'workwechat',
              messageType: 'text',
              recipient: {
                type: 'employee',
                values: readerIds,
              },
              payload: {
                text: {
                  content: `您预约${physicalExaminationOrgName}的体检将于明日${checkStartDateStr}开始，请提前做好准备。`,
                },
              },
            },
          });
        } else {
          // ctx.auditLog('未开启企业微信消息发送');
          // 发送短信
          const type = '3';
          const employeeStr = [];
          for (let i = 0; i < appointmentDetails.length; i++) {
            const employee = appointmentDetails[i];
            const employeeData = await ctx.model.Employee.findOne({ _id: employee.employeeId }, { name: 1, phoneNum: 1 });
            // ctx.auditLog(4444, employeeData);
            if (employeeData) {
              employeeStr.push(employeeData);
            }
          }
          const res = await ctx.curl(`${this.config.iServiceHost}/api/sxccAppointmentSMS`, {
            method: 'POST',
            dataType: 'json',
            headers: {
              'Content-Type': 'application/json',
            },
            data: JSON.stringify({
              type,
              tjPlanId: _id,
              cname: physicalExaminationOrgName,
              employees: employeeStr,
              duration: checkStartDateStr,
            }),
          });
          ctx.auditLog('短信发送成功', res, 'info');
        }
      }
    } catch (error) {
      ctx.auditLog('获取体检计划时发生错误：', error.message, 'error');
      throw error;
    }
  }

  // 发送消息给用户
  async checkPhysical() {
    const { ctx } = this;
    // 从TjPlan表中获取体检开始时间字段checkStartDate为当天的体检计划
    // 获取当前日期和时间范围
    const startOfToday = moment().startOf('day').toISOString();
    const endOfToday = moment().endOf('day').toISOString();

    ctx.auditLog(`查询体检计划日期范围：${startOfToday} - ${endOfToday}`, '', 'info');

    const isQyWeChat = this.config.isQyWeChat;

    try {
      const TjPlanList = await ctx.model.TjPlan.aggregate([
        {
          $match: {
            $and: [
              { checkStartDate: { $lte: new Date(endOfToday) } },
              { checkEndDate: { $gte: new Date(startOfToday) } },
            ],
          },
        },
        {
          $unwind: '$employees', // 展开 employees 数组
        },
        {
          $match: {
            $and: [
              { 'employees.appointmentStatus': 1 },
              { 'employees.checkStatus': 0 },
            ],
          },
        },
        {
          $group: {
            _id: '$_id',
            EnterpriseID: { $first: '$EnterpriseID' },
            EnterpriseName: { $first: '$EnterpriseName' },
            physicalExaminationOrgName: { $first: '$physicalExaminationOrgName' },
            checkStartDate: { $first: '$checkStartDate' },
            checkEndDate: { $first: '$checkEndDate' },
            employees: { $push: '$employees' },
          },
        },
      ]);

      ctx.auditLog(`获取到的体检计划数量：${TjPlanList.length}`, 'info');
      // ctx.auditLog('获取到的体检计划列表：', JSON.stringify(TjPlanList));
      ctx.auditLog('获取到的体检计划列表：', TjPlanList, 'info');

      for (const TjPlan of TjPlanList) {
        const { _id, EnterpriseID, EnterpriseName, physicalExaminationOrgName, employees } = TjPlan;
        // 根据_id和employees中的employeeId获取appointmentDetails表中appointDate为明天的数据
        const appointmentDetails = await ctx.model.AppointmentDetails.find(
          {
            tjPlanId: _id,
            employeeId: { $in: employees.map(user => user.employeeId) },
            appointDate: { $gte: new Date(startOfToday), $lte: new Date(endOfToday) },
          },
          { _id: 1, appointDate: 1, employeeId: 1, physicalExaminationOrgName: 1, physicalExaminationName: 1 }
        );
        // ctx.auditLog('获取到的体检预约详情：', JSON.stringify(appointmentDetails));
        const checkStartDateStr = moment(startOfToday).format('YYYY-MM-DD');
        // ctx.auditLog('体检计划开始日期：', checkStartDateStr);
        // const checkStartDateStr = moment(checkStartDate).format('YYYY-MM-DD');
        // ctx.auditLog('体检计划开始日期：', checkStartDateStr);
        // 从Adminorg表中获取体检机构的adminUserId
        const adminUserId = await ctx.model.Adminorg.findOne(
          { _id: EnterpriseID },
          { adminUserId: 1 }
        );
          // 发送铃铛消息
        const readerIds = appointmentDetails.map(user => ({
          readerID: user.employeeId,
          readerGroup: '',
          isRead: 0,
        }));
          // const { _id } = await ctx.model.AdminUser.findOne(
          //   { newAddEnterpriseID: EnterpriseID },
          //   { _id: 1 }
          // );
          // readerIds.push({
          //   readerID: _id,
          //   readerGroup: '',
          //   isRead: 0,
          // });
        // ctx.auditLog(readerIds.length, readerIds, 'readerIdsreaderIds');
        const messageNotice = {
          title: '体检计划',
          message: `您预约${physicalExaminationOrgName}的体检已经开始，请重新预约。`,
          reader: readerIds,
          sendWay: 'systemMessage',
          templateCode: '',
          type: 1,
          SignName: '',
          files: [],
          authorID: adminUserId,
          authorGroup: '',
          informer: EnterpriseName || '', // 发通知的人名
        };
        await this.sendMessageToUser(messageNotice);
        // 更新TjPlan表中的employees数组中employees.checkStatus为0的数据的appointmentStatus字段为0
        await ctx.model.TjPlan.updateOne(
          { _id },
          { $set: { 'employees.$[elem].appointmentStatus': 0 } },
          { arrayFilters: [{ 'elem.checkStatus': 0 }] }
        );

        if (isQyWeChat) {
          // 发送企业微信消息
          await ctx.curl(`${this.config.iServiceHost}/api/message/send`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            dataType: 'json',
            data: {
              channel: 'workwechat',
              messageType: 'text',
              recipient: {
                type: 'employee',
                values: readerIds,
              },
              payload: {
                text: {
                  content: `您预约${physicalExaminationOrgName}的体检已经开始，请重新预约。`,
                },
              },
            },
          });
        } else {
          // 发送短信
          const type = '4';
          const employeeStr = [];
          for (let i = 0; i < appointmentDetails.length; i++) {
            const employee = appointmentDetails[i];
            const employeeData = await ctx.model.Employee.findOne({ _id: employee.employeeId }, { name: 1, phoneNum: 1 });
            // ctx.auditLog(4444, employeeData);
            if (employeeData) {
              employeeStr.push(employeeData);
            }
          }
          const res = await ctx.curl(`${this.config.iServiceHost}/api/sxccAppointmentSMS`, {
            method: 'POST',
            dataType: 'json',
            headers: {
              'Content-Type': 'application/json',
            },
            data: JSON.stringify({
              type,
              tjPlanId: _id,
              cname: physicalExaminationOrgName,
              employees: employeeStr,
              duration: checkStartDateStr,
            }),
          });
          ctx.auditLog('短信发送成功', res, 'info');
        }
      }
    } catch (error) {
      ctx.auditLog('获取体检计划时发生错误：', error.message, 'error');
      throw error;
    }
  }
  async sendMessageToUser(payload) {
    const res = await this.ctx.model.MessageNotification.create(payload);
    return res;
  }

  // 处理答卷完成体检完成
  async handleCompletePhysicalExam(data) {
    const { ctx } = this;
    const { tjPlanId, employeeId } = data;
    if (!tjPlanId || !employeeId) {
      throw new Error('tjPlanId和employeeId不能为空');
    }
    try {
      const res = await ctx.model.TjPlan.updateOne({
        _id: tjPlanId,
        'employees.employeeId': employeeId,
      }, {
        $set: { 'employees.$.checkStatus': 1 },
      });
      const appointment = await ctx.model.AppointmentDetails.findOne({
        tjPlanId,
        employeeId,
      });
      // 更新体检医院已体检人数
      if (appointment.status !== '1') {
        await ctx.model.AppointPeopleCount.updateOne({
          organizationId: appointment.physicalExaminationOrgId,
          appointDate: appointment.appointDate,
        },
        {
          $inc: { physicalExamined: 1 },
        }
        );
      }
      await ctx.model.AppointmentDetails.updateOne({
        tjPlanId,
        employeeId,
      }, {
        $set: { status: '1' },
      });
      ctx.auditLog(
        '答卷完成体检完成',
        `人员：${employeeId}，计划：${tjPlanId}`,
        'info'
      );
      return res;
    } catch (error) {
      ctx.auditLog('答卷系统回传答完结果失败', error.message, 'error');
    }
  }
}

module.exports = SxccPhysicalAppointmentService;
