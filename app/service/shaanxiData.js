const Service = require('egg').Service;
const path = require('path');
const shortid = require('shortid');
const fs = require('fs');
const moment = require('moment');
const mkdirp = require('mkdirp');

class ShaanxiService extends Service {
  // 上传个人体检信息个案卡（文件）
  async uploadSuspectFileV1_0(physicalExamOrgId, ctx) {
    try {
      const stream = await ctx.getFileStream();
      const {
        projectNumber = '',
        fileType = 'caseCard',
        IDNum,
        manageYear,
        cname,
        code,
        name,
        reportCode,
      } = stream.fields;
      const requiredFields = [
        {
          field: 'code',
          message: '企业社会信用代码',
          format: /^(([0-9A-Za-z]{15})|([0-9A-Za-z]{18})|([0-9A-Za-z]{20}))$/,
        },
        { field: 'cname', message: '企业名称' },
        // { field: 'projectNumber', message: '体检项目编号' },
        { field: 'name', message: '员工姓名' },
        {
          field: 'manageYear',
          message: '体检年月',
          format: /^(19|20)\d{2}(0[1-9]|1[0-2])$/,
        },
        {
          field: 'IDNum',
          message: '员工身份证号',
          format:
            /^[1-9]\d{5}(?:18|19|20)\d{2}(?:0[1-9]|10|11|12)(?:0[1-9]|[1-2]\d|30|31)\d{3}[\dXx]$/,
        },
        {
          field: 'reportCode',
          message: '个人体检编号',
        },
      ];
      this.validateParams(
        { code, cname, projectNumber, name, manageYear, IDNum, reportCode },
        requiredFields
      );
      const extname = path.extname(stream.filename);
      if (![ '.pdf' ].includes(extname)) {
        throw new Error('文件格式不正确');
      }
      const logData = {
        projectNumber,
        fileType,
        IDNum,
        manageYear,
        cname,
        code,
        name,
        reportCode,
        fileName: stream.filename,
      };
      await ctx.service.healthCheckAppointment.addInterfaceLog(
        ctx.request.url,
        physicalExamOrgId,
        logData
      );
      let randomFileName =
        await ctx.service.healthMachineCheck.randomFileName();
      randomFileName = `${randomFileName}${extname}`;
      // 获取文件后缀
      const physicalExamOrg = await this.ctx.model.PhysicalExamOrg.findOne({
        _id: physicalExamOrgId,
      });
      let adminorgInfo;
      const { branch, isAllowCreateOrgAndEmployee, isUseOrgIdAndIdCardQuery = true } = this.config;
      if (branch === 'by') {
        adminorgInfo = await this.ctx.model.Adminorg.findOne({
          cname,
        });
      } else {
        adminorgInfo = await this.ctx.model.Adminorg.findOne({
          code,
        });
      }
      if (!adminorgInfo && isAllowCreateOrgAndEmployee) {
        adminorgInfo = await this.ctx.model.Adminorg.create({
          code,
          cname,
        });
        ctx.auditLog('上传个案卡文件-创建企业成功', { cname, code }, 'info');
      } else {
        adminorgInfo = {
          _id: 'weichadao' + code,
        };
      }
      const employeeData = {
        IDNum,
        name,
        EnterpriseID: adminorgInfo._id,
      };
      let employeeInfo = null;
      if (isUseOrgIdAndIdCardQuery) {
        employeeInfo = await ctx.model.Employee.findOne({
          IDNum,
          EnterpriseID: adminorgInfo._id,
        });
        if (!employeeInfo && isAllowCreateOrgAndEmployee) {
          employeeInfo = await ctx.model.Employee.create(employeeData);
          ctx.auditLog('上传个案卡文件-创建employee成功', employeeData, 'info');
        } else if (!employeeInfo) {
          employeeInfo = employeeData;
        }
        if (!employeeInfo._id) {
          employeeInfo._id = 'weichadao' + IDNum;
        }
      } else {
        employeeInfo = await ctx.model.Employee.findOne({
          IDNum,
        });
        if (!employeeInfo && isAllowCreateOrgAndEmployee) {
          employeeData.EnterpriseID = 'weichadaoqy' + code;
          employeeInfo = await ctx.model.Employee.create(employeeData);
          ctx.auditLog('上传个案卡文件-创建employee成功', employeeData, 'info');
        } else if (!employeeInfo) {
          employeeInfo = employeeData;
        }
        if (!employeeInfo._id) {
          employeeInfo._id = 'weichadao' + IDNum;
        }
      }
      let fileName = '';
      let fileInfo = {};
      const suspectCodeInfo = await ctx.model.Suspect.findOne({
        reportCode,
        employeeId: employeeInfo._id,
        EnterpriseID: isUseOrgIdAndIdCardQuery
          ? adminorgInfo._id
          : employeeInfo.EnterpriseID,
      });
      // const suspectList = await ctx.model.Suspect.find({
      //   reportCode,
      //   employeeId: employeeInfo._id,
      //   EnterpriseID: adminorgInfo._id,
      // });
      // if (suspectList.length > 1) {
      //   ctx.auditLog('上传个案卡文件-查询个案卡', JSON.stringify(suspectList), 'error');
      //   if (this.config.branch === 'wkzwy') {
      //     throw new Error('存在多个相同编号的个案卡，请确认个案卡编号之后重新提交或者联系管理员');
      //   }
      // }
      // const suspectCodeInfo = suspectList[0];
      if (suspectCodeInfo) {
        ctx.auditLog(
          '上传个案卡文件-查询个案卡id',
          suspectCodeInfo._id,
          'info'
        );
        const configFilePath = path.join(
          ctx.app.config.upload_path,
          employeeInfo.EnterpriseID
        );
        if (fileType === 'caseCard') {
          if (suspectCodeInfo.caseCard) {
            await this.deleteSourceFileCaseCard(
              suspectCodeInfo.caseCard,
              employeeInfo.EnterpriseID
            );
          }
          fileName = `${IDNum}+${name}${extname}`;
          fileInfo = {
            staticName: randomFileName,
            originName: fileName,
          };
        }
        const target = path.resolve(configFilePath, randomFileName);
        await this.ctx.helper.pipe({
          readableStream: stream,
          target,
        });
        // this.suspectInfoSend({
        //   reportCode,
        //   employeeInfo,
        //   manageYear,
        //   fileType,
        //   organization: physicalExamOrg.name,
        //   file: fileInfo,
        //   suspectCodeInfo,
        // });
        await this.addHealthCheckSuspectFile({
          reportCode,
          employeeInfo,
          manageYear,
          fileType,
          organization: physicalExamOrg.name,
          file: fileInfo,
          suspectCodeInfo,
        });
        ctx.auditLog(
          '上传个人体检信息报告文件成功',
          '上传个人体检信息报告文件成功',
          'info'
        );
        return {
          status: 200,
          message: '上传个人体检信息个案卡成功',
        };
      }
      const healthCheckInfo = await ctx.model.Healthcheck.findOne({
        projectNumber,
        EnterpriseID: isUseOrgIdAndIdCardQuery
          ? adminorgInfo._id
          : employeeInfo.EnterpriseID,
      });
      if (!healthCheckInfo) {
        ctx.auditLog('上传个案卡文件', '未查找到体检项目和个案记录', 'info');
        const configFilePath = path.join(
          ctx.app.config.upload_path,
          employeeInfo.EnterpriseID
        );
        if (fileType === 'caseCard') {
          fileName = `${manageYear}+${IDNum}+${name}${extname}`;
          fileInfo = {
            staticName: randomFileName,
            originName: fileName,
          };
        }
        const target = path.resolve(configFilePath, randomFileName);
        await this.ctx.helper.pipe({
          readableStream: stream,
          target,
        });
        // if (res.status === 200) {
        //   this.suspectInfoSend({
        //     reportCode,
        //     employeeInfo,
        //     manageYear,
        //     fileType,
        //     organization: physicalExamOrg.name,
        //     file: fileInfo,
        //   });
        // }
        await this.addHealthCheckSuspectFile({
          reportCode,
          employeeInfo,
          manageYear,
          fileType,
          organization: physicalExamOrg.name,
          file: fileInfo,
        });
      } else {
        const configFilePath = path.join(
          ctx.app.config.upload_path,
          healthCheckInfo.EnterpriseID
        );
        if (fileType === 'caseCard') {
          fileName = `${IDNum}+${name}${extname}`;
          fileInfo = {
            staticName: randomFileName,
            originName: fileName,
          };
        }
        const target = path.resolve(configFilePath, randomFileName);
        await this.ctx.helper.pipe({
          readableStream: stream,
          target,
        });
        ctx.auditLog(
          '上传个案卡文件-查询到的项目id',
          healthCheckInfo._id,
          'info'
        );
        // this.suspectInfoSend({
        //   reportCode,
        //   healthCheckInfo,
        //   employeeInfo,
        //   manageYear,
        //   fileType,
        //   organization: physicalExamOrg.name,
        //   file: fileInfo,
        // });
        await this.addHealthCheckSuspectFile({
          reportCode,
          healthCheckInfo,
          employeeData,
          manageYear,
          organization: physicalExamOrg.name,
          fileType,
          file: fileInfo,
        });
      }
      ctx.auditLog(
        '上传个人体检信息报告文件成功',
        '上传个人体检信息报告文件成功',
        'info'
      );
      return {
        status: 200,
        message: '上传个人体检信息个案卡成功',
      };
    } catch (error) {
      ctx.auditLog('上传个人体检信息报告文件失败', error.message, 'error');
      throw error; // 重新抛出错误
    }
  }

  // 删除源文件
  async deleteSourceFileCaseCard(caseCardInfo, EnterpriseID) {
    if (!caseCardInfo.staticName) {
      return;
    }
    const { ctx } = this;
    try {
      const configFilePath = path.join(
        ctx.app.config.upload_path,
        EnterpriseID
      );
      const target = path.resolve(configFilePath, caseCardInfo.staticName);
      await ctx.helper.deleteObject(target);
      ctx.auditLog('删除源个案卡文件成功', caseCardInfo.staticName, 'info');
    } catch (error) {
      ctx.auditLog('删除源个案卡文件失败', error.message, 'error');
      throw error; // 重新抛出错误
    }
  }

  async addHealthCheckSuspectFile({
    reportCode,
    healthCheckInfo = {},
    employeeInfo,
    manageYear,
    organization,
    fileType,
    file,
    suspectCodeInfo,
  }) {
    const { ctx } = this;
    if (suspectCodeInfo) {
      await ctx.model.Suspect.findOneAndUpdate(
        { _id: suspectCodeInfo._id },
        {
          $set: {
            [fileType]: {
              _id: shortid.generate(),
              ...file,
            },
          },
        },
        { new: true }
      );
      return;
    }
    const suspectInfo = {
      name: employeeInfo.name,
      age: employeeInfo.age,
      reportCode,
      gender: employeeInfo.gender,
      workType: employeeInfo.workType || '',
      harmFactors: employeeInfo.harmFactors || '',
      EnterpriseID: employeeInfo.EnterpriseID,
      opinion: '',
      CwithO: '',
      dedicalAdvice: '',
      checkType: '1',
      IDCard: employeeInfo.IDNum,
      checkDate: new Date(
        manageYear.slice(0, 4) + '/' + manageYear.slice(4, 6)
      ),
      organization,
      employeeId: employeeInfo._id,
      manageYear,
    };
    suspectInfo[fileType] = {
      _id: shortid.generate(),
      ...file,
    };
    if (healthCheckInfo._id) {
      suspectInfo.batch = healthCheckInfo._id;
      suspectInfo.checkType = healthCheckInfo.checkType;
      suspectInfo.checkDate = healthCheckInfo.checkDate;
      delete suspectInfo.manageYear;
      const suspect = await ctx.model.Suspect.findOne({
        employeeId: employeeInfo._id,
        batch: healthCheckInfo._id,
      });
      if (suspect) {
        if (suspect.caseCard) {
          await this.deleteSourceFileCaseCard(
            suspect.caseCard,
            suspect.EnterpriseID
          );
        }
        await ctx.model.Suspect.findOneAndUpdate(
          { _id: suspect._id },
          {
            $set: {
              [fileType]: {
                _id: shortid.generate(),
                ...file,
              },
            },
          },
          { new: true }
        );
        ctx.auditLog(
          '上传个人体检信息报告文件成功',
          '上传个人体检信息报告文件成功',
          'info'
        );
        return;
      }
      await this.ctx.model.Suspect.create(suspectInfo);
      ctx.auditLog(
        '创建个人体检信息报告文件成功',
        '创建个人体检信息报告文件成功',
        'info'
      );
      return;
    }
    await ctx.model.Suspect.findOneAndUpdate(
      {
        employeeId: employeeInfo._id,
        manageYear,
      },
      {
        $setOnInsert: {
          _id: shortid.generate(),
        },
        $set: suspectInfo,
      },
      { new: true, upsert: true }
    );
  }

  suspectInfoSend(data) {
    // this.ctx.service.rabbitmq.produce(
    //   data,
    //   'shaanxi',
    //   'healthExamRecord'
    // );
    this.ctx.service.rabbitmq.produce({
      message: data,
      exchange: 'shaanxi',
      routingKey: 'healthExamRecord',
    });
  }

  async uploadShaanxiHealthExamRecordData(params) {
    const { ctx } = this;
    try {
      // const result = { successful: [], failed: [] };
      const { convertedBody, physicalExamOrgId, version } = params;
      const data = convertedBody.DATAS.PERSONS.PERSON;
      // 生成陕西个人职业健康档案数据
      if (Array.isArray(data)) {
        for (let i = 0; i < data.length; i++) {
          const item = data[i];
          await ctx.service.shaanxiData['shaanxiHealthExamRecordV' + version](
            physicalExamOrgId,
            item
          );
        }
      } else if (Object.prototype.toString.call(data) === '[object Object]') {
        await ctx.service.shaanxiData['shaanxiHealthExamRecordV' + version](
          physicalExamOrgId,
          data
        );
      }
      ctx.auditLog(
        '上传个人体检信息个案卡成功',
        '上传个人体检信息个案卡成功',
        'info'
      );
    } catch (error) {
      ctx.auditLog('上传个人体检信息个案卡失败', error.message, 'error');
      throw error; // 重新抛出错误
    }
  }

  // 上传个人体检信息个案卡（数据）
  async shaanxiHealthExamRecordV1_0(physicalExamOrgId, item) {
    const { ctx } = this;
    try {
      const physicalExamOrg = await ctx.model.PhysicalExamOrg.findOne({
        _id: physicalExamOrgId,
      });
      if (
        item.TD_TJ_BHK.ORG_CODE &&
        item.TD_TJ_BHK.ORG_CODE !== physicalExamOrg.organization
      ) {
        throw new Error('您的token跟上传的创建机构代码不匹配');
      }
      await this.clearShaanxiHealthCheckData(item, physicalExamOrg);
      ctx.auditLog(
        '上传个人体检信息个案卡成功',
        '上传个人体检信息个案卡成功',
        'info'
      );
    } catch (error) {
      ctx.auditLog('上传个人体检信息个案卡失败', error.message, 'error');
      throw error; // 重新抛出错误
    }
  }

  async clearShaanxiHealthCheckData(data, physicalExamOrg) {
    const { ctx, config } = this;
    const {
      isUseOrgIdAndIdCardQuery = true,
      isAllowCreateOrgAndEmployee = false,
    } = config;
    try {
      // const { bhkList } = await this.getOldDataFromZip('baseTjData');
      console.log('体检HealtheCheck清洗');
      const successLog = [];
      const errorLog = [];
      try {
        const employeeAdminInfo = data.TD_TJ_BHK;
        if (!employeeAdminInfo.IDC) {
          ctx.auditLog('体检对接数据失败', '缺少身份证信息', 'error');
          return;
        }
        const workType = this.getWorkType(
          employeeAdminInfo.WORK_NAME,
          employeeAdminInfo.WORK_TYPE_CODE
        ); // 工种
        // 1. 根据企业组织机构代码获取企业信息
        let inOurCompany = null;
        if (isUseOrgIdAndIdCardQuery) {
          inOurCompany = await this.getOneCompanyByCodeAndName({
            code: employeeAdminInfo.INSTITUTION_CODE,
            cname: employeeAdminInfo.CRPT_NAME,
          });
        } else {
          const employeeInfo =
            await ctx.service.importToDb.getOneEmployeeBySearchKey({
              searchKey: employeeAdminInfo.IDC,
            });
          if (employeeInfo) {
            inOurCompany = await ctx.model.Adminorg.findOne({
              _id: employeeInfo.EnterpriseID,
            });
          } else {
            ctx.auditLog('体检数据对接失败', `系统未找到该员工信息，身份证号为 ${employeeAdminInfo.IDC}`, 'error');
            return;
            // throw new Error(
            //   `系统未找到该员工信息，身份证号为 ${employeeAdminInfo.IDC}`
            // );
          }
        }
        // const districts = this.getAddress(employeeAdminInfo.CRPT_ADDR);
        if (!inOurCompany && isAllowCreateOrgAndEmployee) {
          // 创建企业
          console.log('创建企业', employeeAdminInfo.INSTITUTION_CODE);
          const adminUser = {
            group: ctx.app.config.groupID.adminGroupID,
            password: this.getSixStr(employeeAdminInfo.INSTITUTION_CODE) || '',
            enable: true,
          };
          const adminUserCreated = await ctx.model.AdminUser.create(adminUser);
          let toCreateCompanyInfo = {
            cname: employeeAdminInfo.CRPT_NAME || '',
            code: employeeAdminInfo.INSTITUTION_CODE || '',
            adminUserId: adminUserCreated._id,
            // districtRegAdd: districts,
            leadIn: 4,
            isactive: 1,
          };
          toCreateCompanyInfo = this.filterEmptyFields(toCreateCompanyInfo);
          inOurCompany = await ctx.service.importToDb.createAdminorg(
            toCreateCompanyInfo
          );
        } else if (!inOurCompany) {
          // throw new Error('当前用户未找到企业');
          ctx.auditLog('体检数据对接失败', '未找到当前用户企业', 'error');
        }
        // 2. 查询是否存在体检报告
        console.log('体检类型为', employeeAdminInfo.ONGUARD_STATE);
        let haveHealthCheckData =
          await ctx.service.importToDb.getOneHealthCheckByProjectNumber({
            projectNumber: employeeAdminInfo.PROJECT_NUM, // 项目编号
            EnterpriseID: inOurCompany._id, // 企业id
          });
        if (!haveHealthCheckData) {
          // const dist = {
          //   districts,
          //   address: '',
          //   point: [],
          // };
          // 创建体检报告
          const healCheckData = {
            physicalExaminationOrgID: physicalExamOrg._id, // 体检机构id
            enterpriseName: inOurCompany.cname || '', // 企业名称
            EnterpriseID: inOurCompany._id, // 企业id
            enterpriseContactsName: inOurCompany.contract || '', // 企业联系人姓名
            enterpriseContactsPhonNumber: inOurCompany.phoneNum || '', // 企业联系人电话
            workAddress:
              inOurCompany.workAddress && inOurCompany.workAddress.length > 0
                ? inOurCompany.workAddress
                : [], // 工作场所
            year: '' + new Date(employeeAdminInfo.BHK_DATE).getFullYear(), // 年份
            projectNumber: employeeAdminInfo.PROJECT_NUM, // 项目编号
            checkDate: new Date(employeeAdminInfo.BHK_DATE), // 项目开始时间
            checkEndDate: new Date(employeeAdminInfo.BHK_DATE), // 项目结束时间
            // checkPlace: '', // 体检机构地点
            approvalDate: new Date(employeeAdminInfo.JDGDAT), // 批准日期
            // applyTime: '', // 申请时间
            checkType: this.getOnguardState(employeeAdminInfo.ONGUARD_STATE), // 检查类型
            shouldCheckNum: 0, // 应检人数
            actuallNum: 0, // 实检人数
            normal: 0, // 正常人数
            re_examination: 0, // 复检人数
            suspected: 0, // 疑似职业病人数
            forbid: 0, // 禁忌证人数
            otherDisease: 0, // 其他疾病人数
            // comment: '', // 评价
            recheck: employeeAdminInfo.IF_RHK === 0,
            organization: physicalExamOrg.name || '',
            reportStatus: true,
            applyTime: employeeAdminInfo.RPT_PRINT_DATE && new Date(employeeAdminInfo.RPT_PRINT_DATE),
          };
          // 创建体检报告
          haveHealthCheckData = await ctx.service.importToDb.createHealthCheck(
            healCheckData
          );
        }
        // 4. 查询是否存在员工  假设身份证号必有 文档是 人员类型为1 必填
        let inOurEmployee =
          await ctx.service.importToDb.getOneEmployeeBySearchKey({
            searchKey: employeeAdminInfo.IDC,
          });
        if (!inOurEmployee && ctx.app.config.isAllowCreateOrgAndEmployee) {
          // 创建员工 需要user 和 employee
          const workYears = employeeAdminInfo.WRKLNT
            ? employeeAdminInfo.WRKLNT + '年'
            : employeeAdminInfo.WRKLNTMONTH + '个月'; // 工龄
          let employeeInfo = {
            name: employeeAdminInfo.PERSON_NAME || '', // 姓名
            phoneNum: employeeAdminInfo.LNKTEL || '', // 联系电话
            age: employeeAdminInfo.AGE || '', // 年龄
            IDNum: employeeAdminInfo.IDC || '', // 身份证号
            gender: employeeAdminInfo.SEX === 1 ? '0' : '1', // 性别
            marriage: this.getMarriage(employeeAdminInfo.ISXMRD) || '', // 婚姻状况
            workYears, // 工龄年
            EnterpriseID: inOurCompany._id, // 企业id
            workType, // 工种
            status:
              this.getOnguardState(employeeAdminInfo.ONGUARD_STATE) === '2'
                ? 0
                : 1, // 在职状态[编码需要转义]
          };
          let userInfo = {
            name: employeeAdminInfo.PERSON_NAME || '', // 姓名
            gender: employeeAdminInfo.SEX === 1 ? '0' : '1', // 性别
            idNo: employeeAdminInfo.IDC || '', // 身份证号
            phoneNum: employeeAdminInfo.LNKTEL || '', // 联系电话
            companyId: [ inOurCompany._id ], // 所在企业
            companyStatus: 2, // 企业审核通过
          };
          // 过滤一下employeeInfo 删除里面为空的字段
          employeeInfo = this.filterEmptyFields(employeeInfo);
          userInfo = this.filterEmptyFields(userInfo);
          // 创建员工 employee 和user
          const createEmployeeAndUserRes =
            await ctx.service.importToDb.createEmployeeAndUserWorkFlows({
              employee: employeeInfo,
              user: userInfo,
            });
          inOurEmployee = createEmployeeAndUserRes.employee;
        }
        // #region // 数据格式转换 体检危害因素 主检判断主检结论 症状信息 疑似职业病 职业禁忌证
        const processedBadrsnList = []; // 体检危害因素
        const harmFactors = data.TD_TJ_BADRSNSES.OTHER_BADRSN || '';
        const badRsnList = data.TD_TJ_BADRSNSES.TD_TJ_BADRSNS;
        if (badRsnList && Array.isArray(badRsnList) && badRsnList.length > 0) {
          for (const badRsnItem of badRsnList) {
            processedBadrsnList.push({
              harmFactor: this.getHarmFactors(badRsnItem.BADRSN_CODE), // 有害因素
              examConclusion: this.getConclusion(
                badRsnItem.EXAM_CONCLUSION_CODE
              ), // 体检结论
              suspectedOccupationalDisease:
                this.getSuspectedOccupationalDisease(badRsnItem.YSZYB_CODE),
              occupationalContraindications:
                this.getOccupationalContraindication(badRsnItem.ZYJJZ_CODE), // 职业禁忌证
              otherDisease: badRsnItem.QTJB_NAME, // 其他疾病
            });
          }
        } else if (
          Object.prototype.toString.call(badRsnList) === '[object Object]'
        ) {
          processedBadrsnList.push({
            harmFactor: this.getHarmFactors(badRsnList.BADRSN_CODE), // 有害因素
            examConclusion: this.getConclusion(badRsnList.EXAM_CONCLUSION_CODE), // 体检结论
            suspectedOccupationalDisease: this.getSuspectedOccupationalDisease(
              badRsnList.YSZYB_CODE
            ),
            occupationalContraindications: this.getOccupationalContraindication(
              badRsnList.ZYJJZ_CODE
            ), // 职业禁忌证
            otherDisease: badRsnList.QTJB_NAME, // 其他疾病
          });
        }
        const processedMhkRstList = []; // 主检判断主检结论
        const mhkRstList = data.TD_TJ_MHKRSTS.TD_TJ_MHKRST;
        if (mhkRstList && Array.isArray(mhkRstList) && mhkRstList.length > 0) {
          for (const mhkRsItem of mhkRstList) {
            const item = {};
            item.examConclusion = this.getConclusion(mhkRsItem.BHKRST_CODE);
            processedMhkRstList.push(item);
          }
        } else if (
          Object.prototype.toString.call(mhkRstList) === '[object Object]'
        ) {
          processedMhkRstList.push({
            examConclusion: this.getConclusion(mhkRstList.BHKRST_CODE),
          });
        }
        const processedSymptomList = []; // 症状信息
        const symptomList = data.TD_TJ_SYMPTOMS.TD_TJ_SYMPTOM;
        if (
          symptomList &&
          Array.isArray(symptomList) &&
          symptomList.length > 0
        ) {
          for (const symptomItem of symptomList) {
            const item = {};
            item.symptom = this.getSymptom(symptomItem.SYMPTOM_CODE);
            item.othsym = symptomItem.OTHSYM || '';
            item.chkdat = symptomItem.CHKDAT && new Date(symptomItem.CHKDAT);
            item.chkdoct = symptomItem.CHKDOCT || '';
            processedSymptomList.push(item);
          }
        } else if (
          Object.prototype.toString.call(symptomList) === '[object Object]'
        ) {
          processedSymptomList.push({
            symptom: this.getSymptom(symptomList.SYMPTOM_CODE),
            othsym: symptomList.OTHSYM || '',
            chkdat: symptomList.CHKDAT && new Date(symptomList.CHKDAT),
            chkdoct: symptomList.CHKDOCT || '',
          });
        }
        // const processedSupoccdiseList = []; // 疑似职业病
        // if (item.supoccdiseList) {
        //   for (const supoccdiseItem of item.supoccdiseList) {
        //     processedSupoccdiseList.push({
        //       badrsn: this.getHarmFactors(supoccdiseItem.badrsn), // 有害因素
        //       occdiseCode: this.getSuspectedOccupationalDisease(
        //         supoccdiseItem.occdiseCode
        //       ), // 疑似职业病
        //     });
        //   }
        // }
        const processedBhkSubList = []; // 体检详细项目
        const bhkSubList = data.TD_TJ_BHKSUBS.TD_TJ_BHKSUB;
        if (bhkSubList && Array.isArray(bhkSubList) && bhkSubList.length > 0) {
          for (const bhkSubItem of bhkSubList) {
            const subData = this.getSubData(bhkSubItem);
            processedBhkSubList.push(subData);
          }
        } else if (
          Object.prototype.toString.call(bhkSubList) === '[object Object]'
        ) {
          const subData = this.getSubData(bhkSubList);
          processedBhkSubList.push(subData);
        }
        const processedExmdDataList = this.getExmdDataList(data.TD_TJ_EXMSDATA); // 一般问诊项目
        const processedBhkAnamnesisList = []; // 既往病史
        const bhkAnamnesisList = data.TD_BHK_ANAMNESISES.TD_BHK_ANAMNESIS;
        if (
          bhkAnamnesisList &&
          Array.isArray(bhkAnamnesisList) &&
          bhkAnamnesisList.length > 0
        ) {
          for (const bhkAnamnesisItem of bhkAnamnesisList) {
            const item = {};
            item.hstnam = bhkAnamnesisItem.HSTNAM || '';
            item.hstcruprc = bhkAnamnesisItem.HSTCRUPRC;
            item.chkdoct = bhkAnamnesisItem.CHKDOCT || '';
            item.hstlps = bhkAnamnesisItem.HSTLPS || '';
            item.hstunt = bhkAnamnesisItem.HSTUNT || '';
            item.chkdat = bhkAnamnesisItem.HSTDAT && new Date(bhkAnamnesisItem.HSTDAT);
            item.othsym = bhkAnamnesisItem.OTHSYM || '';
            processedBhkAnamnesisList.push(item);
          }
        } else if (
          Object.prototype.toString.call(bhkAnamnesisList) === '[object Object]'
        ) {
          const item = {};
          item.hstnam = bhkAnamnesisList.HSTNAM || '';
          item.hstcruprc = bhkAnamnesisList.HSTCRUPRC;
          item.chkdoct = bhkAnamnesisList.CHKDOCT || '';
          item.hstlps = bhkAnamnesisList.HSTLPS || '';
          item.hstunt = bhkAnamnesisList.HSTUNT || '';
          item.chkdat = bhkAnamnesisList.HSTDAT
            ? new Date(bhkAnamnesisList.HSTDAT)
            : '';
          item.othsym = bhkAnamnesisList.OTHSYM || '';
          processedBhkAnamnesisList.push(item);
        }
        // const processedContraindList = []; // 职业禁忌证
        // if (item.contraindList) {
        //   for (const contraindItem of item.contraindList) {
        //     processedContraindList.push({
        //       badrsn: this.getHarmFactors(contraindItem.badrsn), // 有害因素
        //       contraind: this.getOccupationalContraindication(
        //         contraindItem.contraindCode
        //       ), // 职业禁忌证
        //     });
        //   }
        // }
        // #endregion
        const workYears = employeeAdminInfo.TCHBADRSNTIM
          ? employeeAdminInfo.TCHBADRSNTIM + '年'
          : employeeAdminInfo.TCHBADRSNMONTH + '个月'; // 工龄
        const suspectInfo = {
          batch: haveHealthCheckData._id,
          reportCode: employeeAdminInfo.BHK_CODE || '', // 个人体检编号
          name: employeeAdminInfo.PERSON_NAME || '', // 姓名
          age: employeeAdminInfo.AGE || '', // 年龄
          gender: employeeAdminInfo.SEX === 1 ? '0' : '1', // 性别
          workType, // 工种
          lastBhkCode: employeeAdminInfo.LAST_BHK_CODE, // 上次体检编号
          harmFactors: employeeAdminInfo.BADRSN, // 危害因素
          illness: employeeAdminInfo.MHKADV, // 建议
          recheck: this.getIsReview(employeeAdminInfo.IF_RHK), // 是否复查
          checkDate: new Date(employeeAdminInfo.BHK_DATE), // 体检日期
          IDCard: employeeAdminInfo.IDC || '', // 身份证号
          EnterpriseID: inOurCompany._id, // 企业id
          organization: physicalExamOrg.name || '', // 体检机构
          workYears, // 工龄
          abnormalIndex: employeeAdminInfo.BHKRST, // 异常指标
          opinion: employeeAdminInfo.MHKADV, // 主检建议
          CwithO: this.getFinalConclusion(processedMhkRstList), // 体检结论
          employeeId: inOurEmployee
            ? inOurEmployee._id
            : 'weichadao' + new Date().getTime(), // 员工id
          checkType: this.getOnguardState(employeeAdminInfo.ONGUARD_STATE), // 体检类型
          otherHarmFactors: harmFactors, // 其他危害因素
          riskFactorsOfPhysicalExaminations: processedBadrsnList, // 体检危害因素
          bhkSubList: processedBhkSubList, // 体检结果
          mhkRstList: processedMhkRstList, // 主检判断主检结论
          exmdDataList: [ processedExmdDataList ], // 一般问诊项目
          symptomList: processedSymptomList, // 症状信息
          bhkAnamnesisList: processedBhkAnamnesisList, // 既往病史
        };
        // 5. 查询该项目中是否存在该员工，如果存在的话，就跳过
        const existEmployeeOfProject =
          await ctx.service.importToDb.getSuspectByQuery({
            // batch: suspectInfo.batch,
            employeeId: suspectInfo.employeeId, // 员工id
            reportCode: employeeAdminInfo.BHK_CODE,
          });
        if (existEmployeeOfProject) {
          await ctx.model.Suspect.updateOne(
            { _id: existEmployeeOfProject._id },
            { $set: suspectInfo }
          );
        } else {
          await ctx.service.importToDb.createSuspect(suspectInfo);
          // 自增实检人数
          const $inc = { actuallNum: 1 };
          $inc[
            this.getConclusionField(
              this.getFinalConclusion(processedMhkRstList)
            )
          ] = 1;
          await ctx.model.Healthcheck.findOneAndUpdate(
            { _id: haveHealthCheckData._id },
            { $inc }
          );
        }
        // // 自增实检人数
        // const $inc = { actuallNum: 1 };
        // $inc[
        //   this.getConclusionField(this.getFinalConclusion(processedMhkRstList))
        // ] = 1;
        // await ctx.model.Healthcheck.findOneAndUpdate(
        //   { _id: haveHealthCheckData._id },
        //   { $inc }
        // );
        const processedEmhistoryList = []; // 职业史
        const emhistoryList = data.TD_TJ_EMHISTORYS.TD_TJ_EMHISTORY;
        if (
          emhistoryList &&
          Array.isArray(emhistoryList) &&
          emhistoryList.length > 0
        ) {
          for (const emhistoryItem of emhistoryList) {
            processedEmhistoryList.push({
              employeeId: inOurEmployee
                ? inOurEmployee._id
                : 'weichadao' + new Date().getTime(),
              entryTime: emhistoryItem.STASTP_DATE
                ? emhistoryItem.STASTP_DATE.split('-')[0]
                : '', // 入职时间
              leaveTime: emhistoryItem.STASTP_DATE
                ? emhistoryItem.STASTP_DATE.split('-')[1]
                : '', // 离职时间
              workUnit: emhistoryItem.UNIT_NAME,
              workshop: emhistoryItem.DEPARTMENT,
              workType: emhistoryItem.WORK_TYPE,
              prfraysrt: emhistoryItem.PRFRAYSRT, // 接触有害因素
              prfwrklod: emhistoryItem.PRFWRKLOD, // (放射)每日工作时数或工作量
              prfshnvlu: emhistoryItem.PRFSHNVLU, //	(放射)职业史累积受照剂量
              prfexcshn: emhistoryItem.PRFEXCSHN, // (放射)职业史过量照射史
              prfraysrt2: emhistoryItem.PRFRAYSRT2, //	(放射)职业照射种类
              prfraysrtcods: emhistoryItem.PRFRAYSRTCODS, // (放射)职业照射种类代码
              fsszl: emhistoryItem.FSSZL, //	(放射)放射线种类
              defendStep: emhistoryItem.DEFEND_STEP, //	(非放射)防护措施
              chkdat: emhistoryItem.CHKDAT && new Date(emhistoryItem.CHKDAT), // 检查日期
              chkdoc: emhistoryItem.CHKDOCT, //	检查医生
            });
            // 职业病史整理
            await ctx.service.importToDb.createOccupationalHistory(
              processedEmhistoryList,
              'many'
            );
            // res.push({
            //   haveHealthCheckData,
            //   createSuspectRes,
            //   createOccupationalHistoryRes,
            //   incRes,
            // });
          }
        } else if (
          Object.prototype.toString.call(emhistoryList) === '[object Object]'
        ) {
          processedEmhistoryList.push({
            employeeId: inOurEmployee
              ? inOurEmployee._id
              : 'weichadao' + new Date().getTime(),
            entryTime: emhistoryList.STASTP_DATE
              ? emhistoryList.STASTP_DATE.split('-')[0]
              : '', // 入职时间
            leaveTime: emhistoryList.STASTP_DATE
              ? emhistoryList.STASTP_DATE.split('-')[1]
              : '', // 离职时间
            workUnit: emhistoryList.UNIT_NAME,
            workshop: emhistoryList.DEPARTMENT,
            station: emhistoryList.STATION,
            workType: emhistoryList.WORK_TYPE,
            prfraysrt: emhistoryList.PRFRAYSRT, // 接触有害因素
            prfwrklod: emhistoryList.PRFWRKLOD, // (放射)每日工作时数或工作量
            prfshnvlu: emhistoryList.PRFSHNVLU, //	(放射)职业史累积受照剂量
            prfexcshn: emhistoryList.PRFEXCSHN, // (放射)职业史过量照射史
            prfraysrt2: emhistoryList.PRFRAYSRT2, //	(放射)职业照射种类
            prfraysrtcods: emhistoryList.PRFRAYSRTCODS, // (放射)职业照射种类代码
            fsszl: emhistoryList.FSSZL, //	(放射)放射线种类
            defendStep: emhistoryList.DEFEND_STEP, //	(非放射)防护措施
            chkdat: emhistoryList.CHKDAT && new Date(emhistoryList.CHKDAT), // 检查日期
            chkdoc: emhistoryList.CHKDOCT, //	检查医生
          });
        }
        // 职业病史整理
        await ctx.service.importToDb.createOccupationalHistory(
          processedEmhistoryList,
          'many'
        );
        // res.push({
        //   haveHealthCheckData,
        // 体检报告数据处理 企业表里更新
        await ctx.service.healthcheck.dealWithHealcheckInfo(
          haveHealthCheckData._id
        );
        // 这个地方 以后如果要加预警的话在这里加；
        try {
          await ctx.service.report.updateHealthCheck(haveHealthCheckData._id);
        } catch (error) {
          ctx.auditLog('体检预警判断', `${error}`, 'error');
        }
        successLog.push({
          RID: data.TD_TJ_BHK.RID,
          type: 0,
          errorMsg: '',
        });
      } catch (error) {
        errorLog.push({
          RID: data.TD_TJ_BHK.RID,
          type: 2,
          errorMsg: '创建体检报告数据时出错，可能必传参数丢失',
        });
        ctx.auditLog('清洗体检报告数据出错啦', `${error}`, 'error');
        throw error; // 重新抛出错误
      }
      ctx.auditLog(
        '清洗体检报告数据成功整体数据',
        `${JSON.stringify(successLog)}`,
        'info'
      );
      if (errorLog.length > 0) {
        ctx.auditLog(
          '清洗体检报告数据失败整体数据',
          `${JSON.stringify(errorLog)}`,
          'error'
        );
      }
      const merageLog = successLog.concat(errorLog);
      await this.writeLogToZipFolder(merageLog, 'HealthyCheck');
    } catch (error) {
      ctx.auditLog('清洗体检报告数据', `${error}`, 'error');
      throw error; // 重新抛出错误
    }
  }

  /** 根据企业组织机构代码和名称获取企业信息
   * @param {Object} params - 参数对象
   * @param {String} params.code - 搜索关键字 必填
   * @param {String} params.cname - 搜索关键字 必填
   * @return {Object} - 返回企业信息
   */
  async getOneCompanyByCodeAndName(params) {
    const { ctx } = this;
    const { code, cname } = params;
    try {
      if (!code || !cname) {
        throw new Error('组织机构代码和名称不能为空');
      }
      const res = await ctx.model.Adminorg.findOne({
        cname,
      });
      if (!res) {
        return await ctx.model.Adminorg.findOne({
          code,
        });
      }
      return res;
    } catch (error) {
      console.log('error', error);
      throw new Error('查询企业失败' + error);
    }
  }

  /** 获取工种 3.21
   * @param {string} name - The value to be processed.
   * @param {string} code - The value to be processed.
   * @return {string} - 工种
   * */
  getWorkType(name, code) {
    if (code === '0014' || code === '0033' || code === '999999') {
      return name || '';
    }
    const codeObj = {
      10001: '铅锌模工',
      10002: '编绕烧结滤器工',
      10003: '飞机散热导管制造工',
      10004: '化学铣切工',
      10005: '飞机发动机叶片抛光工',
      10006: '复合材料成型工',
      10007: '复合材料装配工',
      10008: '救生设备非金属零件制作工',
      10009: '航空救生装备裁剪工',
      10010: '航空救生设备缝制工',
      10011: '飞机模线样板钳工',
      10012: '飞机型架装配钳工',
      10013: '飞机夹层结构制造工',
      10014: '飞机铆装钳工',
      10015: '起落架、附件装配试验工',
      10016: '军械安装试验工',
      10017: '结合测量工',
      10018: '起落架、冷气、液压系统安装调试',
      10019: '飞机发动机装配修理钳工',
      10020: '飞机发动机平衡工',
      10021: '航空燃油喷嘴装配试验工',
      10022: '飞机桨叶平衡工',
      10023: '飞机螺旋浆装配试验工',
      10024: '航空调速器装配试验工',
      10025: '航空附件装配钳工',
      10026: '过滤器组合钳工',
      10027: '外勤机械工',
      10028: '外勤军械工',
      10029: '外勤仪表、电器工',
      10030: '外勤无线电雷达工',
      10031: '飞机发动机试车工',
      10032: '飞机发动机外场排故工',
      10033: '导弹机械装配调试工',
      10034: '导弹光学装配调试工',
      10035: '航空电动陀螺仪表装配工',
      10036: '航空电气机械仪表装配工',
      10037: '飞机仪表电器安装试验工',
      10038: '飞机动力系统安装调试工',
      10039: '飞机操纵系统安装调试工',
      10040: '飞机电缆工',
      10041: '飞机无线电雷达安装试验工',
      10042: '无线电台操纵修理工',
      10043: '机场雷达操纵修理工',
      10044: '飞机自动驾驶仪测试调整工',
      10045: '导弹电气装配调试工',
      10046: '航空电机平衡工',
      10047: '玻璃液体电门制造工',
      10048: '飞机导管工',
      10049: '飞机发动机管子工',
      10050: '飞机钣金工',
      10051: '飞机蒙皮落压钣金工',
      10052: '爆炸成型工',
      10053: '飞机钣金下料工',
      10054: '飞机发动机钣金工',
      10055: '飞机钣金检验工',
      10056: '飞机导管检验工',
      10057: '飞机铆接部装检验工',
      10058: '飞机总装、试飞检验工',
      10059: '飞机电缆检验工',
      10060: '飞机起落架、附件装配试验检验工',
      10061: '飞机模线样板检验工',
      10062: '飞机发动机装配检验工',
      10063: '导弹光学装配调试检验工',
      10064: '导弹电气装配调试检验工',
      10065: '航空附件装配检验工',
      10066: '飞机附件试验检验工',
      10067: '航空散热导管检验工',
      10068: '过滤器组合检验工',
      10069: '航空救生设备缝制检验工',
      10070: '航空特纺材料检验',
      10071: '航空仪表装配检验工',
      10072: '航空电器调试工',
      10073: '航空仪表调试工',
      10074: '航空无线电调试工',
      10075: '机载雷达调试工',
      10076: '飞机军械调试工',
      10077: '航空氧气设备调试工',
      10078: '飞机试验工(曲线显形仪表工)',
      10079: '飞机试验工',
      10080: '航空发动机试车检验工',
      10081: '导弹例行试验工',
      10082: '飞机螺旋桨强度试验工',
      10083: '飞机附件试验工',
      10084: '飞机机轮试验工',
      10085: '航空油泵壳体试验冲洗工',
      10086: '航空救生装备空试工',
      10087: '航空救生地面试验工',
      10088: '航空救生装备精密光学测试工',
      10089: '航空救生装备遥控遥测测试工',
      10090: '航空试验设备调试工',
      10091: '航空硅器件测试工',
      10092: '航空绝缘材料应用试验工',
      10093: '航空仪表成品试验工',
      10094: '飞机油箱导管试验工',
      10095: '飞机模线样板移形工',
      10096: '航空保伞工',
      10097: '试飞站养场工',
      10098: '液压固化成型工',
      10099: '防热材料成型工',
      10100: '泡沫夹层制造工',
      10101: '胶接装配工',
      10102: '胶布制品工',
      10103: '喷涂发泡工',
      10104: '弹头部段装配工',
      10105: '头部钳铆装配工',
      10106: '波纹管成型工',
      10107: '金属软管、补偿管、导管试验工',
      10108: '导弹箱体装配测试工',
      10109: '下料成型装配工',
      10110: '导弹附件装配试验工',
      10111: '合成膜电位计工',
      10112: '伺服机构装配工',
      10113: '伺服机构调试工',
      10114: '特种密封件制品工',
      10115: '导弹总体装配工',
      10116: '导弹总体测试工',
      10117: '惯性器件装配工',
      10118: '惯性器件调试工',
      10119: '金属网软管编织工',
      10120: '金属软管成型工',
      10121: '特种接插件装配工',
      10122: '静电记录头制作工',
      10123: '液、电、气压部件装配调试工',
      10124: '战术导弹引信装配工',
      10125: '引信、检查仪检验试验工',
      10126: '自动驾驶仪测度调整工',
      10127: '火箭推进剂化验工',
      10128: '试车台推进剂供应工',
      10129: '试车台供气配气工',
      10130: '试车台振动测量工',
      10131: '试车台流量测量工',
      10132: '试车台推力测量工',
      10133: '试车台压力测量工',
      10134: '试车台温度测量工',
      10135: '试车台控制工',
      10136: '试车台吊装清洗工',
      10137: '火箭推进剂色谱分析工',
      10138: '试车台测力计量检定工',
      10139: '液氧、氮气化验工',
      10140: '液体推进剂物化性能试验工',
      10141: '高空模拟试验工',
      10142: '推进剂转运操作工',
      10143: '火箭发动机组件介质试验工',
      10144: '火箭发动机组件气流试验工',
      10145: '火箭发动机组件水力试验工',
      10146: '火箭发动机组件试验电测工',
      10147: '火箭发动机组件液流试验工',
      10148: '控制系统试验工',
      10149: '激光陀螺调试工',
      10150: '惯性仪器试验工',
      10151: '陀螺仪表检漏、充油、充气工',
      10152: '气动元件、设备及系统试验工',
      10153: '地面设备液压系统元件试验工',
      10154: '液体晃动试验工',
      10155: '特种力学环境试验工',
      10156: '飞行器振动特性试验加注工',
      10157: '飞行器动试验测试工',
      10158: '结构静(热)强度环境测试工',
      10159: '导弹结构强度环境试验安装试验工',
      10160: '扫描电镜试验工',
      10161: '涂料试验工',
      10162: '热物理性能试验工',
      10163: '环境试验工',
      10164: '铝蜂窝胶接工',
      10165: '胶接试验工',
      10166: '特种钎焊试验工',
      10167: '特种炭素制品工',
      10168: '特种材料声发射检测试验工',
      10169: '复合材料超声波探伤试验工',
      10170: '复合材料射线探伤工',
      10171: '特种材料渗透探伤试验工',
      10172: '导弹工程塑料工',
      10173: '复合材料试验工',
      10174: '遥测系统试验工',
      10175: '硅酸盐复合材料工',
      10176: '热等静压试验工',
      10177: '风洞试验操作工',
      10178: '气瓶鉴定、充气工',
      10179: '液体火箭发动机试验直测工',
      10180: '液体火箭发动机试验数据处理工',
      10181: '液体火箭发动机装配试验工',
      10182: '活门自动器装配试验工',
      10183: '涡轮泵装配工',
      10184: '推力室装配试验工',
      10185: '发动机高温处理工',
      10186: '冲压发动机试车台气路系统运行工',
      10187: '冲压发动机试车台燃油加热系统运行工',
      10188: '冲压发动机试车台供油系统运行工',
      10189: '壳体芯模装配成型工',
      10190: '壳体绝热芯模成型工',
      10191: '预浸渍成型工',
      10192: '复合材料胶接成型工',
      10193: '壳体缠绕成型工',
      10194: '浸胶模压成型工',
      10195: '布带浸胶缠绕成型工',
      10196: '特种纤维编织工',
      10197: '碳/碳喉衬气相沉积工',
      10198: '碳素制品高温处理工',
      10199: '钨渗铜制品工',
      10200: '等离子喷涂工',
      10201: '高温焰流烧蚀试验工',
      10202: '发动机壳体表面处理工',
      10203: '绝热衬层配方试验工',
      10204: '绝热衬层模压工',
      10205: '绝热层成型工',
      10206: '绝热层固化工',
      10207: '衬层成型工',
      10208: '固体发动机绝热壳体检测工',
      10209: '绝热层、衬层随机试件制作工',
      10210: '绝热层粘结剂合成工',
      10211: '氧化剂准备工',
      10212: '氧化剂分析测试工',
      10213: '复合固体推进剂配料工',
      10214: '复合固体推进剂胶化工',
      10215: '复合固体推进剂浇注工',
      10216: '复合固体推进剂硫化工',
      10217: '固体发动机药面修理工',
      10218: '固体发动机灌浆工',
      10219: '固体发动机装药检测工',
      10220: '固体发动机总装齐套工',
      10221: '固体发动机总装工',
      10222: '固体发动机高能X射线探伤工',
      10223: '固体发动机总装检测工',
      10224: '非金属芯模制作工',
      10225: '固体推进剂超声波清理工',
      10226: '复合固体推进剂配方试验工',
      10227: '复合固体推进剂燃烧性能测试工',
      10228: '复合固体推进剂物理机械性能测试',
      10229: '发动机强度与环境试验工',
      10230: '发动机力学环境试验电测工',
      10231: '发动机静力强度测试工',
      10232: '喷管冷态试验电测工',
      10233: '喷管冷态试验装配工',
      10234: '激光点火燃烧实验工',
      10235: '点火药制药工',
      10236: '发火管装配工',
      10237: '金属玻璃、陶瓷封装工',
      10238: '点火器试验工',
      10239: '固体火箭发动机温度环境试验工',
      10240: '固体火箭发动机试验校准安装工',
      10241: '固体火箭发动机试验电测工',
      10242: '固体火箭发动机静止试验装调工',
      10243: '固体火箭发动机试验数据处理工',
      10244: '光胶胶缝工',
      10245: '卫星总体装配工',
      10246: '卫星综合电性能测试工',
      10247: '卫星蜂窝夹层结构件制造工',
      10248: '卫星光学冷加工',
      10249: '空间环模光学装校工',
      10250: '空间环模光学测量工',
      10251: '空间环模试验及卫星检漏工',
      10252: '空间环模低温试验工',
      10253: '空间环模深冷试验工',
      10254: '空间环模真空试验工',
      10255: '空间环模试验温度标定温度测量工',
      10256: '热敏电阻红外探测器制造工',
      10257: '薄膜加热器制造工',
      10258: '时间程序控制器装配工',
      10259: '真空电子束焊工',
      11001: '燃油值班员',
      11002: '卸储煤值班员',
      11003: '输煤值班员',
      11004: '燃料集控值班员',
      11005: '燃油设备检修工',
      11006: '卸储煤设备检修工',
      11007: '输煤机械检修工',
      11008: '燃料化验员',
      11009: '电厂水化验员',
      11010: '油务员',
      11011: '电厂水处理值班员',
      11012: '环境保护监察员',
      11013: '除灰值班员',
      11014: '电除尘值班员',
      11015: '锅炉辅机值班员',
      11016: '锅炉运行值班员',
      11017: '锅炉本体检修工',
      11018: '锅炉辅机检修工',
      11019: '管阀检修工',
      11020: '除灰设备检修工',
      11021: '电除尘设备检修工',
      11022: '热力网值班员',
      11023: '汽轮机辅机值班员',
      11024: '水泵值班员',
      11025: '汽轮机运行值班员',
      11026: '汽轮机本体检修工',
      11027: '汽轮机调速系统检修工',
      11028: '水泵检修工',
      11029: '汽轮机辅机检修工',
      11030: '电机氢冷值班员',
      11031: '厂用电值班员',
      11032: '电气值班员',
      11033: '集控巡视员',
      11034: '集控值班员',
      11035: '电机检修工',
      11036: '热工仪表检修工',
      11037: '热工自动装置检修工',
      11038: '热工程控保护工',
      11039: '水轮发电机组值班员',
      11040: '水轮发电机机械检修工',
      11041: '水轮机检修工',
      11042: '水轮机调速器机械检修工',
      11043: '水力机械试验工',
      11044: '水电自动装置检修工',
      11045: '高压线路带电检修工',
      11046: '送电线路工',
      11047: '配电线路工',
      11048: '电力电缆工',
      11049: '内线安装工',
      11050: '变电站值班员',
      11051: '调相机值班员',
      11052: '换流站值班员',
      11053: '变压器检修工',
      11054: '直流设备检修工',
      11055: '变电检修工',
      11056: '变电带电检修工',
      11057: '电气试验工',
      11058: '电测仪表工',
      11059: '继电保护工',
      11060: '电力负荷控制工',
      11061: '用电监察员',
      11062: '抄表收费核算员',
      11063: '装表接电工',
      11064: '电能表修理工',
      11065: '电能表校验工',
      11066: '锅炉钢架安装工',
      11067: '锅炉受热面安装工',
      11068: '电厂筑炉保温工',
      11069: '锅炉辅机安装工',
      11070: '汽轮机本体安装工',
      11071: '汽轮机调速安装工',
      11072: '汽轮机辅机安装工',
      11073: '电厂管道安装工',
      11074: '热工仪表及控制装置安装工',
      11075: '热工仪表及控制装置试验工',
      11076: '高压电气安装工',
      11077: '二次线安装工',
      11078: '厂用电安装工',
      11079: '电缆安装工',
      11080: '送电线路架设工',
      11081: '变电一次安装工',
      11082: '变电二次安装工',
      11083: '水轮机安装工',
      11084: '水轮发电机安装工',
      11085: '卷线安装工',
      11086: '调速器安装工',
      11087: '水轮发电机组管路安装工',
      11088: '金属结构制作与安装工',
      12001: '开挖钻工',
      12002: '水工爆破工',
      12003: '锻钎工',
      12004: '坝工模板工',
      12005: '坝工钢筋工',
      12006: '坝工混凝土工',
      12007: '钻探灌浆工',
      12008: '喷护工',
      12009: '防渗墙工',
      12010: '砌筑工',
      12011: '坝工土料试验工',
      12012: '坝土混凝土试验工',
      12013: '水工泥沙实验工',
      12014: '水工结构实验工',
      12015: '混凝土维修工',
      12016: '土石维修工',
      12017: '闸门运行工',
      12018: '水工防腐工',
      12019: '水工监测工',
      12020: '河道修防工',
      12021: '渠道维修工',
      12022: '灌区供水工',
      12023: '灌溉试验工',
      12024: '泵站机电设备维修工',
      12025: '泵站运行工',
      12026: '灌排工程工',
      12027: '水文勘测工',
      12028: '水文勘测船工',
      12029: '化学灌浆工',
      12030: '防治工',
      12031: '水土保持防治工',
      12032: '水土保持测试工',
      12033: '水土保持勘测工',
      13001: '木工',
      13002: '古建木工',
      13003: '瓦工',
      13004: '古建瓦工',
      13005: '抹灰工',
      13006: '石工',
      13007: '古建石工',
      13008: '建筑油漆工',
      13009: '古建油漆工',
      13010: '古建彩画工',
      13011: '防水工',
      13012: '钢筋工',
      13013: '混凝土工',
      13014: '架子工',
      13015: '测量放线工',
      13016: '建筑材料试验工',
      13017: '土工试验工',
      13018: '混凝土制品模具工',
      13019: '翻模工',
      13020: '金属门窗工',
      13021: '工程安装钳工',
      13022: '管道工',
      13023: '工程电气设备安装调试工',
      13024: '通风工',
      13025: '安装起重工',
      13026: '筑炉工',
      13027: '起重机驾驶员',
      13028: '塔式起重机驾驶员',
      13029: '推土、铲运机驾驶员',
      13030: '挖掘机驾驶员',
      13031: '中小型建筑机械操纵工',
      13032: '工程机械修理工',
      13033: '工程凿岩工',
      13034: '工程爆破工',
      13035: '打桩工',
      13036: '电梯安装维修工',
      13037: '白蚁防治工',
      13038: '筑路工',
      13039: '下水道工',
      13040: '沥青加工工',
      13041: '道路养护工',
      13042: '下水道养护工',
      13043: '污水处理工',
      13044: '污泥处理工',
      13045: '污水化验监测工',
      13046: '沥青混凝土摊铺机操作工',
      13047: '管函顶进工',
      13048: '隧道工',
      13049: '道路巡视工',
      13050: '电汽车售票员',
      13051: '无轨电车修理工',
      13052: '无轨电车架线工',
      13053: '无轨电车驾驶员',
      13054: '净水工',
      13055: '水质检验工',
      13056: '泵站操作工',
      13057: '供水调度员',
      13058: '水井工',
      13059: '水表装修工',
      13060: '管道检漏工',
      13061: '机械煤气发生炉工',
      13062: '炭化炉工',
      13063: '水煤气炉工',
      13064: '炼焦煤气炉工',
      13065: '重油制气工',
      13066: '配煤工',
      13067: '炉温工',
      13068: '冷凝鼓风工',
      13069: '废热钴炉工',
      13070: '煤气调压工',
      13071: '煤气输送工',
      13072: '煤气化验工',
      13073: '煤气表装修工',
      13074: '煤气户内检修工',
      13075: '液化石油气罐区运行工',
      13076: '液化石油气灌瓶工',
      13077: '煤气燃具制造工',
      13078: '液化石油气机械修理工',
      13079: '热力运行工',
      13080: '供热仪表工',
      13081: '热力司炉工',
      13082: '锅炉水质化验工',
      13083: '育苗工',
      13084: '绿化工',
      13085: '花卉工',
      13086: '盆景工',
      13087: '植保工',
      13088: '假山工',
      13089: '观赏动物饲养工',
      13090: '建筑雕刻工',
      13091: '动物标本制作工',
      13092: '道路清扫工',
      13093: '粪便清理工',
      13094: '公厕保洁工',
      13095: '垃圾综合利用工',
      13096: '垃圾处理工',
      13097: '粪便净化处理工',
      13098: '死畜无害化处理工',
      13099: '环卫化验工',
      13100: '环卫机动车驾驶员',
      14001: '固体矿产钻探工',
      14002: '水文、水井钻探工',
      14003: '工程地质、工程施工钻工',
      14004: '轻型浅孔钻探工',
      14005: '坑探工',
      14006: '物探工',
      14007: '采样工',
      14008: '水文地质工',
      14009: '碎样工',
      14010: '磨片工',
      14011: '淘洗工',
      14012: '岩心保管工',
      14013: '地震勘探爆炸工',
      14014: '海洋勘探震源工',
      14015: '可控震源工',
      14016: '平台水手',
      14017: '水下设备操作工',
      14018: '海洋地质取样工',
      14019: '海洋地震电缆工',
      14020: '海洋地震电缆修造工',
      14021: '钻探、坑探材料工',
      14022: '金刚石钻头、扩孔器准备工',
      14023: '金刚厂钻头、扩孔器组装工',
      14024: '金刚石钻头、扩孔器烧结工',
      15001: '翻车机工',
      15002: '烧结原料工',
      15003: '配料工',
      15004: '混合料工',
      15005: '烧结工',
      15006: '冷却筛分工',
      15007: '返矿工',
      15008: '风机工',
      15009: '球团原料工',
      15010: '造球工',
      15011: '球团竖炉工',
      15012: '成品矿运送工',
      15013: '炼焦洗煤工',
      15014: '洗煤过滤工',
      15015: '洗煤药剂工',
      15016: '备煤工',
      15017: '配煤工',
      15018: '烧焦工',
      15019: '焦炉调温工',
      15020: '煤焦车司机',
      15021: '运焦工',
      15022: '冷凝鼓风工',
      15023: '硫铵工',
      15024: '蒸馏工',
      15025: '化产泵工',
      15026: '洗涤工',
      15027: '结晶工',
      15028: '脱酚工',
      15029: '沥青工',
      15030: '焦化副产品包装工',
      15031: '槽罐清洗工',
      15032: '高炉原料工',
      15033: '炉前工',
      15034: '炼铁工',
      15035: '热风炉工',
      15036: '煤粉工',
      15037: '高炉配管工',
      15038: '碾泥工',
      15039: '铸铁工',
      15040: '铁库工',
      15041: '渣处理工',
      15042: '炉衬工',
      15043: '钢水罐准备工',
      15044: '整模工',
      15045: '废钢加工工',
      15046: '炼钢原料工',
      15047: '混铁炉工',
      15048: '装入机工',
      15049: '平炉炼钢工',
      15050: '转炉炼钢工',
      15051: '电炉炼钢工',
      15052: '特种炉炼钢工',
      15053: '炉外精炼工',
      15054: '铸钢工',
      15055: '连铸工',
      15056: '脱模工',
      15057: '钢锭坯整理工',
      15058: '换罐清渣工',
      15059: '炼钢备品工',
      15060: '轧钢原料工',
      15061: '均热工',
      15062: '加热工',
      15063: '轧钢工',
      15064: '轧钢精整工',
      15065: '重轨加工工',
      15066: '大管坯处理工',
      15067: '铸管造型工',
      15068: '铸管工',
      15069: '铸管精整工',
      15070: '铸管检查工',
      15071: '铸管涂油工',
      15072: '铸管备品工',
      15073: '钢材热处理工',
      15074: '酸洗工',
      15075: '脱脂工',
      15076: '镀锌工',
      15077: '镀锡工',
      15078: '彩涂工',
      15079: '轧钢成品工',
      15080: '轧钢备品工',
      15081: '拉丝工',
      15082: '钢丝绳制造工',
      15083: '金属纱网编织工',
      15084: '钢丝制品精整工',
      15085: '钢丝制品成品工',
      15086: '钢丝制品备品工',
      15087: '铁合金原料工',
      15088: '铁合金电炉冶炼工',
      15089: '铁合金成品工',
      15090: '铁合金电极糊工',
      15091: '钒铁原料工',
      15092: '钒铁焙烧工',
      15093: '钒铁浸滤工',
      15094: '钒铁沉淀工',
      15095: '钒铁熔化工',
      15096: '金属铬原料工',
      15097: '金属铬焙烧工',
      15098: '金属铬浸滤工',
      15099: '金属铬反应工',
      15100: '金属铬还原工',
      15101: '铁合金转炉冶炼工',
      15102: '铁合金高炉冶炼工',
      15103: '铁合金特种炉冶炼工',
      15104: '铁合金真空炉冶炼工',
      15105: '铁合金回转窑冶炼工',
      15106: '铁合金备品工',
      15107: '炭素煅烧工',
      15108: '炭素配料工',
      15109: '炭素混捏工',
      15110: '炭素压型工',
      15111: '炭素焙烧工',
      15112: '炭素浸渍工',
      15113: '炭素石墨化工',
      15114: '炭素制品加工工',
      15115: '炭素纤维工',
      15116: '石墨制品工',
      15117: '耐火原料工',
      15118: '耐火成型工',
      15119: '耐火砖干燥工',
      15120: '隧道窑烧成工',
      15121: '倒焰窑烧成工',
      15122: '回转窑工',
      15123: '竖窑工',
      15124: '浸油退火工',
      15125: '磨砖工',
      15126: '粉状耐火材料加工工',
      15127: '特殊耐火材料工',
      15128: '耐火纤维制品工',
      15129: '煤气炉工',
      15130: '物理性能检验工',
      15131: '化学分析工',
      15132: '工艺监督工',
      15133: '产品质量检查工',
      15134: '取制样工',
      16001: '硫化物焙烧工',
      16002: '二氧化硫气体净化工',
      16003: '二氧化硫气体转化工',
      16004: '氨氧化工',
      16005: '浓硝酸工',
      16006: '酸性气体吸收工',
      16007: '氯化氢合成工',
      16008: '盐水工',
      16009: '蒸吸工',
      16010: '纯碱碳化工',
      16011: '重碱煅烧工',
      16012: '纯碱石灰工',
      16013: '重质纯碱工',
      16014: '联碱洗盐工',
      16015: '联碱结晶工',
      16016: '苛化工',
      16017: '变电整流工',
      16018: '烧碱电解工',
      16019: '氯氢处理工',
      16020: '烧碱蒸发工',
      16021: '固碱工',
      16022: '液氯工',
      16023: '电解槽修槽工',
      16024: '无机反应工',
      16025: '电化学反应工',
      16026: '窑炉反应工',
      16027: '高频等离子工',
      16028: '无机试剂工',
      16029: '有机试剂工',
      16030: '高纯试剂工',
      16031: '临床试剂工',
      16032: '催化剂制造工',
      16033: '催化剂试验工',
      16034: '合成氨总控工',
      16035: '水煤气工',
      16036: '油气化工',
      16037: '气体深冷分离工',
      16038: '合成氨转变工',
      16039: '合成氨净化工',
      16040: '合成氨气体压缩工',
      16041: '氨合成工',
      16042: '甲醇合成工',
      16043: '尿素合成工',
      16044: '尿素加工工',
      16045: '硝酸铵中和工',
      16046: '硝酸铵结晶造粒工',
      16047: '多孔硝酸铵造粒工',
      16048: '碳酸氢铵碳化工',
      16049: '硫酸铵中和工',
      16050: '黄磷电炉工',
      16051: '热法磷酸燃磷工',
      16052: '过磷酸钙混化工',
      16053: '过磷酸钙熟化工',
      16054: '磷矿酸解工',
      16055: '磷酸铵氨化造粒工',
      16056: '钙镁磷肥工',
      16057: '元素肥料混配工',
      16058: '醋纤酯制备工',
      16059: '棉胶液制备工',
      16060: '片基流延工',
      16061: '感光材料涂布工',
      16062: '照像乳剂合成工',
      16063: '照像乳剂熔化工',
      16064: '感光专用药液配制工',
      16065: '油乳制备工',
      16066: '感光材料包装工',
      16067: '135暗盒制造工',
      16068: '感光材料整理工',
      16069: '感光材料工艺检查工',
      16070: '废片、白银回收工',
      16071: '照相性能测定工',
      16072: '包装材料检验工',
      16073: '感光材料检验工',
      16074: '片(纸)基检验工',
      16075: '有机合成试验工',
      16076: '乳剂试验工',
      16077: '片(纸)基试验工',
      16078: '磁记录材料检验工',
      16079: '磁记录材料试验工',
      16080: '磁粉制备工',
      16081: '磁浆制备工',
      16082: '磁带涂布压光工',
      16083: '磁带整理工',
      16084: '软磁盘整理工',
      16085: '管式炉裂解工',
      16086: '裂解气分离工',
      16087: '裂解气压缩工',
      16088: '乙醇合成工',
      16089: '乙醇精馏工',
      16090: '羰基合成工',
      16091: '丁(辛)醇合成工',
      16092: '电石炉工',
      16093: '乙炔发生工',
      16094: '氯乙烯合成工',
      16095: '氯乙烯精馏工',
      16096: '氧氯化工',
      16097: '二氯乙烷精馏工',
      16098: '二氯乙烷裂解工',
      16099: '二氯乙烷裂解气分离工',
      16100: '氯乙烯聚合工',
      16101: '聚氯乙烯汽提干燥工',
      16102: '乙醛合成工',
      16103: '醋酸合成工',
      16104: '苯烃化工',
      16105: '乙苯脱氢工',
      16106: '苯乙烯精馏工',
      16107: '丁烯脱氢反应工',
      16108: '萃取精制工',
      16109: '丁苯聚合工',
      16110: '胶乳脱气工',
      16111: '丁苯后处理工',
      16112: '聚合工',
      16113: '有机合成工',
      16114: '炭黑生产工',
      16115: '染料拼混工',
      16116: '染料标准化工',
      16117: '染料应用试验工',
      16118: '农药生测试验工',
      16119: '涂料合成树脂工',
      16120: '研磨分散工',
      16121: '制漆配色调制工',
      16122: '溶剂蒸煮工',
      16123: '溶剂培菌工',
      16124: '溶剂发酵工',
      16125: '溶剂蒸馏工',
      16126: '原料准备工',
      16127: '司泵工',
      16128: '压缩机工',
      16129: '气体净化工',
      16130: '过滤工',
      16131: '油加热工',
      16132: '制冷工',
      16133: '蒸发工',
      16134: '蒸馏工',
      16135: '萃取工',
      16136: '吸收工',
      16137: '吸附工',
      16138: '干燥工',
      16139: '结晶工',
      16140: '包装工',
      16141: '水处理工',
      16142: '三废处理工',
      16143: '分析工',
      16144: '物性检验工',
      16145: '化工工艺试验工',
      16146: '化工总控工',
      16147: '化工检修钳工',
      16148: '化工检修铆工',
      16149: '化工检修管工',
      16150: '化工检修焊工',
      16151: '化工检修电工',
      16152: '化工仪表维修工',
      16153: '分析仪器维修工',
      16154: '化工防腐衬胶工',
      16155: '化工防腐喷镀工',
      16156: '化工防腐铅焊工',
      16157: '化工防腐砖板衬里工',
      16158: '化工防腐塑料工',
      16159: '化工玻璃钢防腐工',
      16160: '橡胶配料工',
      16161: '炼胶工',
      16162: '制浆工',
      16163: '浸胶刮浆工',
      16164: '压延工',
      16165: '压出工',
      16166: '裁断工',
      16167: '橡胶鞋工业缝纫工',
      16168: '橡胶半成品、半制品收发工',
      16169: '橡胶硫化工',
      16170: '胶件成品整修工',
      16171: '橡胶试验工',
      16172: '钢丝圈制造工',
      16173: '帘布贴合工',
      16174: '外胎成型工',
      16175: '内胎水胎制造工',
      16176: '编织缠绕工',
      16177: '胶管制品成型工',
      16178: '胶带成型工',
      16179: '胶布制品成型工',
      16180: '橡胶金属零附件处理及橡胶模具保管工',
      16181: '橡胶杂品成型工',
      16182: '软管及杂件总成工',
      16183: '浇注型聚氨脂胶件、胶带成型工',
      16184: '胶鞋成型工',
      16185: '亮油配制工',
      16186: '胶乳配制工',
      16187: '胶乳制品成型工',
      16188: '胶乳制品后处理工',
      16189: '废胶处理工',
      16190: '再生胶再生工',
      16191: '再生胶精炼工',
      17001: '纺织设备保全工',
      17002: '纺织设备检修工',
      17003: '纺织设备措施工',
      17004: '纺织设备揩车工',
      17005: '精梳植针工',
      17006: '纺织设备专件修理工',
      17007: '测试修理工',
      17008: '纺织设备部件配套工',
      17009: '清花挡车工',
      17010: '梳棉(毛)挡车工',
      17011: '并条挡车工',
      17012: '粗纱挡车工',
      17013: '细纱挡车工',
      17014: '络筒挡车工',
      17015: '并筒挡车工',
      17016: '捻线挡车工',
      17017: '摇纱挡车工',
      17018: '打小包工',
      17019: '打中(大)包工',
      17020: '转杯纺挡车工',
      17021: '空气调节工',
      17022: '售纱(筒)检验工',
      17023: '纤维验配工',
      17024: '纺部试验工',
      17025: '整经挡车工',
      17026: '浆纱挡车工',
      17027: '调浆工',
      17028: '穿筘挡车工',
      17029: '结经挡车工',
      17030: '织布挡车工',
      17031: '织布帮接工',
      17032: '织布换纬工',
      17033: '织布加油工',
      17034: '织布上轴工',
      17035: '验布工',
      17036: '修布工',
      17037: '整理分等工',
      17038: '码布工',
      17039: '织部试验工',
      17040: '劳动定额测定工',
      17041: '选毛(验毛)工',
      17042: '洗毛挡车工',
      17043: '合毛挡车工',
      17044: '针梳复洗挡车工',
      17045: '制条指导工',
      17046: '条染挡车工',
      17047: '混条挡车工',
      17048: '蒸纱工',
      17049: '纺部检验工',
      17050: '织补工',
      17051: '烧毛挡车工',
      17052: '匹染挡车工',
      17053: '缩呢挡车工',
      17054: '树脂整理工',
      17055: '烤呢挡车工',
      17056: '中间检验工',
      17057: '剪呢挡车工',
      17058: '电压挡车工',
      17059: '热定型挡车工',
      17060: '回修工',
      17061: '卷呢挡车工',
      17062: '上机开出工',
      17063: '织部检查工',
      17064: '准织皮工',
      17065: '坯布整理工',
      17066: '烫呢挡车工',
      17067: '染色牢度试验工',
      17068: '染色小样工',
      17069: '含油量分析工',
      17070: '纤维含量分析工',
      17071: '染纱挡车工',
      17072: '脱水挡车工',
      17073: '烘干挡车工',
      17074: '并线挡车工',
      17075: '络纬挡车工',
      17076: '蒸轴剪纱挡车工',
      17077: '量绒挡车工',
      17078: '毛纺织成品检验工',
      17079: '抄车工',
      17080: '散毛染色挡车工',
      17081: '分梳挡车工',
      17082: '刮车工',
      17083: '梳纺质量检查工',
      17084: '断头测定工',
      17085: '后纺质量检验工',
      17086: '纺部验纱工',
      17087: '拉毛挡车工',
      17088: '团毛挡车工',
      17089: '洗线挡车工',
      17090: '洗毛烘毛挡车工',
      17091: '弹毛挡车工',
      17092: '纬纱验纱工',
      17093: '卷轴工',
      17094: '洗呢(毯)挡车工',
      17095: '炭化挡车工',
      17096: '人工(机械)裁毯工',
      17097: '缝角叠毯工',
      17098: '化料工',
      17099: '罗纹机挡车工',
      17100: '柯登机罗纹套口工',
      17101: '横机挡车工',
      17102: '规格检验工',
      17103: '横机(圆机)半成品检验工',
      17104: '缝纫检验工',
      17105: '挑补绣工',
      17106: '成衫染色工',
      17107: '成衣定型工',
      17108: '成衣检验工',
      17109: '成衣指导工',
      17110: '化验分析工',
      17111: '布包检验工',
      17112: '扎把工',
      17113: '浸酸工',
      17114: '装麻工',
      17115: '烤麻工',
      17116: '精炼给油(制油)工',
      17117: '软麻挡车工',
      17118: '精干麻分磅工',
      17119: '扯麻工',
      17120: '开松挡车工',
      17121: '大小切挡车工',
      17122: '圆梳挡车工',
      17123: '拣麻分磅工',
      17124: '延展挡车工',
      17125: '制条挡车工',
      17126: '大小切圆梳指导工',
      17127: '麻球成包工',
      17128: '绕团挡车工',
      17129: '制乳液工',
      17130: '软麻(喂接)挡车工',
      17131: '运油麻工',
      17132: '清纤挡车工',
      17133: '量检挡车工',
      17134: '折切挡车工',
      17135: '缝边(口)挡车工',
      17136: '印袋挡车工',
      17137: '原料检验工',
      17138: '分束工',
      17139: '栉梳挡车工',
      17140: '联梳挡车工',
      17141: '成条挡车工',
      17142: '粗纱煮漂挡车工',
      17143: '剪毛挡车工',
      17144: '开幅挡车工',
      17145: '染色防水防腐浸渍挡车工',
      17146: '上蜡挡车工',
      17147: '配麻工',
      17148: '纺部指导工',
      17149: '织部指导工',
      17150: '剥茧挡车工',
      17151: '选茧挡车工',
      17152: '练(煮)茧挡车工',
      17153: '自动缫挡车工',
      17154: '立缫挡车工',
      17155: '复摇挡车工',
      17156: '丝小真空给湿工',
      17157: '编检工',
      17158: '绞丝工',
      17159: '疵点丝整理工',
      17160: '缫丝成品检验工',
      17161: '纤度丝检验工',
      17162: '汰头挡车工',
      17163: '炼油挡车工',
      17164: '选茧质量检验工',
      17165: '缫丝工艺试验工',
      17166: '缫丝半成品质量检验工',
      17167: '纤度感知器调验工',
      17168: '复整指导工',
      17169: '意匠工',
      17170: '成绞挡车工',
      17171: '准备指导工',
      17172: '泡丝(浸渍)工',
      17173: '整经指导工',
      17174: '经轴回修工',
      17175: '纤维分析工',
      17176: '准备原料保燥(给湿)工',
      17177: '织机装造工',
      17178: '纹版复制工',
      17179: '丝绸成品处理工',
      17180: '着色工',
      17181: '剪花机挡车工',
      17182: '拣丝(原料筛网)工',
      17183: '筛网成品烘燥工',
      17184: '纹丝染色工',
      17185: '绷丝工',
      17186: '榔丝工',
      17187: '开绵挡车工',
      17188: '外加工指导工',
      17189: '扒茧工',
      17190: '选配丝工',
      17191: '纹板连接工',
      17192: '润绸机挡车工',
      17193: '呢橡毯预缩机挡车工',
      17194: '烧摇挡车工',
      17195: '叠绸挡车工',
      17196: '精炼助剂配料工',
      17197: '水洗扯块工',
      17198: '打绵挡车工',
      17199: '调和挡车工',
      17200: '原料选别工',
      17201: '切茧挡车工',
      17202: '除蛹挡车工',
      17203: '腐化(发醇)工',
      17204: '练桶工',
      17205: '扎吐槽洗工',
      17206: '水洗挡车工',
      17207: '精干绵选别工',
      17208: '精练锅挡车工',
      17209: '配绵工',
      17210: '给湿工',
      17211: '精干品磅球工',
      17212: '开茧挡车工',
      17213: '落绵挡车工',
      17214: '切绵挡车工',
      17215: '排绵挡车工',
      17216: '磅绵配球工',
      17217: '制绵(圆梳工艺)指导工',
      17218: '练条挡车工',
      17219: '延绞挡车工',
      17220: '扬返挡车工',
      17221: '节取挡车工',
      17222: '翻丝挡车工',
      17223: '练绸工',
      17224: '轧水挡车工',
      17225: '呢毯挡车工',
      17226: '练整指导工',
      17227: '台车挡车工',
      17228: '大圆机挡车工',
      17229: '纬编挡车工',
      17230: '经编挡车工',
      17231: '上轴工',
      17232: '穿丝工',
      17233: '坯布修验工',
      17234: '络缝线工',
      17235: '绣花挡车工',
      17236: '铸针工',
      17237: '经编钳针工',
      17238: '图案打样工',
      17239: '织袜挡车工',
      17240: '缝头挡车工',
      17241: '袜子半成品检验工',
      17242: '袜子抽验工',
      17243: '修袜工',
      17244: '皂煮洗袜工',
      17245: '袜子定型工',
      17246: '烫袜工',
      17247: '配袜工',
      17248: '坯布检查工',
      17249: '翻布打印工',
      17250: '坯布缝接工',
      17251: '烧毛机挡车工',
      17252: '烧毛机司炉工',
      17253: '煮练机挡车工',
      17254: '漂酸洗机挡车工',
      17255: '开幅轧水烘燥机挡车工',
      17256: '单双层丝光机挡车工',
      17257: '碱(酸)减量机挡车工',
      17258: '炼漂机挡车工',
      17259: '三效挡车工',
      17260: '荷化工',
      17261: '配碱工',
      17262: '冷冻机操作工',
      17263: '染色挡车工',
      17264: '打卷机挡车工',
      17265: '退捻机挡车工',
      17266: '热载体油锅炉工',
      17267: '印染磨料工',
      17268: '印染煮糊工',
      17269: '印花色浆配制工',
      17270: '印花剩浆管理工',
      17271: '装拆花筒工正(副)手',
      17272: '打底机挡车工',
      17273: '印花挡车工',
      17274: '蒸化机挡车工',
      17275: '洗布机挡车工',
      17276: '拉幅机挡车工',
      17277: '泡沫整理机挡车工',
      17278: '液氨整理机挡车工',
      17279: '液氨回收工',
      17280: '树脂整理机挡车工',
      17281: '热定型机挡车工',
      17282: '焙烘机挡车工',
      17283: '蒸呢机挡车工',
      17284: '防缩机挡车工',
      17285: '轧光机挡车工',
      17286: '轧花机挡车工',
      17287: '电光机挡车工',
      17288: '涂层整理机挡车工',
      17289: '印染成品验布工',
      17290: '复查折布工',
      17291: '码剪定等工',
      17292: '卷板机挡车工',
      17293: '印染成品打包装箱工',
      17294: '印染成品拼件工',
      17295: '印染样本工',
      17296: '印染试验工',
      17297: '印染化验工',
      17298: '印染工艺仿样(色)工',
      17299: '印染工艺检查工',
      17300: '印染成品抽查工',
      17301: '染化料配制工',
      17302: '印花辊筒车磨工',
      17303: '印花辊筒打样工',
      17304: '印花辊筒镀铬抛光工',
      17305: '辊筒雕刻质检工',
      17306: '花样放样制板工',
      17307: '辊筒雕刻工',
      17308: '辊筒涂蜡工',
      17309: '辊筒腐蚀工',
      17310: '印花花样描绘工',
      17311: '印花花样拍摄制片工',
      17312: '印花感光工',
      17313: '平网型低雕刻工',
      17314: '平网防漆制板工',
      17315: '平网感光制板上漆工',
      17316: '平网绷框工',
      17317: '钢芯硬轧雕刻工',
      17318: '起绒机挡车工',
      17319: '洗绒机挡车工',
      17320: '割绒轧碱机挡车工',
      17321: '割绒机挡车工',
      17322: '割绒刀片磨制工',
      17323: '刷毛机挡车工',
      17324: '磨绒机挡车工',
      17325: '绞纱丝光挡车工',
      17326: '绞纱上酸挡车工',
      17327: '染纱挡车工',
      17328: '绞纱上浆工',
      17329: '绞纱烘燥机挡车工',
      17330: '色纱检查工',
      17331: '色纱司库工',
      17332: '配经工',
      17333: '配纬工',
      17334: '倒筒挡车工',
      17335: '拆链工',
      17336: '染纱出笼工',
      17337: '皂炼工',
      17338: '染色拼纱工',
      17339: '对折机挡车工',
      17340: '台板印花机挡车工',
      17341: '印花加料工',
      17342: '印花汽蒸工',
      17343: '烫平机挡车工',
      17344: '踏花板工',
      17345: '翻布打印工',
      17346: '加捻成绞挡车工',
      17347: '订商标工',
      17348: '刺绣机挡车工',
      17349: '打裥机挡车工',
      17350: '超声波花边机挡车工',
      17351: '地毯补头工',
      17352: '碱缩轧染平洗联合机挡车工',
      17353: '巾被开裁工',
      17354: '巾被漂白工',
      17355: '浸纱清洗工',
      17356: '光挡车工',
      17357: '并打丝挡车工',
      17358: '拖浆机挡车工',
      17359: '制线挡车工',
      17360: '蜡光挡车工',
      17361: '长丝分级工',
      17362: '线带产品定型工',
      17363: '线带产品检验包装工',
      17364: '织带机换筒工',
      17365: '印刷商标机挡车工',
      17366: '排须机挡车工',
      17367: '爱丽纱机挡车工',
      17368: '织带产品改机工',
      17369: '抓剪挡车工',
      17370: '烫剪挡车工',
      17371: '拆烫挡车工',
      17372: '剪花机挡车工',
      17373: '拼晾纱工',
      17374: '刷白浆工',
      17375: '贴布工',
      17376: '捎布工',
      17377: '洗台板工',
      17378: '轧漂工',
      17379: '翻布工',
      17380: '铲边纱框工',
      17381: '服装裁剪工',
      17382: '服装缝纫工',
      17383: '电脑绣花工',
      17384: '服装整烫工',
      17385: '胶制服装上胶工',
      17386: '服装检验工',
      17387: '服装包装工',
      17388: '羽绒洗毛工',
      17389: '服装水洗工',
      17390: '充羽工',
      17391: '制鞋刮浆工',
      17392: '制鞋帮底工',
      17393: '鞋帽缝制工',
      17394: '制鞋挤注工',
      17395: '制鞋排鞋工',
      17396: '鞋帽检验工',
      17397: '制帽整烫工',
      17398: '针织帽倒纱工',
      17399: '针织帽挡车工',
      17400: '针织帽铺工',
      17401: '锭杆热轧工',
      17402: '锭子装配工',
      17403: '锭子检查工',
      17404: '罗拉刨丝工',
      17405: '金属冷打工',
      17406: '摇架装配工',
      17407: '摇架检查工',
      17408: '喷丝头冶炼工',
      17409: '喷丝头(板)制作工',
      17410: '喷丝头(板)检查工',
      17411: '喷丝头(板)清洗工',
      17412: '计量泵选配工',
      17413: '计量泵流量检测工',
      17414: '金属植针工',
      17415: '塑、木梭制作工',
      17416: '塑、木梭修检工',
      17417: '塑、木管制作工',
      17418: '木管涂料工',
      17419: '胶合工',
      17420: '木锭制造工',
      17421: '纹板制造工',
      17422: '木配(杂)件制造工',
      17423: '木针板制造工',
      17424: '卷综棒制造工',
      17425: '压缩木制造工',
      17426: '层压木配件制造工',
      17427: '成坯工',
      17428: '钢芯、模胎制造工',
      17429: '镍网、模胎处理工',
      17430: '涂胶卷管工',
      17431: '纸管制作工',
      17432: '钢纸制作工',
      17433: '配料准备工',
      17434: '塑料纱管制作工',
      17435: '织针制作工',
      17436: '织针专职检验工',
      17437: '织针刀模具工',
      17438: '打结刀工',
      17439: '纺织刀剪工',
      17440: '纺器检验包装工',
      17441: '钢丝综三联机挡车工',
      17442: '钢筘制片工',
      17443: '钢筘编筘整型工',
      17444: '停经片压延制条工',
      17445: '停经片冲剪工',
      17446: '拉丝工',
      17447: '盖板(针布)制作工',
      17448: '切边包装工',
      17449: '针布修理工',
      17450: '钨铜制件工',
      17451: '齿条制作工',
      17452: '齿条检验工',
      17453: '圈钩成型工',
      17454: '裁料工',
      17455: '磨尖冲剪切工',
      17456: '针圈研抛工',
      17457: '针圈检验工',
      17458: '压延压出工',
      17459: '打(刮)浆工',
      17460: '底片成型工',
      17461: '底布配料工',
      17462: '皮仁(皮圈)成型工',
      17463: '整修工',
      17464: '橡胶磨床工',
      17465: '胶圈酸处理工',
      17466: '胶圈切割工',
      17467: '胶圈成品检验工',
      17468: '喷砂工',
      17469: '橡胶辊筒胶圈胶管成型工',
      17470: '橡胶车工',
      17471: '涂胶热压复合工',
      17472: '剑杆整修工',
      17473: '瓷件整理包装工',
      17474: '热压铸修工',
      17475: '原料处理工',
      17476: '成型整修工',
      17477: '瓷件检验工',
      17478: '炼漂染色绸坯准备工',
      17479: '串绸工',
      17480: '机头布剥色水洗工',
      17481: '起绉机挡车工',
      17482: '挂吊染操作工',
      17483: '手绘工',
      17484: '单滚筒整理机挡车工',
      17485: '布料折景机挡车工',
      17486: '星型架上下架操作工',
      17487: '丝绒烤花机挡车工',
      17488: '排绒机挡车工',
      17489: '粘胶、浆粕装球操作工',
      17490: '粘胶、浆粕蒸煮操作工',
      17491: '粘胶、浆粕打浆操作工',
      17492: '粘胶、浆粕漂白操作工',
      17493: '粘胶、浆粕烘干操作工',
      17494: '二硫化碳熔硫操作工',
      17495: '二硫化碳电炉操作工',
      17496: '二硫化碳煤气炉操作工',
      17497: '二硫化碳精馏操作工',
      17498: '二硫化碳回收操作工',
      17499: '原液准备浸渍操作工',
      17500: '原液准备老成黄化操作工',
      17501: '粘胶短丝后处理操作工',
      17502: '粘胶长丝后处理操作工',
      17503: '玻璃纸制造工',
      17504: '电石操作工',
      17505: '聚乙烯醇制造工',
      17506: '凝固浴操作工',
      17507: '短丝整理操作工',
      17508: '长丝整理操作工',
      17509: '牵切挡车工',
      17510: '皮板制造工',
      17511: '腈纶聚合操作工',
      17512: '腈纶回收操作工',
      17513: '腈纶溶液循环操作工',
      17514: '腈纶后处理操作工',
      17515: '湿法纺原液制造工',
      17516: '湿法纺纺丝操作工',
      17517: '湿法纺纺丝精密工',
      17518: '湿法纺酸碱站操作工',
      17519: '涤纶卸料、投料操作工',
      17520: '涤纶聚合操作工',
      17521: '涤纶切粘操作工',
      17522: '涤纶热媒操作工',
      17523: '涤纶乙二醇回收操作工',
      17524: '锦纶盐处理操作工',
      17525: '锦纶聚合操作工',
      17526: '锦纶单体回收操作工',
      17527: '熔融纺干燥操作工',
      17528: '熔融纺纺丝操作工',
      17529: '熔融纺卷绕操作工',
      17530: '熔融纺组件工',
      17531: '熔融纺短丝后处理工',
      17532: '熔融纺长丝牵伸挡车工',
      17533: '熔融纺长丝加弹挡车工',
      17534: '氨纶溶剂精制操作工',
      17535: '氨纶聚合操作工',
      17536: '氨纶包缠操作工',
      17537: '芳砜纶加料过滤操作工',
      17538: '芳砜纶纺丝牵伸操作工',
      17539: '毛条制造工',
      17540: '帘子布初复捻挡车工',
      17541: '织布挡车工',
      17542: '吸胶工',
      17543: '无纺布热轧挡车工',
      17544: '喷胶棉挡车工',
      17545: '联合机挡车工',
      17546: '小无纺挡车工',
      17547: '空滤针刺挡车工',
      17548: '浆点挡车工',
      17549: '化纤设备保全工',
      18001: '制浆备料工',
      18002: '化学制浆工',
      18003: '机械制浆工',
      18004: '造纸工',
      18005: '纸张整饰工',
      18006: '制浆残液回收工',
      18007: '制浆残液利用工',
      18008: '涂料制备工',
      18009: '涂料加工纸制造工',
      18010: '玻璃纸制造工',
      18011: '合成纸制造工',
      18012: '造纸网制纸工',
      18013: '造纸网织造工',
      18014: '造纸网定型工',
      18015: '自行车钢材改制工',
      18016: '自行车高频焊接工',
      18017: '自行车车圈、泥板成型工',
      18018: '自行车零件制造工',
      18019: '自行车部件组合工',
      18020: '自行车装配工',
      18021: '自行车表面精饰工',
      18022: '缝纫机坯件制造工',
      18023: '缝纫机零件制造工',
      18024: '缝纫机机头装配工',
      18025: '缝纫机机针制造工',
      18026: '钟(表)零件制造工',
      18027: '钟(表)部件、组件装配工',
      18028: '钟(表)成品装配工',
      18029: '钟(表)壳体制造工',
      18030: '钟(表)零件精密注塑工',
      18031: '钟(表)面盘(针)制造工',
      18032: '钟(表)带(链)制造工',
      18033: '钟(表)宝石轴承防震器制造工',
      18034: '钟(表)专用石英谐震器制造工',
      18035: '钟(表)发条制造工',
      18036: '钟、表专用润滑油脂合成工',
      18037: '玻璃配料工',
      18038: '玻璃(搪瓷)窑(炉)砌筑工',
      18039: '玻璃熔化工',
      18040: '玻璃手工成型工',
      18041: '玻璃自动机成型工',
      18042: '玻璃热处理工',
      18043: '玻璃灯工',
      18044: '安瓶(片剂瓶)成型工',
      18045: '玻璃仪器刻(印)度工',
      18046: '玻璃制品割、磨、烘工',
      18047: '玻璃刻磨工',
      18048: '气炼石英玻璃工',
      18049: '电熔石英玻璃工',
      18050: '陶瓷原料制备工',
      18051: '陶瓷机械成型工',
      18052: '陶瓷注浆成型工',
      18053: '陶瓷手工成型工',
      18054: '陶瓷烧成工',
      18055: '陶瓷彩瓷工',
      18056: '陶瓷手工彩绘工',
      18057: '陶瓷雕塑、雕刻、堆雕工',
      18058: '陶瓷石膏模型工',
      18059: '陶瓷制匣钵工',
      18060: '金水制备工',
      18061: '瓷用颜料工',
      18062: '陶瓷棉制作工',
      18063: '陶瓷装出窑工',
      18064: '瓷用花纸印刷工',
      18065: '瓷用花纸制版工',
      18066: '刻瓷工',
      18067: '搪瓷坯体制作工',
      18068: '搪瓷瓷釉熔制工',
      18069: '搪瓷釉浆制作工',
      18070: '搪瓷涂搪工',
      18071: '搪瓷烧成工',
      18072: '搪瓷饰花工',
      18073: '搪瓷花版制版工',
      18074: '搪瓷窑炉司炉工',
      18075: '瓶胆封口工',
      18076: '瓶胆拉底工',
      18077: '瓶胆镀银工',
      18078: '瓶胆真空工',
      18079: '保温瓶外壳制造工',
      18080: '瓶壳表面装饰工',
      18081: '保温瓶装配工',
      18082: '玻璃灯头压制工',
      18083: '电光源导丝制造工',
      18084: '灯用化学配料工',
      18085: '电光源灯丝制造工',
      18086: '电光源芯柱制造工',
      18087: '电光源灯芯装架工',
      18088: '电光源封口工',
      18089: '电光源充排气工',
      18090: '电光源喷涂工',
      18091: '电光源真空蒸镀工',
      18092: '电光源装焊、老练工',
      18093: '皂用油脂原料准备工',
      18094: '皂化工',
      18095: '皂基处理工',
      18096: '肥皂成型工',
      18097: '甘油水处理工',
      18098: '甘油精制工',
      18099: '石腊氧化工',
      18100: '脂肪酸钠皂分离工',
      18101: '脂肪酸精制工',
      18102: '合脂残渣处理工',
      18103: '合脂醇回收工',
      18104: '尾气处理工',
      18105: '磺化及中和工',
      18106: '供氯和盐酸吸收工',
      18107: '烷基磺酸钠制造工',
      18108: '洗衣粉成型工',
      18109: '热风制备工',
      18110: '香料原料处理工',
      18111: '香料合成工',
      18112: '香料分馏工',
      18113: '香料精制工',
      18114: '香精配制工',
      18115: '天然香料制备工',
      18116: '复合管制管工',
      18117: '铝管制管工',
      18118: '膏体制造工',
      18119: '火柴印磷工',
      18120: '旋切梗盒片工',
      18121: '火柴沾制药工',
      18122: '火柴配药工',
      18123: '火柴装盒工',
      18124: '刷磷包封工',
      18125: '火柴制盒工',
      18126: '练油树脂工',
      18127: '油墨颜料制作工',
      18128: '油墨配料工',
      18129: '油墨轧制工',
      18130: '挤水油墨工',
      18131: '三胶原料预处理工',
      18132: '三胶制胶工',
      18133: '三胶后处理工',
      18134: '锌筒成型工',
      18135: '炭棒成型工',
      18136: '炭棒焙烧工',
      18137: '电解质配制工',
      18138: '正极配料工',
      18139: '正极成型工',
      18140: '隔离层制备工',
      18141: '电池部件制备工',
      18142: '电池装配工',
      18143: '电池试制工',
      18144: '醋纤酯制备工',
      18145: '棉胶液制备工',
      18146: '片基流延工',
      18147: '感光材料涂布工',
      18148: '照相乳剂合成工',
      18149: '照像乳剂熔化工',
      18150: '感光专用药液配制工',
      18151: '油乳制备工',
      18152: '感光材料包装工',
      18153: '135暗盒制造工',
      18154: '感光材料整理工',
      18155: '感光材料工艺检查工',
      18156: '废片、白银回收工',
      18157: '照相性能测定工',
      18158: '包装材料检验工',
      18159: '感光材料检验工',
      18160: '片(纸)基检验工',
      18161: '有机合成试验工',
      18162: '乳剂试验工',
      18163: '片(纸)基试验工',
      18164: '制卤工',
      18165: '采盐工',
      18166: '制卤维修工',
      18167: '扬水工',
      18168: '驳盐工',
      18169: '筑盐工',
      18170: '苦卤综合利用工',
      18171: '原盐精制工',
      18172: '真空制盐蒸发工',
      18173: '真空制盐干燥工',
      18174: '真空制盐脱水工',
      18175: '平锅制盐工',
      18176: '卷扬机卤工',
      18177: '井口工',
      18178: '输卤轮水工',
      18179: '水举采卤工',
      18180: '顿钻工',
      18181: '辊工',
      18182: '制筒工',
      18183: '逗丝工',
      18184: '枧工',
      18185: '潜卤泵采卤工',
      18186: '输蔗破碎机工',
      18187: '压榨机工',
      18188: '糖汁中和工',
      18189: '碳酸饱充工',
      18190: '硫漂工',
      18191: '过滤机工',
      18192: '糖汁蒸发工',
      18193: '煮糖助晶工',
      18194: '分密机工',
      18195: '糖机工',
      18196: '生牛(羊)乳预处理工',
      18197: '牛(羊)乳杀菌工',
      18198: '乳品浓缩工',
      18199: '乳品干燥工',
      18200: '练乳结晶工',
      18201: '乳品发酵工',
      18202: '冰激淋成型工',
      18203: '奶油搅拌压炼工',
      18204: '干酪素点制工',
      18205: '乳清工',
      18206: '微生物培菌工',
      18207: '酵母工',
      18208: '制曲工',
      18209: '粉碎工',
      18210: '糖化工',
      18211: '发酵工',
      18212: '贮酒工',
      18213: '过滤工',
      18214: '蒸馏串香工',
      18215: '提取工',
      18216: '浸泡工',
      18217: '配酒工',
      18218: '酿酒工',
      18219: '麦芽制造工',
      18220: '啤酒麦汁制造工',
      18221: '二氧化碳制备工',
      18222: '固体酒精制造工',
      18223: '洗瓶机工',
      18224: '罐装机工',
      18225: '贴标机工',
      18226: '消毒灭菌工',
      18227: '装酒工',
      18228: '糖坯制造工',
      18229: '糖果成型工',
      18230: '胶基糖制造工',
      18231: '冰糖制造工',
      18232: '饼干配料调粉工',
      18233: '饼干成型工',
      18234: '饼干焙烤工',
      18235: '巧克力原料处理工',
      18236: '巧克力制造成型工',
      18237: '镀锡薄板裁剪工',
      18238: '空罐制盖工',
      18239: '罐身成型工',
      18240: '罐头封罐工',
      18241: '罐头原料处理工',
      18242: '罐头调味工',
      18243: '罐头装罐工',
      18244: '罐头杀菌工',
      18245: '制革原皮工',
      18246: '制革准备工',
      18247: '制革鞣制工',
      18248: '制革整理工',
      18249: '皮鞋划裁工',
      18250: '皮鞋制帮工',
      18251: '皮鞋制底工',
      18252: '皮鞋设计工',
      18253: '鞋楦设计工',
      18254: '制楦工',
      18255: '制球片工',
      18256: '制球成型工',
      18257: '制球整理工',
      18258: '皮革服装(手套)制作工',
      18259: '皮箱(包)制作工',
      18260: '毛皮染整工',
      18261: '毛皮配制工',
      18262: '毛皮划裁工',
      18263: '毛皮缝制工',
      18264: '毛皮吊制工',
      18265: '木家具配料工',
      18266: '家具机械木工',
      18267: '木家具胶压工',
      18268: '家具手木工',
      18269: '旋木工',
      18270: '木制品涂饰工',
      18271: '木容器工',
      18272: '沙发工',
      18273: '穿、织、棚工',
      18274: '金属家具成型工',
      18275: '铱粒制造工',
      18276: '金合金冶炼工',
      18277: '自来水笔尖制造工',
      18278: '球珠制造工',
      18279: '球座体工具制作工',
      18280: '圆珠笔芯制作工',
      18281: '纤维笔头、贮水芯制造工',
      18282: '笔类金属零件制造工',
      18283: '笔类非金属零件制造工',
      18284: '笔类套杆彩漆喷涂工',
      18285: '套杆镶刻工',
      18286: '笔类零件阳极氧化工',
      18287: '笔类零件装饰镀膜工',
      18288: '笔类装配工',
      18289: '墨水制造工',
      18290: '书写油墨制造工',
      18291: '铅芯制造工',
      18292: '铅笔板制造工',
      18293: '铅笔板变型处理工',
      18294: '铅笔杆制造工',
      18295: '铅笔外观加工工',
      18296: '手风琴装配工',
      18297: '手风琴零件制作工',
      18298: '手风琴校音工',
      18299: '口琴制作工',
      18300: '拉弦乐器制作工',
      18301: '弹拨乐器制作工',
      18302: '吹奏乐器制作工',
      18303: '击奏乐器制作工',
      18304: '鼓乐乐器制作工',
      18305: '铜响乐器制作工',
      18306: '提琴制作工',
      18307: '琴弓制作工',
      18308: '琴弦制作工',
      18309: '管乐器制作工',
      18310: '管乐器音孔定音工',
      18311: '管乐器弯管制作工',
      18312: '管乐器活塞附管制作工',
      18313: '击剑制作工',
      18314: '球网制作工',
      18315: '赛艇制作工',
      18316: '航模内燃机制作工',
      18317: '乒乓球制作工',
      18318: '羽毛球拍制作工',
      18319: '乒乓球拍制作工',
      18320: '网球制作工',
      18321: '羽毛球制作工',
      18322: '油画笔制作工',
      18323: '本册制作工',
      18324: '绘图仪器制作工',
      18325: '美术颜料制作工',
      18326: '订书钉、大头针、回形针制作工',
      18327: '制刷工',
      18328: '玩具装配工',
      18329: '搪塑模具工',
      18330: '搪塑玩具工',
      18331: '长毛绒玩具制作工',
      18332: '童车装配工',
      18333: '工艺编结工',
      18334: '手锈工',
      18335: '机绣工',
      18336: '地毯图案工',
      18337: '毛纱洗染工',
      18338: '织毯工',
      18339: '平毯工',
      18340: '地毯剪花工',
      18341: '洗毯工',
      18342: '地毯整修工',
      18343: '胶背地毯划布工',
      18344: '扎毯工',
      18345: '地毯制胶工',
      18346: '地毯挂胶工',
      18347: '拴穗工',
      18348: '缝边工',
      18349: '砸花整经工',
      18350: '打毛轴工',
      18351: '合纱工',
      18352: '毛毯挡车工',
      18353: '穿修工',
      18354: '依车毯挡车工',
      18355: '景秦蓝制胎工',
      18356: '景秦蓝掐丝工',
      18357: '景秦蓝点蓝工',
      18358: '景泰蓝磨蓝工',
      18359: '釉料工',
      18360: '漆器镶嵌工',
      18361: '漆器制胎工',
      18362: '漆器髹漆工',
      18363: '彩绘雕填工',
      18364: '漆器浮雕工',
      18365: '雕漆工',
      18366: '雕漆修漆工',
      18367: '木雕工',
      18368: '镜木滚花工',
      18369: '工艺画工',
      18370: '珍珠饰品工',
      18371: '玉宝石鉴别工',
      18372: '玉石雕刻工',
      18373: '玉器抛光工',
      18374: '玉石染烧工',
      18375: '象牙雕刻工',
      18376: '象牙磨光工',
      18377: '彩熏工',
      18378: '手工剔刻工',
      18379: '首饰制作工',
      18380: '首饰摆件工',
      18381: '首饰机制工',
      18382: '首饰浇铸工',
      18383: '头套髯口工',
      18384: '头面工',
      18385: '盔帽工',
      18386: '戏鞋工',
      18387: '戏服制作工',
      18388: '机刻工',
      18389: '橡章铸印工',
      18390: '有机制品工',
      18391: '钻石琢磨工',
      18392: '红宝石琢磨工',
      18393: '塑料筛粉工',
      18394: '塑料研磨工',
      18395: '塑料配料工',
      18396: '塑料捏和工',
      18397: '塑化工',
      18398: '塑料制糊工',
      18399: '塑料挤出工',
      18400: '塑料注塑工',
      18401: '塑料压延工',
      18402: '塑料模压工',
      18403: '塑料层压工',
      18404: '可发性聚苯乙烯发泡工',
      18405: '聚氨脂发泡工',
      18406: '塑料烧结工',
      18407: '塑料浇铸工',
      18408: '涂塑工',
      18409: '塑料圆网涂布工',
      18410: '塑料真空成型工',
      18411: '塑料切割工',
      18412: '塑料热合工',
      18413: '塑料编织工',
      18414: '电冰箱零部件制作工',
      18415: '电冰箱泡沫绝热层灌注工',
      18416: '电冰箱装配工',
      18417: '电冰箱制冷工',
      18418: '家用电器喷涂工',
      18419: '家用电器维修工',
      18420: '冰箱压缩机零件制作工',
      18421: '冰箱压缩机组装工',
      18422: '洗衣机零件制作工',
      18423: '洗衣机装配工',
      18424: '电风扇零件制作工',
      18425: '电风扇装饰面板工',
      18426: '电风扇装配工',
      18427: '电风扇扇叶制造工',
      18428: '电风扇网罩制造工',
      18429: '空调器配管工',
      18430: '热交换器作业工',
      18431: '空调器检漏工',
      18432: '空调器装配工',
      18433: '电饭煲发热板制作工',
      18434: '电饭煲发热管制作工',
      18435: '电饭煲装配工',
      18436: '电熨斗装配工',
      18437: '吸尘器电机制作工',
      18438: '吸尘器装配工',
      18439: '灯具软梗制作工',
      18440: '灯具装配工',
      18441: '灯具电器制作工',
      18442: '电光源零件制作工',
      18443: '灯管热加工',
      18444: '灯具零部件制作工',
      18445: '工具设备维修保养工',
      18446: '锉、锯造齿工',
      18447: '工具校直工',
      18448: '锯类拔齿工',
      18449: '工具装配工',
      18450: '拉丝剥壳工',
      18451: '拉丝模具工',
      18452: '制钉工',
      18453: '制钉刀具工',
      18454: '金属丝网弹簧工',
      18455: '金属丝织网工',
      18456: '金属丝网线团工',
      18457: '金属线团热镀锌工',
      18458: '金属拉链排咪工',
      18459: '金属拉链配件制作工',
      18460: '树脂拉链制作工',
      18461: '打火机零件制作工',
      18462: '打火机装配工',
      18463: '西餐具刃磨工',
      18464: '手电筒装配工',
      18465: '伞制作工',
      18466: '铝制品电化工',
      18467: '铝制品装配工',
      18468: '理发推剪零件制作工',
      18469: '理发推剪装配工',
      18470: '刀剪制作工',
      18471: '锁零件制作工',
      18472: '锁具装配工',
      18473: '衡器装配工',
      18474: '衡器调试工',
      18475: '衡器校砣工',
      18476: '电子衡器仪表工',
      18477: '衡器传感器仪表工',
      18478: '石油器用具装配工',
      18479: '石油器具测试工',
      18480: '铸锅成品整理工',
      18481: '凹凸雕刻工',
      18482: '装潢彩印工',
      18483: '烫印增光工',
      18484: '彩色调墨工',
      18485: '瓦楞纸板制作工',
      18486: '瓦楞纸箱成型工',
      18487: '纸盒成型工',
      18488: '装潢锦盒制作工',
      18489: '卷捅卷纸管(罐)工',
      18490: '热熔(高频)复合工',
      18491: '真空喷铝工',
      18492: '烫箔配料涂布工',
      18493: '树脂合成浇片工',
      18494: '产品装箱工',
      18495: '产品包装工',
      18496: '特殊物品包装工',
      18497: '产品化验分析工',
      18498: '产品检验工',
      18499: '玻璃印描花工',
      18500: '玻璃钢化处理工',
      18501: '烤花工',
      18502: '光敏微晶玻璃工',
      18503: '石英玻璃原料工',
      18504: '紫外光学石英玻璃熔制工',
      18505: '不透明石英玻璃熔制工',
      18506: '石英玻璃切磨工',
      18507: '石英玻璃火焰抛光和热整形工',
      18508: '石英玻璃制砣工',
      18509: '光学眼镜片毛坯自动成型工',
      18510: '光学眼镜片毛坯退火工',
      19001: '车站(场)值班员',
      19002: '助理值班员',
      19003: '值班员',
      19004: '扳道长',
      19005: '扳道员',
      19006: '信号长',
      19007: '信号员',
      19008: '驼峰值班员',
      19009: '调车长',
      19010: '连结员',
      19011: '驼峰作业员',
      19012: '制动长',
      19013: '制动员',
      19014: '车站调度员',
      19015: '站调助理',
      19016: '调车区长',
      19017: '调车指导',
      19018: '车号长',
      19019: '车号员',
      19020: '指导车长',
      19021: '运转车长',
      19022: '引导员',
      19023: '铁路客运员',
      19024: '铁路检票员',
      19025: '铁路售票员',
      19026: '客运计划员',
      19027: '铁路行李员',
      19028: '行李计划员',
      19029: '客车给水员',
      19030: '列车员',
      19031: '供水员',
      19032: '餐车长',
      19033: '客车备品员',
      19034: '客车整备工',
      19035: '货运值班员',
      19036: '货运调度员',
      19037: '货运计划员',
      19038: '货运核算员',
      19039: '货运安全员',
      19040: '货运检查员',
      19041: '货运员',
      19042: '交接员',
      19043: '篷布工',
      19044: '篷布修理工',
      19045: '站务员',
      19046: '加冰工',
      19047: '蒸汽起重机司机',
      19048: '蒸汽起重机副司机',
      19049: '叉车司机',
      19050: '装卸车司机',
      19051: '机车司机',
      19052: '机车副司机',
      19053: '机车司炉',
      19054: '指导司机',
      19055: '机车检查保养员',
      19056: '救援起复工',
      19057: '救援机械副司机',
      19058: '救援机械司机',
      19059: '机车钳工',
      19060: '机车锅炉工',
      19061: '机车电气试验工',
      19062: '内燃机车钳工',
      19063: '电力机车钳工',
      19064: '洗炉司机',
      19065: '机车整备工',
      19066: '给油指导',
      19067: '烟管清检工',
      19068: '洗刷机工',
      19069: '叫班员',
      19070: '检车员',
      19071: '车辆钳工',
      19072: '车电钳工',
      19073: '绝热材料工',
      19074: '罐车洗刷工',
      19075: '制动钳工',
      19076: '轮轴装修工',
      19077: '挂瓦工',
      19078: '油线工',
      19079: '开山工',
      19080: '线路工',
      19081: '路基工',
      19082: '桥梁装吊工',
      19083: '铁路桥梁工',
      19084: '铁路隧道工',
      19085: '电力线路工',
      19086: '接触网工',
      19087: '道岔钳工',
      19088: '钢轨探伤工',
      19089: '钢轨焊接工',
      19090: '钢轨整修工',
      19091: '铺架机司机',
      19092: '大型线路机械司机',
      19093: '轨道车司机',
      19094: '轨道检查车钳工',
      19095: '铁路报务员',
      19096: '铁路话务员',
      19097: '通信工',
      19098: '信号工',
      19099: '信号钳工',
      19100: '电控组调工',
      19101: '机车电工',
      19102: '车辆电工',
      19103: '牵车台司机',
      19104: '浸注处理工',
      19105: '木材防腐整备工',
      20001: '汽车驾驶员',
      20002: '超重型汽车列车驾驶员',
      20003: '超重型汽车列车挂车工',
      20004: '公路运输起重工',
      20005: '汽车客运调度员',
      20006: '汽车客运行包员',
      20007: '汽车客运售票员',
      20008: '汽车客车危险品检查员',
      20009: '汽车客运服务员',
      20010: '汽车客运乘务员',
      20011: '汽车客运行包装卸工',
      20012: '汽车货运站场调度员',
      20013: '汽车货运站务员',
      20014: '公路货运装卸工',
      20015: '汽车油料工',
      20016: '汽车货运库工',
      20017: '汽车维修工',
      20018: '汽车发动机维修工',
      20019: '汽车底盘维修工',
      20020: '汽车维修检验工',
      20021: '汽车维修电工',
      20022: '汽车维修漆工',
      20023: '汽车维修轮胎工',
      20024: '汽车维修钣金工',
      20025: '汽车维修铁工',
      20026: '汽车维修缝工',
      20027: '汽车维修钳工',
      20028: '汽车维修材料工',
      20029: '汽车喷油泵调试工',
      20030: '汽车检测工',
      20031: '汽车检测设备维修工',
      20032: '公路养护工',
      20033: '桥梁养护工',
      20034: '隧道养护工',
      20035: '公路重油沥青操作工',
      20036: '乳化沥青工',
      20037: '公路监控设备操作工',
      20038: '公路标志(标线)工',
      20039: '公路渡口渡工',
      20040: '车辆通行费收费员',
      20041: '公路交通量调查工',
      20042: '船舶水手',
      20043: '船舶木匠',
      20044: '船舶理货员',
      20045: '船舶机工',
      20046: '船舶电工',
      20047: '船舶冷藏工',
      20048: '船舶驾驶员',
      20049: '驳船驾长',
      20050: '船舶轮机员',
      20051: '船舶加油',
      20052: '船舶客运员',
      20053: '船舶业务员',
      20054: '内燃装卸机械司机',
      20055: '电动装卸机械司机',
      20056: '内燃装卸机械修理工',
      20057: '电动装卸机械修理工',
      20058: '装卸机械电器修理工',
      20059: '衡器修理工',
      20060: '码头维修工',
      20061: '港口装卸工',
      20062: '港口理货员',
      20063: '港口系缆工',
      20064: '装卸工具修制工',
      20065: '流体装卸操作工',
      20066: '皮带输送机操作工',
      20067: '衡器操作工',
      20068: '运输带粘接工',
      20069: '水面防污工',
      20070: '港口除尘操作工',
      20071: '移排工',
      20072: '木材防火喷涂工',
      20073: '港口客运员',
      20074: '港口行李员',
      20075: '港口售票员',
      20076: '疏浚管线工',
      20077: '疏浚测量工',
      20078: '疏浚测量仪器修理工',
      20079: '航道钻探工',
      20080: '航道爆破工',
      20081: '船闸机械运行工',
      20082: '船闸电气运行工',
      20083: '绞滩工',
      20084: '航道信号工',
      20085: '扎笼(扎排)工',
      20086: '沉排(抛石)工',
      20087: '工程船舶水手',
      20088: '疏浚统计工',
      20089: '海上救捞潜水员',
      20090: '内河潜水员',
      20091: '海上救捞工',
      20092: '潜水衣工',
      20093: '灯塔工',
      20094: '沿海航标工',
      20095: '内河航标工',
      20096: '航标保养工',
      20097: '船标充电工',
      20098: '航标灯器修理工',
      20099: '无线电导航发射工',
      20100: '无线电导航定时工',
      20101: '无线电指向操作工',
      20102: '无线电导航机电工',
      20103: '航道测量工',
      20104: '航道绘图工',
      20105: '测深仪修理工',
      20106: '无线电定位仪修理工',
      20107: '水工工程潜水工',
      20108: '水上打桩工',
      20109: '水上起重工',
      20110: '水上抛填工',
      20111: '路基工',
      20112: '桥基钻孔工',
      20113: '路面工',
      20114: '水工质量检验工',
      20115: '平地机操作工',
      20116: '压路机操作工',
      20117: '铲运机操作工',
      20118: '稳定土厂拌和设备操作工',
      20119: '中小型机械操作工',
      20120: '沥青混凝土拌和设备操作工',
      20121: '稳定土拌和机操作工',
      20122: '沥青混凝土摊铺机操作工',
      20123: '水泥混凝土搅拌设备操作工',
      20124: '水泥混凝土摊铺机操作工',
      20125: '筑路机械修理工',
      20126: '驳船司机',
      20127: '港机装配钳工',
      20128: '港机装配电工',
      20129: '港机结构装配工',
      20130: '港机机械检查工',
      20131: '港机电气检查工',
      20132: '港机结构检查工',
      20133: '航标灯器装配工',
      20134: '太阳能电池组合工',
      20135: '航标灯泡挂丝工',
      20136: '航标灯泡排气工',
      20137: '航标灯工',
      21001: '邮电营业员',
      21002: '邮件分拣员',
      21003: '邮件接发员',
      21004: '邮件转运员',
      21005: '火车、轮船邮件押运员',
      21006: '汽车邮件押运员',
      21007: '城乡投递员',
      21008: '机要营业员',
      21009: '机要分拣员',
      21010: '机要投递员',
      21011: '国际邮电营业员',
      21012: '国际邮件分拣员',
      21013: '国际邮件接发员',
      21014: '特快专递邮件业务员',
      21015: '邮政储蓄业务员',
      21016: '汇兑检查员',
      21017: '汇兑稽核员',
      21018: '报刊发行员',
      21019: '报刊分发员',
      21020: '报刊零售员',
      21021: '集邮业务员',
      21022: '邮政业务档案员',
      21023: '电报投递员',
      21024: '电信册报员',
      21025: '电子信函业务员',
      21026: '报务员',
      21027: '话务员',
      21028: '长途话务员',
      21029: '查号话务员',
      21030: '传真值机处理员',
      21031: '国际报务员',
      21032: '国际话务员',
      21033: '无线寻呼业务员',
      21034: '邮政机务员',
      21035: '电报机务员',
      21036: '电报自动交换机务员',
      21037: '无线机务员',
      21038: '微波机务员',
      21039: '卫星地球站机务员',
      21040: '载波机务员',
      21041: '长途机务员',
      21042: '市话机务员',
      21043: '市话测量员',
      21044: '电力机务员',
      21045: '长途线务员',
      21046: '电缆线务员',
      21047: '市话线务员',
      21048: '机线员',
      21049: '天线线务员',
      22001: '舞台灯光照明工',
      22002: '化妆工',
      22003: '剧装工',
      22004: '雕塑翻制工',
      22005: '壁画制作工',
      22006: '油画外框配制工',
      22007: '字画装裱工',
      22008: '版画制作工',
      22009: '装饰美工',
      22010: '考古发掘工',
      22011: '文物修复工',
      22012: '文物拓印工',
      22013: '古建筑琉璃工',
      22014: '缩微摄影工',
      22015: '缩微胶片处理工',
      22016: '缩微品检验工',
      23001: '影视置景木工',
      23002: '影视置景雕刻工',
      23003: '影视置景泥塑工',
      23004: '影视置景涂绘工',
      23005: '影视置景纸塑工',
      23006: '影视置景围幔工',
      23007: '影视照明员',
      23008: '影视发电员',
      23009: '影视照明设备修理工',
      23010: '影视服装管理员',
      23011: '影视服装制作员',
      23012: '影视道具员',
      23013: '影视特技模型制作工',
      23014: '影视烟火特效员',
      23015: '影视烟火特效维修保管员',
      23016: '电影摄影机械员',
      23017: '电影录音机械员',
      23018: '电影洗片员',
      23019: '电影印片员',
      23020: '电影染印员',
      23021: '电影洗片配药员',
      23022: '电影洗印环保员',
      23023: '电影洗印设备检修员',
      23024: '影片洁片员',
      23025: '影片切片员',
      23026: '影片拷贝整理员',
      23027: '影片拷贝修改员',
      23028: '电影放映员',
      23029: '拷贝检片员',
      23030: '拷贝涂磁录音员',
      23031: '电影字幕制版员',
      23032: '拷贝字幕印字员',
      23033: '动画片调色员',
      23034: '动画片描线上色员',
      23035: '电影剪纸制作员',
      23036: '木偶片花草制作员',
      23037: '木偶片翻制员',
      23038: '木偶片服装制作工',
      23039: '木偶片雕刻制作工',
      23040: '电视美术造型工',
      23041: '唱片刻纹工',
      23042: '唱片制版工',
      23043: '模版检听工',
      23044: '唱片造粒工',
      23045: '唱片工',
      23046: '唱片检听工',
      23047: '唱片配版工',
      23048: '广播电视天线工',
      23049: '有线广播机务员',
      23050: '有线广播线务员',
      24001: '体育场地工',
      25001: '水泥生料制备工',
      25002: '水泥烘干工',
      25003: '水泥均化工',
      25004: '水泥供料工',
      25005: '水泥熟料煅烧工',
      25006: '水泥风机工',
      25007: '水泥回转窑托轮工',
      25008: '水泥煤粉制备工',
      25009: '水泥熟料冷却工',
      25010: '水泥中央控室操作工',
      25011: '水尼预热分解炉巡检工',
      25012: '水泥收尘器工',
      25013: '水泥制成工',
      25014: '水泡包装机工',
      25015: '水泥输送机工',
      25016: '水泥制品工',
      25017: '水泥制品养护工',
      25018: '玻璃原料加工工',
      25019: '玻璃配料工',
      25020: '玻璃熔化工',
      25021: '玻璃制板工',
      25022: '玻璃切裁工',
      25023: '玻璃装箱工',
      25024: '浮法玻璃成型工',
      25025: '压延玻璃成型工',
      25026: '玻璃压辊刻花工',
      25027: '玻璃平拉成型工',
      25028: '玻璃压延编网工',
      25029: '耐火制砖工',
      25030: '预处理工',
      25031: '钢化玻璃工',
      25032: '夹层玻璃工',
      25033: '中空玻璃工',
      25034: '钢化模具工',
      25035: '镀膜玻璃工',
      25036: '玻璃喷砂工',
      25037: '石英玻璃原料工',
      25038: '气炼石英玻璃制造工',
      25039: '石英玻璃灯工',
      25040: '电熔石英玻璃制造工',
      25041: '石英玻璃冷加工工',
      25042: '石英光导纤维制造工',
      25043: '玻璃制球工',
      25044: '铂坩埚制造工',
      25045: '铂回收工',
      25046: '玻纤保全保养工',
      25047: '玻纤拉丝工',
      25048: '玻纤退并工',
      25049: '玻纤准整工',
      25050: '玻纤织布(带)工',
      25051: '玻纤后处理工',
      25052: '玻纤配油(胶)工',
      25053: '玻璃钢手糊工',
      25054: '玻璃钢压型工',
      25055: '玻璃钢卷材工',
      25056: '玻璃钢缠绕工',
      25057: '玻璃钢喷注工',
      25058: '玻璃钢拉挤工',
      25059: '树脂混凝土工',
      25060: '热塑玻璃钢工',
      25061: '玻璃钢切裁修整工',
      25062: '玻璃钢模具工',
      25063: '铸石原料工',
      25064: '铸石熔制工',
      25065: '铸石成品输送工',
      25066: '陶瓷原料准备工',
      25067: '陶瓷模具制造工',
      25068: '建筑卫生陶瓷成型工',
      25069: '建筑卫生陶瓷施釉工',
      25070: '建筑卫生陶瓷烧成工',
      25071: '砖瓦原料工',
      25072: '砖瓦成型工',
      25073: '砖瓦装(出)窑工',
      25074: '砖瓦干燥工',
      25075: '砖瓦码窑工',
      25076: '砖瓦烧火工',
      25077: '加气混凝土配料浇注工',
      25078: '加气混凝土切割工',
      25079: '加气混凝土蒸压养护工',
      25080: '加气混凝土钢筋工',
      25081: '加气混凝土大板拼装工',
      25082: '建筑石膏制备工',
      25083: '石膏板制备工',
      25084: '油毡制毡工',
      25085: '油毡氧化塔工',
      25086: '建筑保温材料原料工',
      25087: '建筑保温材料熔制工',
      25088: '建筑保温材料成棉控制工',
      25089: '建筑保温材料制品生产工',
      25090: '珍珠岩焙烧工',
      25091: '珍珠岩制品工',
      25092: '石材加工工',
      25093: '石材制造工',
      25094: '石材装修工',
      25095: '雕刻工',
      25096: '矿棉吸音板原料工',
      25097: '矿棉吸音板制造工',
      25098: '石膏有纸装饰板制备工',
      25099: '石膏浮雕板制备工',
      25100: '云母加工工',
      25101: '云母绝缘制品工',
      25102: '云母制粉工',
      25103: '石棉精选配料工',
      25104: '石棉梳纺工',
      25105: '石棉纺织工',
      25106: '石棉盘根工',
      25107: '石棉刹车带工',
      25108: '石棉磨擦片工',
      25109: '石棉橡胶制品工',
      25110: '石棉抄取纸、板工',
      25111: '石棉垫片制品工',
      25112: '石棉水泥制浆工',
      25113: '石棉水泥制坯工',
      25114: '石棉水泥成型工',
      25115: '石棉水泥养护工',
      25116: '石墨坩埚工',
      25117: '炭素制品工',
      25118: '石墨烧成工',
      25119: '胶体石墨工',
      25120: '显像管石墨乳工',
      25121: '高岭土制浆工',
      25122: '高岭土压滤工',
      25123: '金刚石锯片制作工',
      25124: '金刚石磨钻工',
      25125: '人工合成晶体工',
      25126: '燃油输送工',
      25127: '窑炉瓦工',
      25128: '专用气体制造工',
      25129: '专用供热工',
      25130: '包装工',
      26001: '飞机维护机械员',
      26002: '飞机维护电气员',
      26003: '飞机维护电子员',
      26004: '飞机(苏式)维护无线电、雷达员',
      26005: '飞机(苏式)维护电气员',
      26006: '飞机(苏式)维护仪表员',
      26015: '飞机发动机修理工',
      26020: '飞机发动机附件修理工',
      26022: '飞机电气修理工',
      26027: '塔台集中控制机务员',
      26028: '归航机/指点标机务员',
      26029: '全向信标机务员',
      26030: '测距设备机务员',
      26031: '仪表着陆系统机务员',
      26032: '一次雷达机务员',
      26033: '二次雷达机务员',
      26034: '显示设备机务员',
      26035: '航管计算机硬件机务员',
      26036: '飞行计划处理设备机务员',
      26037: '航管计算机外围设备机务员',
      26038: '着陆雷达机务员',
      26039: '自动转报控制席报务员',
      26040: '油料保管员',
      26041: '油料化验员',
      26042: '油料司泵员',
      26043: '油料加油员',
      26044: '油料计量统计员',
      26045: '油料电气仪表员',
      26046: '油料特种设备修理员',
      26047: '航空材料员',
      26051: '机场场务机械维修工',
      26052: '机场场道维修工',
      26053: '机场助航灯光电工',
      26054: '航站楼自动化设备机修工',
      26055: '航站楼自动化设备电气维修工',
      26056: '民航特种车辆机械维修工',
      26057: '民航特种车辆电气维修工',
      26058: '民航特种车辆操作工',
      26059: '民航(国内)售票员',
      26060: '民航(国内)客运员',
      26061: '民航(国内)货运员',
      26062: '民航乘务员',
      26066: '气象观测员',
      26068: '气象卫星云图接收设备机务员',
      26069: '气象雷达设备机务员',
      26070: '气象无线电设备机务员',
      26071: '气象自动观测系统机务员',
      26072: '气象自动填图设备机务员',
      26073: '安全检查员',
      26074: '安全检查设备维修工',
      26075: '航空摄影照相设备员',
      26076: '航空摄影冲洗员',
      26077: '航空摄影测绘员',
      26078: '飞机结构修理工',
      26079: '飞机气动、救生设备修理工',
      26080: '飞机机械附件修理工',
      26081: '飞机电子修理工',
      26083: '无线电短波收、发信机务员',
      26084: '甚高频收、发信机务员',
      26086: '航管内话通信机务员',
      26087: '自动转报机务员',
      26090: '电话交换机机务员',
      26091: '有线机务员',
      26094: '不间断电源机务员',
      26095: '空调设备机务员',
      26096: '油机机务员',
      26097: '电讯计量仪表机务员',
      26098: '电讯材料员',
      26101: '计算机系统及网络设备机务员',
      27001: '海洋水文气象观测工',
      27002: '海洋化学分析工',
      27003: '海洋生物调查工',
      27004: '海洋地质调查工',
      27005: '海洋监测监视工',
      27006: '海洋资料浮标岸站工',
      27007: '海洋资料浮标工',
      27008: '船舶测报员',
      27009: '海洋仪器检测工',
      27010: '导航设备工',
      27011: '海洋水文气象填图员',
      27012: '海洋气象卫星接收员',
      27013: '功能膜制作工',
      27014: '功能膜性能测试工',
      27015: '功能膜性能装调工',
      27016: '特种电极制作工',
      27017: '烫网工',
      27018: '组件制作工',
      28001: '天文测量工',
      28002: '重力测量工',
      28003: '三角测量工',
      28004: '水准测量工',
      28005: '测量计算工',
      28006: '测量造标工',
      28007: '测量埋石工',
      28008: '测量司光工',
      28009: '量距测量工',
      28010: '航测外业测量工',
      28011: '航测内业照像工',
      28012: '航测内业加密工',
      28013: '航测内业测量工',
      28014: '地图编绘工',
      28015: '地图刻图工',
      28016: '地图清绘工',
      28017: '测绘仪器修理工',
      28018: '工程测量工',
      28019: '控制测量工',
      28020: '地形测量工',
      28021: '地籍测量工',
      29001: '铸字工',
      29002: '铸排工',
      29003: '拣字工',
      29004: '拼版工',
      29005: '活版辅助工',
      29006: '手动照相排版工',
      29007: '电脑照排工',
      29008: '电脑照排主机操作工',
      29009: '刻铅字工',
      29010: '凸版制型工',
      29011: '铅版制版工',
      29012: '感光树脂版制版工',
      29013: '球震转印工',
      29014: '平版照相工',
      29015: '电子分色工',
      29016: '平版修拼版工',
      29017: '电子图像处理工',
      29018: '磨版工',
      29019: '平版晒版工',
      29020: '平版打样工',
      29021: '晾纸工',
      29022: '凹版照相工',
      29023: '凹版修拼版工',
      29024: '凹版制版工',
      29025: '凹版滚筒磨镀工',
      29026: '凹版电子雕刻工',
      29027: '铜锌版照相工',
      29028: '铜锌版修版工',
      29029: '铜锌版晒版工',
      29030: '铜锌版腐蚀工',
      29031: '铜锌版完成工',
      29032: '铜锌版混合工',
      29033: '凸版印刷工',
      29034: '图版印刷工',
      29035: '平版印刷工',
      29036: '凹版印刷工',
      29037: '压光覆膜工',
      29038: '铸印刷胶辊工',
      29039: '折页机工',
      29040: '索线机工',
      29041: '平装胶订联动线工',
      29042: '精装联动机工',
      29043: '骑订联动机工',
      29044: '平订联动加工',
      29045: '三面切书机工',
      29046: '切纸机工',
      29047: '制书壳工',
      29048: '烫印工',
      29049: '精装上封工',
      29050: '平装混合工',
      29051: '精装混合工',
      29052: '校对工',
      29053: '印刷辅助工',
      29054: '装订检查工',
      29055: '印刷机械维修工',
      29056: '印刷电器维修工',
      29057: '木版水印雕刻版工',
      29058: '木版水印工',
      29059: '木版水印制色工',
      29060: '木版水印装帧工',
      29061: '珂罗版照相工',
      29062: '珂罗版修版工',
      29063: '珂罗版制版工',
      29064: '珂罗版印刷工',
      29065: '盲文翻译制版工',
      29066: '盲文校对工',
      29067: '盲文印刷工',
      29068: '盲文装订混合工',
      29069: '音像复制设备维修调试工',
      29070: '音像复制质检工',
      29071: '音像复制母带制作工',
      29072: '音像带复制工',
      29073: '音像带装带工',
      29074: '音像带包装工',
      29075: '音像发行员',
      29076: '图书仓储员',
      29077: '图书发货员',
      29078: '图书运输员',
      29079: '图书发行员',
      29080: '字模模坯工',
      29081: '机刻字模工',
      29082: '字模电铸工',
      29083: '字模母版工',
      29084: '字模加工工',
      29085: '钢线工',
      29086: '锌线工',
      29087: '字形版粘磨工',
      29088: 'PS版工',
      30001: '长度量具计量检定工',
      30002: '长度量仪计量检定工',
      30003: '长度精密测量工',
      30004: '温度计量检定工',
      30005: '压力真空计量检定工',
      30006: '天平、砝码计量检定工',
      30007: '衡器操作工',
      30008: '衡器计量检定工',
      30009: '硬度计量检定工',
      30010: '测力计量检定工',
      30011: '流量计量检定工',
      30012: '大容量计量检定工',
      30013: '小容量计量检定工',
      30014: '电磁计量检定工',
      30015: '电磁计量修理工',
      30016: '无线电计量检定工',
      30017: '无线电计量修理工',
      30018: '声学计量检定工',
      30019: '时间频率计量检定工',
      30020: '光学计量检定工',
      30021: '电离辐射计量检定工',
      30022: '化学计量检定工',
      30023: '化学检验工',
      30024: '食品检验工',
      30025: '材料成分检验工',
      30026: '材料力学性能检验工',
      30027: '产品环境适应性能检验工',
      30028: '产品可靠性能检验工',
      30029: '产品安全性能检验工',
      30030: '纺织纤维分类分级检验工',
      30031: '纺织纤维物理性能检验工',
      31001: '采金船操纵工',
      31002: '黄金氰化工',
      31003: '炼金工',
      32001: '真空回潮工',
      32002: '配叶工',
      32003: '打叶工',
      32004: '抽梗工',
      32005: '润储叶工',
      32006: '蒸压梗工',
      32007: '切尖工',
      32008: '切丝工',
      32009: '烘丝工',
      32010: '加香工',
      32011: '储丝工',
      32012: '喂丝工',
      32013: '卷烟工',
      32014: '滤嘴装接工',
      32015: '卷接工',
      32016: '小包烟包装工',
      32017: '透明纸包装工',
      32018: '条包包装工',
      32019: '听烟包装工',
      32020: '封箱工',
      32021: '温湿度调节工',
      32022: '烟叶发酵工',
      32023: '烟草薄片工',
      32024: '碎叶分离工',
      32025: '精选烟叶工',
      32026: '粘合剂配胶工',
      32027: '刻牌印工',
      32028: '焙烟工',
      32029: '废烟处理工',
      32030: '除尘工',
      32031: '配料工',
      32032: '预回潮工',
      32033: '回潮工',
      32034: '原烟分级工',
      32035: '铺叶摆把工',
      32036: '切尖解把工',
      32037: '热风润叶工',
      32038: '定量喂料工',
      32039: '复烤打叶工',
      32040: '圆桶筛工',
      32041: '叶片复烤工',
      32042: '烟梗复烤工',
      32043: '预压、打包工',
      32044: '挂杆工',
      32045: '推车上杆工',
      32046: '烟把复烤工',
      32047: '下杆、装箱工',
      32048: '压包工',
      32049: '捆、缝包工',
      32050: '唛头工',
      32051: '筛烟工',
      32052: '检斤工',
      32053: '晾包工',
      32054: '滤棒成型工',
      32055: '码棒工',
      32056: '滤棒固化工',
      32057: '烟叶调制指导',
      32058: '烟叶分级指导',
      32059: '烤房修建指导',
      32060: '烟机设备修理工',
      32061: '烟草检验工',
      32062: '烟叶质量检查工',
      32063: '卷烟化验工',
      32064: '制烟材料收发工',
      32065: '烟叶保管工',
      32066: '卷烟包装工',
      32067: '烟机设备修理工(制丝)',
      32068: '烟机设备修理工(卷烟)',
      32069: '烟机设备修理工(包装)',
      32070: '膨胀烟丝工',
      32072: '烟机设备修理工(膨胀烟丝)',
      32073: '制丝工',
      32074: '烟机设备修理工(滤棒成型)',
      32075: '烟机设备修理工(薄片)',
      32076: '烟机设备修理工(复烤打叶)',
      32077: '白肋烟处理工',
      32078: '烟叶分级工',
      32079: '烟叶调制工',
      33001: '合成药卤化工',
      33002: '合成药碳化(含氯磺化)工',
      33003: '合成药硝化(含亚硝化)工',
      33004: '合成药烃化工',
      33005: '合成药氰化工',
      33006: '合成药酰化工',
      33007: '合成药酯化工',
      33008: '合成药醚化工',
      33009: '合成药羧化工',
      33010: '合成药胺化工',
      33011: '合成药重氨化(含偶合反应)工',
      33012: '合成药置换反应工',
      33013: '合成药氧化(含氯氧化)工',
      33014: '合成药还原工',
      33015: '合成药加成反应工',
      33016: '合成药缩合工',
      33017: '合成药环合(含环氧化)工',
      33018: '合成药扩开环反应工',
      33019: '合成药消除反应工',
      33020: '合成药水解工',
      33021: '合成药重排(含转位)反应工',
      33022: '合成药催化氧化工',
      33023: '合成药催化氢化工',
      33024: '合成药酶催化反应工',
      33025: '合成药X氏反应工',
      33026: '合成药硫化(含巯化)工',
      33027: '合成药胂(含锑、铋)化工',
      33028: '合成药膦化(含季膦化)工',
      33029: '合成药降解工',
      33030: '合成药聚合工',
      33031: '合成药裂解(裂化)工',
      33032: '合成药缩酮化工',
      33033: '合成药拆分、消旋工',
      33034: '合成药肼化(肼解)工',
      33035: '合成药异构化工',
      33036: '合成药转化工',
      33037: '合成药叠氮反应工',
      33038: '合成药肟化工',
      33039: '合成药乙炔化工',
      33040: '合成药中和成盐(含成季胺盐)工',
      33041: '合成药备料、配料工',
      33042: '合成药精制、结晶工',
      33043: '合成药提取工',
      33044: '合成药固、液分离工',
      33045: '合成药蒸发工',
      33046: '合成药蒸馏工',
      33047: '合成药干燥、包装工',
      33048: '生化药品提取工',
      33049: '抗生素酶裂解工',
      33050: '菌种培育工',
      33051: '微生物发酵工',
      33052: '微生物发酵灭菌工',
      33053: '发酵液提取工',
      33054: '微生物发酵药品精制工',
      33055: '药物配料制粒工',
      33056: '片剂压片工',
      33057: '片剂包衣工',
      33058: '注射液调剂工',
      33059: '水针剂灌封工',
      33060: '输液剂灌封工',
      33061: '粉针剂分装工',
      33062: '硬胶囊剂灌装工',
      33063: '软胶囊剂调剂工',
      33064: '软胶囊剂成型工',
      33065: '气雾剂工',
      33066: '滴丸工',
      33067: '口服药液调剂工',
      33068: '口服液灌装工',
      33069: '软膏剂调剂工',
      33070: '软膏剂灌装工',
      33071: '栓剂调剂工',
      33072: '栓剂成型工',
      33073: '膜剂工',
      33074: '滴液剂工',
      33075: '酊水剂工',
      33076: '锭剂工',
      33077: '注射用水、纯水制备工',
      33078: '制剂及医用制品灭菌工',
      33079: '灯检工',
      33080: '理洗瓶工',
      33081: '药用塑料制瓶工',
      33082: '冷冻干燥工',
      33083: '制剂原料精制工',
      33084: '制剂质量检查工',
      33085: '制剂包装工',
      33086: '制剂试验工',
      33087: '原料药试验工',
      33088: '药物分析工',
      33089: '微生物检定工',
      33090: '药理实验工',
      33091: '药理实验动物饲养工',
      33092: '药品保管养护工',
      33093: '净化空气调节工',
      33094: '湿淀粉工',
      33095: '干淀粉工',
      33096: '玉米浆蒸发工',
      33097: '淀粉包装工',
      33098: '胚芽干燥工',
      33099: '精制玉米油制造工',
      33100: '葡萄糖糖化工',
      33101: '葡萄糖净化工',
      33102: '葡萄糖蒸发工',
      33103: '葡萄糖结晶工',
      33104: '葡萄糖分离工',
      33105: '橡胶胶膏工',
      33106: '导声膏(超声波偶合剂)工',
      33107: '医用脱脂棉工',
      33108: '医用纱布(脱脂棉)炼漂工',
      33109: '医用纱布成型工',
      33110: '合成药车间维修工',
      33111: '制剂药品生产车间维修工',
      33112: 'CT组装调试工',
      33113: 'CT程序工',
      33114: 'CT精密部件调试工',
      33115: 'CT检验工',
      33116: 'CT安装修理工',
      33117: 'X射线机电气组装调试工',
      33118: 'X射线机机械组装调试工',
      33119: 'X射线机机械检验工',
      33120: 'X射线机电气检验工',
      33121: 'X射线机机械安装维修工',
      33122: 'X射线机电气安装维修工',
      33123: '体外循环设备机械组装调试工',
      33124: '体外循环设备电气组装调试工',
      33125: '体外循环设备机械安装维修工',
      33126: '体外循环设备电气安装维修工',
      33127: 'X射线管、影像增强管清洗工',
      33128: 'X射线管、影像增强管去气工',
      33129: 'X射线管、影像增强管装架工',
      33130: 'X射线管、影像增强管排气工',
      33131: 'X射线管老练测试工',
      33132: '影像增强管调试工',
      33133: '增强管制屏工',
      33134: '增强管涂膜工',
      33135: 'X射线管、影像增强管烧氢工',
      33136: '增强管研磨工',
      33137: 'X射线管、影像增强管玻璃工',
      33138: '加速器安装调试工',
      33139: '加速器电气线路装接调试工',
      33140: '加速器机械装配工',
      33141: '加速器测量检验工',
      33142: '电真空调试测量工',
      33143: '放疗用钴60机械安装维修工',
      33144: '血压计装配工',
      33145: '麻醉机蒸发器调试工',
      33146: '呼吸器装配调试工',
      33147: '医用电子仪器装接工',
      33148: '医用电子仪器调试工',
      33149: '医用电子仪器修理工',
      33150: '医用电子仪器成品检验工',
      33151: '医用电了仪器换能工',
      33152: '医用荧光器件制造工',
      33153: '齿科设备、器械调试工',
      33154: '齿科设备、器械修理工',
      33155: '齿科设备、器械检验工',
      33156: '齿科专用设备调试工',
      33157: '医用光学零件冷加工工',
      33158: '医用光学玻璃特加工工',
      33159: '医用光学零件检验工',
      33160: '医用光纤传相束制造工',
      33161: '医用光纤传相束检验工',
      33162: '医用内镜装配工',
      33163: '医用内镜检验工',
      33164: '手术器械锻压工',
      33165: '手术器械机切工',
      33166: '手术器械装配工',
      33167: '手术器械工装模具工',
      33168: '手术器械检验工',
      33169: '塑料注射器制作工',
      33170: '人工脏器制作工',
      33171: '造影导管制作工',
      33172: '输液(血)器制作工',
      33173: '化学指示卡制作工',
      33174: '玻璃注射器成型整形工',
      33175: '玻璃注射器外套整形模具工',
      33176: '玻璃注射器磨配工',
      33177: '医用玻璃仪器制版印色工',
      33178: '医用玻璃仪器检验工',
      33179: '医用激光管制作工',
      33180: '注射针管制作工',
      33181: '注射针座制作铆接工',
      33182: '牙齿成型制作工',
      33183: '齿用金属材料制作工',
      33184: '牙齿模具工',
      33185: '齿科磨削制品制造工',
      33186: '温度计制造工',
      33187: '体温计管料拉制工',
      33188: '体温计制胚工',
      33189: '体温计水银液胀工',
      33190: '体温计装校工',
      33191: '医用商品营业员',
      33192: '医用商品采购员',
      33193: '医用商品供应员',
      33194: '医用商品保管员',
      33195: '医用商品组配员',
      33196: '药品、化学试剂分装员',
      33197: '医用商品运输员',
      33198: '医疗器械、玻璃仪器检修员',
      34001: '中药材种植员',
      34002: '中药材养殖员',
      34003: '中药材生产管理员',
      34004: '中药材资源护管员',
      34005: '中药材收购员',
      34006: '中药调剂员',
      34007: '中药临方制剂员',
      34008: '中药购销员',
      34009: '中药验收员',
      34010: '中药保管员',
      34011: '中药养护员',
      34012: '中药材净选润切工',
      34013: '中药炮炙工',
      34014: '中药配料工',
      34015: '中药粉碎工',
      34016: '中药提取工',
      34017: '中药合成工',
      34018: '中药酒(酊)剂工',
      34019: '中药露剂工',
      34020: '中药油剂工',
      34021: '中药糖浆剂工',
      34022: '中药合剂工',
      34023: '中药口服液剂工',
      34024: '中药饮料工',
      34025: '中药针剂工',
      34026: '中药煎膏剂工',
      34027: '中药软膏剂工',
      34028: '中药塑丸工',
      34029: '中药泛丸工',
      34030: '中药散剂(研配)工',
      34031: '中约曲(锭)剂工',
      34032: '中药茶剂工',
      34033: '中药胶剂工',
      34034: '中药炼丹工',
      34035: '膏药剂工',
      34036: '中药灸熨剂工',
      34037: '中药片剂工',
      34038: '中药冲剂工',
      34039: '中药硬胶囊剂工',
      34040: '中药软胶囊剂工',
      34041: '中药滴丸剂工',
      34042: '中药橡皮膏剂工',
      34043: '中药巴布剂工',
      34044: '中药包装工',
      34045: '中药质检工',
      34046: '中成药试制工',
      35001: '大气环境监测工',
      35002: '水环境监测工',
      35003: '土壤环境监测工',
      35004: '环境生物监测工',
      35005: '环境噪声监测工',
      35006: '固体废物监测工',
      35007: '环境放射性监测工',
      35008: '大气环境探测工',
      36001: '无线电机械装校工',
      36002: '无线电装接工',
      36003: '无线电调试工',
      36004: '无线电成品检验工',
      36005: '例行试验工',
      36006: '外购电气零、部整件检验工',
      36007: '电子仪表检定修理工',
      36008: '电子变压器铁芯制造工',
      36009: '电子变压器线圈绕制工',
      36010: '电子变压器装校工',
      36011: '浸渍、灌注处理工',
      36012: '印制电路照相制版工',
      36013: '印制电路图形制作工',
      36014: '印制电路镀覆工',
      36015: '印制电路机加工',
      36016: '印制电路检验工',
      36017: '电源调试工',
      36018: '家用电子产品维修工',
      36019: '电讯装接工',
      36020: '精密机械装配工',
      36021: '雷达指挥仪总装配钳工',
      36022: '雷达调试工',
      36023: '指挥仪调试工',
      36024: '雷达、指挥仪例行试验工',
      36025: '玻璃钢成型工',
      36026: '计算机调试工',
      36027: '计算机整机检验工',
      36028: '计算机系统操作工',
      36029: '计算机软件工',
      36030: '针式打印机机械装调工',
      36031: '针式打印机电气装调工',
      36032: '针式打印机维修工',
      36033: '针式打印头装调工',
      36034: '针式打印机整机检验工',
      36035: '磁头研磨工',
      36036: '磁头装配工',
      36037: '磁头检验工',
      36038: '磁盘机机械装调工',
      36039: '磁盘机调试工',
      36040: '磁盘机维修工',
      36041: '磁盘机整机检验工',
      36042: '计算机文字录入处理员',
      36043: '载波通信设备调试工',
      36044: '载波通信设备检验工',
      36045: '光纤、数字通信设备调试工',
      36046: '光纤、数字通信设备检验工',
      36047: '无源网络装调工',
      36048: '有线通信产品可靠性试验工',
      36049: '有线电装接工',
      36050: '有线通信机架装配工',
      36051: '交换机调试工',
      36052: '交换机整机检验工',
      36053: '接线器装调工',
      36054: '交换机部器件装配工',
      36055: '交换机插件装调工',
      36056: '电话机装配工',
      36057: '电话机检验工',
      36058: '电传打字机机械装调工',
      36059: '电传打字机电气调试工',
      36060: '电传打字机检验工',
      36061: '传真机装配工',
      36062: '传真机调试工',
      36063: '传真机检验工',
      36064: '程控交换机调试工',
      36065: '程控交换机整机检验工',
      36066: '装架工',
      36067: '特种管装配工',
      36068: '封口工',
      36069: '特种钎焊工',
      36070: '特种熔融焊接工',
      36071: '放射性同位素分装工',
      36072: '真空检漏工',
      36073: '排气工',
      36074: '老试工',
      36075: '外部件装调工',
      36076: '碳化工',
      36077: '玻璃刻线工',
      36078: '荫罩装配工',
      36079: '涂层焙烧工',
      36080: '低熔点玻璃熔封工',
      36081: '微波元件校修工',
      36082: '调制器装修工',
      36083: '真空设备调修工',
      36084: '荧光屏涂履工',
      36085: '屏涂料配制工',
      36086: '曝光设备调修工',
      36087: '校正透镜制造工',
      36088: '真空镀膜工',
      36089: '激光全息工',
      36090: '激光器装配工',
      36091: '偏转线圈调整工',
      36092: '金属陶瓷封接工',
      36093: '真空光电器件阴极制造工',
      36094: '蒸馏净化工',
      36095: '吸气剂压制工',
      36096: '氧化物阴极制造工',
      36097: '阴极发射材料制配工',
      36098: '氧化铝微粉制造工',
      36099: '零部件涂覆工',
      36100: '热丝制造工',
      36101: '清洗腐蚀工',
      36102: '烧氢工',
      36103: '真空处理工',
      36104: '特种阴极制造工',
      36105: '发光材料原料制造工',
      36106: '发光材料制粉工',
      36107: '钾水玻璃制造工',
      36108: '真空电子器件金属粉末制造工',
      36109: '表面取向层制备工',
      36110: '液晶盒制备工',
      36111: '液晶屏制备工',
      36112: '液晶显示器装配工',
      36113: '吸气剂检测工',
      36114: '发光材料测试工',
      36115: '真空电子器件零件冲压焊接工',
      36116: '荫层框架制造工',
      36117: '金属丝料加工',
      36118: '栅极制造工',
      36119: '真空浇铸工',
      36120: '荫罩制造工',
      36121: '荫罩制板工',
      36122: '引出线制造工',
      36123: '电子玻璃配料工',
      36124: '电子玻璃熔焊工',
      36125: '电子玻璃制品压制工',
      36126: '电子玻璃制品吹制工',
      36127: '电子玻璃拉管工',
      36128: '电子玻璃制品退火工',
      36129: '电子玻璃制品研磨抛光工',
      36130: '电子玻璃制品分类工',
      36131: '管颈喇叭工',
      36132: '异形芯柱制造工',
      36133: '真空电子器件玻璃封接工',
      36134: '手制玻璃工',
      36135: '微晶玻璃制造工',
      36136: '阳极帽引出线处理工',
      36137: '玻粉、玻杆制造工',
      36138: '玻壳模具整修工',
      36139: '型式试验工',
      36140: '巡回检验工',
      36141: '成品检验工',
      36142: '原材料外购件检验工',
      36143: '玻璃制品监测工',
      36144: '玻璃物理性能测试工',
      36145: '钨铜粉末制造工',
      36146: '钨、钼压坯工',
      36147: '钨、钼坯条烧结工',
      36148: '钨、钼轧制开坯工',
      36149: '钨、钼材料旋锻工',
      36150: '钨、钼材料粗拉丝工',
      36151: '钨钼材料细拉丝工',
      36152: '硬质合金拉拔模制造工',
      36153: '金刚石拉丝模制造工',
      36154: '杜美丝制造工',
      36155: '精密合金管制造工',
      36156: '钨、钼板带材制造工',
      36157: '钨绞丝加热子制造工',
      36158: '电极丝制造工',
      36159: '镀锡铜包钢线制造工',
      36160: '电真空原材料检验工',
      36161: '电真空材料、成品检验工',
      36162: '单晶制备工',
      36163: '单晶片加工工',
      36164: '外延工',
      36165: '掩膜版制造工',
      36166: '氧化扩散工',
      36167: '离子注入工',
      36168: '化学气相淀积工',
      36169: '光刻工',
      36170: '台面成型工',
      36171: '芯片装架工',
      36172: '封装工',
      36173: '半导体器件、集成电路测试工',
      36174: '半导体器件、集成电路试验工',
      36175: '混合集成电路装调工',
      36176: '点接触二极管制造工',
      36177: '合金烧结工',
      36178: '半导体器件、集成电路电镀工',
      36179: '管壳制造工',
      36180: '半导体特种工具制造工',
      36181: '电子用水处理工',
      36182: '理化检测工',
      36183: '半导体温差电致冷材料制备工',
      36184: '半导体温差电致冷元件制造工',
      36185: '半导体温差电致冷组件制造工',
      36186: '传声器装调工',
      36187: '扬声器装调工',
      36188: '送受话器装调工',
      36189: '音圈绕制工',
      36190: '电声振动件制造工',
      36191: '扬声器号筒擀制工',
      36192: '籽晶片制造工',
      36193: '压电石英晶体配料装釜工',
      36194: '高压釜温控工',
      36195: '压电石英晶体检验工',
      36196: '压电石英晶体划线、切割测角工',
      36197: '压电石英晶体研磨工',
      36198: '压电石英片烧银、焊线工',
      36199: '石英晶体元件装配工',
      36200: '石英晶体元器件成品检验工',
      36201: '石英晶体振荡器制造工',
      36202: '石英晶体滤波器制造工',
      36203: '线绕电阻器、电位器制造工',
      36204: '有机实芯电阻器、电位器制造工',
      36205: '电阻器专用金属粉制造工',
      36206: '薄膜电阻器制造工',
      36207: '金属玻璃釉电阻器、电位器制造工',
      36208: '光敏电阻器制造工',
      36209: '压敏电阻器制造工',
      36210: '热敏电阻器制造工',
      36211: '合成碳膜电位器制造工',
      36212: '有机介质电容器纸、膜切割工',
      36213: '有机介质电容器纸、薄膜金属化工',
      36214: '有机介质电容器装配工',
      36215: '云母电容器制造工',
      36216: '陶瓷电容器制造工',
      36217: '铝箔腐蚀氧化工',
      36218: '钽电解电容器成型烧结工',
      36219: '钽电解电容器赋能、被膜工',
      36220: '电解电容器装配工',
      36221: '可变电容器装校工',
      36222: '电子专用设备装调工',
      36223: '电子专用设备安装调试工',
      36224: '真空测试工',
      36225: '胶粘工',
      36226: '继电器装配工',
      36227: '继电器调整工',
      36228: '半导体继电器装调工',
      36229: '继电器封装工',
      36230: '玻璃绝缘子工',
      36231: '舌簧管封装工',
      36232: '继电器线圈工',
      36233: '继电器试验工',
      36234: '继电器成品检验工',
      36235: '接插件零件制造工',
      36236: '接插件装校工',
      36237: '电子陶瓷料制备工',
      36238: '电子陶瓷模压成型工',
      36239: '电子陶瓷挤制成型工',
      36240: '电子陶瓷注、铸成型工',
      36241: '电子陶瓷薄膜成型工',
      36242: '电子陶瓷生坯机加工',
      36243: '电子陶瓷烧成工',
      36244: '电子陶瓷瓷件磨工',
      36245: '电了陶瓷试验检测工',
      36246: '电子陶瓷窑具制造工',
      36247: '铁氧体材料制备工',
      36248: '铁氧体元件成型工',
      36249: '铁氧体材料、元件烧成工',
      36250: '铁氧体元件研磨工',
      36251: '铁氧体材料、元件分测工',
      36252: '铁氧体材料、元器件检验工',
      36253: '微波铁氧体器件调测工',
      36254: '微特电机铁心叠压工',
      36255: '微特电机换向器装配工',
      36256: '微特电机绕线嵌线工',
      36257: '微特电机浸渍、灌注工',
      36258: '微特电机装配工',
      36259: '微特电机试验工',
      36260: '活性物质工',
      36261: '电极工',
      36262: '电池装配工',
      36263: '化成工',
      36264: '碱性蓄电池总装工',
      36265: '隔膜处理工',
      36266: '蓄电池检测工',
      36267: '蓄电池试验工',
      36268: '电池零件冲剪工',
      36269: '浆层纸制造工',
      36270: '导电层制造工',
      36271: '迭层电池装配工',
      36272: '碱性电池负极制造工',
      36273: '碱性电池装配工',
      36274: '碱性电池正极制造工',
      36275: '锂电池正极制造工',
      36276: '锂电池电液配制工',
      36277: '锂电池装配工',
      36278: '原电池总装工',
      36279: '原电池试验工',
      36280: '原电池检验工',
      36281: '热电池电极工',
      36282: '热电池激活、加热片工',
      36283: '热电池组装工',
      36284: '硅太阳电池焊接测试工',
      36285: '硅太阳电池方阵组合工',
      36286: '硅太阳电池检验工',
      36287: '制液工',
      36288: '电解工',
      36289: '后处理工',
      36290: '检验工',
      36291: '试验工',
      36292: '光电线缆上引连铸工',
      36293: '光电线缆制模工',
      36294: '光电线缆拉线工',
      36295: '光电线缆电镀工',
      36296: '光电线缆绞制工',
      36297: '光电线缆混塑工',
      36298: '光电线缆挤塑工',
      36299: '光电线缆交联工',
      36300: '光电线缆混橡工',
      36301: '光电线缆挤橡硫化工',
      36302: '光电线缆涂覆工',
      36303: '光电线缆编织工',
      36304: '光电线缆压绕工',
      36305: '光电线缆切带工',
      36306: '光电线缆附件工',
      36307: '光电线缆氩弧焊工',
      36308: '光纤光缆工',
      36309: '光电线缆检测工',
      36310: '树脂制造工',
      36311: '电子绝缘材料上胶工',
      36312: '电子绝缘材料压制工',
      36313: '电子绝缘材料试制工',
      36314: '电子绝缘材料性能试验工',
      36315: '电子封装材料制造工',
      36316: '含磨料尼龙刷辊刷体制造工',
      36317: '含磨料尼龙刷辊制造工',
      36318: '高频电感器件包封工',
      36319: '高频电感器件绕制工',
      36320: '高频电感器件测试工',
      36321: '高频电感器件检验工',
      37001: '船体放样号料工',
      37002: '船体冷加工',
      37003: '船体火工',
      37004: '配套工',
      37005: '船体装配工',
      37006: '批碳工',
      37007: '船体密性试验工',
      37008: '船舶钳工',
      37009: '船舶管系工',
      37010: '船舶电工',
      37011: '船舶电讯工',
      37012: '船舶电气钳工',
      37013: '船舶钣金工',
      37014: '船舶木塑工',
      37015: '船舶泥工',
      37016: '船舶帆缆工',
      37017: '船舶涂装工',
      37018: '螺旋浆钳工',
      37019: '制链工',
      37020: '锚链热处理工',
      37021: '锚链拉力试验工',
      37022: '锚链打包浸漆工',
      37023: '船用柴油机装配工',
      37024: '换能器胶合装配工',
      37025: '换能器密封工',
      37026: '船用仪器装配工',
      37027: '船用仪器电器装配工',
      37028: '船用仪器调试工',
      37029: '船用电子仪器装接工',
      37030: '船用电子仪器调试工',
      37031: '鱼雷装配钳工',
      37032: '鱼雷仪表装配工',
      37033: '鱼雷热动力试验工',
      37034: '发射装置操作检修工',
      37035: '水雷装配钳工',
      37036: '水雷仪表装配工',
      37037: '机械糖压制工',
      37038: '电爆管装配试验工',
      37039: '鱼雷仪表例行试验工',
      37040: '电子测量工',
      37041: '水声测量工',
      37042: '光学测量工',
      37043: '气垫船驾驶员(试飞员)',
      37044: '船模工',
      37045: '船坞工',
      37046: '船舶结构试验工',
      37047: '船舶流体试验工',
      37048: '检漏、充气、充液工',
      37049: '坞修钳工',
      37050: '螺旋浆模型工',
      38001: '熔纺聚合工',
      38002: '熔纺纺丝工',
      38003: '熔纺后处理工',
      38004: '原液制备工',
      38005: '湿纺纺丝工',
      38006: '湿纺后处理工',
      38007: '溶剂回收工',
      38008: '反应工',
      38009: '分离工',
      38010: '精制工',
      38011: '润滑油脱蜡脱油工',
      38012: '调合(油槽工)',
      38013: '原料准备工',
      38014: '干馏工',
      38015: '硫铵工',
      38016: '输送工',
      38017: '蒸馏工',
      38018: '溶液制备工',
      38019: '洗涤过滤工',
      38020: '成品处理工',
      38021: '水质处理工',
      38022: '辅助工',
      38023: '油品化验工',
      38024: '油品再生调合工',
      38025: '油品供销作业工',
      38026: '石油库储运工',
      38027: '桶装油品作业工',
      39001: '烧结机工',
      39002: '沸腾炉焙烧工',
      39003: '回转窑工',
      39004: '制团、制粒工',
      39005: '焦结炉工',
      39006: '蒸馏炉工',
      39007: '竖炉工',
      39008: '煅烧工',
      39009: '密闭鼓风炉备料工',
      39010: '鼓风炉工',
      39011: '密闭鼓风炉工',
      39012: '熔炼反射炉工',
      39013: '闪速炉熔炼工',
      39014: '矿热电炉熔炼工',
      39015: 'QSL法熔炼工',
      39016: '白银熔池溶炼工',
      39017: '旋涡炉工',
      39018: '锑白炉工',
      39019: '卡尔多炉工',
      39020: '烟化炉工',
      39021: '金属制粉工',
      39022: '真空冶炼工',
      39023: '隔焰炉工',
      39024: '氢还原工',
      39025: '富集工',
      39026: '转炉工',
      39027: '精炼反射炉工',
      39028: '精馏塔工',
      39029: '电解精炼工',
      39030: '电积工',
      39031: '火法精炼工',
      39032: '贵金属精炼工',
      39033: '熔析炉工',
      39034: '锡冶炼离心机工',
      39035: '白砷制取工',
      39036: '氯化汞触媒合成工',
      39037: '钛汞合金冶炼工',
      39038: '氯化汞气相合成工',
      39039: '锡酸钠制取工',
      39040: '铋冶炼工',
      39041: '阳极泥冶炼工',
      39042: '配液工',
      39043: '净液工',
      39044: '浸出工',
      39045: '蒸发浓缩结晶工',
      39046: '阴阳极制作工',
      39047: '萃取工',
      39048: '塔盘制炼工',
      39049: '烟气制酸工',
      39050: '汞加工工',
      39051: '酸原料制备工',
      39052: '碱原料制备工',
      39053: '氟化氢反应炉工',
      39054: '氢氟酸制取工',
      39055: '粗氟酸精制工',
      39056: '氟化盐合成工',
      39057: '氟化盐过滤机工',
      39058: '高浓度氢氟酸制取工',
      39059: '解析二氧化硫制取工',
      39060: '含氟烟气处理回收工',
      39061: '含氟污水处理回收工',
      39062: '含氟硫酸铝原液制取工',
      39063: '酸原液调整蒸发工',
      39064: '旋风炉工',
      39065: '生料浆制备工',
      39066: '熟料制备工',
      39067: '浸出液制备工',
      39068: '锂渣综合处理工',
      39069: '湿产品制取工',
      39070: '碳酸化提取工',
      39071: '二氧化碳制取工',
      39072: '料浆配料工',
      39073: '煤粉制备工',
      39074: '熟料烧结工',
      39075: '熟料溶出工',
      39076: '浆液调整输送工',
      39077: '液固分离工',
      39078: '沉降工',
      39079: '脱硅机工',
      39080: '高压溶出工',
      39081: '氢氧化铝分解工',
      39082: '蒸发工',
      39083: '热交换器工',
      39084: '氧化铝焙烧工',
      39085: '空气输送工',
      39086: '隔离泵工',
      39087: '氢氧化铝精制工',
      39088: '容器设备修整工',
      39089: '镓提炼工',
      39090: '铝电解工',
      39091: '阳极工',
      39092: '阳极组装工',
      39093: '多功能机组操作工',
      39094: '铝及铝合金熔铸工',
      39095: '母线焊接工',
      39096: '电解槽砌扎工',
      39097: '镁电解工',
      39098: '镁精炼工',
      39099: '镁氯化工',
      39100: '氯压机工',
      39101: '硅冶炼工',
      39102: '氯化炉工',
      39103: '四氯化钛精制工',
      39104: '准备拆装工',
      39105: '还原蒸馏工',
      39106: '成品打取工',
      39107: '熔体镁工',
      39108: '钽铌分离工',
      39109: '钽铌化合物制取工',
      39110: '铌碳还原火法冶炼工',
      39111: '压制成型工',
      39112: '钽铌精炼工',
      39113: '钽铌加工材制取工',
      39114: '钽钠还原火法冶炼工',
      39115: '钽碳还原火法冶炼工',
      39116: '铌铁火法冶炼工',
      39117: '铌酸锂晶体制取工',
      39118: '稀土精矿分解工',
      39119: '稀土化工操作工',
      39120: '稀土萃取工',
      39121: '稀土离子交换工',
      39122: '稀土电解工',
      39123: '稀土真空热还原工',
      39124: '稀土抛光粉工',
      39125: '稀土发光材料工',
      39126: '稀土挤压工',
      39127: '稀土熔炼工',
      39128: '稀土永磁材料工',
      39129: '稀土后处理工',
      39130: '预处理工',
      39131: '三氯氢硅、四氯化硅合成工',
      39132: '三氯氢硅、四氯化硅提纯工',
      39133: '三氯氢硅、四氯化硅还原工',
      39134: '硅烷法多晶硅制取工',
      39135: '硅外延片制取工',
      39136: '单晶硅制取工',
      39137: '单晶硅制取备料工',
      39138: '晶体切割工',
      39139: '硅片研磨工',
      39140: '硅片抛光工',
      39141: '腐蚀清洗工',
      39142: '滚磨倒角工',
      39143: '化合物半导体材料制取工',
      39144: '高纯金属制取工',
      39145: '粗钨酸钠溶液制备工',
      39146: '钨酸铵溶液制备工',
      39147: '氧化钨制备工',
      39148: '纯三氧化钨、仲钨酸铵、兰钨制取工',
      39149: '偏钨酸铵制备工',
      39150: '草酸(氧化)钴制备工',
      39151: '钨、钼、钴粉还原工',
      39152: '碳化钨制备工',
      39153: '热压工',
      39154: '铸造碳化钨熔炼破碎工',
      39155: '铸造碳化钨制管工',
      39156: '复式碳化钨制备工',
      39157: '混合料制备工',
      39158: '喷雾干燥塔工',
      39159: '参胶(蜡)工',
      39160: '混合料鉴定下料工',
      39161: '压制工',
      39162: '热等静压工',
      39163: '脱蜡工',
      39164: '压坯加工工',
      39165: '脱胶(剂)工',
      39166: '压模试压工',
      39167: '挤压制品生产工',
      39168: '金属陶瓷合金制备工',
      39169: '增塑性毛坯与异型制品加工工',
      39170: '钨钼制品烧结工',
      39171: '合金成品加工工',
      39172: '硬质合金涂层工',
      39173: '成形剂制备工',
      39174: '合金深度加工工',
      39175: '碳化钛制备工',
      39176: '铁粉还原工',
      39177: '合金探伤工',
      39178: '焙烧压煮工',
      39179: '仲钼酸铵制备工',
      39180: '钨钼预烧和半检工',
      39181: '钨钼制品加工工',
      39182: '合金制品包装工',
      39183: '有色金属配料工',
      39184: '有色金属熔炼工',
      39185: '有色金属铸造工',
      39186: '坯料机加工',
      39187: '酸碱洗工',
      39188: '热压延工',
      39189: '冷压延工',
      39190: '板、带、箔材剪切工',
      39191: '板、带材精整工',
      39192: '箔材精制工',
      39193: '挤压工',
      39194: '轧管工',
      39195: '拉伸工',
      39196: '拉线工',
      39197: '管、棒、型材精整工',
      39198: '卷管工',
      39199: '焊接制管工',
      39200: '水压机锻压工',
      39201: '锻造成品工',
      39202: '阳极氧化工',
      39203: '化学氧化工',
      39204: '扩口卷边工',
      39205: '熔喷工',
      39206: '铝镁粉球磨工',
      39207: '铣粉工',
      39208: '筛粉工',
      39209: '铸轧工',
      39210: '修模工',
      39211: '工艺润滑工',
      39212: '研磨工',
      39213: '粉末冶金压制工',
      39214: '粉末冶金烧结工',
      39215: '换辊轴承调整工',
      39216: '高压水泵工',
      39217: '铝型材装配工',
      39218: '压铸工',
      39219: '有色金属热处理工',
      39220: '成形工',
      39221: '真空垂熔工',
      39222: '真空熔炼工',
      39223: '稀有金属配料工',
      39224: '真空烘料工',
      39225: '真空热处理工',
      39226: '回收工',
      39227: '鉴别工',
      39228: '石墨成型工',
      39229: '加热工',
      39230: '刮管工',
      39231: '穿孔工',
      39232: '打磨工',
      39233: '拉网工',
      39234: '钛设备焊工',
      39235: '线材轧制工',
      39236: '冷拉丝工',
      39237: '旋锻工',
      39238: '称丝复绕工',
      39239: '热拉丝工',
      39240: '复合板工',
      39241: '多膛炉工',
      39242: '熔硫工',
      39243: 'TD炉熔炼工',
      39244: '化学分离工',
      39245: '精质提纯工',
      39246: '稀土色层工',
      39247: '液膜提取工',
      39248: '冷等静压工',
      39249: '制罐工',
      39250: '检查工',
      39251: '有色金属试验工',
      39252: '仪器分析工',
      39253: '化学分析工',
      39254: '余热锅炉工',
      39255: '收尘工',
      39256: '检斤工',
      39257: '采、制样工',
      39258: '有色金属干燥工',
      39259: '微机操作工',
      39260: '固体物料配料工',
      39261: '固体输送工',
      39262: '破碎机工',
      39263: '矿石磨细工',
      40001: '钻井架安装工',
      40002: '石油钻井工',
      40003: '钻井泥浆工',
      40004: '钻井地质工',
      40005: '综合录井工',
      40006: '钻井柴油机工',
      40007: '固井工',
      40008: '井下钻井工具装修工',
      40009: '地面钻井工具装修工',
      40010: '管子修理工',
      40011: '钻井仪表工',
      40012: '测井工',
      40013: '测井仪修工',
      40014: '射孔取心工',
      40015: '气测工',
      40016: '井下作业工',
      40017: '作业机司机',
      40018: '地层测试工',
      40019: '井下作业工具工',
      40020: '作业井架安装工',
      40021: '特车泵工',
      40022: '采油工',
      40023: '采油地质工',
      40024: '采油测试工',
      40025: '试井工',
      40026: '集输工',
      40027: '注水泵工',
      40028: '脱水工',
      40029: '油(气)田水处理工',
      40030: '轻烃装置操作工',
      40031: '输油工',
      40032: '综合计量工',
      40033: '管道保护工',
      40034: '采气工',
      40035: '采气测试工',
      40036: '输气工',
      40037: '输气管线维护工',
      40038: '石油钻机修理工',
      40039: '抽油机安装工',
      40040: '注输泵修理工',
      40041: '特车泵修理工',
      40042: '油层物性实验工',
      40043: '原油分析工',
      40044: '采油化验工',
      41001: '采煤工',
      41002: '支护工',
      41003: '爆破工',
      41004: '充填回收工',
      41005: '水采工',
      41006: '采煤机司机',
      41007: '液压支架工',
      41008: '输送机操作工',
      41009: '液压泵工',
      41010: '综采集中控制操纵工',
      41011: '井筒掘砌工',
      41012: '竖井钻井工',
      41013: '巷道掘砌工',
      41014: '装岩机司机',
      41015: '综掘机司机',
      41016: '钻车司机',
      41017: '天井钻机工',
      41018: '锚喷工',
      41019: '冻结安装运转工',
      41020: '主提升机操作工',
      41021: '钢缆皮带操作工',
      41022: '绞车操作工',
      41023: '电机车司机',
      41024: '拥罐工',
      41025: '煤矿搬运工',
      41026: '信号工',
      41027: '翻罐工',
      41028: '矿井轨道工',
      41029: '瓦斯检查工',
      41030: '矿井通风工',
      41031: '矿井测风工',
      41032: '矿井测尘工',
      41033: '矿井防尘工',
      41034: '矿压观测工',
      41035: '巷修工',
      41036: '注浆注水工',
      41037: '井下钻探工',
      41038: '矿山救护工',
      41039: '矿山火药库工',
      41040: '瓦斯抽放工',
      41041: '矿井泵工',
      41042: '露天坑下普工',
      41043: '主扇风机操作工',
      41044: '推土犁司机',
      41045: '挖掘机司机',
      41046: '矿山排水工',
      41047: '钻孔机操作工',
      41048: '矿用重型汽车司机',
      41049: '煤矿输电线路工',
      41050: '矿用重型汽车轮胎修换工',
      41051: '铁路移道工',
      41052: '凿岩工',
      41053: '装渣工',
      41054: '运渣工',
      41055: '移道机司机',
      41056: '轨配工',
      41057: '锻磨钎工',
      41058: '破碎工',
      41059: '凿岩机修理工',
      41060: '索道工',
      41061: '索道维修工',
      41062: '翻车指挥工',
      41063: '边坡工',
      41064: '采砂船工',
      41065: '重介质分选工',
      41066: '跳汰选煤工',
      41067: '浮选工',
      41068: '洗选供料工',
      41069: '筛选工',
      41070: '脱水工',
      41071: '洗煤干燥工',
      41072: '洗选煤技术检查工',
      41073: '洗选集中控制操作工',
      41074: '重介质制备回收工',
      41075: '压滤工',
      41076: '换布工',
      41077: '磨矿分级工',
      41078: '衬板工',
      41079: '选矿药剂工',
      41080: '尾矿工',
      41081: '手选工',
      41082: '重选工',
      41083: '磁选工',
      41084: '电选工',
      41085: '湿冶工',
      41086: '石灰乳化工',
      41087: '选矿试验工',
      41088: '矿山地质工',
      41089: '矿山测量工',
      41090: '矿灯管理工',
      41091: '井下普工',
      41092: '配气分析工',
      41093: '煤矿机安装工',
      41094: '采掘电钳工',
      41095: '综采维修钳工',
      41096: '矿井维修钳工',
      41097: '电机车修配工',
      41098: '矿车修理工',
      41099: '液压支架(柱)修理工',
      41100: '露天采剥机械机修工',
      41101: '煤矿电气安装工',
      41102: '露天采剥机械电修工',
      41103: '安全仪器监测工',
      41104: '综采维修电工',
      41105: '井筒维修工',
      41106: '矿井维修电工',
      41107: '矿山电子修理工',
      41108: '矿灯装配工',
      41109: '抓岩机司机',
      41110: '矿山工程检查验收工(员)',
      41111: '矿灯检验工',
      41112: '火工品检验工',
      41113: '电化学检验工',
      41114: '电化学工',
      41115: '煤质化验工',
      41116: '采制样工',
      41117: '火工化验工',
      41118: '安全检查工(员)',
      43001: '装甲车辆装试工',
      43002: '装甲车辆光学仪器武器装试工',
      43003: '装甲车辆电器装试工',
      43004: '装甲车辆装配检验工',
      43005: '装甲车辆电武器装配检验工',
      43006: '装甲车辆驾驶试验工',
      43007: '装甲试验工',
      43008: '装甲车辆发动机装试工',
      43009: '装甲车辆发动机拆洗鉴定修理工',
      43010: '火炮装试工',
      43011: '火炮随动系统装试工',
      43012: '火炮装试检验工',
      43013: '火炮随动系统装试检验工',
      43014: '炮身深孔机加工',
      43015: '炮膛线制作工',
      43016: '枪支装配工',
      43017: '枪管校直工',
      43018: '枪支试射工',
      43019: '枪管线膛制作工',
      43020: '枪管弹膛制作工',
      43021: '弹装配工',
      43022: '炮弹试射工',
      43023: '半可燃药筒制造工',
      43024: '炮弹零件机加工',
      43025: '枪弹装配工',
      43026: '枪弹挤压引伸工',
      43027: '枪弹组合机床工',
      43028: '机械引信装试工',
      43029: '电子引信装试工',
      43030: '引信试验工',
      43031: '引信组合机床工',
      43032: '压电陶瓷瓷料制备工',
      43033: '压电陶瓷瓷件制造工',
      43034: '压电陶瓷测试工',
      43035: '光学晶体制造工',
      43036: '光纤器件拉丝(排丝)工',
      43037: '光纤器件烧氢还原工',
      43038: '光电辅料制造工',
      43039: '化学镀膜工',
      43040: '精密刻度工',
      43041: '精密照相工',
      43042: '红外滤光器制造工',
      43043: '感光屏制造工',
      43044: '光电阴极制造工',
      43045: '荧光屏制造工',
      43046: '水准泡制造工',
      43047: '光电器件玻壳制造工',
      43048: '微光管装配工',
      43049: '微光管检验工',
      43050: '观瞄仪器装试工',
      43051: '测距机装试工',
      43052: '航瞄仪器装试工',
      43053: '观瞄仪器检验工',
      43054: '测距机检验工',
      43055: '航瞄仪器检验工',
      43056: '精制棉制造工',
      43057: '硝化棉制造工',
      43058: '单基发射药成型工',
      43059: '单基发射药后处理工',
      43060: '溶剂回收工',
      43061: '硝化甘油制造工',
      43062: '吸收药制造工',
      43063: '双基药成型工',
      43064: '双基药后处理工',
      43065: '双基粒状药制造工',
      43066: '硝基胍制造工',
      43067: '三基发射药成型工',
      43068: '三基发射药后处理工',
      43069: '复合药制造工',
      43070: '点火药盒制造工',
      43071: '火箭发动机静止试验工',
      43072: '泰胺炸药制造工',
      43073: '梯恩梯制造工',
      43074: '胶质硝甘炸药制造工',
      43075: '硝化棉软片制造工',
      43076: '乙基纤维制造工',
      43077: '合成樟脑制造工',
      43078: '二苯胺制造工',
      43079: '炸药干燥包装工',
      43080: '火炸药废酸处理工',
      43081: '标准溶液配制工',
      43082: '丁醇总溶剂制造工',
      43083: '乙醚制造工',
      43084: '硝基甲烷制造工',
      43085: '起爆药制造工',
      43086: '黑火药制造工',
      43087: '硝铵炸药制造工',
      43088: '含水炸药制造工',
      43089: '纸雷管壳制造工',
      43090: '雷管脚线制造工',
      43091: '雷管装配工',
      43092: '爆破器材试验工',
      43093: '火工品装配工',
      43094: '索状爆破器材制造工',
      43095: '系列加热电池制造工',
      43096: '浸渍活性炭制造工',
      43097: '过滤板制造工',
      43098: '毒剂制造工',
      43099: '防毒织物制造工',
      43100: '霍加拉特制造工',
      43101: '防毒器材装配工',
      43102: '防毒器材试验工',
      43103: '酚醛模塑料制造工',
      43104: '压敏胶粘带制造工',
      43105: '纤维塑料制造工',
      43106: 'TDI制造工',
      43107: 'TDI气体净化工',
      43108: '靶场测试工',
      43109: '火炮试射工',
      43110: '弹药制型装配工',
      43111: '火炸药理化分析工',
      43112: '兵器产品环境试验工',
      43113: '兵器产品包装工',
      43114: '摩托车装配工',
      43115: '摩托车调试修理工',
      43116: '摩托车检验工',
      43117: '摩托车包装工',
      43118: '摩托车发动机装配工',
      43119: '摩托车发动机调试修理工',
      43120: '摩托车发动机检验工',
      43121: '摩托车化油器装试工',
      43122: '摩托车化油器检验工',
      43123: '摩托车磁电机装试工',
      43124: '摩托车组合机床工',
      44001: '汽车装调工',
      44002: '汽车内饰装调工',
      44003: '汽车道路试验工',
      44004: '汽车性能试验工',
      44005: '发动机装调工',
      44006: '发动机试验工',
      44007: '变速箱装调工',
      44008: '变速箱试验工',
      44009: '汽车前后桥装调工',
      44010: '化油器装调工',
      44011: '油泵装调工',
      44012: '汽车空调机装调工',
      44013: '散热器制造工',
      44014: '钢圈制造工',
      44015: '轴瓦双金属带浇铸工',
      44016: '机加生产线工',
      44017: '汽车板金工',
      44018: '汽车焊装工',
      44019: '生产线调整工',
      44020: '汽车软轴制造工',
      44021: '汽车仪表装调工',
      44022: '汽车电器装调工',
      44023: '汽车电机装调工',
      44024: '汽车电器试验工',
      44025: '钨触头(白金)制造工',
      44026: '汽车板簧工',
      44027: '发动机检验工',
      44028: '变速箱检验工',
      44029: '汽车电器检查工',
      45001: '甲板工',
      45002: '液压工',
      45003: '压载工',
      45004: '定位工',
      45005: '潜水作业工',
      45006: '土质试验工',
      46001: '制图员',
      46002: '广播员',
      46003: '生物标本工',
      46004: '生物玻片标本工',
      46005: '生物模型工',
      46006: '中式烹调师',
      46007: '西式烹调师',
      46008: '中式面点师',
      46009: '西式面点师',
      46010: '餐厅服务员',
      46011: '美发师',
      46012: '美容师',
      46013: '按摩师',
      46014: '推销员',
      46015: '秘书',
      46016: '防卫',
      46017: '汽车配件销售员',
      46018: '估价师',
      46019: '音响调音员',
      46020: '公关员',
      46501: '飞机桨叶车工',
      46502: '数控车床工',
      46503: '飞机桨叶靠模铣工',
      46504: '数控铣床操作工',
      46505: '数控磨床操作工',
      46506: '数控镗床',
      46507: '组合机床操作工',
      46508: '造型工',
      46509: '特种铸造工',
      46510: '地铁（轻轨）钢轨焊接工',
      46511: '手工气割工',
      46512: '数控气焊切割机操作工',
      46513: '等离子切割工',
      46514: '钛设备焊工',
      46515: '钣金工',
      46516: '涂装工',
      46517: '喷漆工',
      46518: '装配钳工',
      46519: '飞机桨叶钳工',
      46520: '变压器开关装配工',
      46521: '装卸工具修理工',
      46522: '建材设备维修工',
      46523: '包装设备修理工',
      46524: '消防车辆修理工',
      46525: '汽车维修辅助工',
      46527: '变压器安装工',
      46528: '变电设备安装工',
      46529: '木质成品制造工',
      46530: '音响调音员',
      46531: '碾压工',
      46532: '喷筑工',
      46533: '管工',
      46534: '出租汽车驾驶员',
      46535: '无轨电车驾驶员',
      46536: '公共汽车驾驶员',
      46537: '汽车运材司机',
      46538: '矿用重型卡车司机',
      46539: '桥式起重机操作工',
      46540: '建材化学分析工',
      46541: '水产品质量检验工',
      46542: '贵金属首饰检验员',
      46543: '钻石检验员',
      46544: '宝玉石检验员',
      46545: '动物疫情信息员',
      46546: '兽医消毒工',
      46548: '旧货估价员',
      46549: '旧机动车鉴定估价师',
      46550: '营养配餐员',
      46551: '足部按摩师',
      46552: '职业指导员',
      46553: '物业管理员',
      46554: '保育员',
      46555: '家庭服务员',
      46556: '信息服务话务员',
      46557: '用户通信终端维修员',
      46558: '计算机维修工',
      46559: '养老护理员',
      999999: '其他',
      '0010': '煤矿混合工',
      '0011': '选煤工',
      '0013': '采矿工',
      '0014': '矿山其他工',
      '0016': '运输工',
      '0018': '成型工',
      '0019': '装出窑工',
      '0020': '烧成工',
      '0022': '水泥原料工',
      '0025': '石棉编织制品工',
      '0026': '石棉制品工',
      '0028': '清砂工',
      '0029': '冶炼、浇铸',
      '003': '支柱工',
      '0031': '修、筑炉工',
      '0032': '原料工',
      '0033': '工厂其他工种',
      '004': '运搬工',
      '005': '选矿工',
      '006': '纯掘进工',
      '007': '主掘进工',
      '008': '纯采煤工',
      '009': '主采煤工',
      '01001': '尸体搬运工',
      '01002': '尸体火化工',
      '01003': '尸体防腐工',
      '01004': '殡仪服务员',
      '01005': '墓地管理员',
      '01006': '尸体接运工',
      '01007': '假肢制作装配工',
      '01008': '矫形器制作装配工',
      '02001': '钞券原版雕刻工',
      '02002': '钞券原分版制版工',
      '02003': '钞券平凸版制版工',
      '02004': '电镀(铸)制版工',
      '02005': '雕刻凹印版制版工',
      '02006': '钞券号码机制作(修理)工',
      '02007': '钞券试样工',
      '02008': '隔色接纹印钞工',
      '02009': '平版隔色印钞工',
      '02010': '雕刻凹版印钞工',
      '02011': '雕刻凹印制辊工',
      '02012': '钞券凸版印刷工',
      '02013': '钞券裁切工',
      '02014': '钞券检验工',
      '02015': '钞券数封包装工',
      '02016': '单开分类印码工',
      '02017': '印数切封联线操作工',
      '02018': '印钞油墨工',
      '02019': '钞币库房管理工',
      '02020': '政府印章制作工',
      '02021': '印钞造币机修理工',
      '02022': '印钞造币设备维修电工',
      '02023': '水印原模雕刻工',
      '02024': '水印模具制作工',
      '02025': '钞纸水印制网工',
      '02026': '钞券抄纸工',
      '02027': '钞纸水印定位整选工',
      '02028': '钞纸水印光电切纸工',
      '02029': '蒸漂联合操作工',
      '02030': '洗扣漂工',
      '02031': '硬币模具模型工',
      '02032': '硬币模具雕缩工',
      '02033': '硬币模具手雕工',
      '02034': '硬币模具翻压工',
      '02035': '硬币模具研磨工',
      '02036': '硬币坯饼制作工',
      '02037': '硬币检验工',
      '02038': '硬币印花工',
      '02039': '硬币数封工',
      '02040': '硬币坯饼烧结工',
      '02041': '金库管库工',
      '02042': '标准金银锭熔铸工',
      '02043': '标准金银锭联合电解工',
      '02044': '标准金银锭化验工',
      '03001': '旅店服务员',
      '03002': '摄影师',
      '03003': '暗室师',
      '03004': '整修师',
      '03005': '着色师',
      '03006': '彩照扩印工',
      '03007': '洗衣师',
      '03008': '染色师',
      '03009': '织补师',
      '03010': '烫衣师',
      '03011': '修脚师',
      '03012': '浴池服务员',
      '03013': '商品营业员',
      '03014': '商品采购员',
      '03015': '商品供应员',
      '03016': '商品收购员',
      '03017': '司镑员',
      '03018': '商品检验员',
      '03019': '畜禽产品卫生检验员',
      '03020': '商品化验员',
      '03021': '商业美工',
      '03022': '粮油票证员',
      '03023': '蔬菜作价员',
      '03024': '商品购销信件收发员',
      '03025': '商品查询员',
      '03026': '商品送货员',
      '03027': '商品保管员',
      '03028': '商品保鲜员',
      '03029': '商品养护员',
      '03030': '库工',
      '03031': '制冷工',
      '03032': '冷藏工',
      '03033': '制冰工',
      '03034': '商品安全员',
      '03035': '食品包装工',
      '03036': '鲜活易腐、易碎商品包装工',
      '03037': '轻泡货物打包工',
      '03038': '包装回收修整加工工',
      '03039': '畜禽验收饲养员',
      '03040': '粮食烘干工',
      '03041': '商业储运机械操作工',
      '03042': '商品运输员',
      '03043': '商品押运员',
      '03044': '畜禽赶运员',
      '03045': '刻制印章工',
      '03046': '摩托车维修工',
      '03047': '家用视频设备维修工',
      '03048': '制冷设备维修工',
      '03049': '家用音频设备维修工',
      '03050': '家用电热器与电动器具维修工',
      '03051': '办公设备维修工',
      '03052': '照相器材维修工',
      '03053': '乐器维修工',
      '03054': '棉花检验仪器维修工',
      '03055': '钟表维修工',
      '03056': '自行车维修工',
      '03057': '眼镜修理工',
      '03058': '生猪屠宰加工工',
      '03059': '猪肉分割工',
      '03060': '动物油脂炼油工',
      '03061': '血粉、蛋白胨加工工',
      '03062': '动物蛋白饲料加工工',
      '03063': '牛羊屠宰工',
      '03064': '牛羊肉分割工',
      '03065': '肠衣工',
      '03066': '禽类屠宰加工工',
      '03067': '火腿加工工',
      '03068': '熟肉制品加工工',
      '03069': '腌腊肠类制品加工工',
      '03070': '蛋品及再制蛋品加工工',
      '03071': '酱油制作工',
      '03072': '食醋制作工',
      '03073': '酱腌菜加工',
      '03074': '食用调料加工工',
      '03075': '豆制品制作工',
      '03076': '腐乳品制作工',
      '03077': '淀粉及制品加工工',
      '03078': '中式糕点制作工',
      '03079': '西式糕点制作工',
      '03080': '糖果制作工',
      '03081': '饴糖制作工',
      '03082': '冷食品制作工',
      '03083': '小食品制作工',
      '03084': '碾米清理工',
      '03085': '碾米砻谷工',
      '03086': '碾米工',
      '03087': '制粉清理工',
      '03088': '制粉磨粉工',
      '03089': '制粉筛理工',
      '03090': '制粉分装工',
      '03091': '制油清理工',
      '03092': '制油剥壳工',
      '03093': '制油破碎软化轧坯工',
      '03094': '制油榨油工',
      '03095': '制油浸出工',
      '03096': '制油精炼工',
      '03097': '烘焙成型工',
      '03098': '烘焙烘烤工',
      '03099': '制面和面工',
      '03100': '制面面机工',
      '03101': '制面烘房工',
      '03102': '制面熟面工',
      '03103': '粮油食品制作工',
      '03104': '锯齿轧花工',
      '03105': '气力输送工',
      '03106': '皮辊轧花工',
      '03107': '锯齿剥绒工',
      '03108': '絮锦加工工',
      '03109': '棉花加工辅助工',
      '03110': '果汁酱加工工',
      '03111': '果脯密饯加工工',
      '03112': '野生植物原料加工工',
      '03113': '生漆加工工',
      '03114': '竹制品加工工',
      '03115': '蜜蜂产品加工工',
      '03116': '炒货加工工',
      '03117': '食用菌菌种工',
      '03118': '蚕茧烘烤工',
      '03119': '魔芋切片工',
      '03120': '菜类干制加工工',
      '03121': '蔬菜加工工',
      '03122': '茶叶精制工',
      '03123': '芳香油类加工工',
      '03124': '废旧物资挑选工',
      '03125': '废旧物资加工工',
      '03126': '服装设计定制工',
      '03127': '眼镜验光员',
      '03128': '眼镜加工工',
      '03129': '多种经营技术辅导员',
      '03130': '粮油管理员',
      '03131': '蔬菜生产联络员',
      '03132': '果品生产培植员',
      '03133': '食用菌生产技术指导员',
      '04001': '调酒师',
      '04002': '收银审核员',
      '04003': '客房服务员',
      '04004': '前厅服务员',
      '04005': '康乐服务员',
      '04006': '订票员',
      '04007': '宾客行李员',
      '04008': '餐具清洗工',
      '04009': '公共区域保洁员',
      '04010': '外币兑换员',
      '04011': '导游员',
      '05001': '茶叶拼配工',
      '05002': '京果类挑选检验工',
      '05003': '生漆加工检验工',
      '05004': '工艺蜡烛化蜡工',
      '05005': '工艺蜡烛制蜡工',
      '05006': '工艺蜡烛包装工',
      '05007': '猪鬃加工检验工',
      '05008': '马鬃尾及牛尾加工检验工',
      '05009': '细尾毛加工工',
      '05010': '笔料毛加工检验工',
      '06001': '物资进货员(采购员)',
      '06002': '物资供应员',
      '06003': '废金属加工挑选工',
      '06004': '废金属机械打包工',
      '06005': '废旧金属设备修旧利废工',
      '06006': '物资维护保养工',
      '06007': '仓库保管工',
      '06008': '成型煤生产工',
      '06009': '仪表拆解工',
      '06010': '机舱拆解工',
      '06011': '船体拆解工',
      '06012': '油船清洗工',
      '06013': '木材制箱工',
      '06014': '物资调运员',
      '07001': '农艺工',
      '07002': '果树工',
      '07003': '蔬菜工',
      '07004': '食用菌生产工',
      '07005': '啤酒花栽培工',
      '07006': '啤酒花加工工',
      '07007': '桑园工',
      '07008': '蚕业工',
      '07009': '参业工',
      '07010': '人参加工工',
      '07011': '茶园工',
      '07012': '茶叶初制工',
      '07013': '烟叶调制工',
      '07014': '农业实验工',
      '07015': '橡胶育苗工',
      '07016': '橡胶栽培工',
      '07017': '橡胶割胶工',
      '07018': '橡胶制胶工',
      '07019': '天然橡胶产品检验工',
      '07020': '剑麻栽培工',
      '07021': '剑麻制品工',
      '07022': '剑麻制品检验工',
      '07023': '热带作物育苗工',
      '07024': '热带作物栽培工',
      '07025': '热带作物植保工',
      '07026': '热带作物初制工',
      '07027': '热带作物初制品检验工',
      '07028': '生牛(羊)乳预处理工',
      '07029': '牛(羊)乳杀菌工',
      '07030': '乳品浓缩工',
      '07031': '乳品干燥工',
      '07032': '炼乳结晶工',
      '07033': '乳品发酵工',
      '07034': '乳品检验工',
      '07035': '冰淇淋成形工',
      '07036': '奶油搅拌压炼工',
      '07037': '干酪素点制工',
      '07038': '乳清工',
      '07039': '乳品设备保全工',
      '07040': '家畜饲养工',
      '07041': '家禽饲养工',
      '07042': '特种经济动物饲养工',
      '07043': '实验动物饲养工',
      '07044': '养蜂工',
      '07045': '畜禽繁殖工',
      '07046': '畜禽育种工',
      '07047': '冷冻精液制作工',
      '07048': '孵化工',
      '07049': '雏禽性别鉴别工',
      '07050': '畜禽阉割工',
      '07051': '挤奶工',
      '07052': '牧草种子繁育工',
      '07053': '牧草种子检验工',
      '07054': '牧草栽培工',
      '07055': '草地培育工',
      '07056': '草地植保工',
      '07057': '牧草产品加工工',
      '07058': '饲料厂中心控制室操作工',
      '07059': '饲料检验化验员',
      '07060': '饲料加工设备维修工',
      '07061': '饲料制粒工',
      '07062': '饲料粉碎工',
      '07063': '饲料原料清理上料工',
      '07064': '兽医化验员',
      '07065': '兽医防治员',
      '07066': '动物检疫检验员',
      '07067': '兽用生物制品制造工',
      '07068': '兽用化学药品制剂工',
      '07069': '兽用药物添加剂工',
      '07070': '兽用原料药制造工',
      '07071': '培养基制造工',
      '07072': '鹿茸加工工',
      '07073': '蜂具制造工',
      '07074': '海水鱼(虾)贝育苗工',
      '07075': '海水鱼(虾)养成工',
      '07076': '淡水鱼苗种繁育工',
      '07077': '淡水成鱼饲养工',
      '07078': '淡水育珠工',
      '07079': '淡水捕捞工',
      '07080': '海藻育苗工',
      '07081': '海水藻、贝类养殖工',
      '07082': '海藻制碘工',
      '07083': '海藻制醇工',
      '07084': '海藻制胶工',
      '07085': '海藻食品加工工',
      '07086': '湿法鱼粉工',
      '07087': '干法鱼粉蒸干工',
      '07088': '干法鱼粉脱脂工',
      '07089': '鱼肝油制(炼)油工',
      '07090': '鱼肝油滤油工',
      '07091': '鱼肝油配油工',
      '07092': '鱼肝油溶胶工',
      '07093': '鱼肝油酸钠工',
      '07094': '鱼肝油丸成型工',
      '07095': '水产品琼脂糖工',
      '07096': '鱼肝油乳白糖浆工',
      '07097': '水产品水解蛋白工',
      '07098': '水产品要素合剂工',
      '07099': '鱼蛋白胨工',
      '07100': '水产品干燥工',
      '07101': '水产品原料处理工',
      '07102': '水产品剖片工',
      '07103': '水产品调味摊片工',
      '07104': '水产品烘烤辊压工',
      '07105': '水产烘干制品包装工',
      '07106': '鱼糜工',
      '07107': '鱼糜充填结扎工',
      '07108': '渔用浮子成型工',
      '07109': '渔用沉子成型工',
      '07110': '渔网纺丝工',
      '07111': '渔网分丝工',
      '07112': '渔网捻线工',
      '07113': '渔网制绳工',
      '07114': '网片染整工',
      '07115': '网具装配工',
      '07116': '机织有结网片工',
      '07117': '机织无结网片工',
      '07118': '理鱼工',
      '07119': '水产养殖潜水员',
      '07120': '水生植物栽培工',
      '07121': '生物饵料培养工',
      '07122': '拖拉机(联合收获机)驾驶员',
      '07123': '农业机械操作工',
      '07124': '金属喷涂工',
      '07125': '农机散热器(水箱)修理工',
      '07126': '农机轮胎修理工',
      '07127': '农机燃油系修理工',
      '07128': '农机液压系修理工',
      '07129': '农机内燃机曲轴修磨工',
      '07130': '农机修理工',
      '07131': '农机电器设备修理工',
      '07132': '农业机械试验工',
      '07133': '沼气生产工',
      '07134': '节能改灶工',
      '07135': '太阳能利用工',
      '08001': '林木种苗工',
      '08002': '造林工',
      '08003': '抚育间伐工',
      '08004': '森林管护工',
      '08005': '营林试验工',
      '08006': '油锯工',
      '08007': '人力采伐工',
      '08008': '集材拖拉机司机',
      '08009': '集材工',
      '08010': '索道工',
      '08011': '伐区机修工',
      '08012': '汽车运材司机',
      '08013': '电锯造材工',
      '08014': '装卸归工',
      '08015': '木材检验工',
      '08016': '单漂流送工',
      '08017': '木材收储工',
      '08018': '编放排工',
      '08019': '出河机司机',
      '08020': '带锯工',
      '08021': '框锯工',
      '08022': '圆、截锯工',
      '08023': '选材检验工',
      '08024': '木材搬运工',
      '08025': '积材工',
      '08026': '修锯工',
      '08027': '木材干燥工',
      '08028': '原木划线锯断工',
      '08029': '木材蒸煮工',
      '08030': '原木旋切工',
      '08031': '定芯卷板工',
      '08032': '单板干燥工',
      '08033': '单板剪切工',
      '08034': '单板整理工',
      '08035': '单板机拼工',
      '08036': '单板分等工',
      '08037': '合板组坯工',
      '08038': '胶合板热(冷)压工',
      '08039': '胶合板修饰分等工',
      '08040': '纤维板制浆工',
      '08041': '乳化施胶工',
      '08042': '纤维板成型工',
      '08043': '纤维板热压工',
      '08044': '纤维板热、湿处理工',
      '08045': '刨花干燥工',
      '08046': '施胶工',
      '08047': '刨花板成型工',
      '08048': '刨花板热压工',
      '08049': '木材削片工',
      '08050': '人造板锯边工',
      '08051': '人造板砂光工',
      '08052': '人造板质量检验工',
      '08053': '磨刀、锯工',
      '08054': '人造板制胶工',
      '08055': '刨切单板原木制方工',
      '08056': '单板刨切工',
      '08057': '热固性树脂层压装饰板浸渍工',
      '08058': '热固性树脂层压装饰板铺装工',
      '08059': '热固性树脂层压装饰板热压工',
      '08060': '热固性树脂层压装饰板抛光工',
      '08061': '供料破碎工',
      '08062': '林化化验工',
      '08063': '松脂工',
      '08064': '熔解澄清工',
      '08065': '松香蒸馏工',
      '08066': '滴水法松香工',
      '08067': '松香浸提工',
      '08068': '松香改性反应工',
      '08069': '松节油合成反应工',
      '08070': '催化剂处理工',
      '08071': '松香包装工',
      '08072': '松焦油工',
      '08073': '栲胶浸提工',
      '08074': '栲胶蒸发工',
      '08075': '栲胶干燥工',
      '08076': '紫胶洗色干燥工',
      '08077': '紫胶溶胶过滤工',
      '08078': '紫胶蒸发工',
      '08079': '紫胶热滤工',
      '08080': '紫胶制片工',
      '08081': '紫胶漂白工',
      '08082': '紫胶色素工',
      '08083': '水解蒸煮工',
      '08084': '水解物料中和工',
      '08085': '水解蒸馏工',
      '08086': '水解酵母分离工',
      '08087': '水解酵母干燥法',
      '08088': '酵母营养盐工',
      '08089': '水解设备搪砌工',
      '08090': '木材干馏工',
      '08091': '木焦油工',
      '08092': '活性炭炭化工',
      '08093': '活性炭活化工',
      '08094': '活性炭酸洗工',
      '08095': '活性炭干燥工',
      '08096': '樟脑升华工',
      '08097': '软木烘焙工',
      '08098': '制胚剖片工',
      '08099': '栓皮制品检验工',
      '09001': '铸造工',
      '09002': '熔化工',
      '09003': '型砂工',
      '09004': '型砂烘干工',
      '09005': '锻造工',
      '09006': '热处理工',
      '09007': '清理工',
      '09008': '电炉配电工',
      '09009': '电镀工',
      '09010': '模型工',
      '09011': '粉末冶金烧结工（包括混料）',
      '09012': '粉末冶金压制工（包括整型复压）',
      '09013': '抛(磨)光工',
      '09014': '喷砂工',
      '09015': '钳工',
      '09016': '工具钳工',
      '09017': '镗工',
      '09018': '车工',
      '09019': '铣工',
      '09020': '刨、插工',
      '09021': '磨工',
      '09022': '制齿工',
      '09023': '拉床工',
      '09024': '电切削工',
      '09025': '下料工',
      '09026': '热工仪表修理工',
      '09027': '润滑保养工',
      '09028': '油漆工',
      '09029': '电工仪表修理工',
      '09030': '电工',
      '09031': '维修电工',
      '09032': '机修钳工',
      '09033': '电焊工',
      '09034': '气焊工',
      '09035': '冷作工',
      '09036': '天车工',
      '09037': '无损探伤工',
      '09038': '工业化学分析工',
      '09039': '物理金相实验工',
      '09040': '包装工',
      '09041': '加工中心操作工',
      '09042': '弹簧工',
      '09043': '绕线工',
      '09044': '绝缘处理浸渍工',
      '09045': '绝缘制品工',
      '09046': '热塑成型工',
      '09047': '电子仪器、仪表装调工',
      '09048': '电工、电路仪器仪表装调工',
      '09049': '分析仪器装调工',
      '09050': '光学仪器装调工',
      '09051': '电气计量检定工',
      '09052': '天平装校工',
      '09053': '照相机装校工',
      '09054': '复印机装调工',
      '09055': '弹性元件制造工',
      '09056': '宝石轴承制造工',
      '09057': '人造宝石制造工',
      '09058': '专用磨料、磨具制造工',
      '09059': '热电偶、热电阻装校工',
      '09060': '分析测量元件制造工',
      '09061': '传感器装校工',
      '09062': '应变片制作工',
      '09063': '光学刻、磨工',
      '09064': '光栅工',
      '09065': '光学胶合工',
      '09066': '轴尖工',
      '09067': '游、张丝工',
      '09068': '仪表压铸工',
      '09069': '感光鼓涂敷液调合工',
      '09070': '感光鼓涂敷工',
      '09071': '永磁造型工',
      '09072': '永磁烧结热处理工',
      '09073': '永磁磨工',
      '09074': '特种合金制(修)模工',
      '09075': '漆(丝)包工',
      '09076': '贵金属制品工',
      '09077': '光学玻璃热加工工',
      '09078': '光学玻璃物理化学性能测试工',
      '09079': '锅炉装配工',
      '09080': '锅炉试压工',
      '09081': '电机嵌试工',
      '09082': '电机(汽机)装配工',
      '09083': '电机(汽机)试验工',
      '09084': '辅机电气装配工',
      '09085': '换向器装配工',
      '09086': '电机车机械装配工',
      '09087': '电机车电气装配工',
      '09088': '低压电器及元件装配工',
      '09089': '高压电器及元件装配工',
      '09090': '高低压开关板(柜)装配配线工',
      '09091': '环氧树脂浇注工',
      '09092': '低压电器及元件试验工',
      '09093': '高压电器及元件试验工',
      '09094': '变压器、互感器装配工',
      '09095': '水泥电抗器制造工',
      '09096': '变压器、互感器试验工',
      '09097': '电焊条制造工',
      '09098': '电焊机装配工',
      '09099': '电焊机试验工',
      '09100': '电炉装配工',
      '09101': '电炉砌筑工',
      '09102': '蓄电池铸型工',
      '09103': '制铅粉工',
      '09104': '生极板制造工',
      '09105': '蓄电池化成工',
      '09106': '蓄电池装配工',
      '09107': '蓄电池试验工',
      '09108': '蓄电池微隔(孔)板工',
      '09109': '模塑料制造工',
      '09110': '绝缘污水处理工',
      '09111': '电碳备料工',
      '09112': '碳、石墨压制工',
      '09113': '碳、石墨焙烧工',
      '09114': '石墨化工',
      '09115': '石墨电刷制品工',
      '09116': '胶装工',
      '09117': '高压熔断器装配工',
      '09118': '干燥控制工',
      '09119': '电阻体制造工',
      '09120': '避雷器装配工',
      '09121': '火花塞瓷体制造工',
      '09122': '火花塞装配工',
      '09123': '玻璃绝缘子熔化、成型工',
      '09124': '绝缘子、避雷器试验工',
      '09125': '电缆硫化工',
      '09126': '电缆、电线制造工',
      '09127': '电缆干燥浸油工',
      '09128': '贵金属冶炼工',
      '09129': '电缆、电线拉线工',
      '09130': '电缆韧炼工',
      '09131': '刚玉冶炼工',
      '09132': '碳化硅冶炼工',
      '09133': '普通磨料及微粉制造工',
      '09134': '特殊材料磨具制造工',
      '09135': '超硬磨料制造工',
      '09136': '硅碳棒制造工',
      '09137': '尺、表、量仪类装配工',
      '09138': '刃具扭制工',
      '09139': '刃具铲工',
      '09140': '滚丝、搓丝工',
      '09141': '氮化钛涂层工',
      '09142': '酸洗钝化工',
      '09143': '盐浴炉钎焊工',
      '09144': '盐浴炉试车工',
      '09145': '紧固件冷镦工',
      '09146': '轴承装配工',
      '09147': '钢球研磨工',
      '09148': '光学磨工',
      '09149': '光学玻璃熔炼工',
      '09150': '仪器调修工',
      '09151': '轴承试验工',
      '09152': '拖拉机装试工',
      '09153': '农牧机械类装试工',
      '09154': '内燃机装试机',
      '09155': '喷灌、排灌机械装试工',
      '09156': '农牧车辆装试工',
      '09157': '胶轮力车制轮辋(辐条)工',
      '09158': '涡轮增压器装试工',
      '09159': '刹车装置装试工',
      '09160': '农副产品加工机械装试工',
      '09161': '棉花机械肋条工',
      '09162': '油泵油嘴装试工',
      '09163': '研磨工',
      '09164': '机动车仪器仪表装试工',
      '09165': '链条装试工',
      '09166': '液压元件装试工',
      '09167': '滤清器装试工',
      '09168': '齿轮箱装试工',
      '09169': '模型检查工',
      '09170': '铸件检查工',
      '09171': '锻件检查工',
      '09172': '机械检查工',
      '09173': '热处理检查工',
      '09174': '电镀、油漆检查工',
      '09175': '铆、焊检查工',
      '09176': '材料检查工',
      '09177': '弹簧检查工',
      '09178': '绝缘检查工',
      '09179': '超硬磨料制品检查工',
      '09180': '硅碳棒检查工',
      '09181': '刃具检查工',
      '09182': '轴承检查工',
      '09183': '电缆(电线)产品检查工',
      '09184': '宝石轴承检查工',
      '09185': '玻璃分类检查工',
      '09186': '电炉装配检查工',
      '09187': '蓄电池检查工',
      '09188': '橡、塑制品检查',
      '09189': '火花塞成品检查工',
      '09190': '碳制品检查工',
      '09191': '特殊材料检查工',
      '09192': '超硬磨料检查工',
      '09193': '电机、汽机装配检查工',
      '09194': '低压电器及元件装配检查工',
      '09195': '电容器检查工',
      '09196': '变压器、互感器检查工',
      '09197': '变压器零件检查工',
      '09198': '电焊机电气检查工',
      '09199': '高压电器装配检查工',
      '09200': '化学零件检查工',
      '09201': '永磁检查工',
      '09202': '电气检查工',
      '09203': '电机车机械装配检查工',
      '09204': '电机车电气装配检查工',
      '09205': '光学零件检查工',
      '09206': '光学仪器检查工',
      '09207': '电工电路仪表仪器检查工',
      '09208': '电子仪器仪表检查工',
      '09209': '量具检查工',
      '09210': '复印机检查工',
      '09211': '感光鼓检查工',
    };
    return codeObj[code] || '';
  }

  /**
   * 处理体检结论 3.9
   * @param {number} value - The value to be processed.
   * @return {string} - 目前未见异常  复查 疑似职业病 禁忌证 其他疾病或异常
   */
  getConclusion(value) {
    // value转换为数字
    value = +value;
    const codeObj = {
      12001: '目前未见异常',
      12002: '复查',
      12003: '疑似职业病',
      12004: '禁忌证',
      12005: '其他疾病或异常',
    };
    return codeObj[value] || '';
  }

  /** 取str后6位 用于组织机构代码取后6位
   * @param {String} str - 企业组织机构代码 91350200MA31X6JX7A
   * @return {String} - 返回后6位  X6JX7A
   */
  getSixStr(str) {
    return str.slice(-6);
  }

  /**
   * 过滤对象中的空字段
   * @param {Object} obj - 需要过滤的对象
   * @return {Object} - 过滤后的对象
   */
  filterEmptyFields(obj) {
    const result = {};
    for (const key in obj) {
      if (obj[key] !== '') {
        result[key] = obj[key];
      }
    }
    return result;
  }

  /** 转换体检类型  3.7
   * @param {String} code - 体检类型编码 1001
   * @return {String} - 返回体检类型 0上岗前 1在岗 2离岗 3复查 4应急
   */
  getOnguardState(code) {
    const codeObj = {
      1001: '0', // 上岗前
      1002: '1', // 在岗
      1003: '2', // 离岗
      1004: '3', // 复查
      1005: '4', // 应急
    };
    return codeObj[code] || '';
  }

  /** 获取企业地址
   * @param value  code
   * @return {Array} 企业地址
   */
  getAddress(value) {
    value = +value;
    const obj = {
      6100000000: '陕西省',
      6101000000: '陕西省_西安市',
      6101020000: '陕西省_西安市_新城区',
      6101020100: '陕西省_西安市_新城区_西一路街道',
      6101020200: '陕西省_西安市_新城区_长乐中路街道',
      6101020300: '陕西省_西安市_新城区_中山门街道',
      6101020400: '陕西省_西安市_新城区_韩森寨街道',
      6101020500: '陕西省_西安市_新城区_解放门街道',
      6101020600: '陕西省_西安市_新城区_自强路街道',
      6101020700: '陕西省_西安市_新城区_太华路街道',
      6101020800: '陕西省_西安市_新城区_长乐西路街道',
      6101020900: '陕西省_西安市_新城区_胡家庙街道',
      6101030000: '陕西省_西安市_碑林区',
      6101030100: '陕西省_西安市_碑林区_南院门街道',
      6101030200: '陕西省_西安市_碑林区_柏树林街道',
      6101030300: '陕西省_西安市_碑林区_长乐坊街道',
      6101030400: '陕西省_西安市_碑林区_东关南街街道',
      6101030500: '陕西省_西安市_碑林区_太乙路街道',
      6101030600: '陕西省_西安市_碑林区_文艺路街道',
      6101030700: '陕西省_西安市_碑林区_长安路街道',
      6101030800: '陕西省_西安市_碑林区_张家村街道',
      6101040000: '陕西省_西安市_莲湖区',
      6101040100: '陕西省_西安市_莲湖区_青年路街道',
      6101040200: '陕西省_西安市_莲湖区_北院门街道',
      6101040300: '陕西省_西安市_莲湖区_北关街道',
      6101040400: '陕西省_西安市_莲湖区_红庙坡街道',
      6101040500: '陕西省_西安市_莲湖区_环城西路街道',
      6101040600: '陕西省_西安市_莲湖区_西关街道',
      6101040700: '陕西省_西安市_莲湖区_土门街道',
      6101040800: '陕西省_西安市_莲湖区_桃园路街道',
      6101040900: '陕西省_西安市_莲湖区_枣园街道',
      6101110000: '陕西省_西安市_灞桥区',
      6101110100: '陕西省_西安市_灞桥区_纺织城街道',
      6101110200: '陕西省_西安市_灞桥区_十里铺街道',
      6101110300: '陕西省_西安市_灞桥区_红旗街道',
      6101110400: '陕西省_西安市_灞桥区_席王街道',
      6101110500: '陕西省_西安市_灞桥区_洪庆街道',
      6101110600: '陕西省_西安市_灞桥区_狄寨街道',
      6101110700: '陕西省_西安市_灞桥区_灞桥街道',
      6101120000: '陕西省_西安市_未央区',
      6101120100: '陕西省_西安市_未央区_张家堡街道',
      6101120200: '陕西省_西安市_未央区_辛家庙街道',
      6101120300: '陕西省_西安市_未央区_徐家湾街道',
      6101120400: '陕西省_西安市_未央区_大明宫街道',
      6101120500: '陕西省_西安市_未央区_谭家街道',
      6101120600: '陕西省_西安市_未央区_草滩街道',
      6101120700: '陕西省_西安市_未央区_六村堡街道',
      6101120800: '陕西省_西安市_未央区_未央宫街道',
      6101120900: '陕西省_西安市_未央区_汉城街道',
      6101121000: '陕西省_西安市_未央区_未央湖街道',
      6101130000: '陕西省_西安市_雁塔区',
      6101130100: '陕西省_西安市_雁塔区_小寨路街道',
      6101130200: '陕西省_西安市_雁塔区_大雁塔街道',
      6101130300: '陕西省_西安市_雁塔区_长延堡街道',
      6101130400: '陕西省_西安市_雁塔区_电子城街道',
      6101130500: '陕西省_西安市_雁塔区_等驾坡街道',
      6101130600: '陕西省_西安市_雁塔区_曲江街道',
      6101130700: '陕西省_西安市_雁塔区_杜城街道',
      6101130800: '陕西省_西安市_雁塔区_漳浒寨街道',
      6101140000: '陕西省_西安市_阎良区',
      6101140100: '陕西省_西安市_阎良区_凤凰路街道',
      6101140200: '陕西省_西安市_阎良区_新华路街道',
      6101140300: '陕西省_西安市_阎良区_振兴街道',
      6101140400: '陕西省_西安市_阎良区_新兴街道',
      6101140500: '陕西省_西安市_阎良区_北屯街道',
      6101140600: '陕西省_西安市_阎良区_武屯街道',
      6101140700: '陕西省_西安市_阎良区_关山街道',
      6101150000: '陕西省_西安市_临潼区',
      6101150100: '陕西省_西安市_临潼区_骊山街道',
      6101150200: '陕西省_西安市_临潼区_秦陵街道',
      6101150300: '陕西省_西安市_临潼区_新丰街道',
      6101150400: '陕西省_西安市_临潼区_代王街道',
      6101150500: '陕西省_西安市_临潼区_斜口街道',
      6101150600: '陕西省_西安市_临潼区_行者街道',
      6101150700: '陕西省_西安市_临潼区_马额街道',
      6101150800: '陕西省_西安市_临潼区_零口街道',
      6101150900: '陕西省_西安市_临潼区_雨金街道',
      6101151000: '陕西省_西安市_临潼区_栎阳街道',
      6101151100: '陕西省_西安市_临潼区_相桥街道',
      6101151200: '陕西省_西安市_临潼区_徐杨街道',
      6101151300: '陕西省_西安市_临潼区_西泉街道',
      6101151400: '陕西省_西安市_临潼区_新市街道',
      6101151500: '陕西省_西安市_临潼区_交口街道',
      6101151600: '陕西省_西安市_临潼区_北田街道',
      6101151700: '陕西省_西安市_临潼区_油槐街道',
      6101151800: '陕西省_西安市_临潼区_何寨街道',
      6101151900: '陕西省_西安市_临潼区_铁炉街道',
      6101152000: '陕西省_西安市_临潼区_任留街道',
      6101152100: '陕西省_西安市_临潼区_穆寨街道',
      6101152200: '陕西省_西安市_临潼区_小金街道',
      6101152300: '陕西省_西安市_临潼区_仁宗街道',
      6101160000: '陕西省_西安市_长安区',
      6101160100: '陕西省_西安市_长安区_韦曲街道',
      6101160200: '陕西省_西安市_长安区_郭杜街道',
      6101160300: '陕西省_西安市_长安区_航天街道',
      6101160400: '陕西省_西安市_长安区_滦镇街道',
      6101160500: '陕西省_西安市_长安区_子午街道',
      6101160600: '陕西省_西安市_长安区_太乙宫街道',
      6101160700: '陕西省_西安市_长安区_引镇街道',
      6101160800: '陕西省_西安市_长安区_王曲街道',
      6101160900: '陕西省_西安市_长安区_杜曲街道',
      6101161000: '陕西省_西安市_长安区_鸣犊街道',
      6101161100: '陕西省_西安市_长安区_黄良街道',
      6101161200: '陕西省_西安市_长安区_大兆街道',
      6101161300: '陕西省_西安市_长安区_五台街道',
      6101161400: '陕西省_西安市_长安区_王莽街道',
      6101161500: '陕西省_西安市_长安区_杨庄街道',
      6101161600: '陕西省_西安市_长安区_炮里街道',
      6101161700: '陕西省_西安市_长安区_魏寨街道',
      6101170000: '陕西省_西安市_高陵区',
      6101170100: '陕西省_西安市_高陵区_鹿苑街道',
      6101170200: '陕西省_西安市_高陵区_泾渭街道',
      6101170300: '陕西省_西安市_高陵区_崇皇街道',
      6101170400: '陕西省_西安市_高陵区_姬家街道',
      6101170500: '陕西省_西安市_高陵区_通远街道',
      6101170600: '陕西省_西安市_高陵区_耿镇街道',
      6101170700: '陕西省_西安市_高陵区_张卜街道',
      6101180000: '陕西省_西安市_鄠邑区',
      6101180100: '陕西省_西安市_鄠邑区_甘亭街道',
      6101180200: '陕西省_西安市_鄠邑区_余下街道',
      6101180300: '陕西省_西安市_鄠邑区_五竹街道',
      6101180400: '陕西省_西安市_鄠邑区_玉蝉街道',
      6101180500: '陕西省_西安市_鄠邑区_森林旅游景区',
      6101180600: '陕西省_西安市_鄠邑区_祖庵街道',
      6101180700: '陕西省_西安市_鄠邑区_蒋村街道',
      6101180800: '陕西省_西安市_鄠邑区_涝店街道',
      6101180900: '陕西省_西安市_鄠邑区_甘河街道',
      6101181000: '陕西省_西安市_鄠邑区_石井街道',
      6101181100: '陕西省_西安市_鄠邑区_渭丰街道',
      6101220000: '陕西省_西安市_蓝田县',
      6101220100: '陕西省_西安市_蓝田县_蓝关街道',
      6101220200: '陕西省_西安市_蓝田县_洩湖镇',
      6101220300: '陕西省_西安市_蓝田县_华胥镇',
      6101220400: '陕西省_西安市_蓝田县_前卫镇',
      6101220500: '陕西省_西安市_蓝田县_汤峪镇',
      6101220600: '陕西省_西安市_蓝田县_焦岱镇',
      6101220700: '陕西省_西安市_蓝田县_玉山镇',
      6101220800: '陕西省_西安市_蓝田县_三里镇',
      6101220900: '陕西省_西安市_蓝田县_普化镇',
      6101221000: '陕西省_西安市_蓝田县_葛牌镇',
      6101221100: '陕西省_西安市_蓝田县_蓝桥镇',
      6101221200: '陕西省_西安市_蓝田县_辋川镇',
      6101221300: '陕西省_西安市_蓝田县_灞源镇',
      6101221400: '陕西省_西安市_蓝田县_孟村镇',
      6101221500: '陕西省_西安市_蓝田县_安村镇',
      6101221600: '陕西省_西安市_蓝田县_小寨镇',
      6101221700: '陕西省_西安市_蓝田县_三官庙镇',
      6101221800: '陕西省_西安市_蓝田县_九间房镇',
      6101221900: '陕西省_西安市_蓝田县_厚镇',
      6101240000: '陕西省_西安市_周至县',
      6101240100: '陕西省_西安市_周至县_二曲街道',
      6101240200: '陕西省_西安市_周至县_哑柏镇',
      6101240300: '陕西省_西安市_周至县_终南镇',
      6101240400: '陕西省_西安市_周至县_马召镇',
      6101240500: '陕西省_西安市_周至县_楼观镇',
      6101240600: '陕西省_西安市_周至县_尚村镇',
      6101240700: '陕西省_西安市_周至县_广济镇',
      6101240800: '陕西省_西安市_周至县_厚畛子镇',
      6101240900: '陕西省_西安市_周至县_四屯镇',
      6101241000: '陕西省_西安市_周至县_竹峪镇',
      6101241100: '陕西省_西安市_周至县_青化镇',
      6101241200: '陕西省_西安市_周至县_翠峰镇',
      6101241300: '陕西省_西安市_周至县_富仁镇',
      6101241400: '陕西省_西安市_周至县_司竹镇',
      6101241500: '陕西省_西安市_周至县_骆峪镇',
      6101241600: '陕西省_西安市_周至县_陈河镇',
      6101241700: '陕西省_西安市_周至县_板房子镇',
      6101241800: '陕西省_西安市_周至县_王家河镇',
      6101300000: '陕西省_西安市_西咸新区',
      6101300100: '陕西省_西安市_西咸新区_建章路街办',
      6101300200: '陕西省_西安市_西咸新区_三桥街办',
      6101300300: '陕西省_西安市_西咸新区_斗门街办',
      6101300400: '陕西省_西安市_西咸新区_王寺街办',
      6101300500: '陕西省_西安市_西咸新区_高桥街办',
      6101300600: '陕西省_西安市_西咸新区_上林街道',
      6101300700: '陕西省_西安市_西咸新区_马王街道',
      6101300800: '陕西省_西安市_西咸新区_钓台街道',
      6101300900: '陕西省_西安市_西咸新区_渭城街道',
      6101301000: '陕西省_西安市_西咸新区_窑店街道',
      6101301100: '陕西省_西安市_西咸新区_正阳街道',
      6101301200: '陕西省_西安市_西咸新区_周陵街道',
      6101301300: '陕西省_西安市_西咸新区_北杜街道',
      6101301400: '陕西省_西安市_西咸新区_底张街道',
      6101301500: '陕西省_西安市_西咸新区_大王镇',
      6101301600: '陕西省_西安市_西咸新区_高庄镇',
      6101301700: '陕西省_西安市_西咸新区_崇文镇',
      6101301800: '陕西省_西安市_西咸新区_永乐镇',
      6101301900: '陕西省_西安市_西咸新区_太平镇',
      6101310000: '陕西省_西安市_国际港务区',
      6101310100: '陕西省_西安市_国际港务区_新筑街道办',
      6101310200: '陕西省_西安市_国际港务区_新合街道办',
      6101320000: '陕西省_西安市_高新区',
      6101320100: '陕西省_西安市_高新区_鱼化街道',
      6101320200: '陕西省_西安市_高新区_丈八街道',
      6101320300: '陕西省_西安市_高新区_东大街办',
      6101320400: '陕西省_西安市_高新区_细柳街办',
      6101320500: '陕西省_西安市_高新区_五星街办',
      6101320600: '陕西省_西安市_高新区_兴隆街办',
      6101320700: '陕西省_西安市_高新区_灵沼街办',
      6101320800: '陕西省_西安市_高新区_集贤镇',
      6101320900: '陕西省_西安市_高新区_九峰镇',
      6101321000: '陕西省_西安市_高新区_秦渡镇',
      6101321100: '陕西省_西安市_高新区_草堂镇',
      6101321200: '陕西省_西安市_高新区_庞光镇',
      6102000000: '陕西省_铜川市',
      6102020000: '陕西省_铜川市_王益区',
      6102020100: '陕西省_铜川市_王益区_七一路街道',
      6102020200: '陕西省_铜川市_王益区_红旗街街道',
      6102020300: '陕西省_铜川市_王益区_桃园街道',
      6102020400: '陕西省_铜川市_王益区_青年路街道',
      6102020500: '陕西省_铜川市_王益区_王家河街道',
      6102020600: '陕西省_铜川市_王益区_王益街道',
      6102020700: '陕西省_铜川市_王益区_黄堡镇',
      6102030000: '陕西省_铜川市_印台区',
      6102030100: '陕西省_铜川市_印台区_城关街道',
      6102030200: '陕西省_铜川市_印台区_三里洞街道',
      6102030300: '陕西省_铜川市_印台区_王石凹街道',
      6102030400: '陕西省_铜川市_印台区_印台街道',
      6102030500: '陕西省_铜川市_印台区_玉华街道办事处',
      6102030600: '陕西省_铜川市_印台区_陈炉镇',
      6102030700: '陕西省_铜川市_印台区_红土镇',
      6102030800: '陕西省_铜川市_印台区_广阳镇',
      6102030900: '陕西省_铜川市_印台区_金锁关镇',
      6102031000: '陕西省_铜川市_印台区_阿庄镇',
      6102040000: '陕西省_铜川市_耀州区',
      6102040100: '陕西省_铜川市_耀州区_天宝路街道',
      6102040200: '陕西省_铜川市_耀州区_永安路街道',
      6102040300: '陕西省_铜川市_耀州区_锦阳路街道',
      6102040400: '陕西省_铜川市_耀州区_董家河镇',
      6102040500: '陕西省_铜川市_耀州区_庙湾镇',
      6102040600: '陕西省_铜川市_耀州区_瑶曲镇',
      6102040700: '陕西省_铜川市_耀州区_照金镇',
      6102040800: '陕西省_铜川市_耀州区_小丘镇',
      6102040900: '陕西省_铜川市_耀州区_孙塬镇',
      6102041000: '陕西省_铜川市_耀州区_关庄镇',
      6102041100: '陕西省_铜川市_耀州区_石柱镇',
      6102050000: '陕西省_铜川市_新区',
      6102050100: '陕西省_铜川市_新区_正阳路街道',
      6102050200: '陕西省_铜川市_新区_咸丰路街道',
      6102050300: '陕西省_铜川市_新区_坡头街道',
      6102220000: '陕西省_铜川市_宜君县',
      6102220100: '陕西省_铜川市_宜君县_宜阳街道',
      6102220200: '陕西省_铜川市_宜君县_彭镇',
      6102220300: '陕西省_铜川市_宜君县_五里镇',
      6102220400: '陕西省_铜川市_宜君县_太安镇',
      6102220500: '陕西省_铜川市_宜君县_棋盘镇',
      6102220600: '陕西省_铜川市_宜君县_尧生镇',
      6102220700: '陕西省_铜川市_宜君县_哭泉镇',
      6102220800: '陕西省_铜川市_宜君县_云梦乡',
      6103000000: '陕西省_宝鸡市',
      6103020000: '陕西省_宝鸡市_渭滨区',
      6103020100: '陕西省_宝鸡市_渭滨区_金陵街道',
      6103020200: '陕西省_宝鸡市_渭滨区_经二路街道',
      6103020300: '陕西省_宝鸡市_渭滨区_清姜街道',
      6103020400: '陕西省_宝鸡市_渭滨区_姜谭街道',
      6103020500: '陕西省_宝鸡市_渭滨区_桥南街道',
      6103020600: '陕西省_宝鸡市_渭滨区_马营镇',
      6103020700: '陕西省_宝鸡市_渭滨区_石鼓镇',
      6103020800: '陕西省_宝鸡市_渭滨区_神农镇',
      6103020900: '陕西省_宝鸡市_渭滨区_高家镇',
      6103021000: '陕西省_宝鸡市_渭滨区_八鱼镇',
      6103030000: '陕西省_宝鸡市_金台区',
      6103030100: '陕西省_宝鸡市_金台区_中山东路街道',
      6103030200: '陕西省_宝鸡市_金台区_西关街道',
      6103030300: '陕西省_宝鸡市_金台区_中山西路街道',
      6103030400: '陕西省_宝鸡市_金台区_群众路街道',
      6103030500: '陕西省_宝鸡市_金台区_东风路街道',
      6103030600: '陕西省_宝鸡市_金台区_十里铺街道',
      6103030700: '陕西省_宝鸡市_金台区_卧龙寺街道',
      6103030800: '陕西省_宝鸡市_金台区_店子街街道',
      6103030900: '陕西省_宝鸡市_金台区_陈仓镇',
      6103031000: '陕西省_宝鸡市_金台区_蟠龙镇',
      6103031100: '陕西省_宝鸡市_金台区_金河镇',
      6103031200: '陕西省_宝鸡市_金台区_硖石镇',
      6103031300: '陕西省_宝鸡市_金台区_陵塬乡',
      6103040000: '陕西省_宝鸡市_陈仓区',
      6103040100: '陕西省_宝鸡市_陈仓区_虢镇街道',
      6103040200: '陕西省_宝鸡市_陈仓区_东关街道',
      6103040300: '陕西省_宝鸡市_陈仓区_千渭街道',
      6103040400: '陕西省_宝鸡市_陈仓区_阳平镇',
      6103040500: '陕西省_宝鸡市_陈仓区_千河镇',
      6103040600: '陕西省_宝鸡市_陈仓区_磻溪镇',
      6103040700: '陕西省_宝鸡市_陈仓区_天王镇',
      6103040800: '陕西省_宝鸡市_陈仓区_慕仪镇',
      6103040900: '陕西省_宝鸡市_陈仓区_周原镇',
      6103041000: '陕西省_宝鸡市_陈仓区_贾村镇',
      6103041100: '陕西省_宝鸡市_陈仓区_县功镇',
      6103041200: '陕西省_宝鸡市_陈仓区_新街镇',
      6103041300: '陕西省_宝鸡市_陈仓区_坪头镇',
      6103041400: '陕西省_宝鸡市_陈仓区_香泉镇',
      6103041500: '陕西省_宝鸡市_陈仓区_赤沙镇',
      6103041600: '陕西省_宝鸡市_陈仓区_拓石镇',
      6103041700: '陕西省_宝鸡市_陈仓区_凤阁岭镇',
      6103041800: '陕西省_宝鸡市_陈仓区_钓渭镇',
      6103220000: '陕西省_宝鸡市_凤翔县',
      6103220100: '陕西省_宝鸡市_凤翔县_城关镇',
      6103220200: '陕西省_宝鸡市_凤翔县_虢王镇',
      6103220300: '陕西省_宝鸡市_凤翔县_彪角镇',
      6103220400: '陕西省_宝鸡市_凤翔县_横水镇',
      6103220500: '陕西省_宝鸡市_凤翔县_田家庄镇',
      6103220600: '陕西省_宝鸡市_凤翔县_糜杆桥镇',
      6103220700: '陕西省_宝鸡市_凤翔县_南指挥镇',
      6103220800: '陕西省_宝鸡市_凤翔县_陈村镇',
      6103220900: '陕西省_宝鸡市_凤翔县_长青镇',
      6103221000: '陕西省_宝鸡市_凤翔县_柳林镇',
      6103221100: '陕西省_宝鸡市_凤翔县_姚家沟镇',
      6103221200: '陕西省_宝鸡市_凤翔县_范家寨镇',
      6103230000: '陕西省_宝鸡市_岐山县',
      6103230100: '陕西省_宝鸡市_岐山县_益店镇',
      6103230200: '陕西省_宝鸡市_岐山县_蒲村镇',
      6103230300: '陕西省_宝鸡市_岐山县_青化镇',
      6103230400: '陕西省_宝鸡市_岐山县_枣林镇',
      6103230500: '陕西省_宝鸡市_岐山县_雍川镇',
      6103230600: '陕西省_宝鸡市_岐山县_凤鸣镇',
      6103230700: '陕西省_宝鸡市_岐山县_蔡家坡镇',
      6103230800: '陕西省_宝鸡市_岐山县_京当镇',
      6103230900: '陕西省_宝鸡市_岐山县_故郡镇',
      6103240000: '陕西省_宝鸡市_扶风县',
      6103240100: '陕西省_宝鸡市_扶风县_城关街道',
      6103240200: '陕西省_宝鸡市_扶风县_天度镇',
      6103240300: '陕西省_宝鸡市_扶风县_午井镇',
      6103240400: '陕西省_宝鸡市_扶风县_绛帐镇',
      6103240500: '陕西省_宝鸡市_扶风县_段家镇',
      6103240600: '陕西省_宝鸡市_扶风县_杏林镇',
      6103240700: '陕西省_宝鸡市_扶风县_召公镇',
      6103240800: '陕西省_宝鸡市_扶风县_法门镇',
      6103260000: '陕西省_宝鸡市_眉县',
      6103260100: '陕西省_宝鸡市_眉县_首善街道',
      6103260200: '陕西省_宝鸡市_眉县_横渠镇',
      6103260300: '陕西省_宝鸡市_眉县_槐芽镇',
      6103260400: '陕西省_宝鸡市_眉县_汤峪镇',
      6103260500: '陕西省_宝鸡市_眉县_常兴镇',
      6103260600: '陕西省_宝鸡市_眉县_金渠镇',
      6103260700: '陕西省_宝鸡市_眉县_营头镇',
      6103260800: '陕西省_宝鸡市_眉县_齐镇',
      6103270000: '陕西省_宝鸡市_陇县',
      6103270100: '陕西省_宝鸡市_陇县_城关镇',
      6103270200: '陕西省_宝鸡市_陇县_东风镇',
      6103270300: '陕西省_宝鸡市_陇县_八渡镇',
      6103270400: '陕西省_宝鸡市_陇县_温水镇',
      6103270500: '陕西省_宝鸡市_陇县_天成镇',
      6103270600: '陕西省_宝鸡市_陇县_曹家湾镇',
      6103270700: '陕西省_宝鸡市_陇县_固关镇',
      6103270800: '陕西省_宝鸡市_陇县_东南镇',
      6103270900: '陕西省_宝鸡市_陇县_河北镇',
      6103271000: '陕西省_宝鸡市_陇县_新集川镇',
      6103280000: '陕西省_宝鸡市_千阳县',
      6103280100: '陕西省_宝鸡市_千阳县_城关镇',
      6103280200: '陕西省_宝鸡市_千阳县_崔家头镇',
      6103280300: '陕西省_宝鸡市_千阳县_南寨镇',
      6103280400: '陕西省_宝鸡市_千阳县_张家塬镇',
      6103280500: '陕西省_宝鸡市_千阳县_水沟镇',
      6103280600: '陕西省_宝鸡市_千阳县_草碧镇',
      6103280700: '陕西省_宝鸡市_千阳县_高崖镇',
      6103290000: '陕西省_宝鸡市_麟游县',
      6103290100: '陕西省_宝鸡市_麟游县_九成宫镇',
      6103290200: '陕西省_宝鸡市_麟游县_崔木镇',
      6103290300: '陕西省_宝鸡市_麟游县_招贤镇',
      6103290400: '陕西省_宝鸡市_麟游县_两亭镇',
      6103290500: '陕西省_宝鸡市_麟游县_常丰镇',
      6103290600: '陕西省_宝鸡市_麟游县_丈八镇',
      6103290700: '陕西省_宝鸡市_麟游县_酒房镇',
      6103300000: '陕西省_宝鸡市_凤县',
      6103300100: '陕西省_宝鸡市_凤县_双石铺镇',
      6103300200: '陕西省_宝鸡市_凤县_凤州镇',
      6103300300: '陕西省_宝鸡市_凤县_黄牛铺镇',
      6103300400: '陕西省_宝鸡市_凤县_红花铺镇',
      6103300500: '陕西省_宝鸡市_凤县_河口镇',
      6103300600: '陕西省_宝鸡市_凤县_唐藏镇',
      6103300700: '陕西省_宝鸡市_凤县_平木镇',
      6103300800: '陕西省_宝鸡市_凤县_坪坎镇',
      6103300900: '陕西省_宝鸡市_凤县_留凤关镇',
      6103310000: '陕西省_宝鸡市_太白县',
      6103310100: '陕西省_宝鸡市_太白县_咀头镇',
      6103310200: '陕西省_宝鸡市_太白县_桃川镇',
      6103310300: '陕西省_宝鸡市_太白县_靖口镇',
      6103310400: '陕西省_宝鸡市_太白县_太白河镇',
      6103310500: '陕西省_宝鸡市_太白县_鹦鸽镇',
      6103310600: '陕西省_宝鸡市_太白县_王家堎镇',
      6103310700: '陕西省_宝鸡市_太白县_黄柏塬镇',
      6104000000: '陕西省_咸阳市',
      6104020000: '陕西省_咸阳市_秦都区',
      6104020100: '陕西省_咸阳市_秦都区_人民路街道',
      6104020200: '陕西省_咸阳市_秦都区_西兰路街道',
      6104020300: '陕西省_咸阳市_秦都区_吴家堡街道',
      6104020400: '陕西省_咸阳市_秦都区_渭阳西路街道',
      6104020500: '陕西省_咸阳市_秦都区_陈杨寨街道',
      6104020600: '陕西省_咸阳市_秦都区_古渡街道',
      6104020700: '陕西省_咸阳市_秦都区_马泉街道',
      6104020800: '陕西省_咸阳市_秦都区_渭滨街道',
      6104020900: '陕西省_咸阳市_秦都区_马庄街道',
      6104021000: '陕西省_咸阳市_秦都区_双照街道',
      6104040000: '陕西省_咸阳市_渭城区',
      6104040100: '陕西省_咸阳市_渭城区_中山街道',
      6104040200: '陕西省_咸阳市_渭城区_文汇路街道',
      6104040300: '陕西省_咸阳市_渭城区_新兴街道',
      6104040400: '陕西省_咸阳市_渭城区_渭阳街道',
      6104220000: '陕西省_咸阳市_三原县',
      6104220100: '陕西省_咸阳市_三原县_城关街道',
      6104220200: '陕西省_咸阳市_三原县_陂西镇',
      6104220300: '陕西省_咸阳市_三原县_独李镇',
      6104220400: '陕西省_咸阳市_三原县_大程镇',
      6104220500: '陕西省_咸阳市_三原县_西阳镇',
      6104220600: '陕西省_咸阳市_三原县_鲁桥镇',
      6104220700: '陕西省_咸阳市_三原县_陵前镇',
      6104220800: '陕西省_咸阳市_三原县_新兴镇',
      6104220900: '陕西省_咸阳市_三原县_嵯峨镇',
      6104221000: '陕西省_咸阳市_三原县_渠岸镇',
      6104221100: '陕西省_咸阳市_三原县_安乐镇',
      6104221200: '陕西省_咸阳市_三原县_马额镇',
      6104221300: '陕西省_咸阳市_三原县_徐木乡',
      6104221400: '陕西省_咸阳市_三原县_高渠乡',
      6104230000: '陕西省_咸阳市_泾阳县',
      6104230100: '陕西省_咸阳市_泾阳县_泾干街道',
      6104230200: '陕西省_咸阳市_泾阳县_云阳镇',
      6104230300: '陕西省_咸阳市_泾阳县_桥底镇',
      6104230400: '陕西省_咸阳市_泾阳县_王桥镇',
      6104230500: '陕西省_咸阳市_泾阳县_口镇',
      6104230600: '陕西省_咸阳市_泾阳县_三渠镇',
      6104230700: '陕西省_咸阳市_泾阳县_安吴镇',
      6104230800: '陕西省_咸阳市_泾阳县_中张镇',
      6104230900: '陕西省_咸阳市_泾阳县_兴隆镇',
      6104240000: '陕西省_咸阳市_乾县',
      6104240100: '陕西省_咸阳市_乾县_城关街道',
      6104240200: '陕西省_咸阳市_乾县_薛录镇',
      6104240300: '陕西省_咸阳市_乾县_梁村镇',
      6104240400: '陕西省_咸阳市_乾县_临平镇',
      6104240500: '陕西省_咸阳市_乾县_姜村镇',
      6104240600: '陕西省_咸阳市_乾县_王村镇',
      6104240700: '陕西省_咸阳市_乾县_马连镇',
      6104240800: '陕西省_咸阳市_乾县_阳峪镇',
      6104240900: '陕西省_咸阳市_乾县_峰阳镇',
      6104241000: '陕西省_咸阳市_乾县_注泔镇',
      6104241100: '陕西省_咸阳市_乾县_灵源镇',
      6104241200: '陕西省_咸阳市_乾县_阳洪镇',
      6104241300: '陕西省_咸阳市_乾县_梁山镇',
      6104241400: '陕西省_咸阳市_乾县_周城镇',
      6104241500: '陕西省_咸阳市_乾县_新阳镇',
      6104241600: '陕西省_咸阳市_乾县_大杨镇',
      6104241700: '陕西省_咸阳市_乾县_薛录镇大墙社区',
      6104241800: '陕西省_咸阳市_乾县_梁山镇关头社区',
      6104241900: '陕西省_咸阳市_乾县_城关镇漠西社区',
      6104242000: '陕西省_咸阳市_乾县_临平镇石牛社区',
      6104250000: '陕西省_咸阳市_礼泉县',
      6104250100: '陕西省_咸阳市_礼泉县_城关街道',
      6104250200: '陕西省_咸阳市_礼泉县_史德镇',
      6104250300: '陕西省_咸阳市_礼泉县_西张堡镇',
      6104250400: '陕西省_咸阳市_礼泉县_阡东镇',
      6104250500: '陕西省_咸阳市_礼泉县_烽火镇',
      6104250600: '陕西省_咸阳市_礼泉县_烟霞镇',
      6104250700: '陕西省_咸阳市_礼泉县_赵镇',
      6104250800: '陕西省_咸阳市_礼泉县_昭陵镇',
      6104250900: '陕西省_咸阳市_礼泉县_叱干镇',
      6104251000: '陕西省_咸阳市_礼泉县_南坊镇',
      6104251100: '陕西省_咸阳市_礼泉县_石潭镇',
      6104251200: '陕西省_咸阳市_礼泉县_骏马镇',
      6104260000: '陕西省_咸阳市_永寿县',
      6104260100: '陕西省_咸阳市_永寿县_监军街道',
      6104260200: '陕西省_咸阳市_永寿县_店头镇',
      6104260300: '陕西省_咸阳市_永寿县_常宁镇',
      6104260400: '陕西省_咸阳市_永寿县_甘井镇',
      6104260500: '陕西省_咸阳市_永寿县_马坊镇',
      6104260600: '陕西省_咸阳市_永寿县_渠子镇',
      6104260700: '陕西省_咸阳市_永寿县_永平镇',
      6104260800: '陕西省_咸阳市_永寿县_豆家镇',
      6104260900: '陕西省_咸阳市_永寿县_仪井镇',
      6104261000: '陕西省_咸阳市_永寿县_御驾宫乡',
      6104261100: '陕西省_咸阳市_永寿县_永太乡',
      6104280000: '陕西省_咸阳市_长武县',
      6104280100: '陕西省_咸阳市_长武县_昭仁街道',
      6104280200: '陕西省_咸阳市_长武县_相公镇',
      6104280300: '陕西省_咸阳市_长武县_巨家镇',
      6104280400: '陕西省_咸阳市_长武县_丁家镇',
      6104280500: '陕西省_咸阳市_长武县_洪家镇',
      6104280600: '陕西省_咸阳市_长武县_亭口镇',
      6104280700: '陕西省_咸阳市_长武县_彭公镇',
      6104280800: '陕西省_咸阳市_长武县_枣园镇',
      6104290000: '陕西省_咸阳市_旬邑县',
      6104290100: '陕西省_咸阳市_旬邑县_城关街道',
      6104290200: '陕西省_咸阳市_旬邑县_土桥镇',
      6104290300: '陕西省_咸阳市_旬邑县_职田镇',
      6104290400: '陕西省_咸阳市_旬邑县_张洪镇',
      6104290500: '陕西省_咸阳市_旬邑县_太村镇',
      6104290600: '陕西省_咸阳市_旬邑县_郑家镇',
      6104290700: '陕西省_咸阳市_旬邑县_湫坡头镇',
      6104290800: '陕西省_咸阳市_旬邑县_底庙镇',
      6104290900: '陕西省_咸阳市_旬邑县_马栏镇',
      6104291000: '陕西省_咸阳市_旬邑县_清塬镇',
      6104291100: '陕西省_咸阳市_旬邑县_土桥镇丈八寺社区',
      6104291200: '陕西省_咸阳市_旬邑县_土桥镇排厦社区',
      6104291300: '陕西省_咸阳市_旬邑县_太村镇赤道社区',
      6104291400: '陕西省_咸阳市_旬邑县_张洪镇原底社区',
      6104300000: '陕西省_咸阳市_淳化县',
      6104300100: '陕西省_咸阳市_淳化县_城关街道',
      6104300200: '陕西省_咸阳市_淳化县_官庄镇',
      6104300300: '陕西省_咸阳市_淳化县_方里镇',
      6104300400: '陕西省_咸阳市_淳化县_润镇',
      6104300500: '陕西省_咸阳市_淳化县_车坞镇',
      6104300600: '陕西省_咸阳市_淳化县_铁王镇',
      6104300700: '陕西省_咸阳市_淳化县_石桥镇',
      6104300800: '陕西省_咸阳市_淳化县_十里塬镇',
      6104300900: '陕西省_咸阳市_淳化县_十里塬镇马家便民服务中心',
      6104301000: '陕西省_咸阳市_淳化县_大店新区',
      6104301100: '陕西省_咸阳市_淳化县_石桥镇秦庄便民服务中心',
      6104301200: '陕西省_咸阳市_淳化县_方里镇固贤便民服务中心',
      6104301300: '陕西省_咸阳市_淳化县_官庄镇胡家庙便民服务中心',
      6104301400: '陕西省_咸阳市_淳化县_润镇卜家便民服务中心',
      6104301500: '陕西省_咸阳市_淳化县_铁王镇秦河便民服务中心',
      6104310000: '陕西省_咸阳市_武功县',
      6104310100: '陕西省_咸阳市_武功县_普集街道',
      6104310200: '陕西省_咸阳市_武功县_苏坊镇',
      6104310300: '陕西省_咸阳市_武功县_武功镇',
      6104310400: '陕西省_咸阳市_武功县_游风镇',
      6104310500: '陕西省_咸阳市_武功县_贞元镇',
      6104310600: '陕西省_咸阳市_武功县_长宁镇',
      6104310700: '陕西省_咸阳市_武功县_小村镇',
      6104310800: '陕西省_咸阳市_武功县_大庄镇',
      6104310900: '陕西省_咸阳市_武功县_普集街乡',
      6104311000: '陕西省_咸阳市_武功县_南仁乡',
      6104311100: '陕西省_咸阳市_武功县_河道乡',
      6104311200: '陕西省_咸阳市_武功县_代家乡',
      6104810000: '陕西省_咸阳市_兴平市',
      6104810100: '陕西省_咸阳市_兴平市_东城街道',
      6104810200: '陕西省_咸阳市_兴平市_西城街道',
      6104810300: '陕西省_咸阳市_兴平市_店张街道',
      6104810400: '陕西省_咸阳市_兴平市_马嵬街道',
      6104810500: '陕西省_咸阳市_兴平市_西吴街道',
      6104810600: '陕西省_咸阳市_兴平市_赵村镇',
      6104810700: '陕西省_咸阳市_兴平市_桑镇',
      6104810800: '陕西省_咸阳市_兴平市_南市镇',
      6104810900: '陕西省_咸阳市_兴平市_庄头镇',
      6104811000: '陕西省_咸阳市_兴平市_汤坊镇',
      6104811100: '陕西省_咸阳市_兴平市_丰仪镇',
      6104811200: '陕西省_咸阳市_兴平市_阜寨镇',
      6104811300: '陕西省_咸阳市_兴平市_南位镇',
      6104820000: '陕西省_咸阳市_彬州市',
      6104820100: '陕西省_咸阳市_彬州市_城关街道',
      6104820200: '陕西省_咸阳市_彬州市_北极镇',
      6104820300: '陕西省_咸阳市_彬州市_新民镇',
      6104820400: '陕西省_咸阳市_彬州市_龙高镇',
      6104820500: '陕西省_咸阳市_彬州市_永乐镇',
      6104820600: '陕西省_咸阳市_彬州市_义门镇',
      6104820700: '陕西省_咸阳市_彬州市_水口镇',
      6104820800: '陕西省_咸阳市_彬州市_韩家镇',
      6104820900: '陕西省_咸阳市_彬州市_太峪镇',
      6105000000: '陕西省_渭南市',
      6105020000: '陕西省_渭南市_临渭区',
      6105020100: '陕西省_渭南市_临渭区_杜桥街道',
      6105020200: '陕西省_渭南市_临渭区_人民街道',
      6105020300: '陕西省_渭南市_临渭区_解放街道',
      6105020400: '陕西省_渭南市_临渭区_向阳街道',
      6105020500: '陕西省_渭南市_临渭区_站南街道',
      6105020600: '陕西省_渭南市_临渭区_双王街道',
      6105020700: '陕西省_渭南市_临渭区_桥南镇',
      6105020800: '陕西省_渭南市_临渭区_阳郭镇',
      6105020900: '陕西省_渭南市_临渭区_故市镇',
      6105021000: '陕西省_渭南市_临渭区_下邽镇',
      6105021100: '陕西省_渭南市_临渭区_三张镇',
      6105021200: '陕西省_渭南市_临渭区_交斜镇',
      6105021300: '陕西省_渭南市_临渭区_崇凝镇',
      6105021400: '陕西省_渭南市_临渭区_孝义镇',
      6105021500: '陕西省_渭南市_临渭区_蔺店镇',
      6105021600: '陕西省_渭南市_临渭区_官底镇',
      6105021700: '陕西省_渭南市_临渭区_官路镇',
      6105021800: '陕西省_渭南市_临渭区_丰原镇',
      6105021900: '陕西省_渭南市_临渭区_阎村镇',
      6105022000: '陕西省_渭南市_临渭区_官道镇',
      6105022100: '陕西省_渭南市_临渭区_田市镇',
      6105022200: '陕西省_渭南市_临渭区_大王镇',
      6105030000: '陕西省_渭南市_华州区',
      6105030100: '陕西省_渭南市_华州区_华州街道',
      6105030200: '陕西省_渭南市_华州区_杏林镇',
      6105030300: '陕西省_渭南市_华州区_赤水镇',
      6105030400: '陕西省_渭南市_华州区_高塘镇',
      6105030500: '陕西省_渭南市_华州区_大明镇',
      6105030600: '陕西省_渭南市_华州区_瓜坡镇',
      6105030700: '陕西省_渭南市_华州区_莲花寺镇',
      6105030800: '陕西省_渭南市_华州区_柳枝镇',
      6105030900: '陕西省_渭南市_华州区_下庙镇',
      6105031000: '陕西省_渭南市_华州区_金堆镇',
      6105031100: '陕西省_渭南市_华州区_东阳乡',
      6105031200: '陕西省_渭南市_华州区_毕家乡',
      6105031300: '陕西省_渭南市_华州区_辛庄乡',
      6105031400: '陕西省_渭南市_华州区_金惠乡',
      6105040000: '陕西省_渭南市_经开区',
      6105040100: '陕西省_渭南市_经开区_龙背镇',
      6105040200: '陕西省_渭南市_经开区_辛市镇',
      6105040300: '陕西省_渭南市_经开区_信义镇',
      6105050000: '陕西省_渭南市_高新区',
      6105050100: '陕西省_渭南市_高新区_开发区',
      6105220000: '陕西省_渭南市_潼关县',
      6105220100: '陕西省_渭南市_潼关县_城关街道',
      6105220200: '陕西省_渭南市_潼关县_桐峪镇',
      6105220300: '陕西省_渭南市_潼关县_太要镇',
      6105220400: '陕西省_渭南市_潼关县_秦东镇',
      6105220500: '陕西省_渭南市_潼关县_代字营镇',
      6105220600: '陕西省_渭南市_潼关县_安乐镇',
      6105220700: '陕西省_渭南市_潼关县_南头乡',
      6105220800: '陕西省_渭南市_潼关县_高桥乡',
      6105230000: '陕西省_渭南市_大荔县',
      6105230100: '陕西省_渭南市_大荔县_城关街道',
      6105230200: '陕西省_渭南市_大荔县_两宜镇',
      6105230300: '陕西省_渭南市_大荔县_冯村镇',
      6105230400: '陕西省_渭南市_大荔县_双泉镇',
      6105230500: '陕西省_渭南市_大荔县_范家镇',
      6105230600: '陕西省_渭南市_大荔县_官池镇',
      6105230700: '陕西省_渭南市_大荔县_韦林镇',
      6105230800: '陕西省_渭南市_大荔县_羌白镇',
      6105230900: '陕西省_渭南市_大荔县_下寨镇',
      6105231000: '陕西省_渭南市_大荔县_安仁镇',
      6105231100: '陕西省_渭南市_大荔县_许庄镇',
      6105231200: '陕西省_渭南市_大荔县_朝邑镇',
      6105231300: '陕西省_渭南市_大荔县_埝桥镇',
      6105231400: '陕西省_渭南市_大荔县_段家镇',
      6105231500: '陕西省_渭南市_大荔县_苏村镇',
      6105231600: '陕西省_渭南市_大荔县_赵渡镇',
      6105231700: '陕西省_渭南市_大荔县_高明镇',
      6105231800: '陕西省_渭南市_大荔县_平民镇',
      6105231900: '陕西省_渭南市_大荔县_沙底乡',
      6105232000: '陕西省_渭南市_大荔县_伯士乡',
      6105232100: '陕西省_渭南市_大荔县_户家乡',
      6105232200: '陕西省_渭南市_大荔县_步昌乡',
      6105232300: '陕西省_渭南市_大荔县_西寨乡',
      6105232400: '陕西省_渭南市_大荔县_张家乡',
      6105232500: '陕西省_渭南市_大荔县_石槽乡',
      6105232600: '陕西省_渭南市_大荔县_八鱼乡',
      6105240000: '陕西省_渭南市_合阳县',
      6105240100: '陕西省_渭南市_合阳县_城关街道',
      6105240200: '陕西省_渭南市_合阳县_知堡办事处',
      6105240300: '陕西省_渭南市_合阳县_皇甫庄办事处',
      6105240400: '陕西省_渭南市_合阳县_杨家庄办事处',
      6105240500: '陕西省_渭南市_合阳县_马家庄办事处',
      6105240600: '陕西省_渭南市_合阳县_甘井镇',
      6105240700: '陕西省_渭南市_合阳县_坊镇',
      6105240800: '陕西省_渭南市_合阳县_洽川镇',
      6105240900: '陕西省_渭南市_合阳县_新池镇',
      6105241000: '陕西省_渭南市_合阳县_黑池镇',
      6105241100: '陕西省_渭南市_合阳县_路井镇',
      6105241200: '陕西省_渭南市_合阳县_和家庄镇',
      6105241300: '陕西省_渭南市_合阳县_王村镇',
      6105241400: '陕西省_渭南市_合阳县_同家庄镇',
      6105241500: '陕西省_渭南市_合阳县_百良镇',
      6105241600: '陕西省_渭南市_合阳县_金峪镇',
      6105250000: '陕西省_渭南市_澄城县',
      6105250100: '陕西省_渭南市_澄城县_城关街道',
      6105250200: '陕西省_渭南市_澄城县_冯原镇',
      6105250300: '陕西省_渭南市_澄城县_王庄镇',
      6105250400: '陕西省_渭南市_澄城县_尧头镇',
      6105250500: '陕西省_渭南市_澄城县_赵庄镇',
      6105250600: '陕西省_渭南市_澄城县_交道镇',
      6105250700: '陕西省_渭南市_澄城县_寺前镇',
      6105250800: '陕西省_渭南市_澄城县_韦庄镇',
      6105250900: '陕西省_渭南市_澄城县_安里镇',
      6105251000: '陕西省_渭南市_澄城县_庄头镇',
      6105260000: '陕西省_渭南市_蒲城县',
      6105260100: '陕西省_渭南市_蒲城县_城关街道',
      6105260200: '陕西省_渭南市_蒲城县_罕井镇',
      6105260300: '陕西省_渭南市_蒲城县_孙镇',
      6105260400: '陕西省_渭南市_蒲城县_兴镇',
      6105260500: '陕西省_渭南市_蒲城县_党睦镇',
      6105260600: '陕西省_渭南市_蒲城县_高阳镇',
      6105260700: '陕西省_渭南市_蒲城县_永丰镇',
      6105260800: '陕西省_渭南市_蒲城县_荆姚镇',
      6105260900: '陕西省_渭南市_蒲城县_苏坊镇',
      6105261000: '陕西省_渭南市_蒲城县_龙阳镇',
      6105261100: '陕西省_渭南市_蒲城县_洛滨镇',
      6105261200: '陕西省_渭南市_蒲城县_陈庄镇',
      6105261300: '陕西省_渭南市_蒲城县_龙池镇',
      6105261400: '陕西省_渭南市_蒲城县_椿林镇',
      6105261500: '陕西省_渭南市_蒲城县_桥陵镇',
      6105261600: '陕西省_渭南市_蒲城县_三合镇',
      6105261700: '陕西省_渭南市_蒲城县_坡头镇',
      6105261800: '陕西省_渭南市_蒲城县_翔村镇',
      6105261900: '陕西省_渭南市_蒲城县_上王镇',
      6105262000: '陕西省_渭南市_蒲城县_贾曲镇',
      6105262100: '陕西省_渭南市_蒲城县_东陈镇',
      6105262200: '陕西省_渭南市_蒲城县_东杨镇',
      6105262300: '陕西省_渭南市_蒲城县_原任镇',
      6105270000: '陕西省_渭南市_白水县',
      6105270100: '陕西省_渭南市_白水县_城关街道',
      6105270200: '陕西省_渭南市_白水县_尧禾镇',
      6105270300: '陕西省_渭南市_白水县_杜康镇',
      6105270400: '陕西省_渭南市_白水县_西固镇',
      6105270500: '陕西省_渭南市_白水县_林皋镇',
      6105270600: '陕西省_渭南市_白水县_史官镇',
      6105270700: '陕西省_渭南市_白水县_北塬镇',
      6105270800: '陕西省_渭南市_白水县_雷牙镇',
      6105270900: '陕西省_渭南市_白水县_冯雷镇',
      6105271000: '陕西省_渭南市_白水县_收水乡',
      6105271100: '陕西省_渭南市_白水县_云台乡',
      6105271200: '陕西省_渭南市_白水县_纵目乡',
      6105271300: '陕西省_渭南市_白水县_雷村乡',
      6105271400: '陕西省_渭南市_白水县_北井头乡',
      6105280000: '陕西省_渭南市_富平县',
      6105280100: '陕西省_渭南市_富平县_城关街道',
      6105280200: '陕西省_渭南市_富平县_东华街道',
      6105280300: '陕西省_渭南市_富平县_庄里镇',
      6105280400: '陕西省_渭南市_富平县_张桥镇',
      6105280500: '陕西省_渭南市_富平县_美原镇',
      6105280600: '陕西省_渭南市_富平县_淡村镇',
      6105280700: '陕西省_渭南市_富平县_留古镇',
      6105280800: '陕西省_渭南市_富平县_老庙镇',
      6105280900: '陕西省_渭南市_富平县_薛镇',
      6105281000: '陕西省_渭南市_富平县_曹村镇',
      6105281100: '陕西省_渭南市_富平县_宫里镇',
      6105281200: '陕西省_渭南市_富平县_梅家坪镇',
      6105281300: '陕西省_渭南市_富平县_刘集镇',
      6105281400: '陕西省_渭南市_富平县_齐村镇',
      6105281500: '陕西省_渭南市_富平县_到贤镇',
      6105281600: '陕西省_渭南市_富平县_流曲镇',
      6105820000: '陕西省_渭南市_华阴市',
      6105820100: '陕西省_渭南市_华阴市_太华街道',
      6105820200: '陕西省_渭南市_华阴市_岳庙街道',
      6105820300: '陕西省_渭南市_华阴市_孟塬镇',
      6105820400: '陕西省_渭南市_华阴市_华西镇',
      6105820500: '陕西省_渭南市_华阴市_华山镇',
      6105820600: '陕西省_渭南市_华阴市_罗敷镇',
      6105820700: '陕西省_渭南市_华阴市_桃下镇',
      6105820800: '陕西省_渭南市_华阴市_卫峪乡',
      6105820900: '陕西省_渭南市_华阴市_北社乡',
      6105821000: '陕西省_渭南市_华阴市_五方乡',
      6106000000: '陕西省_延安市',
      6106020000: '陕西省_延安市_宝塔区',
      6106020100: '陕西省_延安市_宝塔区_宝塔山街道',
      6106020200: '陕西省_延安市_宝塔区_南市街道',
      6106020300: '陕西省_延安市_宝塔区_凤凰山街道',
      6106020400: '陕西省_延安市_宝塔区_桥沟街道',
      6106020500: '陕西省_延安市_宝塔区_枣园街道',
      6106020600: '陕西省_延安市_宝塔区_河庄坪镇',
      6106020700: '陕西省_延安市_宝塔区_李渠镇',
      6106020800: '陕西省_延安市_宝塔区_青化砭镇',
      6106020900: '陕西省_延安市_宝塔区_柳林镇',
      6106021000: '陕西省_延安市_宝塔区_甘谷驿镇',
      6106021100: '陕西省_延安市_宝塔区_临镇',
      6106021200: '陕西省_延安市_宝塔区_蟠龙镇',
      6106021300: '陕西省_延安市_宝塔区_姚店镇',
      6106021400: '陕西省_延安市_宝塔区_南泥湾镇',
      6106021500: '陕西省_延安市_宝塔区_川口乡',
      6106021600: '陕西省_延安市_宝塔区_冯庄乡',
      6106021700: '陕西省_延安市_宝塔区_麻洞川乡',
      6106021800: '陕西省_延安市_宝塔区_万花山乡',
      6106021900: '陕西省_延安市_宝塔区_松树林乡',
      6106022000: '陕西省_延安市_宝塔区_梁村乡',
      6106022100: '陕西省_延安市_宝塔区_官庄乡',
      6106030000: '陕西省_延安市_安塞区',
      6106030100: '陕西省_延安市_安塞区_真武洞街道',
      6106030200: '陕西省_延安市_安塞区_金明街道',
      6106030300: '陕西省_延安市_安塞区_白坪街道',
      6106030400: '陕西省_延安市_安塞区_砖窑湾镇',
      6106030500: '陕西省_延安市_安塞区_沿河湾镇',
      6106030600: '陕西省_延安市_安塞区_化子坪镇',
      6106030700: '陕西省_延安市_安塞区_建华镇',
      6106030800: '陕西省_延安市_安塞区_招安镇',
      6106030900: '陕西省_延安市_安塞区_高桥镇',
      6106031000: '陕西省_延安市_安塞区_坪桥镇',
      6106031100: '陕西省_延安市_安塞区_镰刀湾镇',
      6106210000: '陕西省_延安市_延长县',
      6106210100: '陕西省_延安市_延长县_七里村街道',
      6106210200: '陕西省_延安市_延长县_城关镇街道办',
      6106210300: '陕西省_延安市_延长县_黑家堡镇',
      6106210400: '陕西省_延安市_延长县_郑庄镇',
      6106210500: '陕西省_延安市_延长县_张家滩镇',
      6106210600: '陕西省_延安市_延长县_交口镇',
      6106210700: '陕西省_延安市_延长县_罗子山镇',
      6106210800: '陕西省_延安市_延长县_雷赤镇',
      6106210900: '陕西省_延安市_延长县_安沟镇',
      6106220000: '陕西省_延安市_延川县',
      6106220100: '陕西省_延安市_延川县_大禹街道',
      6106220200: '陕西省_延安市_延川县_永坪镇',
      6106220300: '陕西省_延安市_延川县_延水关镇',
      6106220400: '陕西省_延安市_延川县_文安驿镇',
      6106220500: '陕西省_延安市_延川县_杨家圪台镇',
      6106220600: '陕西省_延安市_延川县_贾家坪镇',
      6106220700: '陕西省_延安市_延川县_关庄镇',
      6106220800: '陕西省_延安市_延川县_乾坤湾镇',
      6106230000: '陕西省_延安市_子长市',
      6106230100: '陕西省_延安市_子长市_瓦窑堡街道',
      6106230200: '陕西省_延安市_子长市_城区街道办',
      6106230300: '陕西省_延安市_子长市_玉家湾镇',
      6106230400: '陕西省_延安市_子长市_安定镇',
      6106230500: '陕西省_延安市_子长市_马家砭镇',
      6106230600: '陕西省_延安市_子长市_南沟岔镇',
      6106230700: '陕西省_延安市_子长市_涧峪岔镇',
      6106230800: '陕西省_延安市_子长市_李家岔镇',
      6106230900: '陕西省_延安市_子长市_杨家园则镇',
      6106231000: '陕西省_延安市_子长市_余家坪镇',
      6106231100: '陕西省_延安市_子长市_史家畔乡',
      6106250000: '陕西省_延安市_志丹县',
      6106250100: '陕西省_延安市_志丹县_保安街道',
      6106250200: '陕西省_延安市_志丹县_旦八镇',
      6106250300: '陕西省_延安市_志丹县_金鼎镇',
      6106250400: '陕西省_延安市_志丹县_永宁镇',
      6106250500: '陕西省_延安市_志丹县_杏河镇',
      6106250600: '陕西省_延安市_志丹县_顺宁镇',
      6106250700: '陕西省_延安市_志丹县_义正镇',
      6106250800: '陕西省_延安市_志丹县_双河镇',
      6106260000: '陕西省_延安市_吴起县',
      6106260100: '陕西省_延安市_吴起县_吴起街道',
      6106260200: '陕西省_延安市_吴起县_周湾镇',
      6106260300: '陕西省_延安市_吴起县_白豹镇',
      6106260400: '陕西省_延安市_吴起县_长官庙镇',
      6106260500: '陕西省_延安市_吴起县_长城镇',
      6106260600: '陕西省_延安市_吴起县_铁边城镇',
      6106260700: '陕西省_延安市_吴起县_吴仓堡镇',
      6106260800: '陕西省_延安市_吴起县_庙沟镇',
      6106260900: '陕西省_延安市_吴起县_五谷城镇',
      6106270000: '陕西省_延安市_甘泉县',
      6106270100: '陕西省_延安市_甘泉县_美水街道',
      6106270200: '陕西省_延安市_甘泉县_下寺湾镇',
      6106270300: '陕西省_延安市_甘泉县_道镇',
      6106270400: '陕西省_延安市_甘泉县_石门镇',
      6106270500: '陕西省_延安市_甘泉县_桥镇乡',
      6106270600: '陕西省_延安市_甘泉县_劳山乡',
      6106280000: '陕西省_延安市_富县',
      6106280100: '陕西省_延安市_富县_茶坊街道',
      6106280200: '陕西省_延安市_富县_张村驿镇',
      6106280300: '陕西省_延安市_富县_张家湾镇',
      6106280400: '陕西省_延安市_富县_直罗镇',
      6106280500: '陕西省_延安市_富县_牛武镇',
      6106280600: '陕西省_延安市_富县_寺仙镇',
      6106280700: '陕西省_延安市_富县_羊泉镇',
      6106280800: '陕西省_延安市_富县_北道德乡',
      6106290000: '陕西省_延安市_洛川县',
      6106290100: '陕西省_延安市_洛川县_凤栖街道',
      6106290200: '陕西省_延安市_洛川县_城市社区服务中心',
      6106290300: '陕西省_延安市_洛川县_旧县镇',
      6106290400: '陕西省_延安市_洛川县_交口河镇',
      6106290500: '陕西省_延安市_洛川县_老庙镇',
      6106290600: '陕西省_延安市_洛川县_土基镇',
      6106290700: '陕西省_延安市_洛川县_石头镇',
      6106290800: '陕西省_延安市_洛川县_槐柏镇',
      6106290900: '陕西省_延安市_洛川县_永乡镇',
      6106291000: '陕西省_延安市_洛川县_菩堤镇',
      6106300000: '陕西省_延安市_宜川县',
      6106300100: '陕西省_延安市_宜川县_丹州街道',
      6106300200: '陕西省_延安市_宜川县_秋林镇',
      6106300300: '陕西省_延安市_宜川县_集义镇',
      6106300400: '陕西省_延安市_宜川县_云岩镇',
      6106300500: '陕西省_延安市_宜川县_壶口镇',
      6106300600: '陕西省_延安市_宜川县_英旺乡',
      6106300700: '陕西省_延安市_宜川县_交里乡',
      6106310000: '陕西省_延安市_黄龙县',
      6106310100: '陕西省_延安市_黄龙县_石堡镇',
      6106310200: '陕西省_延安市_黄龙县_白马滩镇',
      6106310300: '陕西省_延安市_黄龙县_瓦子街镇',
      6106310400: '陕西省_延安市_黄龙县_界头庙镇',
      6106310500: '陕西省_延安市_黄龙县_三岔镇',
      6106310600: '陕西省_延安市_黄龙县_圪台乡',
      6106310700: '陕西省_延安市_黄龙县_崾崄乡',
      6106320000: '陕西省_延安市_黄陵县',
      6106320100: '陕西省_延安市_黄陵县_桥山街道',
      6106320200: '陕西省_延安市_黄陵县_店头镇',
      6106320300: '陕西省_延安市_黄陵县_隆坊镇',
      6106320400: '陕西省_延安市_黄陵县_田庄镇',
      6106320500: '陕西省_延安市_黄陵县_阿党镇',
      6106320600: '陕西省_延安市_黄陵县_双龙镇',
      6107000000: '陕西省_汉中市',
      6107020000: '陕西省_汉中市_汉台区',
      6107020100: '陕西省_汉中市_汉台区_北关街道',
      6107020200: '陕西省_汉中市_汉台区_东大街街道',
      6107020300: '陕西省_汉中市_汉台区_汉中路街道',
      6107020400: '陕西省_汉中市_汉台区_中山街街道',
      6107020500: '陕西省_汉中市_汉台区_东关街道',
      6107020600: '陕西省_汉中市_汉台区_龙江街道',
      6107020700: '陕西省_汉中市_汉台区_鑫源街道',
      6107020800: '陕西省_汉中市_汉台区_七里街道',
      6107020900: '陕西省_汉中市_汉台区_铺镇',
      6107021000: '陕西省_汉中市_汉台区_武乡镇',
      6107021100: '陕西省_汉中市_汉台区_河东店镇',
      6107021200: '陕西省_汉中市_汉台区_宗营镇',
      6107021300: '陕西省_汉中市_汉台区_老君镇',
      6107021400: '陕西省_汉中市_汉台区_汉王镇',
      6107021500: '陕西省_汉中市_汉台区_徐望镇',
      6107030000: '陕西省_汉中市_南郑区',
      6107030100: '陕西省_汉中市_南郑区_汉山街道',
      6107030200: '陕西省_汉中市_南郑区_圣水镇',
      6107030300: '陕西省_汉中市_南郑区_大河坎镇',
      6107030400: '陕西省_汉中市_南郑区_协税镇',
      6107030500: '陕西省_汉中市_南郑区_梁山镇',
      6107030600: '陕西省_汉中市_南郑区_阳春镇',
      6107030700: '陕西省_汉中市_南郑区_高台镇',
      6107030800: '陕西省_汉中市_南郑区_新集镇',
      6107030900: '陕西省_汉中市_南郑区_濂水镇',
      6107031000: '陕西省_汉中市_南郑区_黄官镇',
      6107031100: '陕西省_汉中市_南郑区_青树镇',
      6107031200: '陕西省_汉中市_南郑区_红庙镇',
      6107031300: '陕西省_汉中市_南郑区_牟家坝镇',
      6107031400: '陕西省_汉中市_南郑区_法镇',
      6107031500: '陕西省_汉中市_南郑区_湘水镇',
      6107031600: '陕西省_汉中市_南郑区_小南海镇',
      6107031700: '陕西省_汉中市_南郑区_碑坝镇',
      6107031800: '陕西省_汉中市_南郑区_黎坪镇',
      6107031900: '陕西省_汉中市_南郑区_福成镇',
      6107032000: '陕西省_汉中市_南郑区_两河镇',
      6107032100: '陕西省_汉中市_南郑区_胡家营镇',
      6107220000: '陕西省_汉中市_城固县',
      6107220100: '陕西省_汉中市_城固县_莲花街道',
      6107220200: '陕西省_汉中市_城固县_博望街道',
      6107220300: '陕西省_汉中市_城固县_龙头镇',
      6107220400: '陕西省_汉中市_城固县_沙河营镇',
      6107220500: '陕西省_汉中市_城固县_文川镇',
      6107220600: '陕西省_汉中市_城固县_柳林镇',
      6107220700: '陕西省_汉中市_城固县_老庄镇',
      6107220800: '陕西省_汉中市_城固县_桔园镇',
      6107220900: '陕西省_汉中市_城固县_原公镇',
      6107221000: '陕西省_汉中市_城固县_上元观镇',
      6107221100: '陕西省_汉中市_城固县_天明镇',
      6107221200: '陕西省_汉中市_城固县_二里镇',
      6107221300: '陕西省_汉中市_城固县_五堵镇',
      6107221400: '陕西省_汉中市_城固县_双溪镇',
      6107221500: '陕西省_汉中市_城固县_小河镇',
      6107221600: '陕西省_汉中市_城固县_三合镇',
      6107221700: '陕西省_汉中市_城固县_董家营镇',
      6107230000: '陕西省_汉中市_洋县',
      6107230100: '陕西省_汉中市_洋县_洋州街道',
      6107230200: '陕西省_汉中市_洋县_戚氏街道',
      6107230300: '陕西省_汉中市_洋县_纸坊街道',
      6107230400: '陕西省_汉中市_洋县_龙亭镇',
      6107230500: '陕西省_汉中市_洋县_谢村镇',
      6107230600: '陕西省_汉中市_洋县_马畅镇',
      6107230700: '陕西省_汉中市_洋县_溢水镇',
      6107230800: '陕西省_汉中市_洋县_磨子桥镇',
      6107230900: '陕西省_汉中市_洋县_黄家营镇',
      6107231000: '陕西省_汉中市_洋县_黄安镇',
      6107231100: '陕西省_汉中市_洋县_黄金峡镇',
      6107231200: '陕西省_汉中市_洋县_槐树关镇',
      6107231300: '陕西省_汉中市_洋县_金水镇',
      6107231400: '陕西省_汉中市_洋县_华阳镇',
      6107231500: '陕西省_汉中市_洋县_茅坪镇',
      6107231600: '陕西省_汉中市_洋县_关帝镇',
      6107231700: '陕西省_汉中市_洋县_桑溪镇',
      6107231800: '陕西省_汉中市_洋县_八里关镇',
      6107240000: '陕西省_汉中市_西乡县',
      6107240100: '陕西省_汉中市_西乡县_城北街道',
      6107240200: '陕西省_汉中市_西乡县_城南街道',
      6107240300: '陕西省_汉中市_西乡县_杨河镇',
      6107240400: '陕西省_汉中市_西乡县_柳树镇',
      6107240500: '陕西省_汉中市_西乡县_沙河镇',
      6107240600: '陕西省_汉中市_西乡县_私渡镇',
      6107240700: '陕西省_汉中市_西乡县_桑园镇',
      6107240800: '陕西省_汉中市_西乡县_白龙塘镇',
      6107240900: '陕西省_汉中市_西乡县_峡口镇',
      6107241000: '陕西省_汉中市_西乡县_堰口镇',
      6107241100: '陕西省_汉中市_西乡县_茶镇',
      6107241200: '陕西省_汉中市_西乡县_高川镇',
      6107241300: '陕西省_汉中市_西乡县_两河口镇',
      6107241400: '陕西省_汉中市_西乡县_大河镇',
      6107241500: '陕西省_汉中市_西乡县_骆家坝镇',
      6107241600: '陕西省_汉中市_西乡县_子午镇',
      6107241700: '陕西省_汉中市_西乡县_白勉峡镇',
      6107250000: '陕西省_汉中市_勉县',
      6107250100: '陕西省_汉中市_勉县_勉阳街道',
      6107250200: '陕西省_汉中市_勉县_武侯镇',
      6107250300: '陕西省_汉中市_勉县_周家山镇',
      6107250400: '陕西省_汉中市_勉县_同沟寺镇',
      6107250500: '陕西省_汉中市_勉县_新街子镇',
      6107250600: '陕西省_汉中市_勉县_老道寺镇',
      6107250700: '陕西省_汉中市_勉县_褒城镇',
      6107250800: '陕西省_汉中市_勉县_金泉镇',
      6107250900: '陕西省_汉中市_勉县_定军山镇',
      6107251000: '陕西省_汉中市_勉县_温泉镇',
      6107251100: '陕西省_汉中市_勉县_元墩镇',
      6107251200: '陕西省_汉中市_勉县_阜川镇',
      6107251300: '陕西省_汉中市_勉县_新铺镇',
      6107251400: '陕西省_汉中市_勉县_茶店镇',
      6107251500: '陕西省_汉中市_勉县_镇川镇',
      6107251600: '陕西省_汉中市_勉县_长沟河镇',
      6107251700: '陕西省_汉中市_勉县_张家河镇',
      6107251800: '陕西省_汉中市_勉县_漆树坝镇',
      6107260000: '陕西省_汉中市_宁强县',
      6107260100: '陕西省_汉中市_宁强县_高寨子街道',
      6107260200: '陕西省_汉中市_宁强县_汉源街道',
      6107260300: '陕西省_汉中市_宁强县_大安镇',
      6107260400: '陕西省_汉中市_宁强县_代家坝镇',
      6107260500: '陕西省_汉中市_宁强县_阳平关镇',
      6107260600: '陕西省_汉中市_宁强县_燕子砭镇',
      6107260700: '陕西省_汉中市_宁强县_广坪镇',
      6107260800: '陕西省_汉中市_宁强县_青木川镇',
      6107260900: '陕西省_汉中市_宁强县_毛坝河镇',
      6107261000: '陕西省_汉中市_宁强县_铁锁关镇',
      6107261100: '陕西省_汉中市_宁强县_胡家坝镇',
      6107261200: '陕西省_汉中市_宁强县_巴山镇',
      6107261300: '陕西省_汉中市_宁强县_巨亭镇',
      6107261400: '陕西省_汉中市_宁强县_舒家坝镇',
      6107261500: '陕西省_汉中市_宁强县_太阳岭镇',
      6107261600: '陕西省_汉中市_宁强县_安乐河镇',
      6107261700: '陕西省_汉中市_宁强县_禅家岩镇',
      6107261800: '陕西省_汉中市_宁强县_二郎坝镇',
      6107270000: '陕西省_汉中市_略阳县',
      6107270100: '陕西省_汉中市_略阳县_兴州街道',
      6107270200: '陕西省_汉中市_略阳县_横现河街道',
      6107270300: '陕西省_汉中市_略阳县_接官亭镇',
      6107270400: '陕西省_汉中市_略阳县_两河口镇',
      6107270500: '陕西省_汉中市_略阳县_金家河镇',
      6107270600: '陕西省_汉中市_略阳县_徐家坪镇',
      6107270700: '陕西省_汉中市_略阳县_白水江镇',
      6107270800: '陕西省_汉中市_略阳县_硖口驿镇',
      6107270900: '陕西省_汉中市_略阳县_乐素河镇',
      6107271000: '陕西省_汉中市_略阳县_郭镇',
      6107271100: '陕西省_汉中市_略阳县_黑河镇',
      6107271200: '陕西省_汉中市_略阳县_白雀寺镇',
      6107271300: '陕西省_汉中市_略阳县_西淮坝镇',
      6107271400: '陕西省_汉中市_略阳县_五龙洞镇',
      6107271500: '陕西省_汉中市_略阳县_观音寺镇',
      6107271600: '陕西省_汉中市_略阳县_马蹄湾镇',
      6107271700: '陕西省_汉中市_略阳县_仙台坝镇',
      6107271800: '陕西省_汉中市_略阳县_何家岩镇',
      6107280000: '陕西省_汉中市_镇巴县',
      6107280100: '陕西省_汉中市_镇巴县_泾洋街道',
      6107280200: '陕西省_汉中市_镇巴县_渔渡镇',
      6107280300: '陕西省_汉中市_镇巴县_盐场镇',
      6107280400: '陕西省_汉中市_镇巴县_观音镇',
      6107280500: '陕西省_汉中市_镇巴县_巴庙镇',
      6107280600: '陕西省_汉中市_镇巴县_兴隆镇',
      6107280700: '陕西省_汉中市_镇巴县_长岭镇',
      6107280800: '陕西省_汉中市_镇巴县_三元镇',
      6107280900: '陕西省_汉中市_镇巴县_简池镇',
      6107281000: '陕西省_汉中市_镇巴县_碾子镇',
      6107281100: '陕西省_汉中市_镇巴县_小洋镇',
      6107281200: '陕西省_汉中市_镇巴县_青水镇',
      6107281300: '陕西省_汉中市_镇巴县_永乐镇',
      6107281400: '陕西省_汉中市_镇巴县_杨家河镇',
      6107281500: '陕西省_汉中市_镇巴县_赤南镇',
      6107281600: '陕西省_汉中市_镇巴县_巴山镇',
      6107281700: '陕西省_汉中市_镇巴县_大池镇',
      6107281800: '陕西省_汉中市_镇巴县_平安镇',
      6107281900: '陕西省_汉中市_镇巴县_仁村镇',
      6107282000: '陕西省_汉中市_镇巴县_黎坝镇',
      6107290000: '陕西省_汉中市_留坝县',
      6107290100: '陕西省_汉中市_留坝县_紫柏街道',
      6107290200: '陕西省_汉中市_留坝县_马道镇',
      6107290300: '陕西省_汉中市_留坝县_武关驿镇',
      6107290400: '陕西省_汉中市_留坝县_留侯镇',
      6107290500: '陕西省_汉中市_留坝县_江口镇',
      6107290600: '陕西省_汉中市_留坝县_青桥驿镇',
      6107290700: '陕西省_汉中市_留坝县_火烧店镇',
      6107290800: '陕西省_汉中市_留坝县_玉皇庙镇',
      6107300000: '陕西省_汉中市_佛坪县',
      6107300100: '陕西省_汉中市_佛坪县_袁家庄街道',
      6107300200: '陕西省_汉中市_佛坪县_陈家坝镇',
      6107300300: '陕西省_汉中市_佛坪县_大河坝镇',
      6107300400: '陕西省_汉中市_佛坪县_西岔河镇',
      6107300500: '陕西省_汉中市_佛坪县_长角坝镇',
      6107300600: '陕西省_汉中市_佛坪县_石墩河镇',
      6107300700: '陕西省_汉中市_佛坪县_岳坝镇',
      6108000000: '陕西省_榆林市',
      6108020000: '陕西省_榆林市_榆阳区',
      6108020100: '陕西省_榆林市_榆阳区_鼓楼街道',
      6108020200: '陕西省_榆林市_榆阳区_青山路街道',
      6108020300: '陕西省_榆林市_榆阳区_上郡路街道',
      6108020400: '陕西省_榆林市_榆阳区_新明楼街道',
      6108020500: '陕西省_榆林市_榆阳区_航宇路街道',
      6108020600: '陕西省_榆林市_榆阳区_崇文路街道',
      6108020700: '陕西省_榆林市_榆阳区_驼峰路街道',
      6108020800: '陕西省_榆林市_榆阳区_长城路街道',
      6108020900: '陕西省_榆林市_榆阳区_鱼河镇',
      6108021000: '陕西省_榆林市_榆阳区_上盐湾镇',
      6108021100: '陕西省_榆林市_榆阳区_镇川镇',
      6108021200: '陕西省_榆林市_榆阳区_麻黄梁镇',
      6108021300: '陕西省_榆林市_榆阳区_牛家梁镇',
      6108021400: '陕西省_榆林市_榆阳区_金鸡滩镇',
      6108021500: '陕西省_榆林市_榆阳区_马合镇',
      6108021600: '陕西省_榆林市_榆阳区_巴拉素镇',
      6108021700: '陕西省_榆林市_榆阳区_鱼河峁镇',
      6108021800: '陕西省_榆林市_榆阳区_大河塔镇',
      6108021900: '陕西省_榆林市_榆阳区_古塔镇',
      6108022000: '陕西省_榆林市_榆阳区_青云镇',
      6108022100: '陕西省_榆林市_榆阳区_小纪汗镇',
      6108022200: '陕西省_榆林市_榆阳区_芹河镇',
      6108022300: '陕西省_榆林市_榆阳区_孟家湾乡',
      6108022400: '陕西省_榆林市_榆阳区_小壕兔乡',
      6108022500: '陕西省_榆林市_榆阳区_岔河则乡',
      6108022600: '陕西省_榆林市_榆阳区_补浪河乡',
      6108022700: '陕西省_榆林市_榆阳区_红石桥乡',
      6108022800: '陕西省_榆林市_榆阳区_清泉',
      6108022900: '陕西省_榆林市_榆阳区_安崖',
      6108023000: '陕西省_榆林市_榆阳区_余兴庄',
      6108023100: '陕西省_榆林市_榆阳区_刘千河',
      6108030000: '陕西省_榆林市_横山区',
      6108030100: '陕西省_榆林市_横山区_城关街道',
      6108030200: '陕西省_榆林市_横山区_石湾镇',
      6108030300: '陕西省_榆林市_横山区_高镇',
      6108030400: '陕西省_榆林市_横山区_武镇',
      6108030500: '陕西省_榆林市_横山区_党岔镇',
      6108030600: '陕西省_榆林市_横山区_响水镇',
      6108030700: '陕西省_榆林市_横山区_波罗镇',
      6108030800: '陕西省_榆林市_横山区_殿市镇',
      6108030900: '陕西省_榆林市_横山区_塔湾镇',
      6108031000: '陕西省_榆林市_横山区_赵石畔镇',
      6108031100: '陕西省_榆林市_横山区_韩岔镇',
      6108031200: '陕西省_榆林市_横山区_魏家楼镇',
      6108031300: '陕西省_榆林市_横山区_雷龙湾镇',
      6108031400: '陕西省_榆林市_横山区_白界镇',
      6108240000: '陕西省_榆林市_靖边县',
      6108240100: '陕西省_榆林市_靖边县_张家畔街道',
      6108240200: '陕西省_榆林市_靖边县_东坑镇',
      6108240300: '陕西省_榆林市_靖边县_青阳岔镇',
      6108240400: '陕西省_榆林市_靖边县_宁条梁镇',
      6108240500: '陕西省_榆林市_靖边县_周河镇',
      6108240600: '陕西省_榆林市_靖边县_红墩界镇',
      6108240700: '陕西省_榆林市_靖边县_杨桥畔镇',
      6108240800: '陕西省_榆林市_靖边县_王渠则镇',
      6108240900: '陕西省_榆林市_靖边县_中山涧镇',
      6108241000: '陕西省_榆林市_靖边县_杨米涧镇',
      6108241100: '陕西省_榆林市_靖边县_天赐湾镇',
      6108241200: '陕西省_榆林市_靖边县_龙洲镇',
      6108241300: '陕西省_榆林市_靖边县_海则滩镇',
      6108241400: '陕西省_榆林市_靖边县_黄蒿界镇',
      6108241500: '陕西省_榆林市_靖边县_席麻湾镇',
      6108241600: '陕西省_榆林市_靖边县_小河镇',
      6108241700: '陕西省_榆林市_靖边县_镇靖镇',
      6108250000: '陕西省_榆林市_定边县',
      6108250100: '陕西省_榆林市_定边县_定边街道',
      6108250200: '陕西省_榆林市_定边县_贺圈镇',
      6108250300: '陕西省_榆林市_定边县_红柳沟镇',
      6108250400: '陕西省_榆林市_定边县_砖井镇',
      6108250500: '陕西省_榆林市_定边县_白泥井镇',
      6108250600: '陕西省_榆林市_定边县_安边镇',
      6108250700: '陕西省_榆林市_定边县_堆子梁镇',
      6108250800: '陕西省_榆林市_定边县_白湾子镇',
      6108250900: '陕西省_榆林市_定边县_姬塬镇',
      6108251000: '陕西省_榆林市_定边县_杨井镇',
      6108251100: '陕西省_榆林市_定边县_新安边镇',
      6108251200: '陕西省_榆林市_定边县_张崾先镇',
      6108251300: '陕西省_榆林市_定边县_樊学镇',
      6108251400: '陕西省_榆林市_定边县_盐场堡镇',
      6108251500: '陕西省_榆林市_定边县_郝滩镇',
      6108251600: '陕西省_榆林市_定边县_石洞沟镇',
      6108251700: '陕西省_榆林市_定边县_冯地坑镇',
      6108251800: '陕西省_榆林市_定边县_油房庄乡',
      6108251900: '陕西省_榆林市_定边县_学庄乡',
      6108260000: '陕西省_榆林市_绥德县',
      6108260100: '陕西省_榆林市_绥德县_薛家峁镇',
      6108260200: '陕西省_榆林市_绥德县_崔家湾镇',
      6108260300: '陕西省_榆林市_绥德县_定仙焉镇',
      6108260400: '陕西省_榆林市_绥德县_枣林坪镇',
      6108260500: '陕西省_榆林市_绥德县_义合镇',
      6108260600: '陕西省_榆林市_绥德县_吉镇',
      6108260700: '陕西省_榆林市_绥德县_薛家河镇',
      6108260800: '陕西省_榆林市_绥德县_石家湾镇',
      6108260900: '陕西省_榆林市_绥德县_田庄镇',
      6108261000: '陕西省_榆林市_绥德县_中角镇',
      6108261100: '陕西省_榆林市_绥德县_四十铺镇',
      6108261200: '陕西省_榆林市_绥德县_名州镇',
      6108261300: '陕西省_榆林市_绥德县_张家砭镇',
      6108261400: '陕西省_榆林市_绥德县_白家硷镇',
      6108261500: '陕西省_榆林市_绥德县_满堂川镇',
      6108261600: '陕西省_榆林市_绥德县_韭园',
      6108261700: '陕西省_榆林市_绥德县_辛店',
      6108270000: '陕西省_榆林市_米脂县',
      6108270100: '陕西省_榆林市_米脂县_银州街道',
      6108270200: '陕西省_榆林市_米脂县_桃镇',
      6108270300: '陕西省_榆林市_米脂县_龙镇',
      6108270400: '陕西省_榆林市_米脂县_杨家沟镇',
      6108270500: '陕西省_榆林市_米脂县_杜家石沟镇',
      6108270600: '陕西省_榆林市_米脂县_沙家店镇',
      6108270700: '陕西省_榆林市_米脂县_印斗镇',
      6108270800: '陕西省_榆林市_米脂县_郭兴庄镇',
      6108270900: '陕西省_榆林市_米脂县_城郊镇',
      6108280000: '陕西省_榆林市_佳县',
      6108280100: '陕西省_榆林市_佳县_佳州街道',
      6108280200: '陕西省_榆林市_佳县_坑镇',
      6108280300: '陕西省_榆林市_佳县_店镇',
      6108280400: '陕西省_榆林市_佳县_乌镇',
      6108280500: '陕西省_榆林市_佳县_金明寺镇',
      6108280600: '陕西省_榆林市_佳县_通镇',
      6108280700: '陕西省_榆林市_佳县_王家砭镇',
      6108280800: '陕西省_榆林市_佳县_方塌镇',
      6108280900: '陕西省_榆林市_佳县_朱官寨镇',
      6108281000: '陕西省_榆林市_佳县_朱家坬镇',
      6108281100: '陕西省_榆林市_佳县_螅镇',
      6108281200: '陕西省_榆林市_佳县_刘国具镇',
      6108281300: '陕西省_榆林市_佳县_木头峪镇',
      6108281400: '陕西省_榆林市_佳县_兴隆寺乡',
      6108281500: '陕西省_榆林市_佳县_刘山乡',
      6108281600: '陕西省_榆林市_佳县_官庄乡',
      6108281700: '陕西省_榆林市_佳县_上高寨乡',
      6108281800: '陕西省_榆林市_佳县_峪口乡',
      6108281900: '陕西省_榆林市_佳县_大佛寺乡',
      6108282000: '陕西省_榆林市_佳县_康家港乡',
      6108290000: '陕西省_榆林市_吴堡县',
      6108290100: '陕西省_榆林市_吴堡县_宋家川街道',
      6108290200: '陕西省_榆林市_吴堡县_辛家沟镇',
      6108290300: '陕西省_榆林市_吴堡县_郭家沟镇',
      6108290400: '陕西省_榆林市_吴堡县_寇家塬镇',
      6108290500: '陕西省_榆林市_吴堡县_岔上镇',
      6108290600: '陕西省_榆林市_吴堡县_张家山镇',
      6108290700: '陕西省_榆林市_吴堡县_薛下村',
      6108300000: '陕西省_榆林市_清涧县',
      6108300100: '陕西省_榆林市_清涧县_宽洲镇',
      6108300200: '陕西省_榆林市_清涧县_石咀驿镇',
      6108300300: '陕西省_榆林市_清涧县_折家坪镇',
      6108300400: '陕西省_榆林市_清涧县_玉家河镇',
      6108300500: '陕西省_榆林市_清涧县_高杰村镇',
      6108300600: '陕西省_榆林市_清涧县_李家塔镇',
      6108300700: '陕西省_榆林市_清涧县_店则沟镇',
      6108300800: '陕西省_榆林市_清涧县_解家沟镇',
      6108300900: '陕西省_榆林市_清涧县_下廿里铺镇',
      6108301000: '陕西省_榆林市_清涧县_秀延镇',
      6108301100: '陕西省_榆林市_清涧县_石盘乡',
      6108301200: '陕西省_榆林市_清涧县_老舍古乡',
      6108301300: '陕西省_榆林市_清涧县_双庙河乡',
      6108301400: '陕西省_榆林市_清涧县_乐堂堡乡',
      6108301500: '陕西省_榆林市_清涧县_郝家也乡',
      6108310000: '陕西省_榆林市_子洲县',
      6108310100: '陕西省_榆林市_子洲县_双湖峪街道',
      6108310200: '陕西省_榆林市_子洲县_何家集镇',
      6108310300: '陕西省_榆林市_子洲县_老君殿镇',
      6108310400: '陕西省_榆林市_子洲县_裴家湾镇',
      6108310500: '陕西省_榆林市_子洲县_苗家坪镇',
      6108310600: '陕西省_榆林市_子洲县_三川口镇',
      6108310700: '陕西省_榆林市_子洲县_马蹄沟镇',
      6108310800: '陕西省_榆林市_子洲县_周家硷镇',
      6108310900: '陕西省_榆林市_子洲县_电市镇',
      6108311000: '陕西省_榆林市_子洲县_砖庙镇',
      6108311100: '陕西省_榆林市_子洲县_淮宁湾镇',
      6108311200: '陕西省_榆林市_子洲县_马岔镇',
      6108311300: '陕西省_榆林市_子洲县_驼耳巷乡',
      6108311400: '陕西省_榆林市_子洲县_李孝河乡',
      6108311500: '陕西省_榆林市_子洲县_瓜则湾乡',
      6108311600: '陕西省_榆林市_子洲县_水地湾乡',
      6108311700: '陕西省_榆林市_子洲县_槐树岔乡',
      6108311800: '陕西省_榆林市_子洲县_高坪乡',
      6109000000: '陕西省_安康市',
      6109020000: '陕西省_安康市_汉滨区',
      6109020100: '陕西省_安康市_汉滨区_老城街道',
      6109020200: '陕西省_安康市_汉滨区_新城街道',
      6109020300: '陕西省_安康市_汉滨区_江北街道',
      6109020400: '陕西省_安康市_汉滨区_建民街道',
      6109020500: '陕西省_安康市_汉滨区_关庙镇',
      6109020600: '陕西省_安康市_汉滨区_张滩镇',
      6109020700: '陕西省_安康市_汉滨区_瀛湖镇',
      6109020800: '陕西省_安康市_汉滨区_五里镇',
      6109020900: '陕西省_安康市_汉滨区_恒口镇',
      6109021000: '陕西省_安康市_汉滨区_吉河镇',
      6109021100: '陕西省_安康市_汉滨区_流水镇',
      6109021200: '陕西省_安康市_汉滨区_大竹园镇',
      6109021300: '陕西省_安康市_汉滨区_洪山镇',
      6109021400: '陕西省_安康市_汉滨区_茨沟镇',
      6109021500: '陕西省_安康市_汉滨区_大河镇',
      6109021600: '陕西省_安康市_汉滨区_沈坝镇',
      6109021700: '陕西省_安康市_汉滨区_双龙镇',
      6109021800: '陕西省_安康市_汉滨区_叶坪镇',
      6109021900: '陕西省_安康市_汉滨区_中原镇',
      6109022000: '陕西省_安康市_汉滨区_早阳镇',
      6109022100: '陕西省_安康市_汉滨区_石梯镇',
      6109022200: '陕西省_安康市_汉滨区_关家镇',
      6109022300: '陕西省_安康市_汉滨区_县河镇',
      6109022400: '陕西省_安康市_汉滨区_晏坝镇',
      6109022500: '陕西省_安康市_汉滨区_谭坝镇',
      6109022600: '陕西省_安康市_汉滨区_坝河镇',
      6109022700: '陕西省_安康市_汉滨区_牛蹄镇',
      6109022800: '陕西省_安康市_汉滨区_紫荆镇',
      6109022900: '陕西省_安康市_汉滨区_大同镇',
      6109210000: '陕西省_安康市_汉阴县',
      6109210100: '陕西省_安康市_汉阴县_城关镇',
      6109210200: '陕西省_安康市_汉阴县_涧池镇',
      6109210300: '陕西省_安康市_汉阴县_蒲溪镇',
      6109210400: '陕西省_安康市_汉阴县_平梁镇',
      6109210500: '陕西省_安康市_汉阴县_双乳镇',
      6109210600: '陕西省_安康市_汉阴县_铁佛寺镇',
      6109210700: '陕西省_安康市_汉阴县_漩涡镇',
      6109210800: '陕西省_安康市_汉阴县_汉阳镇',
      6109210900: '陕西省_安康市_汉阴县_双河口镇',
      6109211000: '陕西省_安康市_汉阴县_观音河镇',
      6109220000: '陕西省_安康市_石泉县',
      6109220100: '陕西省_安康市_石泉县_城关镇',
      6109220200: '陕西省_安康市_石泉县_饶峰镇',
      6109220300: '陕西省_安康市_石泉县_两河镇',
      6109220400: '陕西省_安康市_石泉县_迎丰镇',
      6109220500: '陕西省_安康市_石泉县_池河镇',
      6109220600: '陕西省_安康市_石泉县_后柳镇',
      6109220700: '陕西省_安康市_石泉县_喜河镇',
      6109220800: '陕西省_安康市_石泉县_熨斗镇',
      6109220900: '陕西省_安康市_石泉县_云雾山镇',
      6109221000: '陕西省_安康市_石泉县_曾溪镇',
      6109221100: '陕西省_安康市_石泉县_中池镇',
      6109230000: '陕西省_安康市_宁陕县',
      6109230100: '陕西省_安康市_宁陕县_城关镇',
      6109230200: '陕西省_安康市_宁陕县_四亩地镇',
      6109230300: '陕西省_安康市_宁陕县_江口回族镇',
      6109230400: '陕西省_安康市_宁陕县_广货街镇',
      6109230500: '陕西省_安康市_宁陕县_龙王镇',
      6109230600: '陕西省_安康市_宁陕县_筒车湾镇',
      6109230700: '陕西省_安康市_宁陕县_金川镇',
      6109230800: '陕西省_安康市_宁陕县_皇冠镇',
      6109230900: '陕西省_安康市_宁陕县_梅子镇',
      6109231000: '陕西省_安康市_宁陕县_新场镇',
      6109231100: '陕西省_安康市_宁陕县_太山庙镇',
      6109231200: '陕西省_安康市_宁陕县_广货街镇丰富村',
      6109240000: '陕西省_安康市_紫阳县',
      6109240100: '陕西省_安康市_紫阳县_城关镇',
      6109240200: '陕西省_安康市_紫阳县_蒿坪镇',
      6109240300: '陕西省_安康市_紫阳县_汉王镇',
      6109240400: '陕西省_安康市_紫阳县_焕古镇',
      6109240500: '陕西省_安康市_紫阳县_向阳镇',
      6109240600: '陕西省_安康市_紫阳县_洞河镇',
      6109240700: '陕西省_安康市_紫阳县_洄水镇',
      6109240800: '陕西省_安康市_紫阳县_双桥镇',
      6109240900: '陕西省_安康市_紫阳县_高桥镇',
      6109241000: '陕西省_安康市_紫阳县_红椿镇',
      6109241100: '陕西省_安康市_紫阳县_高滩镇',
      6109241200: '陕西省_安康市_紫阳县_毛坝镇',
      6109241300: '陕西省_安康市_紫阳县_瓦庙镇',
      6109241400: '陕西省_安康市_紫阳县_麻柳镇',
      6109241500: '陕西省_安康市_紫阳县_双安镇',
      6109241600: '陕西省_安康市_紫阳县_东木镇',
      6109241700: '陕西省_安康市_紫阳县_界岭镇',
      6109250000: '陕西省_安康市_岚皋县',
      6109250100: '陕西省_安康市_岚皋县_城关镇',
      6109250200: '陕西省_安康市_岚皋县_佐龙镇',
      6109250300: '陕西省_安康市_岚皋县_滔河镇',
      6109250400: '陕西省_安康市_岚皋县_官元镇',
      6109250500: '陕西省_安康市_岚皋县_石门镇',
      6109250600: '陕西省_安康市_岚皋县_民主镇',
      6109250700: '陕西省_安康市_岚皋县_大道河镇',
      6109250800: '陕西省_安康市_岚皋县_蔺河镇',
      6109250900: '陕西省_安康市_岚皋县_四季镇',
      6109251000: '陕西省_安康市_岚皋县_孟石岭镇',
      6109251100: '陕西省_安康市_岚皋县_堰门镇',
      6109251200: '陕西省_安康市_岚皋县_南宫山镇',
      6109260000: '陕西省_安康市_平利县',
      6109260100: '陕西省_安康市_平利县_城关镇',
      6109260200: '陕西省_安康市_平利县_兴隆镇',
      6109260300: '陕西省_安康市_平利县_老县镇',
      6109260400: '陕西省_安康市_平利县_大贵镇',
      6109260500: '陕西省_安康市_平利县_三阳镇',
      6109260600: '陕西省_安康市_平利县_洛河镇',
      6109260700: '陕西省_安康市_平利县_广佛镇',
      6109260800: '陕西省_安康市_平利县_八仙镇',
      6109260900: '陕西省_安康市_平利县_长安镇',
      6109261000: '陕西省_安康市_平利县_西河镇',
      6109261100: '陕西省_安康市_平利县_正阳镇',
      6109270000: '陕西省_安康市_镇坪县',
      6109270100: '陕西省_安康市_镇坪县_城关镇',
      6109270200: '陕西省_安康市_镇坪县_牛头店镇',
      6109270300: '陕西省_安康市_镇坪县_钟宝镇',
      6109270400: '陕西省_安康市_镇坪县_上竹镇',
      6109270500: '陕西省_安康市_镇坪县_华坪镇',
      6109270600: '陕西省_安康市_镇坪县_曾家镇',
      6109270700: '陕西省_安康市_镇坪县_曙坪镇',
      6109280000: '陕西省_安康市_旬阳市',
      6109280100: '陕西省_安康市_旬阳市_城关镇',
      6109280200: '陕西省_安康市_旬阳市_棕溪镇',
      6109280300: '陕西省_安康市_旬阳市_关口镇',
      6109280400: '陕西省_安康市_旬阳市_蜀河镇',
      6109280500: '陕西省_安康市_旬阳市_双河镇',
      6109280600: '陕西省_安康市_旬阳市_小河镇',
      6109280700: '陕西省_安康市_旬阳市_赵湾镇',
      6109280800: '陕西省_安康市_旬阳市_麻坪镇',
      6109280900: '陕西省_安康市_旬阳市_甘溪镇',
      6109281000: '陕西省_安康市_旬阳市_白柳镇',
      6109281100: '陕西省_安康市_旬阳市_吕河镇',
      6109281200: '陕西省_安康市_旬阳市_神河镇',
      6109281300: '陕西省_安康市_旬阳市_段家河镇',
      6109281400: '陕西省_安康市_旬阳市_金寨镇',
      6109281500: '陕西省_安康市_旬阳市_桐木镇',
      6109281600: '陕西省_安康市_旬阳市_仙河镇',
      6109281700: '陕西省_安康市_旬阳市_构元镇',
      6109281800: '陕西省_安康市_旬阳市_石门镇',
      6109281900: '陕西省_安康市_旬阳市_红军镇',
      6109282000: '陕西省_安康市_旬阳市_仁河口镇',
      6109282100: '陕西省_安康市_旬阳市_铜钱关镇',
      6109282200: '陕西省_安康市_旬阳市_赤岩镇',
      6109290000: '陕西省_安康市_白河县',
      6109290100: '陕西省_安康市_白河县_城关镇',
      6109290200: '陕西省_安康市_白河县_中厂镇',
      6109290300: '陕西省_安康市_白河县_构扒镇',
      6109290400: '陕西省_安康市_白河县_卡子镇',
      6109290500: '陕西省_安康市_白河县_茅坪镇',
      6109290600: '陕西省_安康市_白河县_宋家镇',
      6109290700: '陕西省_安康市_白河县_西营镇',
      6109290800: '陕西省_安康市_白河县_仓上镇',
      6109290900: '陕西省_安康市_白河县_双丰镇',
      6109291000: '陕西省_安康市_白河县_麻虎镇',
      6109291100: '陕西省_安康市_白河县_冷水镇',
      6110000000: '陕西省_商洛市',
      6110020000: '陕西省_商洛市_商州区',
      6110020100: '陕西省_商洛市_商州区_城关街道',
      6110020200: '陕西省_商洛市_商州区_大赵峪街道',
      6110020300: '陕西省_商洛市_商州区_陈塬街道',
      6110020400: '陕西省_商洛市_商州区_刘湾街道',
      6110020500: '陕西省_商洛市_商州区_夜村镇',
      6110020600: '陕西省_商洛市_商州区_沙河子镇',
      6110020700: '陕西省_商洛市_商州区_杨峪河镇',
      6110020800: '陕西省_商洛市_商州区_金陵寺镇',
      6110020900: '陕西省_商洛市_商州区_黑山镇',
      6110021000: '陕西省_商洛市_商州区_杨斜镇',
      6110021100: '陕西省_商洛市_商州区_麻街镇',
      6110021200: '陕西省_商洛市_商州区_牧护关镇',
      6110021300: '陕西省_商洛市_商州区_大荆镇',
      6110021400: '陕西省_商洛市_商州区_腰市镇',
      6110021500: '陕西省_商洛市_商州区_板桥镇',
      6110021600: '陕西省_商洛市_商州区_北宽坪镇',
      6110021700: '陕西省_商洛市_商州区_三岔河镇',
      6110021800: '陕西省_商洛市_商州区_闫村镇',
      6110210000: '陕西省_商洛市_洛南县',
      6110210100: '陕西省_商洛市_洛南县_城关街道',
      6110210200: '陕西省_商洛市_洛南县_四皓街道',
      6110210300: '陕西省_商洛市_洛南县_景村镇',
      6110210400: '陕西省_商洛市_洛南县_古城镇',
      6110210500: '陕西省_商洛市_洛南县_三要镇',
      6110210600: '陕西省_商洛市_洛南县_灵口镇',
      6110210700: '陕西省_商洛市_洛南县_寺耳镇',
      6110210800: '陕西省_商洛市_洛南县_巡检镇',
      6110210900: '陕西省_商洛市_洛南县_石坡镇',
      6110211000: '陕西省_商洛市_洛南县_石门镇',
      6110211100: '陕西省_商洛市_洛南县_麻坪镇',
      6110211200: '陕西省_商洛市_洛南县_洛源镇',
      6110211300: '陕西省_商洛市_洛南县_保安镇',
      6110211400: '陕西省_商洛市_洛南县_永丰镇',
      6110211500: '陕西省_商洛市_洛南县_高耀镇',
      6110211600: '陕西省_商洛市_洛南县_柏峪寺镇',
      6110220000: '陕西省_商洛市_丹凤县',
      6110220100: '陕西省_商洛市_丹凤县_龙驹寨街道',
      6110220200: '陕西省_商洛市_丹凤县_庾岭镇',
      6110220300: '陕西省_商洛市_丹凤县_蔡川镇',
      6110220400: '陕西省_商洛市_丹凤县_峦庄镇',
      6110220500: '陕西省_商洛市_丹凤县_铁峪铺镇',
      6110220600: '陕西省_商洛市_丹凤县_武关镇',
      6110220700: '陕西省_商洛市_丹凤县_竹林关镇',
      6110220800: '陕西省_商洛市_丹凤县_土门镇',
      6110220900: '陕西省_商洛市_丹凤县_寺坪镇',
      6110221000: '陕西省_商洛市_丹凤县_商镇',
      6110221100: '陕西省_商洛市_丹凤县_棣花镇',
      6110221200: '陕西省_商洛市_丹凤县_花瓶子镇',
      6110230000: '陕西省_商洛市_商南县',
      6110230100: '陕西省_商洛市_商南县_城关街道',
      6110230200: '陕西省_商洛市_商南县_富水镇',
      6110230300: '陕西省_商洛市_商南县_湘河镇',
      6110230400: '陕西省_商洛市_商南县_赵川镇',
      6110230500: '陕西省_商洛市_商南县_金丝峡镇',
      6110230600: '陕西省_商洛市_商南县_过风楼镇',
      6110230700: '陕西省_商洛市_商南县_试马镇',
      6110230800: '陕西省_商洛市_商南县_清油河镇',
      6110230900: '陕西省_商洛市_商南县_十里坪镇',
      6110231000: '陕西省_商洛市_商南县_青山镇',
      6110240000: '陕西省_商洛市_山阳县',
      6110240100: '陕西省_商洛市_山阳县_城关街道',
      6110240200: '陕西省_商洛市_山阳县_十里铺街道',
      6110240300: '陕西省_商洛市_山阳县_高坝店镇',
      6110240400: '陕西省_商洛市_山阳县_天竺山镇',
      6110240500: '陕西省_商洛市_山阳县_中村镇',
      6110240600: '陕西省_商洛市_山阳县_银花镇',
      6110240700: '陕西省_商洛市_山阳县_西照川镇',
      6110240800: '陕西省_商洛市_山阳县_漫川关镇',
      6110240900: '陕西省_商洛市_山阳县_南宽坪镇',
      6110241000: '陕西省_商洛市_山阳县_户家塬镇',
      6110241100: '陕西省_商洛市_山阳县_杨地镇',
      6110241200: '陕西省_商洛市_山阳县_小河口镇',
      6110241300: '陕西省_商洛市_山阳县_色河铺镇',
      6110241400: '陕西省_商洛市_山阳县_板岩镇',
      6110241500: '陕西省_商洛市_山阳县_延坪镇',
      6110241600: '陕西省_商洛市_山阳县_两岭镇',
      6110241700: '陕西省_商洛市_山阳县_王阎镇',
      6110241800: '陕西省_商洛市_山阳县_法官镇',
      6110250000: '陕西省_商洛市_镇安县',
      6110250100: '陕西省_商洛市_镇安县_永乐街道',
      6110250200: '陕西省_商洛市_镇安县_回龙镇',
      6110250300: '陕西省_商洛市_镇安县_铁厂镇',
      6110250400: '陕西省_商洛市_镇安县_大坪镇',
      6110250500: '陕西省_商洛市_镇安县_米粮镇',
      6110250600: '陕西省_商洛市_镇安县_茅坪回族镇',
      6110250700: '陕西省_商洛市_镇安县_西口回族镇',
      6110250800: '陕西省_商洛市_镇安县_高峰镇',
      6110250900: '陕西省_商洛市_镇安县_青铜关镇',
      6110251000: '陕西省_商洛市_镇安县_柴坪镇',
      6110251100: '陕西省_商洛市_镇安县_达仁镇',
      6110251200: '陕西省_商洛市_镇安县_木王镇',
      6110251300: '陕西省_商洛市_镇安县_云盖寺镇',
      6110251400: '陕西省_商洛市_镇安县_庙沟镇',
      6110251500: '陕西省_商洛市_镇安县_月河镇',
      6110260000: '陕西省_商洛市_柞水县',
      6110260100: '陕西省_商洛市_柞水县_乾佑街道',
      6110260200: '陕西省_商洛市_柞水县_营盘镇',
      6110260300: '陕西省_商洛市_柞水县_下梁镇',
      6110260400: '陕西省_商洛市_柞水县_小岭镇',
      6110260500: '陕西省_商洛市_柞水县_凤凰镇',
      6110260600: '陕西省_商洛市_柞水县_红岩寺镇',
      6110260700: '陕西省_商洛市_柞水县_曹坪镇',
      6110260800: '陕西省_商洛市_柞水县_杏坪镇',
      6110260900: '陕西省_商洛市_柞水县_瓦房口镇',
      6110261000: '陕西省_商洛市_柞水县_蔡玉窑镇',
      6110261100: '陕西省_商洛市_柞水县_石瓮镇',
      6110261200: '陕西省_商洛市_柞水县_柴庄乡',
      6110261300: '陕西省_商洛市_柞水县_丰北河乡',
      6111000000: '陕西省_杨凌区',
      6111010000: '陕西省_杨凌区_杨陵区',
      6111010100: '陕西省_杨凌区_杨陵区_杨陵街道',
      6111010200: '陕西省_杨凌区_杨陵区_大寨街道',
      6111010300: '陕西省_杨凌区_杨陵区_李台街道',
      6111010400: '陕西省_杨凌区_杨陵区_五泉镇',
      6111010500: '陕西省_杨凌区_杨陵区_揉谷镇',
      6190000000: '陕西省_省直辖县级行政区划',
      6190210000: '陕西省_省直辖县级行政区划_神木市',
      6190210100: '陕西省_省直辖县级行政区划_神木市_西沙街道',
      6190210200: '陕西省_省直辖县级行政区划_神木市_花石崖',
      6190210300: '陕西省_省直辖县级行政区划_神木市_西沟街道',
      6190210400: '陕西省_省直辖县级行政区划_神木市_迎宾路街道',
      6190210500: '陕西省_省直辖县级行政区划_神木市_永兴街道',
      6190210600: '陕西省_省直辖县级行政区划_神木市_滨河新区街道',
      6190210700: '陕西省_省直辖县级行政区划_神木市_麟州街道',
      6190210800: '陕西省_省直辖县级行政区划_神木市_大柳塔镇',
      6190210900: '陕西省_省直辖县级行政区划_神木市_店塔镇',
      6190211000: '陕西省_省直辖县级行政区划_神木市_孙家岔镇',
      6190211100: '陕西省_省直辖县级行政区划_神木市_高家堡镇',
      6190211200: '陕西省_省直辖县级行政区划_神木市_大保当镇',
      6190211300: '陕西省_省直辖县级行政区划_神木市_万镇',
      6190211400: '陕西省_省直辖县级行政区划_神木市_贺家川镇',
      6190211500: '陕西省_省直辖县级行政区划_神木市_沙峁镇',
      6190211600: '陕西省_省直辖县级行政区划_神木市_栏杆堡镇',
      6190211700: '陕西省_省直辖县级行政区划_神木市_乔岔滩镇',
      6190211800: '陕西省_省直辖县级行政区划_神木市_中鸡镇',
      6190211900: '陕西省_省直辖县级行政区划_神木市_尔林兔镇',
      6190212000: '陕西省_省直辖县级行政区划_神木市_锦界镇',
      6190212100: '陕西省_省直辖县级行政区划_神木市_马镇',
      6190212200: '陕西省_省直辖县级行政区划_神木市_太和寨镇',
      6190220000: '陕西省_省直辖县级行政区划_府谷县',
      6190220100: '陕西省_省直辖县级行政区划_府谷县_清水镇',
      6190220200: '陕西省_省直辖县级行政区划_府谷县_孤山镇',
      6190220300: '陕西省_省直辖县级行政区划_府谷县_新民镇',
      6190220400: '陕西省_省直辖县级行政区划_府谷县_三道沟镇',
      6190220500: '陕西省_省直辖县级行政区划_府谷县_老高川镇',
      6190220600: '陕西省_省直辖县级行政区划_府谷县_府谷镇',
      6190220700: '陕西省_省直辖县级行政区划_府谷县_古城镇',
      6190220800: '陕西省_省直辖县级行政区划_府谷县_田家寨镇',
      6190220900: '陕西省_省直辖县级行政区划_府谷县_麻镇',
      6190221000: '陕西省_省直辖县级行政区划_府谷县_黄甫镇',
      6190221100: '陕西省_省直辖县级行政区划_府谷县_哈镇',
      6190221200: '陕西省_省直辖县级行政区划_府谷县_大昌汗镇',
      6190221300: '陕西省_省直辖县级行政区划_府谷县_庙沟门镇',
      6190221400: '陕西省_省直辖县级行政区划_府谷县_木瓜镇',
      6190221500: '陕西省_省直辖县级行政区划_府谷县_武家庄镇',
      6190221600: '陕西省_省直辖县级行政区划_府谷县_墙头乡',
      6190221700: '陕西省_省直辖县级行政区划_府谷县_赵五湾乡',
      6190221800: '陕西省_省直辖县级行政区划_府谷县_海则庙乡',
      6190221900: '陕西省_省直辖县级行政区划_府谷县_碛塄乡',
      6190222000: '陕西省_省直辖县级行政区划_府谷县_王家墩乡',
      6190810000: '陕西省_省直辖县级行政区划_韩城市',
      6190810100: '陕西省_省直辖县级行政区划_韩城市_新城街道',
      6190810200: '陕西省_省直辖县级行政区划_韩城市_金城街道',
      6190810300: '陕西省_省直辖县级行政区划_韩城市_西庄镇',
      6190810400: '陕西省_省直辖县级行政区划_韩城市_昝村镇',
      6190810500: '陕西省_省直辖县级行政区划_韩城市_龙门镇',
      6190810600: '陕西省_省直辖县级行政区划_韩城市_桑树坪镇',
      6190810700: '陕西省_省直辖县级行政区划_韩城市_王峰镇',
      6190810800: '陕西省_省直辖县级行政区划_韩城市_嵬东镇',
      6190810900: '陕西省_省直辖县级行政区划_韩城市_芝川镇',
      6190811000: '陕西省_省直辖县级行政区划_韩城市_芝阳镇',
      6190811100: '陕西省_省直辖县级行政区划_韩城市_板桥镇',
      6190811200: '陕西省_省直辖县级行政区划_韩城市_龙亭镇',
    };
    return obj[value].split('_') || [];
  }

  /**
   * 获取婚姻状况
   * @param {string} value
   * @return {String} 婚姻状况
   */
  getMarriage(value) {
    const obj = {
      0: '未婚',
      1: '已婚',
      3: '丧偶',
      2: '离异',
      4: '其他',
    };
    return obj[value] || '';
  }

  /** 处理疑似职业病内容 3.13
   * @param value 编码
   * @return {string}
   */
  getSuspectedOccupationalDisease(value) {
    // value转换为数字
    value = +value;
    const codeObj = {
      10009: '职业性慢性铅中毒',
      10013: '职业性急性四乙基铅中毒',
      10016: '职业性急性砷化氢中毒',
      10024: '职业性慢性磷中毒',
      10025: '职业性急性磷中毒',
      10026: '职业性黄磷皮肤灼伤',
      10029: '职业性慢性汞中毒',
      10034: '职业性急性磷化氢中毒',
      10037: '职业性慢性锰中毒',
      10038: '职业性急性钡中毒',
      10040: '职业性急性钒中毒',
      10045: '职业性慢性铊中毒',
      10046: '职业性急性铊中毒',
      10047: '职业性急性羰基镍中毒',
      10050: '工业性氟病',
      10053: '职业性慢性苯中毒',
      10054: '职业性苯所致白血病',
      10055: '职业性急性苯中毒',
      10063: '职业性慢性二硫化碳中毒',
      10064: '职业性中毒性肝病',
      10065: '职业性急性四氯化碳中毒',
      10066: '职业性急性甲醇中毒',
      10069: '职业性溶剂汽油中毒（慢性）',
      10070: '汽油致职业性皮肤病',
      10072: '职业性急性溴甲烷中毒',
      10074: '职业性慢性正己烷中毒',
      10077: '职业性急性苯的氨基或硝基化合物中毒',
      10079: '职业性慢性三硝基甲苯中毒',
      10080: '职业性三硝基甲苯致白内障',
      10084: '联苯胺所致膀胱癌',
      10085: '职业性接触性皮炎',
      10086: '职业性急性氯气中毒',
      10089: '职业性急性氮氧化物中毒',
      10091: '职业性急性氨气中毒',
      10092: '职业性慢性镉中毒',
      10093: '职业性急性镉中毒',
      10094: '金属烟热',
      10095: '职业性铬鼻病',
      10096: '职业性铬溃疡',
      10097: '职业性铬所致皮炎',
      10098: '职业性铬酸盐制造业工人肺癌',
      10099: '职业性慢性砷中毒',
      10100: '职业性砷所致肺癌、皮肤癌',
      10101: '职业性慢性丙烯酰胺中毒',
      10102: '职业性急性偏二甲基肼中毒',
      10103: '职业性急性光气中毒',
      10104: '职业性急性甲醛中毒',
      10105: '职业性急性一甲胺中毒',
      10106: '职业性急性一氧化碳中毒',
      10109: '职业性急性硫化氢中毒',
      10111: '职业性慢性氯乙烯中毒',
      10112: '氯乙烯所致肝血管肉瘤',
      10113: '职业性急性硫酸二甲酯中毒',
      10114: '职业性急性氯乙烯中毒',
      10115: '职业性急性有机磷杀虫剂中毒',
      10116: '职业性急性三氯乙烯中毒',
      10117: '职业性急性氨基甲酸酯杀虫剂中毒',
      10119: '职业性急性拟除虫菊酯中毒',
      10120: '职业性慢性氯丙烯中毒',
      10121: '职业性哮喘',
      10122: '职业性慢性氯丁二烯中毒',
      10123: '职业性急性氯丁二烯中毒',
      10124: '职业性急性有机氟中毒',
      10125: '职业性急性二甲基甲酰胺中毒',
      10126: '职业性急性氰化物中毒',
      10127: '职业性急性腈类化合物中毒',
      10129: '职业性急性酚中毒',
      10130: '职业性酚皮肤灼伤',
      10132: '职业性急性五氯酚中毒',
      10133: '职业性氯甲醚所致肺癌',
      10137: '矽肺',
      10138: '煤工尘肺',
      10140: '石棉肺',
      10141: '石棉所致肺癌、间皮瘤',
      10142: '石墨尘肺',
      10143: '炭黑尘肺',
      10144: '滑石尘肺',
      10145: '水泥尘肺',
      10146: '云母尘肺',
      10147: '陶工尘肺',
      10148: '铝尘肺',
      10149: '电焊工尘肺',
      10150: '铸工尘肺',
      10151: '棉尘病',
      10159: '职业性手臂振动病',
      10164: '职业性中暑',
      10190: '减压性骨坏死',
      10196: '职业性电光性皮炎',
      10197: '职业性白内障',
      10228: '职业性慢性高原病',
      10229: '职业性航空病',
      10230: '职业性急性汞中毒',
      10232: '职业性急性1，2-二氯乙烷中毒',
      10233: '职业性化学性眼灼伤',
      10234: '职业性牙酸蚀病',
      10235: '职业性皮肤灼伤',
      10237: '职业性布氏杆菌病',
      10238: '职业性炭疽',
      10239: '职业性急性二氧化硫中毒',
      10302: '职业性慢性铍病',
      10303: '职业性铍接触性皮炎',
      10304: '职业性铍溃疡',
      10305: '职业性急性铍病',
      10306: '职业性急性三烷基锡中毒',
      10307: '职业性慢性中毒性肝病',
      10308: '职业性慢性溶剂汽油中毒',
      10309: '职业性急性溶剂汽油中毒',
      10310: '腕管综合征',
      10311: '颈肩腕综合征',
      10312: '肺结核',
      10313: '慢性肝病',
      10314: '职业性急性电光性眼炎（紫外线角膜结膜炎）',
      10315: '职业性急性电光性皮炎',
      10316: '职业性刺激性化学物致慢性阻塞性肺疾病',
      10317: '职业性化学性眼部灼伤',
      10318: '职业性化学性皮肤灼伤',
      10319: '甲醛致职业性皮肤病',
      10320: '职业性三氯乙烯药疹样皮炎',
      10321: '职业性急性化学物中毒性呼吸系统疾病',
      10322: '职业性焦炉逸散物所致肺癌',
      10323: '焦炉逸散物所致职业性皮肤病',
      10324: '职业性急性变应性肺泡炎',
      10325: '职业性噪声聋',
      10326: '职业性爆震聋',
      10327: '急性电光性眼炎(紫外线角膜、结膜炎)',
      10328: '电光性皮炎',
      10329: '职业性炭疸',
      10330: '中枢神经系统器质性疾病',
      10331: '急性高原病',
      10332: '甲醛所致职业性哮喘',
      10333: '急性减压病',
      10366: '职业性金属及其化合物粉尘肺沉着病',
      10367: '职业性硬金属肺病',
      10368: '职业性耳鼻喉口腔疾病',
      10369: '尘肺',
      10370: '外照射急性放射病',
      10371: '外照射亚急性放射病',
      10373: '外照射慢性放射病',
      10374: '内照射放射病',
      10375: '放射性皮肤疾病',
      10376: '放射性白内障',
      10377: '放射性肿瘤',
      10378: '放射性骨损伤',
      10379: '放射性甲状腺疾病',
      10380: '放射性性腺疾病',
      10381: '放射复合伤',
      10382: '根据《放射性疾病诊断总则》可以诊断的其他放射性损伤',
      10383: '毛沸石所致肺癌',
      10384: '胸膜间皮瘤',
      10385: '金属及其化合物粉尘（锡、铁、锑、钡及其化合物等）肺沉着病',
      10386: '职业性煤焦油、煤焦油沥青、石油沥青所致皮肤癌',
      10387: '职业性皮肤病',
      10388: 'β-萘胺所致膀胱癌',
      10389: '职业性急性苯的氨基或硝基化合物（β-萘胺）中毒',
    };
    return codeObj[value] || '';
  }

  /** 处理禁忌证内容 3.14
   * @param value 编码
   * @return {string} 禁忌证内容
   */
  getOccupationalContraindication(value) {
    // value转换为数字
    value = +value;
    const codeObj = {
      10007: '卟啉病',
      10008: '多发性周围神经病',
      10009: '贫血',
      10012: '中枢神经系统器质性疾病',
      10017: '牙本质病变（不包括龋齿）',
      10018: '下颌骨疾病',
      10019: '慢性肝炎',
      10020: '慢性肾炎',
      10022: '慢性肾脏疾病',
      10025: '慢性阻塞性肺病',
      10026: '慢性间质性肺病',
      10028: '钾代谢障碍',
      10031: '支气管哮喘',
      10034: '地方性氟病',
      10035: '骨关节疾病',
      10037: '活动性肺结核',
      10039: '视网膜病变',
      10040: '过敏性皮肤病',
      10041: '神经系统器质性疾病',
      10042: '血清葡萄糖-6-磷酸脱氢酶缺乏症',
      10045: '尿脱落细胞检查巴氏分级国际标准Ⅳ级及以上',
      10055: '器质性心脏病',
      10056: '类风湿关节炎',
      10057: '全血胆碱酯酶活性明显低于正常者',
      10058: '致喘物过敏和支气管哮喘',
      10060: '伴肺功能损害的心血管系统疾病',
      10063: '活动性肺结核病',
      10064: '伴肺功能损害的疾病',
      10070: '雷诺病',
      10097: '加压试验不合格或氧敏感试验阳性者',
      10099: '活动性角膜疾病',
      10100: '白内障',
      10101: '面、手背和前臂等暴露部位严重的皮肤病',
      10102: '白化病',
      10103: '癫痫',
      10105: '红绿色盲',
      10107: '四肢关节运动功能障碍',
      10108: '高血压',
      10109: '恐高症',
      10111: '四肢骨关节及运动功能障碍',
      10113: '未治愈的肺结核病',
      10117: '暗适应：＞30s',
      10118: '复视、立体盲、严重视野缺损',
      10119: '梅尼埃病',
      10120: '眩晕',
      10121: '癔病',
      10122: '震颤麻痹',
      10124: '痴呆',
      10125: '影响肢体活动的神经系统疾病',
      10127: '腕管综合征',
      10128: '颈椎病',
      10129: '矫正视力小于4.5',
      10130: '红细胞增多症',
      10138: '红或绿色盲',
      10201: '中度贫血',
      10202: '已确诊并仍需要医学监护的精神障碍性疾病',
      10203: '慢性皮肤溃疡',
      10204: '慢性肾脏病',
      10205: '骨质疏松症',
      10206: '萎缩性鼻炎',
      10207: '未控制的甲状腺功能亢进症',
      10208: '严重慢性皮肤疾病',
      10209: '慢性肝病',
      10210: '慢性器质性心脏病',
      10211: '视网膜及视神经病',
      10212: '呼吸系统疾病史及有关症状',
      10213:
        '血常规检出有如下异常者：白细胞计数低于4.5×10^9/L； 血小板计数低于8×10^10/L；  红细胞计数男性低于4×10^12/L，女性低于3.5×10^12/L或血红蛋白定',
      10214: '血系统疾病',
      10215: '严重慢性皮肤疾患',
      10216: '伴有气道高反应的过敏性鼻炎',
      10217: '伴气道高反应的过敏性鼻炎',
      10218: '严重的皮肤疾病',
      10219: '牙酸蚀病',
      10220:
        '各种原因引起永久性感音神经性听力损失（500Hz、1000Hz和2000Hz中任一频率的纯音气导听阈＞25dB）',
      10221: '任一耳传导性耳聋，平均语频听力损失≥41dB',
      10222: '高频段3000Hz，4000Hz，6000Hz双耳平均听阈≥40dB',
      10223:
        '噪声敏感者（上岗前职业健康体检纯音听力检查各频率听力损失均≤25dB，但噪声作业1年之内，高频段3000Hz，4000Hz，6000Hz中任一耳，任一频率听阈≥65dB）',
      10224:
        '除噪声外各种原因引起的永久性感音神经性听力损失（500Hz，l000Hz和2000Hz中任一频率的纯音气导听阈>25dB）',
      10225: '未控制的高血压',
      10226: '未控制的糖尿病',
      10227: '全身瘢痕面积≥20%以上（工伤标准的八级）',
      10228: '各类器质性心脏病（风湿性心脏病、心肌病、冠心病、先天性心脏病等）',
      10229: '器质性心律不齐、直立性低血压、周围血管病',
      10230: '慢性支气管炎、支气管哮喘、肺结核、结核性胸膜炎、自发性气胸及病史',
      10231:
        '食道、胃、十二指肠、肝、胆、脾、胰疾病、慢性细菌性痢疾、慢性肠炎、腹部包块、消化系统、泌尿系统结石',
      10232: '泌尿、血液、内分泌及代谢系统疾病',
      10233: '结缔组织疾病，过敏体质',
      10234: '中枢神经系统及周围神经系统疾病和病史',
      10235: '癫痫、精神病、晕厥史、神经症和癔病精神活性物质滥用和依赖',
      10236:
        '各种原因引起的头颅异常影响戴面罩者，胸廓畸形，脊椎疾病，损伤及进行性病变，脊椎活动范围受限或明显异常，慢性眼腿痛，关节活动受限或疼痛',
      10237:
        '多发性肝、肾及骨囊肿，多发性脂肪瘤，瘢痕体质或全身瘢痕面积≥20%以上者',
      10238:
        '有颅脑、胸腔及腹腔手术史等外科疾病。阑尾炎术时间未超过半年，腹股沟斜疝和股疝修补术未超过1年者',
      10239: '脉管炎、动脉瘤、动静脉瘘，静脉曲张',
      10240: '脱肛，肛瘘，陈旧性肛裂，多发性痔疮及单纯性痔疮经常出血者',
      10241:
        '腋臭，头癖，泛发性体癣，疥疮，慢性湿疹，神经性皮炎，白癜风，银屑病',
      10242: '单眼裸视力不得低于4.8(0.6)，色弱，色盲，夜盲及眼科其他器质性疾患',
      10243: '外耳畸形耳、鼻、喉及前庭器官的器质性疾病，咽鼓管功能异常者',
      10244: '手足部习惯性冻疮',
      10245:
        '淋病、梅毒、软下疳、性病淋巴肉芽肿、非淋球菌性尿道炎、尖锐湿疣、生殖器疱疹、艾滋病及艾滋病毒携带者',
      10246:
        '纯音听力测试任一耳500Hz听力损失不得超过30dB，l000Hz、2000Hz听力损失不得超过25dB，4000Hz听力损失不得超过35dB',
      10247: '生殖系统疾病',
      10248: '泛发慢性湿疹',
      10249: '泛发慢性皮炎',
      10250: '晕厥（近一年内有晕厥发作史）',
      10251: '2级及以上高血压（未控制）',
      10252: '器质性心脏病或各种心律失常',
      10253: '晕厥，眩晕症',
      10254: '双耳语言频段平均听力损失>25dB',
      10255: '器质性心脏病或心律失常',
      10256: '肺结核',
      10257: '身高：大型机动车驾驶员＜155cm，小型机动车驾驶员＜150cm',
      10258: '听力：双耳平均听阈＞30dB（纯音气导）',
      10259: '深视力：<（－22mm）或>（＋22mm）',
      10260: '各类精神障碍疾病',
      10261: '吸食、注射毒品、长期服用依赖性精神药品成瘾尚未戒除者',
      10262:
        '远视力（对数视力表）：大型机动车驾驶员：两裸眼＜4.0，并＜5.0（矫正）；小型机动车驾驶员：两裸眼＜4.0，并＜4.9（矫正）',
      10263:
        '血压：大型机动车驾驶员：收缩压≥18.7kPa（≥140mmHg）和舒张压≥12kPa（≥90mmHg）；小型机动车驾驶员：2级及以上高血压（未控制）。',
      10264: '颈肩腕综合征',
      10265: '2级及以上高血压或低血压',
      10266: '活动的，潜在的，急性或慢性疾病',
      10267: '创伤性后遗症',
      10268: '影响功能的变形，缺损或损伤及影响功能的肌肉系统疾病',
      10269: '恶性肿瘤或影响生理功能的良性肿瘤',
      10270: '急性感染性，中毒性精神障碍治愈后留有后遗症',
      10271: '神经症，经常性头痛，睡眠障碍',
      10272: '药物成瘾，酒精成瘾者',
      10273: '中枢神经系统疾病，损伤',
      10274: '严重周围神经系统疾病及植物神经系统疾病',
      10275: '呼吸系统慢性疾病及功能障碍，肺结核，自发性气胸，胸腔脏器手术史',
      10276: '心血管器质性疾病，房室传导阻滞以及难以治愈的周围血管疾病',
      10277: '严重消化系统疾病，功能障碍或手术后遗症，病毒性肝炎',
      10278: '泌尿系统疾病，损伤以及严重生殖系统疾病',
      10279: '造血系统疾病',
      10280: '新陈代谢，免疫，内分泌系统系统疾病',
      10281: '运动系统疾病，损伤及其后遗症',
      10282: '难以治愈的皮肤及其附属器疾病（不含非暴露部位范围小的白癜风）',
      10283:
        '任一眼裸眼远视力低于0.7，任一眼裸眼近视力低于1.0；视野异常；色盲，色弱；夜盲治疗无效 者；眼及其附属器疾病治愈后遗有眼功能障碍',
      10284:
        '任一耳纯音听力图气导听力曲线在500HZ，1000Hz，2000Hz任一频率听力损失不得超过35dB或3000Hz频率听力损失不得超过50dB',
      10285:
        '耳气压功能不良治疗无效者，中耳慢性进行性疾病。内耳疾病或眩晕症不合格',
      10286:
        '影响功能的鼻，鼻窦慢性进行性疾病，嗅觉丧失，影响功能且不易矫治的咽喉部慢性进行性疾病者',
      10287: '影响功能的口腔及颞下领关节慢性进行性疾病',
      10288: '职业性航空病',
      10289: '职业性噪声聋',
      10290: '严重的呼吸系统疾病',
      10291: '严重的循环系统疾病',
      10292: '严重的消化系统疾病',
      10293: '严重的造血系统疾病',
      10294: '严重的神经和精神系统疾病',
      10295: '严重的泌尿生殖系统疾病',
      10296: '严重的内分泌系统疾病',
      10297: '严重的免疫系统疾病',
      10299: '严重的视听障碍、严重的听力障碍',
      10300: '恶性肿瘤、有碍于工作的巨大的、复发性良性肿瘤',
      10301: '严重的、有碍于工作的残疾，先天畸形和遗传性疾病',
      10302: '手术后而不能恢复正常功能者',
      10303: '未完全恢复的放射性疾病或其他职业病等',
      10304: '有吸毒、酗酒或其他恶习不能改正者',
      10305:
        '血常规检出有如下异常者：白细胞计数低于4×10^9/L或中性粒细胞低于2×10^9/L；血小板计数低于8×10^10/L。',
      10411: '伴肺功能损害的呼吸系统疾病',
    };
    return codeObj[value] || '';
  }

  /** 处理症状编码 3.12
   * @param value 编码
   * @return {string} 症状内容
   */
  getSymptom(value) {
    // value转换为数字
    value = +value;
    const codeObj = {
      10001: '神经系统',
      10002: '头晕',
      10003: '头痛',
      10004: '眩晕',
      10005: '失眠',
      10006: '嗜睡',
      10007: '多梦',
      10008: '记忆力减退',
      10009: '易激动',
      10010: '疲乏无力',
      10011: '四肢麻木',
      10012: '动作不灵活',
      10013: '肌肉抽搐',
      10014: '呼吸系统',
      10015: '胸痛',
      10016: '胸闷',
      10017: '咳嗽',
      10018: '咳痰',
      10019: '咯血',
      10020: '气促',
      10021: '气短',
      10022: '心血管系统',
      10023: '心悸',
      10024: '心前区不适',
      10025: '心前区疼痛',
      10026: '消化系统',
      10027: '食欲不振',
      10028: '恶心',
      10029: '呕吐',
      10030: '腹胀',
      10031: '腹痛',
      10032: '肝区疼痛',
      10033: '便秘',
      10034: '便血',
      10035: '造血系统、内分泌系统',
      10036: '皮下出血',
      10037: '月经异常',
      10038: '低热',
      10039: '盗汗',
      10040: '多汗',
      10041: '口渴',
      10042: '消瘦',
      10043: '脱发',
      10044: '皮疹',
      10045: '皮肤瘙痒',
      10046: '泌尿生殖系统',
      10047: '尿频',
      10048: '尿急',
      10049: '尿痛',
      10050: '血尿',
      10051: '浮肿',
      10052: '性欲减退',
      10053: '肌肉及四肢关节',
      10054: '全身酸痛',
      10055: '肌肉疼痛',
      10056: '肌无力及关节疼痛',
      10057: '眼、耳、鼻、咽喉及口腔',
      10058: '视物模糊',
      10059: '视力下降',
      10060: '眼痛',
      10061: '羞明',
      10062: '流泪',
      10063: '嗅觉减退',
      10064: '鼻干燥',
      10065: '鼻塞',
      10066: '流鼻血',
      10067: '流涕',
      10068: '耳鸣',
      10069: '耳聋',
      10070: '流涎',
      10071: '牙痛',
      10072: '牙齿松动',
      10073: '刷牙出血',
      10074: '口腔异味',
      10075: '口腔溃疡',
      10076: '咽部疼痛',
      10077: '声嘶',
      10078: '皮肤及附属器',
      10079: '色素脱失或沉着',
      10080: '皮 疹',
      10081: '出血点（斑）',
      10082: '赘生物',
      10083: '水疱或大疱',
      10084: '无异常',
      10085: '目前无不适症状',
      10086: '其他',
      10087: '呼吸困难',
      10088: '喷嚏',
      10089: '吸烟史',
      10090: '喘息',
    };
    return codeObj[value] || '';
  }

  /**
   * 获取体检项目名称和分类
   * @param code
   */
  getBhklist(code) {
    code = +code;
    const obj = {
      1530: { name: '外周血淋巴细胞染色体', classify: '染色体畸变分析' },
      10000: { name: '消化系统', classify: '内科' },
      10001: { name: '肝', classify: '内科' },
      10002: { name: '脾', classify: '内科' },
      10003: { name: '肺部', classify: '内科' },
      10006: { name: '泌尿生殖器官', classify: '外科' },
      10033: { name: '心电图', classify: '心电图' },
      10034: { name: '单核细胞计数', classify: '血常规' },
      10035: { name: '单核细胞比率', classify: '血常规' },
      10042: { name: '嗜酸性粒细胞计数', classify: '血常规' },
      10043: { name: '嗜酸性粒细胞比率', classify: '血常规' },
      10044: { name: '嗜碱性粒细胞比率', classify: '血常规' },
      10045: { name: '红细胞压积', classify: '血常规' },
      10046: { name: '血红蛋白', classify: '血常规' },
      10047: { name: '平均血红蛋白量', classify: '血常规' },
      10048: { name: '平均血红蛋白浓度', classify: '血常规' },
      10049: { name: '变应原皮肤试验', classify: '免疫学化验' },
      10050: { name: '丙型肝炎抗体', classify: '免疫学化验' },
      10051: { name: '超敏C反应蛋白', classify: '免疫学化验' },
      10052: { name: '甲型肝炎抗体', classify: '免疫学化验' },
      10053: { name: '抗链球菌溶血素‘O’测定', classify: '免疫学化验' },
      10054: { name: '血小板计数', classify: '血常规' },
      10055: { name: '肌酸激酶同功酶(CK-MB)', classify: '生化检验' },
      10056: { name: '尿酸', classify: '生化检验' },
      10057: { name: '低密度脂蛋白', classify: '生化检验' },
      10058: { name: '淀粉酶', classify: '生化检验' },
      10059: { name: '碱性磷酸酶', classify: '生化检验' },
      10060: { name: '血清脂蛋白(a)', classify: '生化检验' },
      10061: { name: 'γ-谷氨酰氨转肽酶', classify: '生化检验' },
      10063: { name: '载脂蛋白A1', classify: '生化检验' },
      10064: { name: '载脂蛋白B', classify: '生化检验' },
      10066: { name: '白蛋白', classify: '生化检验' },
      10067: { name: '球蛋白', classify: '生化检验' },
      10068: { name: '白球比', classify: '生化检验' },
      10070: { name: '甘油三酯', classify: '生化检验' },
      10108: { name: '血小板压积', classify: '血常规' },
      10110: { name: '平均血小板体积', classify: '血常规' },
      10111: { name: '血小板分布宽度', classify: '血常规' },
      10112: { name: '大型血小板比率', classify: '血常规' },
      10113: { name: '网织红细胞', classify: '血常规' },
      10114: { name: '中性粒细胞计数', classify: '血常规' },
      10115: { name: '尿维生素C', classify: '尿常规' },
      10116: { name: '总胆红素', classify: '生化检验' },
      10117: { name: '中性细胞比率', classify: '血常规' },
      10128: { name: '血清钙', classify: '血液特殊项' },
      10131: { name: '血清镁（Mg）', classify: '生化检验' },
      10132: { name: '血清铁（lron）', classify: '生化检验' },
      10133: { name: '血清尿素（Urea）', classify: '血液特殊项' },
      10134: { name: '血清肌酐（Crea）', classify: '血液特殊项' },
      10138: { name: '平均红细胞血红蛋白量（MCH）', classify: '生化检验' },
      10139: {
        name: '平均红细胞血红蛋白浓度（MCHC）',
        classify: '生化检验',
      },
      10140: { name: '直接胆红素', classify: '生化检验' },
      10887: { name: '尿蛋白', classify: '尿常规' },
      10889: { name: '比重', classify: '尿常规' },
      10891: { name: '尿隐血', classify: '尿常规' },
      10892: { name: '尿胆红素', classify: '尿常规' },
      10893: { name: '尿胆原', classify: '尿常规' },
      10894: { name: '血溴', classify: '血液特殊项' },
      10897: { name: '裸眼远视力(左)', classify: '视力色觉' },
      10898: { name: '裸眼远视力(右)', classify: '视力色觉' },
      10901: { name: '耳部', classify: '五官科' },
      10902: { name: '扁桃体', classify: '五官科' },
      10905: { name: '鼻中隔', classify: '五官科' },
      10906: { name: '听力(右)', classify: '五官科' },
      10907: { name: '耳廓', classify: '五官科' },
      10908: { name: '外耳道', classify: '五官科' },
      10909: { name: '咽部', classify: '五官科' },
      10910: { name: '喉部', classify: '五官科' },
      10911: { name: '鼻部', classify: '五官科' },
      10912: { name: '咽黏膜', classify: '五官科' },
      10914: { name: '鼻窦', classify: '五官科' },
      10917: { name: '鼻黏膜', classify: '五官科' },
      10919: { name: '一般状况', classify: '一般情况' },
      10920: { name: '肛门指检', classify: '外科' },
      10921: { name: '前列腺(外科)', classify: '外科' },
      10922: { name: '腹部', classify: '外科' },
      10923: { name: '乳房', classify: '外科' },
      10924: { name: '体重', classify: '一般情况' },
      10925: { name: '收缩压', classify: '一般情况' },
      10926: { name: '四肢关节', classify: '外科' },
      10928: { name: '舒张压', classify: '一般情况' },
      10929: { name: '内科其他', classify: '内科' },
      10930: { name: '骨密度', classify: '骨密度' },
      10931: { name: '鼻窦X光摄片', classify: 'X线摄片' },
      10937: { name: '裸眼近视力(左)', classify: '视力色觉' },
      10938: { name: '裸眼近视力(右)', classify: '视力色觉' },
      10941: { name: '外眼', classify: '眼科' },
      10942: { name: '裸眼视力(右)', classify: '眼科' },
      10944: { name: '眼压', classify: '眼科' },
      10945: { name: '色觉', classify: '眼科' },
      10946: { name: '虹膜', classify: '眼科' },
      10951: { name: '宫颈', classify: '妇科' },
      10952: { name: '妇科其他', classify: '妇科' },
      10956: { name: '皮肤', classify: '外科' },
      10960: { name: '血镍', classify: '血液特殊项' },
      10961: { name: '血苯', classify: '血液特殊项' },
      10971: { name: '指鼻试验', classify: '神经科' },
      10973: { name: '指指试验', classify: '神经科' },
      10974: { name: '共济运动', classify: '神经科' },
      10975: { name: '三颤', classify: '神经科' },
      10976: { name: '深感觉（神经系统检查）', classify: '神经科' },
      10977: { name: '膝反射', classify: '神经科' },
      10978: { name: '跟-膝-胫试验', classify: '神经科' },
      10979: { name: '前庭功能', classify: '神经科' },
      10980: { name: '跟腱反射', classify: '神经科' },
      10981: { name: '浅感觉（神经系统检查）', classify: '神经科' },
      10982: { name: '尿酮体', classify: '尿常规' },
      10988: { name: '镜检白细胞', classify: '尿常规' },
      10989: { name: '镜检上皮细胞', classify: '尿常规' },
      10991: { name: '镜检管型', classify: '尿常规' },
      10993: { name: '镜检红细胞', classify: '尿常规' },
      10994: { name: '间接胆红素', classify: '生化检验' },
      10995: { name: '磷酸肌酸激酶', classify: '生化检验' },
      10996: { name: '幽门螺旋菌抗体', classify: '生化检验' },
      10997: { name: 'a-羟丁酸脱氢酶', classify: '生化检验' },
      10999: { name: '前白蛋白', classify: '生化检验' },
      11001: { name: '乙肝核心抗体', classify: '免疫学化验' },
      11002: { name: '抗双链DNA抗体', classify: '免疫学化验' },
      11003: { name: '类风湿因子', classify: '免疫学化验' },
      11004: { name: '淋球菌', classify: '免疫学化验' },
      11005: { name: '梅毒滴度(RPR)', classify: '免疫学化验' },
      11007: { name: 'β2微球蛋白', classify: '免疫学化验' },
      11008: { name: '乙肝核心抗体(定量)', classify: '免疫学化验' },
      11009: { name: '乙型肝炎核心抗体HBc-IgM', classify: '免疫学化验' },
      11010: { name: '免疫球蛋白IgA', classify: '免疫学化验' },
      11011: { name: '免疫球蛋白IgG', classify: '免疫学化验' },
      11013: { name: '牙齿', classify: '口腔科' },
      11014: { name: '牙龈', classify: '口腔科' },
      11017: { name: '血甲醇', classify: '血液特殊项' },
      11019: { name: '五官科其他', classify: '五官科' },
      11024: { name: '血砷', classify: '血液特殊项' },
      11033: { name: '脉搏', classify: '一般情况' },
      11034: { name: '身高', classify: '一般情况' },
      11036: { name: '问诊', classify: '问诊' },
      11037: { name: '镜检结晶', classify: '尿常规' },
      11044: { name: '免疫球蛋白IgM', classify: '免疫学化验' },
      11045: { name: '肾', classify: '内科' },
      11047: { name: '乙肝表面抗原(定量)', classify: '免疫学化验' },
      11061: { name: '腰骶椎正侧位片', classify: 'X线摄片' },
      11063: { name: '手部X线摄片', classify: 'X线摄片' },
      11065: { name: '下颌骨 X 射线摄片', classify: 'X线摄片' },
      11066: { name: '肝脏', classify: '超声' },
      11067: { name: '脾脏', classify: '超声' },
      11068: { name: '胆囊', classify: '超声' },
      11072: { name: '头颅正侧位片', classify: 'X线摄片' },
      11073: { name: '骨盆正位片', classify: 'X线摄片' },
      11074: { name: '腕关节正侧位片', classify: 'X线摄片' },
      11075: { name: '左足部正侧片', classify: 'X线摄片' },
      11076: { name: '肱骨正侧位片', classify: 'X线摄片' },
      11077: { name: '肘关节正侧位片', classify: 'X线摄片' },
      11078: { name: '肩关节正位片', classify: 'X线摄片' },
      11079: { name: '股骨正侧位片', classify: 'X线摄片' },
      11081: { name: '髋关节正位片', classify: 'X线摄片' },
      11082: { name: '同侧胫、腓骨正、侧位片', classify: 'X线摄片' },
      11083: { name: '膝关节正侧位片', classify: 'X线摄片' },
      11084: { name: '踝关节正侧位片', classify: 'X线摄片' },
      11086: { name: '肾脏', classify: '超声' },
      11087: { name: '乳酸脱氢酶', classify: '生化检验' },
      11088: { name: '血清无机磷', classify: '血液特殊项' },
      11094: { name: '尿铅', classify: '尿液特殊项' },
      11099: { name: '尿铬', classify: '尿液特殊项' },
      11104: { name: '血镉', classify: '血液特殊项' },
      11106: { name: '尿β2微球蛋白', classify: '尿液特殊项' },
      11111: { name: '血铬', classify: '血液特殊项' },
      11112: { name: '尿氟', classify: '尿液特殊项' },
      11116: { name: '意识', classify: '神经科' },
      11121: { name: '尿锰', classify: '尿液特殊项' },
      11123: { name: '肌张力', classify: '神经科' },
      11128: { name: '尿镉', classify: '尿液特殊项' },
      11133: { name: 'C-反应蛋白', classify: '免疫学化验' },
      11141: { name: '胸部正位片', classify: 'X线摄片' },
      11142: { name: 'X 射线高千伏胸片', classify: 'X线摄片' },
      11145: { name: 'PGI/PGII', classify: '免疫学化验' },
      11150: { name: '浅表淋巴结', classify: '外科' },
      11153: { name: '龋齿', classify: '口腔科' },
      11154: { name: '裸眼视力(左)', classify: '眼科' },
      11156: { name: '面色', classify: '一般情况' },
      11158: { name: '体重指数BMI', classify: '一般情况' },
      11165: { name: '淋巴细胞计数', classify: '血常规' },
      11209: { name: '血清总胆汁酸', classify: '生化检验' },
      11210: { name: '补体C3', classify: '生化检验' },
      11211: { name: '补体C4', classify: '生化检验' },
      11212: { name: '血清胆碱脂酶', classify: '生化检验' },
      11213: { name: '总蛋白', classify: '生化检验' },
      11216: { name: '甘脯肽酶', classify: '生化检验' },
      11230: { name: '左耳语频平均听阈', classify: '电测听' },
      11231: { name: '右耳语频平均听阈', classify: '电测听' },
      11232: { name: '双耳语频平均听阈', classify: '电测听' },
      11233: { name: '双耳高频平均听阈', classify: '电测听' },
      11234: { name: '左耳听阈加权', classify: '电测听' },
      11235: { name: '右耳听阈加权', classify: '电测听' },
      11236: { name: '左耳500Hz(骨导)', classify: '电测听' },
      11237: { name: '左耳1000Hz(骨导)', classify: '电测听' },
      11238: { name: '左耳2000Hz(骨导)', classify: '电测听' },
      11239: { name: '左耳6000Hz(骨导)', classify: '电测听' },
      11240: { name: '右耳500Hz(骨导)', classify: '电测听' },
      11241: { name: '右耳1000Hz(骨导)', classify: '电测听' },
      11242: { name: '右耳2000Hz(骨导)', classify: '电测听' },
      11243: { name: '右耳3000Hz(骨导)', classify: '电测听' },
      11244: { name: '右耳4000Hz(骨导)', classify: '电测听' },
      11245: { name: '右耳6000Hz(骨导)', classify: '电测听' },
      11246: { name: '颈椎正侧位片', classify: 'X线摄片' },
      11247: { name: '腰椎正侧位片', classify: 'X线摄片' },
      11248: { name: '后前位X射线高千伏胸片', classify: 'X线摄片' },
      11251: { name: '淋巴细胞比率', classify: '血常规' },
      11252: { name: '尿液颜色', classify: '尿常规' },
      11254: { name: '轮替运动', classify: '神经科' },
      11255: { name: '红细胞分布宽度CV', classify: '血常规' },
      11264: { name: '微白蛋白', classify: '尿常规' },
      11269: { name: '病理反射', classify: '神经科' },
      11270: { name: '乙肝表面抗原', classify: '免疫学化验' },
      11273: { name: '颈椎双斜位X射线摄片', classify: 'X线摄片' },
      11274: { name: '胫、腓骨正位片', classify: 'X线摄片' },
      11275: { name: '胃蛋白酶原II', classify: '免疫学化验' },
      11276: { name: '乙肝e抗体', classify: '免疫学化验' },
      11277: { name: '戊型肝炎抗体', classify: '免疫学化验' },
      11278: { name: '血气分析', classify: '免疫学化验' },
      11287: { name: '乙肝e抗体(定量)', classify: '免疫学化验' },
      11288: {
        name: '淋巴细胞微核率',
        classify: '外周血淋巴细胞微核试验',
      },
      11289: { name: '乙肝e抗原', classify: '免疫学化验' },
      11290: { name: '乙肝e抗原(定量)', classify: '免疫学化验' },
      11291: { name: '乙肝表面抗体', classify: '免疫学化验' },
      11292: { name: '血铅', classify: '血液特殊项' },
      11293: { name: '血钾', classify: '血液特殊项' },
      11299: { name: '附件', classify: '妇科' },
      11303: { name: '发育', classify: '内科' },
      11304: { name: '胸廓', classify: '内科' },
      11305: { name: '既往史', classify: '内科' },
      11306: { name: '鼻外形', classify: '五官科' },
      11310: { name: '缺齿', classify: '口腔科' },
      11313: { name: '指甲', classify: '皮肤科' },
      11314: { name: '毛发', classify: '皮肤科' },
      11317: { name: '心血管系统', classify: '内科' },
      11318: { name: '透明度', classify: '尿常规' },
      11319: { name: '尿白细胞', classify: '尿常规' },
      11320: { name: '酸碱度', classify: '尿常规' },
      11321: { name: '亚硝酸盐', classify: '尿常规' },
      11323: { name: '尿葡萄糖', classify: '尿常规' },
      11338: { name: '中间细胞计数', classify: '血常规' },
      11339: { name: '嗜碱性粒细胞计数', classify: '血常规' },
      11345: { name: '呼吸系统', classify: '内科' },
      11346: { name: '口腔黏膜', classify: '口腔科' },
      11347: { name: '牙周', classify: '口腔科' },
      11378: { name: '握力', classify: '神经科' },
      11381: { name: '肺功能结果', classify: '肺功能' },
      11382: { name: '白细胞计数', classify: '血常规' },
      11401: { name: '中间细胞比率', classify: '血常规' },
      11402: { name: '红细胞计数', classify: '血常规' },
      11425: { name: '听力(左)', classify: '五官科' },
      11426: { name: '左耳 500Hz（气导）', classify: '电测听' },
      11427: { name: '左耳 1000Hz（气导）', classify: '电测听' },
      11428: { name: '左耳 2000Hz（气导）', classify: '电测听' },
      11429: { name: '左耳 3000Hz（气导）', classify: '电测听' },
      11430: { name: '左耳 4000Hz（气导）', classify: '电测听' },
      11431: { name: '左耳 6000Hz（气导）', classify: '电测听' },
      11432: { name: '右耳 500Hz（气导）', classify: '电测听' },
      11433: { name: '右耳 1000Hz（气导）', classify: '电测听' },
      11434: { name: '右耳 2000Hz（气导）', classify: '电测听' },
      11435: { name: '右耳 3000Hz（气导）', classify: '电测听' },
      11436: { name: '右耳 4000Hz（气导）', classify: '电测听' },
      11437: { name: '右耳 6000Hz（气导）', classify: '电测听' },
      11440: { name: '无着丝粒体畸变率', classify: '染色体畸变分析' },
      11441: { name: '分析细胞数', classify: '染色体畸变分析' },
      11442: { name: '无着丝粒断片', classify: '染色体畸变分析' },
      11443: { name: '微小体', classify: '染色体畸变分析' },
      11444: { name: '无着丝粒环', classify: '染色体畸变分析' },
      11445: { name: '着丝粒环', classify: '染色体畸变分析' },
      11446: { name: '双着丝粒体', classify: '染色体畸变分析' },
      11447: { name: '相互易位', classify: '染色体畸变分析' },
      11461: { name: '外阴', classify: '妇科' },
      11462: { name: '阴道', classify: '妇科' },
      11463: { name: '分泌物', classify: '妇科' },
      11464: { name: '子宫体', classify: '妇科' },
      11465: { name: '穹隆', classify: '妇科' },
      11487: { name: 'Tinel试验', classify: '神经科' },
      11488: { name: 'Phalen试验', classify: '神经科' },
      11489: { name: '脊柱', classify: '外科' },
      11490: { name: '梅毒螺旋体特异抗体', classify: '免疫学化验' },
      11491: { name: '右足部正侧位片', classify: 'X线摄片' },
      11492: { name: '一侧桡、尺骨正位片', classify: 'X线摄片' },
      11493: { name: '脊椎X射线摄片', classify: 'X线摄片' },
      11494: { name: '口腔牙体X射线全景片', classify: 'X线摄片' },
      11495: { name: '人免疫缺陷病毒抗体', classify: '免疫学化验' },
      11496: { name: '胃蛋白酶原I', classify: '免疫学化验' },
      11499: { name: '乙肝表面抗体(定量)', classify: '免疫学化验' },
      11500: { name: '甲状腺(外科)', classify: '外科' },
      11501: { name: '用力肺活量 FVC（%）', classify: '肺功能' },
      11503: { name: '精神状况', classify: '神经科' },
      11504: { name: '左耳语频平均听阈(骨导)', classify: '电测听' },
      11505: { name: '右耳语频平均听阈(骨导)', classify: '电测听' },
      11506: { name: '双耳语频平均听阈(骨导)', classify: '电测听' },
      11510: { name: '双耳高频平均听阈(骨导)', classify: '电测听' },
      11511: { name: '左耳听阈加权(骨导)', classify: '电测听' },
      11512: { name: '右耳听阈加权(骨导)', classify: '电测听' },
      11513: { name: '左耳4000Hz(骨导)', classify: '电测听' },
      11515: { name: '左耳3000Hz(骨导)', classify: '电测听' },
      11578: { name: '24H动态心电图', classify: '心电图' },
      11579: { name: '肌力', classify: '神经科' },
      12055: { name: '红细胞分布宽度SD', classify: '血常规' },
      13001: { name: '高铁血红蛋白还原试验', classify: '血液特殊项' },
      13003: { name: '第一秒时间肺活量 FEV1（%）', classify: '肺功能' },
      13004: { name: 'FEV1/FVC', classify: '肺功能' },
      13005: { name: '骨科检查', classify: '外科' },
      13006: { name: '胆', classify: '内科' },
      13007: { name: '外科其他', classify: '外科' },
      13014: { name: '角膜', classify: '眼科' },
      13015: { name: '眼科其他', classify: '眼科' },
      13016: { name: '玻璃体（左）', classify: '眼科' },
      13017: { name: '玻璃体（右）', classify: '眼科' },
      13018: { name: '颊面部', classify: '口腔科' },
      13030: { name: '心', classify: '内科' },
      13035: { name: '侧位X射线高千伏胸片', classify: 'X线摄片' },
      13036: { name: '红细胞平均体积', classify: '血常规' },
      13037: { name: '尿素氮', classify: '生化检验' },
      13039: { name: '胸部X线透视', classify: '胸部透视' },
      13040: { name: '胸部CT', classify: 'CT' },
      13041: { name: '颈椎CT', classify: 'CT' },
      13042: { name: '腰椎CT', classify: 'CT' },
      13043: { name: '鼻咽部CT', classify: 'CT' },
      13044: { name: '腹部CT', classify: 'CT' },
      13045: { name: '双肾、输尿管CT', classify: 'CT' },
      13046: { name: '盆腔CT', classify: 'CT' },
      13047: { name: '肾上腺CT', classify: 'CT' },
      13048: { name: '肺部CT', classify: 'CT' },
      13049: { name: '头颅CT', classify: 'CT' },
      13051: { name: '肾脏CT', classify: 'CT' },
      13052: { name: '胰腺CT', classify: 'CT' },
      13053: { name: '颈部CT', classify: 'CT' },
      13054: { name: '鼻窦冠状位CT', classify: 'CT' },
      13055: { name: '输尿管CT', classify: 'CT' },
      13056: { name: '肾动脉CT', classify: 'CT' },
      13057: { name: '胸椎CT', classify: 'CT' },
      13058: { name: '钾(K)', classify: '微量元素' },
      13059: { name: '铁(Fe)', classify: '微量元素' },
      13060: { name: '镁(Mg)', classify: '微量元素' },
      13061: { name: '钠(Na)', classify: '微量元素' },
      13062: { name: '氯(Cl)', classify: '微量元素' },
      13063: { name: '钙(Ca)', classify: '微量元素' },
      13064: { name: '磷(P)', classify: '微量元素' },
      13065: { name: '深视力第一次偏差', classify: '' },
      13066: { name: '深视力第二次偏差', classify: '深视力' },
      13067: { name: '深视力第三次偏差', classify: '' },
      13068: { name: '深视力平均', classify: '' },
      13069: { name: '暗视力', classify: '暗视力' },
      13072: { name: '甲状腺B超', classify: '超声' },
      13073: { name: '肾动脉', classify: '超声' },
      13074: { name: '心脏B超', classify: '超声' },
      13075: { name: '颈动脉B超', classify: '超声' },
      13076: { name: '乳腺B超', classify: '超声' },
      13077: { name: '双肾', classify: '超声' },
      13078: { name: '膀胱', classify: '超声' },
      13079: { name: '前列腺B超', classify: '超声' },
      13080: { name: '输尿管', classify: '超声' },
      13081: { name: '子宫附件', classify: '超声' },
      13083: { name: '胰', classify: '超声' },
      13100: { name: '晶体(左)', classify: '眼科' },
      13101: { name: '血肌酐（Cr）', classify: '生化检验' },
      13102: { name: '血清丙氨酸氨基转移酶', classify: '生化检验' },
      13103: { name: '总胆固醇', classify: '生化检验' },
      13105: { name: '高密度脂蛋白', classify: '生化检验' },
      13106: { name: '天门冬氨酸氨基转移酶', classify: '生化检验' },
      13107: { name: '天门冬氨酸/丙氨酸氨基转移酶', classify: '生化检验' },
      13108: { name: '血清腺苷脱氨酶', classify: '生化检验' },
      13109: { name: '血葡萄糖', classify: '生化检验' },
      13110: { name: '软组织', classify: '口腔科' },
      13111: { name: '牙齿清洁度', classify: '口腔科' },
      13112: { name: '口腔科其他', classify: '口腔科' },
      13113: { name: '手部皮肤', classify: '皮肤科' },
      13114: { name: '全身皮肤', classify: '皮肤科' },
      13115: { name: '划痕症', classify: '皮肤科' },
      13116: { name: '心律', classify: '内科' },
      13117: { name: '结膜', classify: '眼科' },
      13118: { name: '沙眼左', classify: '眼科' },
      13119: { name: '晶体(右)', classify: '眼科' },
      13120: { name: '眼底', classify: '眼科' },
      13121: { name: '暗适应检查', classify: '眼科' },
      13122: { name: '立体视觉', classify: '眼科' },
      13123: { name: '静态视野', classify: '眼科' },
      13124: { name: '房水', classify: '眼科' },
      13125: { name: '眼睑', classify: '眼科' },
      13126: { name: '内眦', classify: '眼科' },
      13127: { name: '外眦', classify: '眼科' },
      13128: { name: '泪囊', classify: '眼科' },
      13129: { name: '矫正视力(左)', classify: '眼科' },
      13130: { name: '眼前部（左）', classify: '眼科' },
      13131: { name: '沙眼右', classify: '眼科' },
      13132: { name: '矫正视力(右)', classify: '眼科' },
      13133: { name: '锌原卟啉', classify: '血液特殊项' },
      13134: { name: '免疫球蛋白IgE', classify: '免疫学化验' },
      13135: { name: '淋巴细胞分类CD4/CD8', classify: '血常规' },
      13136: { name: '尿甲基甲酰胺', classify: '尿液特殊项' },
      13137: { name: '凝血酶原时间', classify: '血液特殊项' },
      13138: {
        name: '虎红缓冲液玻片凝集实验（RPBT）',
        classify: '免疫学化验',
      },
      13139: { name: '试管凝集反应（Wright）', classify: '免疫学化验' },
      13141: { name: '骨关节', classify: '外科' },
      13142: { name: '复杂反应', classify: '神经科' },
      13143: { name: '速度反应', classify: '神经科' },
      13144: { name: '指端试验', classify: '神经科' },
      13145: { name: '冷水复温试验', classify: '神经科' },
      13146: { name: '白指诱发试验', classify: '神经科' },
      13147: { name: '皮肤划痕症', classify: '神经科' },
      13148: { name: '牙齿冷热刺激试验', classify: '口腔科' },
      13151: { name: '嗅觉', classify: '五官科' },
      13152: { name: '四肢长骨正侧位X射线摄片', classify: 'X线摄片' },
      13153: { name: '神经－肌电图', classify: '神经科' },
      13154: {
        name: '游离三碘甲状腺原氨酸指数（FT3I）',
        classify: '甲状腺',
      },
      13155: { name: '游离甲状腺素指数（FT4I）', classify: '甲状腺' },
      13156: { name: '促甲状腺激素TSH', classify: '甲状腺' },
      11417: { name: '促甲状腺激素', classify: '甲状腺' },
      13158: { name: '总甲状腺素T4', classify: '甲状腺' },
      11419: { name: '甲状腺素', classify: '甲状腺' },
      11418: { name: '三碘甲腺原氨酸', classify: '甲状腺' },
      11281: { name: '游离甲状腺素', classify: '甲状腺' },
      11280: { name: '游离三碘甲状腺原氨酸', classify: '甲状腺' },
      13159: { name: '血碳氧血红蛋白', classify: '血液特殊项' },
      13160: { name: '胆碱酯酶活性', classify: '血液特殊项' },
      13161: { name: '红细胞胆碱酯酶活性', classify: '血液特殊项' },
      13162: { name: '血沉', classify: '血液特殊项' },
      13163: { name: '高铁血红蛋白', classify: '血液特殊项' },
      13164: { name: '红细胞赫恩滋小体', classify: '血液特殊项' },
      13165: { name: '抗原特异性IgE抗体', classify: '血液特殊项' },
      13166: { name: '尿甲醇', classify: '尿液特殊项' },
      13167: { name: '粪便潜血试验', classify: '粪便化验' },
      13168: { name: '尿汞', classify: '尿液特殊项' },
      13169: { name: '尿视黄醇结合蛋白', classify: '尿液特殊项' },
      13170: { name: '尿铍', classify: '尿液特殊项' },
      13171: { name: '尿砷', classify: '尿液特殊项' },
      13172: { name: '尿锡', classify: '尿液特殊项' },
      13173: { name: '尿铊', classify: '尿液特殊项' },
      13174: { name: '尿镍', classify: '尿液特殊项' },
      13175: { name: '尿反－反粘糠酸', classify: '尿液特殊项' },
      18000: { name: '尿S-苯巯基脲酸', classify: '尿液特殊项' },
      13176: { name: '尿酚', classify: '尿液特殊项' },
      13177: { name: '尿2，5-己二酮', classify: '血液特殊项' },
      13178: { name: '尿脱落细胞检查', classify: '尿液特殊项' },
      13179: { name: '尿五氯酚测定', classify: '尿液特殊项' },
      13180: { name: '尿三氯乙酸', classify: '尿液特殊项' },
      13181: { name: '尿硫氰酸盐NS', classify: '尿液特殊项' },
      13182: { name: '牙体缺失', classify: '口腔科' },
      13216: { name: '皮肤黏膜（皮肤黏膜）', classify: '皮肤科' },
      13217: { name: '色素沉着（皮肤黏膜）', classify: '皮肤科' },
      13218: { name: '色素减退（皮肤黏膜）', classify: '皮肤科' },
      13219: { name: '皮疹（皮肤黏膜）', classify: '皮肤科' },
      13220: { name: '皮下出血（皮肤黏膜）', classify: '皮肤科' },
      13221: {
        name: '皮肤粘膜的湿度、弹性（皮肤黏膜）',
        classify: '皮肤科',
      },
      13222: { name: '脱屑（皮肤黏膜）', classify: '皮肤科' },
      13223: { name: '皲裂（皮肤黏膜）', classify: '皮肤科' },
      13224: { name: '疣状物（皮肤黏膜）', classify: '皮肤科' },
      13225: { name: '皮肤萎缩（皮肤黏膜）', classify: '皮肤科' },
      13226: { name: '过度角化（皮肤黏膜）', classify: '皮肤科' },
      13227: { name: '溃疡（皮肤黏膜）', classify: '皮肤科' },
      13228: { name: '总前列腺特异性抗原(t-PSA)', classify: '血液特殊项' },
      13229: {
        name: '游离前列腺特异性抗原(f-PSA)',
        classify: '血液特殊项',
      },
      13230: { name: '前列腺酸性磷酸酶（PAP）', classify: '血液特殊项' },
      13231: { name: '染色体畸变细胞率', classify: '染色体畸变分析' },
      13233: { name: '淋巴细胞微核细胞率', classify: '染色体畸变分析' },
      13234: { name: '神经反射检查（神经系统检查）', classify: '神经科' },
      13235: { name: '运动功能检查（神经系统检查）', classify: '神经科' },
      13236: { name: '运动功能（外科检查）', classify: '外科' },
      13320: { name: '眼前部（右）', classify: '眼科' },
      13333: { name: '心率', classify: '内科' },
      13423: { name: '染色体分析细胞数', classify: '染色体畸变分析' },
      13596: { name: '血钠', classify: '血常规' },
      13900: { name: '皮疹', classify: '皮肤科' },
      13901: { name: '皮肤其他', classify: '皮肤科' },
      13902: { name: '皮肤颜色', classify: '皮肤科' },
      13903: { name: '瘀斑、瘀点', classify: '皮肤科' },
      13904: { name: '紫癜', classify: '皮肤科' },
      13905: { name: '心音', classify: '内科' },
      13906: { name: '心界', classify: '内科' },
      13907: { name: '神经系统', classify: '内科' },
      13908: { name: '腹部包块', classify: '内科' },
      13910: { name: '杂音', classify: '内科' },
      13911: { name: '末梢感觉', classify: '内科' },
      13912: { name: '四肢', classify: '外科' },
      13913: { name: '关节', classify: '外科' },
      13914: { name: '眼底（右）', classify: '眼科' },
      13915: { name: '眼底（左）', classify: '眼科' },
      13916: { name: '胸部正侧位片', classify: 'X线摄片' },
      13917: { name: '鼓室图', classify: '五官科' },
      13918: { name: '脑干诱发点位', classify: '五官科' },
      13919: { name: '染色体畸变率', classify: '染色体畸变分析' },
      13920: { name: '大型血小板数目', classify: '血常规' },
      13921: { name: '面部表情', classify: '神经科' },
      13922: { name: '语速', classify: '神经科' },
      13923: { name: '巨大未成熟细胞计数', classify: '血常规' },
      13924: { name: '巨大未成熟细胞比率', classify: '血常规' },
      13925: { name: '异常淋巴细胞计数', classify: '血常规' },
      13926: { name: '异常淋巴细胞比率', classify: '血常规' },
      13927: { name: '尿肌酐', classify: '尿常规' },
      14589: { name: '二氧化碳', classify: '血常规' },
      15324: {
        name: '血清葡萄糖-6-磷酸脱氢酶缺乏症筛查试验',
        classify: '血液特殊项',
      },
      15369: { name: '总三碘甲状腺原氨酸(T3)', classify: '甲状腺' },
      15370: { name: '尿沉渣红细胞形态信息', classify: '尿常规' },
      15371: { name: '沉渣电导率', classify: '尿常规' },
      15372: { name: '糖化血红蛋白', classify: '生化检验' },
      17000: { name: '苯巯基尿酸', classify: '尿液特殊项' },
      17001: { name: '双着丝粒染色体率', classify: '染色体畸变分析' },
      14610: { name: '癌胚抗原', classify: '甲胎癌胚' },
      14611: { name: '甲胎蛋白', classify: '甲胎癌胚' },
      200664: { name: '鼓膜', classify: '五官科' },
      11438: { name: '左耳听阈加权值', classify: '电测听' },
      11439: { name: '右耳听阈加权值', classify: '电测听' },
      10983: { name: '感觉异常', classify: '神经科' },
      11580: { name: '四肢肌力', classify: '神经科' },
    };
    return obj[code] || { name: '', classify: '' };
  }

  /**
   * 转换subData数据
   * @param {Object} data
   * @return {Object}
   */

  getSubData(data) {
    const itmcodInfo = this.getBhklist(data.ITMCOD);
    const msrunt = this.getUnit(data.MSRUNT);
    return {
      chkdat: data.CHKDAT && new Date(data.CHKDAT),
      chkdoct: data.CHKDOCT,
      diagRest: data.DIAG_REST,
      ifLack: data.IF_LACK,
      itmcod: data.ITMCOD,
      name: itmcodInfo.name,
      classify: itmcodInfo.classify,
      result: data.RESULT,
      itemStdValue: data.ITEM_STDVALUE,
      jdgptn: data.JDGPTN,
      maxVal: data.MAXVAL,
      minVal: data.MINVAL,
      msrunt,
      rgltag: data.RGLTAG,
      rstDesc: data.RST_DESC,
      rstFlag: data.RST_FLAG,
    };
  }

  /**
 * 转换一般问诊项目
 * @param {Object} data
 * @return {Object}
 */
  getExmdDataList(data) {
    return {
      mns: this.formatNumber(data.MNS),
      cys: this.formatString(data.CYC),
      mnlage: this.formatNumber(data.MNLAGE),
      isxmns: this.formatNumber(data.ISXMNS),
      mnrage: this.formatNumber(data.MNRAGE),
      chldqty: this.formatNumber(data.CHLDQTY),
      abrqty: this.formatNumber(data.ABRQTY),
      slnkqty: this.formatNumber(data.SLNKQTY),
      stlqty: this.formatNumber(data.STLQTY),
      trsqty: this.formatNumber(data.TRSQTY),
      chldhthCnd: this.formatString(data.CHLDHTHCND),
      mrydat: this.formatDate(data.MRYDAT),
      cplrdtcnd: this.formatString(data.CPLRDTCND),
      cplprfhthcnd: this.formatString(data.CPLPRFHTHCND),
      smksta: this.formatNumber(data.SMKSTA),
      smkdayble: this.formatString(data.SMKDAYBLE),
      smkyerqty: this.formatString(data.SMKYERQTY),
      winsta: this.formatNumber(data.WINSTA),
      windaymlx: this.formatString(data.WINDAYMLX),
      winyerqty: this.formatString(data.WINYERQTY),
      jzs: this.formatString(data.JZS),
      grs: this.formatString(data.GRS),
      oth: this.formatString(data.OTH),
    };
  }

  /**
 * 格式化为数字，如果值为null或undefined则返回默认值0
 * @param {any} value
 * @return {Number}
 */
  formatNumber(value) {
    return value != null && !isNaN(value) ? Number(value) : 0;
  }

  /**
 * 格式化为字符串，如果值为null或undefined则返回空字符串
 * @param {any} value
 * @return {String}
 */
  formatString(value) {
    return value != null ? String(value) : '';
  }

  /**
 * 格式化为日期，如果值为null或undefined则返回当前日期
 * @param {any} value
 * @return {Date}
 */
  formatDate(value) {
    const date = new Date(value);
    // 如果值是无效的日期，返回当前日期
    return !isNaN(date.getTime()) ? date : new Date();
  }

  /** 处理是否复查
   * @param value
   * @return {string}
   */
  getIsReview(value) {
    // value转换为数字
    value = +value;
    const codeObj = {
      0: '否',
      1: '是',
    };
    return codeObj[value] || '';
  }

  /** 处理体检结论 最终
   * @param List
   */
  getFinalConclusion(List) {
    const codeObj = {
      目前未见异常: 0,
      复查: 2,
      疑似职业病: 5,
      禁忌证: 3,
      其他疾病或异常: 1,
    };
    let res = '目前未见异常';
    // 根据list 中每项当中的examConclusion字段，获取对应的code processedMhkRstList
    for (const item of List) {
      if (codeObj[item.examConclusion] > codeObj[res]) {
        res = item.examConclusion;
      }
    }
    return res;
  }

  /** 处理体检结论对应的字段
   * @param value
   * @return {string}
   */
  getConclusionField(value) {
    // actuallNum: 0, // 实检人数
    //         normal: 0, // 正常人数
    //         re_examination: 0, // 复检人数
    //         suspected: 0, // 疑似职业病人数
    //         forbid: 0, // 禁忌证人数
    //         otherDisease: 0, // 其他疾病人数
    const codeObj = {
      目前未见异常: 'normal',
      复查: 're_examination',
      疑似职业病: 'suspected',
      禁忌证: 'forbid',
      其他疾病或异常: 'otherDisease',
    };
    return codeObj[value] || 'normal';
  }

  /**  处理危害因素 3.8
   * @param {String} value - 危害因素值 190902
   * @return {String} - 返回危害因素  有机锡
   */
  getHarmFactors(value) {
    // value转换为数字
    value = +value;
    const codeObj = {
      11: '粉尘',
      110001: '矽尘',
      110002: '煤尘（游离 SiO2 含量＜10%）',
      110003: '石棉（石棉含量＞10%）粉尘纤维',
      110005: '棉尘',
      110007: '电焊烟尘',
      110008: '硬质合金粉尘',
      110010: '沸石粉尘',
      110011: '滑石粉尘（游离 SiO2 含量＜10%）',
      110012: '石墨粉尘',
      110013: '炭黑粉尘',
      110014: '水泥粉尘（游离 SiO2 含量<10 %）',
      110015: '云母粉尘',
      110016: '陶土粉尘',
      110017: '铝尘（铝金属、铝合金粉尘氧化铝粉尘）',
      110018: '铸造粉尘',
      110100: '白云石粉尘',
      110101: '玻璃钢粉尘',
      110102: '茶尘',
      110103: '沉淀 SiO2（白炭黑）',
      110104: '大理石粉尘 （碳酸钙）',
      110105: '二氧化钛粉尘',
      110106: '酚醛树酯粉尘',
      110107: '谷物粉尘（游离 SiO2 含量＜10%）',
      110108: '硅灰石粉尘',
      110109: '硅藻土粉尘（游离 SiO2 含量＜10%）',
      110110: '活性炭粉尘',
      110111: '聚丙烯粉尘',
      110112: '聚丙烯腈纤维粉尘',
      110113: '聚氯乙烯粉尘',
      110114: '聚乙烯粉尘',
      110115: '麻尘（亚麻、黄麻、芒麻）',
      110116: '木粉尘（硬）',
      110117: '凝聚 SiO2 粉尘',
      110118: '膨润土粉尘',
      110119: '皮毛粉尘',
      110120: '人造矿物纤维绝热棉粉尘（玻璃棉、矿渣棉、岩棉）',
      110121: '桑蚕丝尘',
      110122: '砂轮磨尘',
      110123: '石膏粉尘（硫酸钙）',
      110124: '石灰石粉尘',
      110125: '碳化硅粉尘',
      110126: '碳纤维粉尘',
      110127: '稀土粉尘（游离 SiO2 含量<10 %）',
      110128: '洗衣粉混合尘',
      110129: '烟草尘',
      110130: '萤石混合性粉尘',
      110131: '珍珠岩粉尘',
      110132: '蛭石粉尘',
      110133: '重晶石粉尘（硫酸钡）',
      110134: '工业酶混合尘',
      110135: '过氯酸铵粉尘',
      110136: '锑及其化合物粉尘',
      110137: '铁及其化合物粉尘',
      110140: '锡及其化合物粉尘',
      110999: '其他粉尘',
      12: '化学有害因素',
      120001: '四乙基铅（按 Pb 计）',
      120002: '汞-有机汞化合物（按 Hg 计）',
      120003: '锰及其无机化合物（按 MnO2 计）',
      120004: '铍及其化合物（按 Be 计）',
      120005: '镉及其化合物（按 Cd 计）',
      120006: '铬及其化合物',
      120007: '氧化锌',
      120008: '砷',
      120009: '砷化氢（胂）',
      120010: '黄磷',
      120011: '磷化氢',
      120012: '钡及其可溶性化合物（按 Ba 计）',
      120013: '钒及其化合物（按 V 计）',
      120014: '有机锡',
      120015: '铊及其可溶性化合物（按 Tl 计）',
      120016: '羰基镍（按 Ni 计）',
      120017: '氟及其化合物（不含氟化氢）（按 F 计）',
      120018: '苯',
      120019: '二硫化碳',
      120020: '四氯化碳',
      120021: '甲醇',
      120022: '汽油、溶剂汽油',
      120023: '溴甲烷',
      120024: '1,2-二氯乙烷',
      120025: '正己烷',
      120026: '苯的氨基与硝基化合物（不含三硝基甲苯）',
      120027: '三硝基甲苯',
      120028: '联苯胺（4,4’-二氨基联苯）',
      120029: '氯，氯气',
      120030: '二氧化硫',
      120031: '氮氧化物（一氧化氮和二氧化氮）',
      120032: '氨',
      120033: '光气（碳酰氯）',
      120034: '甲醛',
      120035: '一甲胺',
      120036: '一氧化碳',
      120037: '硫化氢',
      120038: '氯乙烯',
      120039: '三氯乙烯',
      120040: '氯丙烯',
      120041: 'β-氯丁二烯（氯丁二烯）',
      120042: '有机氟聚合物单体及其热裂解物',
      120043: '二异氰酸甲苯酯',
      120044: '二甲基甲酰胺',
      120045: '氰及其腈类化合物',
      120046: '酚',
      120047: '五氯酚及其钠盐',
      120048: '氯甲醚',
      120049: '丙烯酰胺',
      120050: '偏二甲基肼',
      120051: '硫酸二甲酯',
      120052: '有机磷',
      120053: '氨基甲酸酯类',
      120054: '拟除虫菊酯',
      120057: '焦炉逸散物（按苯溶物计）',
      120058: '铅及其无机化合物（按 Pb 计，不包括四乙基铅）',
      120059: '砷及其无机化合物（按 As 计）',
      120060: '三氧化铬、铬酸盐、重铬酸盐（按 Cr 计）',
      120061: '煤焦油',
      120062: 'β萘胺',
      120200: '安妥（α-萘硫脲）',
      120201: '2-氨基吡啶',
      120202: '氨基磺酸铵',
      120203: '奥克托今（环四次甲基四硝胺）',
      120204: '巴豆醛（丁烯醛）',
      120205: '百草枯（1,1-二甲基-4,4-联吡啶鎓盐二氯化物）',
      120206: '百菌清',
      120207: '倍硫磷',
      120208: '苯基醚（二苯醚）',
      120209: '苯醌',
      120210: '苯硫磷',
      120211: '苯乙烯',
      120212: '吡啶',
      120213: '苄基氯',
      120214: '丙酸',
      120215: '丙烯醇',
      120217: '丙烯菊酯',
      120218: '丙烯醛',
      120219: '丙烯酸',
      120220: '丙烯酸甲酯',
      120221: '丙烯酸正丁酯',
      120222: '草甘膦',
      120223: '草酸',
      120224: '重氮甲烷',
      120225: '抽余油（60 ℃~220 ℃）',
      120226: '臭氧',
      120227: '二氯二苯基三氯乙烷（滴滴涕，DDT）',
      120228: 'O,O-二甲基-（2,2,2-三氯-1 羟基乙基）磷酸酯（敌百虫）',
      120229: 'N-3,4-二氯苯基-N`,N`-二甲基脲（敌草隆）',
      120230: 'o,o-二甲基-S-（甲基氨基甲酰甲基）二硫代磷酸酯（乐果）',
      120231: '2,4-二氯苯氧基乙酸（2,4-滴）',
      120232: '碲及其化合物（不含碲化氢）（按 Te 计）',
      120233: '碲化铋（按 Bi2Te3 计）',
      120234: '碘',
      120235: '碘仿',
      120236: '叠氮酸蒸气',
      120237: '叠氮化钠',
      120238: '丁醇',
      120239: '1,3-丁二烯',
      120240: '2-丁氧基乙醇',
      120241: '丁醛',
      120242: '丁酮',
      120243: '丁烯',
      120244: '毒死蜱',
      120245: '对苯二胺',
      120246: '对苯二甲酸',
      120247: '对二氯苯（二氯苯）',
      120248: '对硫磷',
      120249: '对特丁基甲苯',
      120250: '对硝基苯胺',
      120251: '对硝基氯苯',
      120252: '多次甲基多苯基多异氰酸酯',
      120253: '二苯胺',
      120254: '二苯基甲烷二异氰酸酯',
      120255: '二丙二醇甲醚（2-甲氧基甲乙氧基丙醇）',
      120256: '二丙酮醇',
      120257: '2-N-二丁氨基乙醇',
      120258: '二噁烷',
      120259: '二噁英类化合物',
      120260: '二氟氯甲烷',
      120261: '二甲胺',
      120262: '二甲苯（全部异构体）',
      120263: 'N,N-二甲基苯胺',
      120264: '1,3-二甲基丁基乙酸酯（仲乙酸己酯、乙酸仲己酯）',
      120265: '二甲基二氯硅烷',
      120266: '3,3-二甲基联苯胺',
      120267: '二甲基亚砜',
      120268: '二甲基乙酰胺',
      120269: '二甲氧基甲烷',
      120270: '二聚环戊二烯',
      120271: '1,1-二氯-1-硝基乙烷',
      120272: '1,3-二氯丙醇',
      120273: '1,2-二氯丙烷',
      120274: '1,3-二氯丙烯',
      120275: '二氯二氟甲烷',
      120276: '二氯甲烷',
      120277: '二氯乙炔',
      120278: '1,2-二氯乙烯（全部异构体）',
      120279: '二硼烷（乙硼烷）',
      120280: '二缩水甘油醚',
      120281: '二硝基苯（全部异构体）',
      120282: '二硝基甲苯',
      120283: '4,6-二硝基邻甲酚',
      120284: '2,4-二硝基氯苯',
      120285: '二氧化氯',
      120286: '二氧化碳',
      120287: '二氧化锡（按 Sn 计）',
      120288: '2-二乙氨基乙醇',
      120289: '二乙烯三胺（二乙撑三胺）',
      120290: '二乙基甲酮',
      120291: '二乙烯基苯',
      120292: '二异丁基甲酮',
      120293: '甲苯 -2,4- 二异氰酸酯（TDI）',
      120294: '二月桂酸二丁基锡',
      120295: '呋喃',
      120296: '氟化氢（按 F 计）',
      120297: '锆及其化合物（按 Zr 计）',
      120298: '汞-金属汞（蒸气）',
      120299: '钴及其化合物（按 Co 计）',
      120300: '癸硼烷',
      120301: '过氧化苯甲酰',
      120302: '过氧化甲乙酮',
      120303: '过氧化氢',
      120304: '环己胺',
      120305: '环己醇',
      120306: '环己酮',
      120307: '环己烷',
      120308: '环三次甲基三硝胺（黑索今）',
      120309: '环氧丙烷',
      120310: '环氧氯丙烷',
      120311: '邻-茴香胺，对-茴香胺',
      120312: '己二醇',
      120313: '1,6-己二异氰酸酯（六亚甲基二异氰酸酯（HDI））',
      120314: '己内酰胺',
      120315: '2-己酮（甲基正丁基甲酮）',
      120316: '甲拌磷',
      120317: '甲苯',
      120318: 'N-甲苯胺，O-甲苯胺',
      120319: '甲酚（全部异构体）',
      120320: '甲基丙烯腈',
      120321: '甲基丙烯酸',
      120322: '甲基丙烯酸甲酯（异丁烯酸甲酯）',
      120323: '甲基丙烯酸缩水甘油酯',
      120324: '甲基肼',
      120325: '甲基内吸磷',
      120326: '18-甲基炔诺酮（炔诺孕酮）',
      120327: '甲基叔丁基醚',
      120328: '甲硫醇',
      120329: '甲酸',
      120330: '甲乙酮（2-丁酮）',
      120331: '2-甲氧基乙醇（甲氧基乙醇）',
      120332: '2-甲氧基乙基乙酸酯',
      120333: '甲氧氯',
      120334: '间苯二酚',
      120335: '肼',
      120336: '久效磷',
      120337: '糠醇',
      120338: '糠醛',
      120339: '考的松',
      120340: '苦味酸（2,4,6-三硝基苯酚）',
      120341: '联苯',
      120342: '邻苯二甲酸二丁酯',
      120343: '邻苯二甲酸酐（PA）',
      120344: '邻二氯苯',
      120345: '邻氯苯乙烯',
      120346: '邻氯苄叉丙二腈',
      120347: '邻仲丁基苯酚',
      120348: '磷胺',
      120349: '磷酸',
      120350: '磷酸二丁基苯酯',
      120351: '硫酸钡（按 Ba 计）',
      120352: '硫酸及三氧化硫',
      120353: '硫酰氟',
      120354: '六氟丙酮',
      120355: '六氟丙烯',
      120356: '六氟化硫',
      120357: '六六六（六氯环已烷）',
      120358: 'γ-六六六（γ-六氯环己烷）',
      120359: '六氯丁二烯',
      120360: '六氯环戊二烯',
      120361: '六氯萘',
      120362: '六氯乙烷',
      120363: '氯苯',
      120364: '氯丙酮',
      120365: '氯化铵烟',
      120366: '氯化汞（升汞）',
      120367: '氯化苦（三氯硝基甲烷）',
      120368: '氯化氢及盐酸',
      120369: '氯化锌烟',
      120370: '氯甲烷',
      120371: '氯联苯（54 %氯）',
      120372: '氯萘',
      120373: '氯乙醇',
      120374: '氯乙醛',
      120375: 'α-氯乙酰苯',
      120376: '氯乙酰氯',
      120377: '马拉硫磷',
      120378: '马来酸酐',
      120379: '吗啉',
      120380: '煤焦油沥青挥发物（按苯溶物计）',
      120381: '钼及其化合物（按 Mo 计）',
      120382: '内吸磷',
      120383: '萘',
      120384: '萘酚',
      120385: '萘烷',
      120386: '尿素',
      120387: '镍及其无机化合物(按 Ni 计) ',
      120388: '氢化锂',
      120389: '氢醌（对苯二酚）',
      120390: '氢氧化钾',
      120391: '氢氧化钠',
      120392: '氢氧化铯',
      120393: '氰氨化钙',
      120394: '氰戊菊酯',
      120395: '全氟异丁烯',
      120396: '壬烷',
      120397: '乳酸正丁酯',
      120398: '三氟化氯',
      120399: '三氟化硼',
      120400: '三氟甲基次氟化物',
      120401: '三甲苯磷酸酯（全部异构体）',
      120402: '三甲基氯化锡',
      120403: '1,2,3-三氯丙烷',
      120404: '三氯化磷',
      120405: '三氯甲烷（氯仿）',
      120406: '三氯硫磷',
      120407: '三氯氢硅',
      120408: '三氯氧磷',
      120409: '三氯乙醛',
      120410: '1,1,1-三氯乙烷',
      120411: '三溴甲烷',
      120412: '三乙基氯化锡',
      120413: '杀螟松',
      120414: '杀鼠灵（3-（1-丙酮基苄基）-4-羟基香豆素；华法林）',
      120415: '石蜡烟',
      120416: '十溴联苯醚',
      120417: '石油沥青烟(按苯溶物计)',
      120418: '双（巯基乙酸）二辛基锡',
      120419: '双酚 A ',
      120420: '双硫醒',
      120421: '双氯甲醚',
      120422: '四氯乙烯',
      120423: '四氢呋喃',
      120424: '四氢化硅',
      120425: '四氢化锗',
      120426: '四溴化碳',
      120427: '松节油',
      120428: '钽及其氧化物（按 Ta 计）',
      120429: '碳酸钠（纯碱）',
      120430: '羰基氟',
      120431: '锑及其化合物（按 Sb 计）',
      120432: '铜及其化合物（按 Cu 计）',
      120433: '钨及其不溶性化合物（按 W 计）',
      120434: '五氟（一）氯乙烷',
      120435: '五硫化二磷',
      120436: '五羰基铁（按 Fe 计）',
      120437: '五氧化二磷',
      120438: '戊醇',
      120439: '戊烷（全部异构体）',
      120440: '硒化氢（按 Se 计）',
      120441: '硒及其化合物（按 Se 计）（不包括六氟化硒、硒化氢）',
      120442: '纤维素',
      120443: '硝化甘油',
      120444: '硝基苯',
      120445: '1-硝基丙烷（硝基丙烷）',
      120446: '2-硝基丙烷',
      120447: '硝基甲苯（全部异构体）',
      120448: '硝基甲烷',
      120449: '硝基乙烷',
      120450: '辛烷',
      120451: '溴',
      120452: '溴化氢（氢溴酸）',
      120453: '溴丙烷（1-溴丙烷；2-溴丙烷）',
      120454: '溴氰菊酯',
      120455: '溴鼠灵',
      120456: '氧化钙',
      120457: '氧化镁烟',
      120458: '氧乐果',
      120459: '液化石油气',
      120460: '乙胺',
      120461: '乙苯',
      120462: '乙醇胺（氨基乙醇）',
      120463: '乙二胺（乙烯二胺，EDA）',
      120464: '乙二醇',
      120465: '乙二醇二硝酸酯',
      120466: '乙酐（乙酸酐）',
      120467: 'N-乙基吗啉',
      120468: '乙基戊基甲酮',
      120469: '乙腈',
      120470: '乙硫醇',
      120471: '乙醚',
      120472: '乙醛',
      120473: '乙酸',
      120474: '乙酸丙酯',
      120475: '乙酸丁酯',
      120476: '乙酸甲酯',
      120477: '乙酸戊酯（全部异构体）',
      120478: '乙酸乙烯酯',
      120479: '乙酸乙酯',
      120480: '乙烯酮',
      120481: '乙酰甲胺磷',
      120482: '乙酰水杨酸（阿司匹林）',
      120483: '2-乙氧基乙醇',
      120484: '2-乙氧基乙基乙酸酯',
      120485: '钇及其化合物（按 Y 计）',
      120486: '异丙胺',
      120487: '异丙醇',
      120488: 'N-异丙基苯胺',
      120489: '异稻瘟净',
      120490: '3，5，5-三甲基-2-环己烯-1-酮（异佛尔酮）',
      120491: '异佛尔酮二异氰酸酯',
      120492: '异氰酸甲酯',
      120493: '异亚丙基丙酮',
      120494: '铟及其化合物（按 In 计）',
      120495: '茚',
      120496: '莠去津',
      120497: '正丙醇',
      120498: '正丁胺',
      120499: '正丁醇',
      120500: '正丁基硫醇',
      120501: '正丁基缩水甘油醚',
      120502: '正丁醛',
      120503: '正庚烷',
      120505: '萘二异氰酸酯（NDI）',
      120506: 'N,N-二甲基-3-氨基苯酚',
      120507: '1,1-二氯乙烯',
      120508: '甲烷',
      120509: '正香草酸（高香草酸）',
      120510: '酚醛树脂',
      120511: '二溴氯丙烷',
      120512: '多氯联苯',
      120513: '1,3-二氯丙烷',
      120514: '二硫化硒',
      120515: '三氯乙酸',
      120516: '氯酸钾',
      120517: '-3,4 二氯苯基丙酰胺（敌稗）',
      120518: '丙酮醛（甲基乙二醛）',
      120519: '双丙酮醇',
      120520: '钽及其化合物',
      120521: '吖啶',
      120522: '环戊酮',
      120523: '铀及其化合物',
      120524: '钼酸',
      120525: '卤化水杨酰苯胺（Ν-水杨酰苯胺）',
      120526: '邻苯二甲酸二甲酯',
      120527: '氯化苄烷胺（洁尔灭）',
      120528: '二硝基苯酚',
      120529: '三氧化钼',
      120530: '多氯萘',
      120531: '氯酸钠',
      120532: '钾盐镁矾',
      120533: '多溴联苯',
      120534: '柴油',
      120535: '木馏油（焦油）',
      120536: '锂及其化合物',
      120537: '亚硝酸乙酯',
      120538: '甲酸乙酯',
      120539: '环氧树脂',
      120540: '乙炔',
      120541: '五氟氯乙烷',
      120542: '三氯一氟甲烷',
      120543: '对氨基酚',
      120544: '二乙烯二胺（哌嗪）',
      120545: '乙酸苄酯',
      120546: '多氯苯',
      120547: '亚硫酸钠',
      120548: '四氯乙烷',
      120549: '氢氧化铵',
      120550: '铂化物',
      120551: '巯基乙酸',
      120552: '聚氯乙烯热解物',
      120553: '1,2,4-苯三酸酐（TMA）',
      120554: '围涎树碱',
      120555: '对溴苯胺',
      120556: '钼酸铵',
      120557: '氟乙酰胺',
      120558: '二苯胍',
      120559: '烯丙胺',
      120560: '铜及其化合物',
      120561: '丙醇',
      120562: '铝酸钠',
      120563: '乙基另戊基甲酮（5-甲基-3-庚酮）',
      120564: '丙烯基芥子油',
      120565: '氯磺酸',
      120566: '苯乙醇',
      120567: '二甲苯酚',
      120568: '氯乙烷',
      120569: '二异丙胺基氯乙烷',
      120570: '二氯乙醚',
      120571: '脲醛树脂',
      120572: '蒽醌及其染料',
      120573: '氯乙基胺',
      120574: '氯甲酸三氯甲酯（双光气）',
      120575: '三聚氰胺甲醛树脂',
      120576: '2-氯苯基羟胺',
      120577: '氟乙酸钠',
      120578: '过硫酸盐（过硫酸钾、过硫酸钠、过硫酸铵等）',
      120579: '磷酸三邻甲苯酯',
      120580: '二氯酚',
      120581: '钼酸钠',
      120582: '碳酸铵',
      120583: '氧化银',
      120584: '乙基硫代磺酸乙酯',
      120585: '二氯化砜（磺酰氯）',
      120586: '多次甲基多苯基异氰酸酯',
      120587: '硝基萘',
      120588: '三氟甲基次氟酸酯',
      120589: '蒽',
      120590: '苯肼',
      120591: '四氯化硅',
      120592: '碳酸钙',
      120593: '1,2,3-苯三酚（焦棓酚）',
      120594: '氯甲酸甲酯',
      120595: '三乙烯四胺（三乙撑四胺）',
      120596: '乙酸异丙酯',
      120597: '羟基香茅醛',
      120598: '硝基萘胺',
      120599: '邻茴香胺',
      120600: '4-氯苯基羟胺',
      120601: '杀虫脒',
      120602: '1,6-己二胺',
      120603: '苯基羟胺（苯胲）',
      120604: '苄基溴（溴甲苯）',
      120605: '1,3-二氯-2-丙醇',
      120606: '溴乙烷',
      120607: '甲基氨基酚',
      120608: '四氯化钛',
      120609: '苯绕蒽酮',
      120610: '甲酸丁酯',
      120611: '羟基乙酸',
      120612: '三甲基己二酸',
      120613: '硼烷',
      120614: '三氯化硼',
      120615: '甲酸甲酯',
      120616: '对苯二甲酸二甲酯',
      120617: '丙烷',
      120618: '4,6-二硝基邻苯甲酚',
      120619: '多氯酚',
      120620: '双-(二甲基硫代氨基甲酰基)二硫化物（秋兰姆、福美双）',
      120621: '3-氯苯基羟胺',
      120622: '异丙醇胺（1-氨基-2-二丙醇）',
      120623: '2-溴乙氧基苯',
      120624: '溴苯',
      120625: '磷化锌',
      120626: '磷化铝',
      120627: '二苯亚甲基二异氰酸酯（MDI）',
      120628: '四氯苯二酸酐（TCPA）',
      120630: '氯乙酸',
      120631: '环氧乙烷',
      120632: '碘甲烷',
      120633: '丙酮',
      120634: '苯胺',
      120900: 'N-3,4 二氯苯基丙酰胺（敌稗）',
      120901: '氢氟酸',
      120902: '硝酸',
      120903: '烷酸',
      120904: '氰化氢',
      120999: '其他化学有害因素',
      13: '物理有害因素',
      130001: '噪声',
      130002: '振动',
      130003: '高温',
      130004: '高气压',
      130006: '微波',
      130009: '紫外辐射（紫外线）',
      130300: '超高频电磁场（超高频辐射）',
      130301: '高频电磁场（高频辐射）',
      130302: '工频电磁场（工频辐射）',
      130303: '低气压',
      130304: '高原低氧',
      130305: '红外线',
      130306: '激光',
      130307: '低温',
      130999: '其他物理有害因素',
      14: '生物因素',
      140001: '布鲁菌属',
      140002: '炭疽杆菌',
      140400: '白僵蚕孢子',
      140401: '枯草杆菌蛋白酶',
      140402: '工业酶',
      140403: '森林脑炎病毒',
      140404: '艾滋病病毒',
      140405: '伯氏疏螺旋体',
      140999: '其他生物有害因素',
      15: '其他职业病危害因素',
      150001: '电工作业',
      150002: '高处作业',
      150003: '压力容器作业',
      150004: '结核病防治工作',
      150005: '肝炎病防治工作',
      150006: '职业机动车驾驶作业',
      150007: '视屏作业',
      150008: '高原作业',
      150009: '航空作业',
      150999: '其他特殊作业',
      16: '放射物质类',
      160002: 'X射线',
      160501: 'α射线',
      160502: 'β射线',
      160503: 'γ射线',
      160504: '中子',
      160506: '铀及其化合物',
      160507: '氡及其短寿命子体',
      160999: '其他',
      19: '其他因素',
      190900: '井下不良作业条件',
      190901: '金属烟',
      190902: '刮研作业',
    };
    return codeObj[value] || '';
  }

  /** 处理男女格式
   * @param {String} value - 男女值 男 女
   * @return {String} - 返回男0女1
   */
  getGender(value) {
    const codeObj = {
      男: '0',
      女: '1',
    };
    return codeObj[value];
  }

  /**
   * 将log 写入到 ctx.app.config.upload_fzDataLog_path 下的日期文件夹下
   * @param log
   * @param name
   */
  async writeLogToZipFolder(log, name = 'Info') {
    const { ctx } = this;
    try {
      const DATE = new Date();
      const filesName = moment(DATE).format('YYYYMMDD'); // 文件夹名称
      const configFilePath = path.resolve(
        path.join(ctx.app.config.upload_shaanxiDataLog_path, `/${filesName}`)
      );
      await mkdirp(configFilePath);
      fs.writeFileSync(
        path.join(
          configFilePath,
          `${moment(DATE).format('HHmmss')}Create${name}.json`
        ),
        JSON.stringify(log)
      );
    } catch (error) {
      ctx.auditLog(
        '将log 写入到 ctx.app.config.upload_shaanxiDataLog_path 下的日期文件夹下',
        `${error}`,
        'error'
      );
    }
  }

  /** 校验参数是否为空的方法
   * @param {Object} params - 参数对象
   * @param {Array} requiredFields - 必填字段数组
   * @param {String} requiredFields.field - 必填字段
   * @param {String} requiredFields.message - 必填字段的中文名称
   * @param {RegExp} requiredFields.format - 必填字段的格式
   * @return {Boolean} - 返回校验结果
   * @throws {Error} - 如果参数为空，则抛出错误
   * @example
   * validateParams({ name: '张三' }, [{ field: 'name', message: '姓名' }])
   * // => true
   * validateParams({ name: '' }, [{ field: 'name', message: '姓名' }])
   * // => Error: 姓名不能为空
   * validateParams({ name: '张三' }, [{ field: 'name', message: '姓名', format: /^[\u4e00-\u9fa5]{2,4}$/ }])
   * // => true
   * validateParams({ name: '张三' }, [{ field: 'name', message: '姓名', format: /^[\u4e00-\u9fa5]{2,4}$/ }])
   * // => Error: 姓名格式不正确/^[\u4e00-\u9fa5]{2,4}$/
   */
  validateParams(params, requiredFields) {
    const errorMessage = '%s不能为空';
    for (const { field, message, format } of requiredFields) {
      if (!params[field] && params[field] !== 0) {
        throw new Error(errorMessage.replace('%s', message));
      }
      if (format && !format.test(params[field])) {
        throw new Error(
          errorMessage.replace('%s', message) +
            `或格式不正确${format.toString().slice(1, -1)}`
        );
      }
    }
    return true;
  }

  /** 处理计量单位
   * @param {String} value - 计量单位值
   * @return {String}
   */
  getUnit(value) {
    const codeObj = {
      1025: '次/分',
      1026: 'cm',
      1028: 'mmHg',
      1027: 'Kg',
      1003: '%',
      1047: 'kg/㎡',
      1049: '无',
      1002: 'dB',
      1012: '10^9/L',
      1018: 'fL',
      1001: '10^12/L',
      1048: 'L/L',
      1015: 'pg',
      1013: 'g/L',
      1005: 'mg/L',
      1037: 'μg/L',
      1052: 'U/ml',
      1017: 'umol/L',
      1029: 'ng/ml',
      1030: 'PEIU/ml',
      1031: 'COI',
      1032: 'IU/L',
      1033: 'IU/ML',
      1016: 'U/L',
      1004: 'mmol/L',
      1007: 'ug/L',
      1057: 'mmol/mol Cr',
      1059: 'mg/g Cr',
      1058: 'μg/g Cr',
      1056: 'μmol/mol Cr',
      1021: 'μmol/L',
      1042: 'mg/mL',
      1041: 'moml',
      1019: 'SG',
      1022: '个/HP',
      1060: '个/LP',
      1061: '个/ul',
      1053: '/uL',
      1062: '/HPF',
      1014: 'PH',
      1044: '个',
      1043: 'ku/L',
      1045: 'mm/h',
      1008: 'ummol/L',
      1023: 'mmol／L',
      1020: 'μmo1/L',
      1051: '‰',
      1034: '1分视角',
      1035: 'mm',
      1036: 'S',
      1054: 'uIU/ml',
      1040: 'mIU/L',
      1039: 'pmol/L',
    };
    return codeObj[value] || value;
  }

  // 定时处理interfaceLog表中的体检数据
  async handleInterfaceLog(params = {}) {
    const { ctx } = this;
    const { handleDStartTime, handleEndTime, orgId } = params;
    const query = {
      requestUrl: '/api/shaanxiReportCard',
    };
    if (handleDStartTime && handleEndTime) {
      // 处理开始结束时间
      query.createdAt = {
        $gte: handleDStartTime,
        $lt: handleEndTime,
      };
    }
    if (orgId) {
      query.orgId = orgId;
    }
    const data = await ctx.model.InterfaceLog.find(query);
    if (data.length === 0) return;
    for (const item of data) {
      const handleData = item.requestBody.DATAS.PERSONS.PERSON;
      if (Object.prototype.toString.call(handleData) === '[object Object]') {
        await this.shaanxiHealthExamRecordV1_0(orgId, handleData);
        ctx.auditLog(
          '定时处理interfaceLog表中的体检数据',
          `处理成功，处理数据为${handleData.TD_TJ_BHK.PERSON_NAME}，体检编号为${handleData.TD_TJ_BHK.BHK_CODE}`,
          'info'
        );
      }
    }
  }

  // 处理体检总报告各人数问题
  async handleHealthCheckAccount(params = {}) {
    const { ctx } = this;
    const { batch } = params;

    try {
      // 使用 aggregate 查询 Suspect 模型，并进行分组统计
      const result = await ctx.model.Suspect.aggregate([
        { $match: { batch, reportCode: { $exists: true }, CwithO: { $ne: '' } } }, // 过滤 batch 字段
        {
          $group: { // 按 CwithO 字段分组
            _id: '$CwithO',
            count: { $sum: 1 }, // 统计每个分组的数量
          },
        },
        {
          $project: { // 映射 CwithO 到对应的状态
            _id: 0,
            status: {
              $switch: {
                branches: [
                  { case: { $eq: [ '$_id', '目前未见异常' ] }, then: 'normal' },
                  { case: { $eq: [ '$_id', '复查' ] }, then: 're_examination' },
                  { case: { $eq: [ '$_id', '疑似职业病' ] }, then: 'suspected' },
                  { case: { $eq: [ '$_id', '禁忌证' ] }, then: 'forbid' },
                  { case: { $eq: [ '$_id', '其他疾病或异常' ] }, then: 'otherDisease' },
                ],
                default: 'unknown', // 默认值，如果没有匹配的 CwithO
              },
            },
            count: 1,
          },
        },
      ]);

      // 处理统计结果
      if (result.length === 0) return;
      const shouldCheckNum = result.filter(item => item.status !== 'unknown').reduce((acc, item) => acc + item.count, 0);
      if (shouldCheckNum === 0) return;
      const normal = result.find(item => item.status === 'normal')?.count || 0;
      const re_examination = result.find(item => item.status === 're_examination')?.count || 0;
      const suspected = result.find(item => item.status === 'suspected')?.count || 0;
      const forbid = result.find(item => item.status === 'forbid')?.count || 0;
      const otherDisease = result.find(item => item.status === 'otherDisease')?.count || 0;
      await ctx.model.Healthcheck.updateOne({
        _id: batch,
      }, {
        $set: {
          shouldCheckNum,
          actuallNum: shouldCheckNum,
          normal,
          re_examination,
          suspected,
          forbid,
          otherDisease,
        },
      });
    } catch (error) {
      ctx.auditLog('处理体检总报告各人数问题失败', `${error}`, 'error');
    }

  }

  // 合并体检报告和体检数据
  async mergeHealthCheckData() {
    const { ctx } = this;
    try {
      const fileInfo = await ctx.model.Suspect.find({
        'caseCard.originName': { $ne: '' },
        batch: { $exists: false },
        reportCode: { $exists: true },
      });
      if (fileInfo.length === 0) return;
      const updateOps = [];
      for (const item of fileInfo) {
        const caseCard = item.caseCard;
        const updateOp = {
          updateOne: {
            filter: { reportCode: item.reportCode, batch: { $exists: true } },
            update: {
              $set: {
                caseCard,
              },
            },
          },
        };
        updateOps.push(updateOp);
      }
      await ctx.model.Suspect.bulkWrite(updateOps);
    } catch (error) {
      ctx.auditLog('合并体检报告和体检数据失败', `${error}`, 'error');
    }
  }
}

module.exports = ShaanxiService;
