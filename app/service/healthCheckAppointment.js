// 浙江体检标准 - 体检系统对接
const Service = require('egg').Service;
const moment = require('moment');
const economicTypeCode = require('../utils/codeName/economicTypeCode'); // 经济类型（GB/T12402-2000《经济类型分类与代码》）
class HealthCheckAppointmentService extends Service {
  // 浙江 根据code获取危害因素名称
  async getHarmFactorsByCode(hazardCode) {
    if (!hazardCode) return '';
    const res = await this.ctx.model.OccupationalexposureLimits.findOne({ zjTjCode: hazardCode }, { showName: 1 });
    if (res) return res.showName;
    const res2 = await this.ctx.model.ZjTjHazard.findOne({ code: hazardCode }, { name: 1 });
    return res2 ? res2.name : hazardCode;
  }
  async zjItemParameterVerification(item) { // 其他体检参数校验
    if (!item.userInfo) throw new Error('userInfo不能为空');
    if (!item.userInfo.idcardCode || !item.userInfo.name || !item.userInfo.telPhone || !item.userInfo.sexCode) {
      throw new Error('userInfo的idcardCode name telPhone sexCode不能为空');
    }
    if (![ '1', '2' ].includes(item.userInfo.sexCode)) throw new Error('sexCode参数值错误: ' + item.userInfo.sexCode);
    if (!/^1[3-9]\d{9}$/.test(item.userInfo.telPhone)) throw new Error('telPhone格式错误');
    if (item.userInfo.maritalStatusCode && ![ '0', '1', '2', '3', '4' ].includes(item.userInfo.maritalStatusCode)) throw new Error('maritalStatusCode参数值错误' + item.userInfo.maritalStatusCode);
    if (!item.empInfoEmployer) throw new Error('empInfoEmployer不能为空');
    if (!item.empInfoEmployer.creditCodeEmployer) throw new Error('empInfoEmployer的creditCodeEmployer不能为空');
    if (!/^[A-Z0-9]{18}$/.test(item.empInfoEmployer.creditCodeEmployer)) throw new Error('creditCodeEmployer格式错误：' + item.empInfoEmployer.creditCodeEmployer);
    if (!item.empInfoEmployer.areaCodeEmployer) throw new Error('areaCodeEmployer不能为空');
    if (item.empInfo && item.empInfo.creditCode && !/^[A-Z0-9]{18}$/.test(item.empInfo.creditCode)) throw new Error('creditCode格式错误：' + item.empInfo.creditCode);
    if (!item.cardInfo) throw new Error('cardInfo不能为空');
    if (!item.cardInfo.code || !item.cardInfo.checkType || !item.cardInfo.checkTime || !item.cardInfo.bodyCheckType) throw new Error('cardInfo的code checkType checkTime bodyCheckType不能为空');
    if (![ '11', '21' ].includes(item.cardInfo.checkType)) throw new Error('checkType参数值错误' + item.cardInfo.checkType);
    if (![ '1', '2', '3', '4', '5' ].includes(item.cardInfo.bodyCheckType)) throw new Error('bodyCheckType参数值错误' + item.cardInfo.bodyCheckType);
    if (item.cardInfo.checkTime.length !== 8) throw new Error('cardInfo的checkTime格式错误');
    if (!item.orgInfo) throw new Error('orgInfo不能为空');
    if (!item.orgInfo.orgName) throw new Error('orgInfo的orgName不能为空');
    if (!item.diagnosisList || item.diagnosisList.length === 0) throw new Error('diagnosisList不能为空');
    if (!item.hazardFactorList || item.hazardFactorList.length === 0) throw new Error('hazardFactorList不能为空');
    if (!item.itemList || item.itemList.length === 0) throw new Error('itemList不能为空');
    item.projectNumber = item.projectNumber ? item.projectNumber.trim() : '';
    if (!item.projectNumber) throw new Error('总报告编号：projectNumber不能为空');
  }
  async zjItemParameterVerification2(item) { // 普通体检参数校验
    if (!item.userInfo) throw new Error('userInfo不能为空');
    if (!item.userInfo.idcardCode || !item.userInfo.name || !item.userInfo.sexCode) {
      throw new Error('userInfo的idcardCode name sexCode不能为空');
    }
    if (![ '1', '2' ].includes(item.userInfo.sexCode)) throw new Error('sexCode参数值错误: ' + item.userInfo.sexCode);
    if (item.userInfo.telPhone && !/^1[3-9]\d{9}$/.test(item.userInfo.telPhone)) throw new Error('telPhone格式错误');
    if (item.userInfo.maritalStatusCode && ![ '0', '1', '2', '3', '4' ].includes(item.userInfo.maritalStatusCode)) throw new Error('maritalStatusCode参数值错误' + item.userInfo.maritalStatusCode);
    if (!item.empInfoEmployer) throw new Error('empInfoEmployer不能为空');
    if (!item.empInfoEmployer.creditCodeEmployer) throw new Error('empInfoEmployer的creditCodeEmployer为空，系统不做处理');
    if (!/^[A-Z0-9]{18}$/.test(item.empInfoEmployer.creditCodeEmployer)) throw new Error('creditCodeEmployer格式错误：' + item.empInfoEmployer.creditCodeEmployer);
    if (item.empInfo && item.empInfo.creditCode && !/^[A-Z0-9]{18}$/.test(item.empInfo.creditCode)) throw new Error('creditCode格式错误：' + item.empInfo.creditCode);
    if (!item.empInfoEmployer.areaCodeEmployer) throw new Error('areaCodeEmployer不能为空');
    if (!item.cardInfo) throw new Error('cardInfo不能为空');
    if (!item.cardInfo.code || !item.cardInfo.checkTime) throw new Error('cardInfo的code  checkTime bodyCheckType不能为空');
    if (item.cardInfo.checkType && ![ '11', '21' ].includes(item.cardInfo.checkType)) throw new Error('checkType参数值错误' + item.cardInfo.checkType);
    if (item.cardInfo.checkTime.length !== 8) throw new Error('cardInfo的checkTime格式错误');
    if (!item.orgInfo) throw new Error('orgInfo不能为空');
    if (!item.orgInfo.orgName) throw new Error('orgInfo的orgName不能为空');
    if (!item.diagnosisList) item.diagnosisList = [];
    if (!item.hazardFactorList) item.hazardFactorList = [];
    if (!item.itemList || item.itemList.length === 0) throw new Error('itemList不能为空');
    item.projectNumber = item.projectNumber ? item.projectNumber.trim() : '';
    if (!item.projectNumber) throw new Error('总报告编号：projectNumber不能为空');
  }
  // 上传职业健康档案(浙江)
  async zjHealthExamRecordV1_0(physicalExamOrgId, item) {
    try {
      if (!item.cardInfo) throw new Error('cardInfo不能为空');
      if (item.cardInfo.bodyCheckType === '6') { // 普通体检
        await this.zjItemParameterVerification2(item);
      } else {
        await this.zjItemParameterVerification(item);
      }
      item.cardInfo.checkTime = this.getTrueTime(item.cardInfo.checkTime);
      if (item.cardInfo.writeDate && item.cardInfo.writeDate.length === 8) {
        item.cardInfo.writeDate = this.getTrueTime(item.cardInfo.writeDate);
      }
      const { ctx } = this;
      const physicalExamOrg = await ctx.model.PhysicalExamOrg.findOne({ _id: physicalExamOrgId });
      if (!physicalExamOrg.name.includes(item.orgInfo.orgName)) throw new Error('您的token跟上传的orgName不匹配');
      // 1、查询体检预约单
      const EnterpriseCode = item.empInfoEmployer.creditCodeEmployer;
      // const isReview = item.cardInfo.checkType === '21'; // 是否复查
      const checkType = [ '', '01', '02', '03', '05', '04', '06' ][ +item.cardInfo.bodyCheckType ]; // 预约单中的体检类型
      let appointment = null;
      if (item.appointmentId) {
        appointment = await this.ctx.model.HealthCheckAppointment.findOne({ _id: item.appointmentId });
        if (!appointment) throw new Error('预约单不存在：' + item.appointmentId);
        // if (appointment.EnterpriseCode !== EnterpriseCode) throw new Error('用工单位统一社会信用代码跟预约单不匹配');
        if (appointment.physicalExamOrgId !== physicalExamOrgId) throw new Error('token中体检机构信息跟预约单不匹配');
        // if (appointment.checkType !== checkType) throw new Error('体检类型跟预约单不匹配');
        // const checkDate = new Date(item.cardInfo.checkTime);
        // if (checkDate < appointment.startTime || checkDate > appointment.endTime) {
        //   throw new Error('体检时间checkTime：' + item.cardInfo.checkTime + '不在预约单' + appointment._id + '的时间范围内, 请检查');
        // }
      }
      // 2、处理用人单位信息
      let laborDispatching = false; // 是否为劳务派遣
      if (item.empInfo && item.empInfo.creditCode && item.empInfo.creditCode !== item.empInfoEmployer.creditCodeEmployer) {
        ctx.auditLog('体检系统对接 上传职业健康档案', '开始处理用人单位信息', 'info');
        await this.handleZjEnterprise(item.empInfo.creditCode, item.empInfo, physicalExamOrg);
        laborDispatching = true;
      }
      // 3、处理用工单位信息
      if (item.empInfo && (item.empInfo.creditCode === item.empInfoEmployer.creditCodeEmployer)) {
        item.empInfoEmployer.contactPerson = item.empInfo.contactPerson || '';
        item.empInfoEmployer.employerPhone = item.empInfo.employerPhone || '';
      }
      const Enterprise = await this.handleZjEnterprise(EnterpriseCode, item.empInfoEmployer, physicalExamOrg, appointment);
      // 4、人员信息
      const workerInfo = item.userInfo;
      const jobCode = workerInfo.jobCode || workerInfo.otherJobName || '';
      const personData = {
        EnterpriseID: Enterprise._id,
        name: workerInfo.name,
        IDNum: workerInfo.idcardCode,
        laborDispatching,
        gender: workerInfo.sexCode === '1' ? '0' : '1',
        phoneNum: workerInfo.telPhone || '',
        // userId: '',
        source: 'oapi',
        status: [ '03', '05' ].includes(checkType) ? 0 : 1,
        station: workerInfo.workshop || '', // 车间
        workType: await this.getWorkTypeCode(jobCode), // 工种
        marriage: workerInfo.maritalStatusCode ? [ '未婚', '已婚', '离异', '丧偶', '其他' ][+workerInfo.maritalStatusCode] || '' : '',
        enable: true,
        age: this.getAgeFromIdCard(workerInfo.idcardCode),
      };
      if (!personData.workType) delete personData.workType;
      const personInfo = await this.handlePersonInfo(personData);
      if (!personInfo) throw new Error('人员信息创建失败');
      // 处理车间-工种信息
      if (personInfo.workType) {
        await this.updateWorkspace(personInfo, item, physicalExamOrgId);
      }
      // 5、体检项目
      const healthcheck = await this.handleZjHealthCheck(physicalExamOrg, item, Enterprise, appointment);
      // 6、体检信息
      await this.handleZjSuspect(healthcheck, personInfo, item, appointment);
      // 7、更新预约单
      if (appointment) await this.updateAppointment(appointment, healthcheck._id, personInfo);
      // 8、更新体检项目中的人数以及企业表中的healcheckInfo
      await this.updateHealthCheck(healthcheck, appointment ? appointment._id : '', Enterprise);
      this.ctx.auditLog('体检系统对接 上传职业健康档案成功', `用工单位：${item.empInfoEmployer.employerNameEmployer || Enterprise.cname} - ${item.userInfo.name}`, 'info');
      return {
        status: 200,
        message: `用工单位：${item.empInfoEmployer.employerNameEmployer || Enterprise.cname} - ${item.userInfo.name}的职业健康档案上传成功`,
      };
    } catch (e) {
      console.log(444444, e);
      this.ctx.auditLog('体检系统对接 上传职业健康档案失败', e, 'error');
      return {
        status: 500,
        message: (item.userInfo ? item.userInfo.name : '') + ' 职业健康档案上传失败：' + e.message,
      };
    }
  }
  // 根据身份证号码获取年龄
  getAgeFromIdCard(idCard) {
    if (!idCard) return null;
    // 提取身份证中的出生日期
    const birthYear = idCard.substr(6, 4);
    const birthMonth = idCard.substr(10, 2);
    const birthDay = idCard.substr(12, 2);
    const birthDate = new Date(birthYear, birthMonth - 1, birthDay);
    // 获取当前日期
    const currentDate = new Date();
    // 计算年龄
    let age = currentDate.getFullYear() - birthDate.getFullYear();
    if (currentDate.getMonth() < birthDate.getMonth() ||
        (currentDate.getMonth() === birthDate.getMonth() && currentDate.getDate() < birthDate.getDate())) {
      age--;
    }
    return age;
  }
  // 处理浙江企业信息(用工单位、用人单位)
  async handleZjEnterprise(EnterpriseCode, params, physicalExamOrg, appointment = null) {
    let Enterprise;
    if (appointment && appointment.EnterpriseID) {
      Enterprise = await this.ctx.model.Adminorg.findOne({ _id: appointment.EnterpriseID });
      if (!Enterprise) throw new Error('预约单的企业信息已被删除');
      if (Enterprise.code !== EnterpriseCode) throw new Error(EnterpriseCode + '企业统一社会信用代码错误');
    } else {
      Enterprise = await this.ctx.model.Adminorg.findOne({ code: EnterpriseCode, isDelete: false });
    }
    if (!Enterprise) {
      const areaCode = params.areaCodeEmployer || params.areaCode;
      if (!areaCode) throw new Error(EnterpriseCode + '企业所属所属地区编码不能为空');
      const { districtRegAdd = [], point = [] } = await this.ctx.service.district.getParentByCode(areaCode);
      const industryCategory = []; // 行业类别
      const industryCategoryCode = params.industryCategoryCode || params.industryCategoryCodeEmployer;
      if (industryCategoryCode) {
        const industryCategoryDeatil = await this.ctx.model.IndustryCategory.findOne({ 'children.children.children.value': industryCategoryCode });
        if (industryCategoryDeatil) {
          industryCategoryDeatil.children.forEach(child1 => {
            child1.children.forEach(child2 => {
              child2.children.forEach(child3 => {
                if (child3.value === industryCategoryCode) {
                  industryCategory.push(industryCategoryDeatil.value, child1.value, child2.value, child3.value);
                }
              });
            });
          });
        } else {
          this.ctx.auditLog('温州体检系统对接 上传体检报告-行业类别不存在', industryCategoryCode, 'error');
          // throw new Error('industryCategoryCode或者industryCategoryCodeEmployer参数值错误');
        }
      }
      const economicTypeCode = params.economicTypeCode || params.economicTypeCodeEmployer;
      const enterpriseSizeCode = params.enterpriseSizeCode || params.enterpriseSizeCodeEmployer;
      const companyScaleObj = { 10000: '大型', 10001: '中型', 10002: '小型', 10004: '微型' };
      const address = params.address || params.areaNameEmployer || '';
      const enterpriseData = {
        cname: params.employerNameEmployer || params.employerName || '',
        code: EnterpriseCode,
        regType: economicTypeCode ? economicTypeCode[economicTypeCode] : '',
        industryCategory,
        companyScale: enterpriseSizeCode ? companyScaleObj[enterpriseSizeCode] : '',
        districtRegAdd,
        regAdd: address,
        workAddress: [{ districts: districtRegAdd, point, address }],
        isactive: '1',
        leadIn: '3',
        physicalExaminationOrgID: [{
          _id: physicalExamOrg._id,
          ServiceContractName: physicalExamOrg.contract || '',
          ServicePhoneNum: physicalExamOrg.phoneNum || '',
        }],
        contract: params.contactPerson || '',
        phoneNum: params.employerPhone || '',
      };
      Enterprise = await this.ctx.model.Adminorg.create(enterpriseData);
      this.ctx.auditLog('温州体检系统对接 上传体检报告-创建企业信息成功', Enterprise, 'info');
      // 处理人员信息
      if (Enterprise._id && Enterprise.phoneNum) {
        await this.handlePersonInfo2({
          EnterpriseID: Enterprise._id,
          name: Enterprise.contract,
          phoneNum: Enterprise.phoneNum,
        });
      } else {
        await this.handlePersonInfo3({
          EnterpriseID: Enterprise._id,
          name: Enterprise.contract || enterpriseData.cname || '',
          userName: EnterpriseCode,
        });
      }
    }
    return Enterprise;
  }
  // 更新HealthCheck中的人数以及企业表中的体检信息
  async updateHealthCheck(healthcheck, appointmentId, Enterprise) {
    const appointment = appointmentId ? await this.ctx.model.HealthCheckAppointment.findOne({ _id: appointmentId }) : {};
    const appointmentPeopleNum = (appointment.employeeIds ? appointment.employeeIds.length : appointment.peopleNum) || 0;
    const suspectList = await this.ctx.model.Suspect.find({ batch: healthcheck._id });
    const updateData = {
      shouldCheckNum: appointmentPeopleNum > suspectList.length ? appointmentPeopleNum : suspectList.length,
      actuallNum: suspectList.length,
      recheckNum: suspectList.filter(ele => ele.recheck === '是').length,
      normal: suspectList.filter(ele => ele.CwithO === '目前未见异常').length,
      suspected: suspectList.filter(ele => ele.CwithO === '疑似职业病').length,
      forbid: suspectList.filter(ele => ele.CwithO === '禁忌证').length,
      otherDisease: suspectList.filter(ele => ele.CwithO === '其他疾病或异常').length,
    };
    await this.ctx.model.Healthcheck.updateOne({ _id: healthcheck._id }, updateData);
    await this.ctx.model.Adminorg.updateOne({ _id: Enterprise._id }, { $set: { healcheckInfo: {
      actuallNum: String(updateData.actuallNum), // 实检人数
      recheck: String(updateData.recheckNum), // 复查
      suspected: String(updateData.suspected), // 疑似
      forbid: String(updateData.forbid), // 禁忌证
      occupational: String(Enterprise.occupational || 0), // 职业病
      recentDay: healthcheck.checkDate, // 体检时间
    } } });
  }
  // 按照体检结果更新预约单
  async updateAppointment(appointment, healthcheckId, personInfo) {
    let updateData = { };
    if (appointment.employeeIds.indexOf(personInfo._id) === -1) {
      updateData = { $push: { employeeIds: personInfo._id }, $inc: { peopleNum: 1 } };
    }
    updateData.healthcheckId = healthcheckId;
    let jcRes = true;
    for (let i = 0; i < appointment.employeeIds.length; i++) {
      const employeeId = appointment.employeeIds[i];
      const res = await this.ctx.model.Suspect.findOne({ employeeId, batch: healthcheckId }, { name: 1 });
      if (!res) {
        jcRes = false;
        break;
      }
    }
    updateData.status = jcRes ? 6 : 5;
    const result = await this.ctx.model.HealthCheckAppointment.updateOne({ _id: appointment._id }, updateData);
    if (result.nModified) {
      this.ctx.auditLog('体检系统对接 上传体检报告-更新预约单成功', updateData, 'info');
      this.synchronizeAppointment(appointment._id); // 同步mongodb中的预约单到sqlserver中
    } else {
      this.ctx.auditLog('体检系统对接 上传体检报告-更新预约单失败', result, 'error');
    }
  }
  // 根据appointmentId获取physicalExamOrg的iService2Host
  async getIService2Host(appointmentId) {
    const { ctx, app } = this;
    const appointment = await ctx.model.HealthCheckAppointment.findOne({ _id: appointmentId }, { physicalExamOrgId: 1 });
    if (!appointment) throw new Error('预约单不存在');
    if (!appointment.physicalExamOrgId) throw new Error('体检机构id不存在');
    const physicalExamOrg = await ctx.model.PhysicalExamOrg.findOne({ _id: appointment.physicalExamOrgId }, { iService2Host: 1 });
    if (!physicalExamOrg) throw new Error('体检机构不存在');
    ctx.auditLog('体检系统对接 同步预约单iService2Host', physicalExamOrg.iService2Host, 'info');
    return physicalExamOrg.iService2Host || app.config.iService2Host;
  }
  // 同步mongodb中的预约单到sqlserver中
  async synchronizeAppointment(appointmentId = '') {
    const { ctx, app } = this;
    try {
      if (app.config.branch !== 'wkzwy') return;
      if (!appointmentId) throw new Error('appointmentId不能为空');
      const iService2Host = await this.getIService2Host(appointmentId);
      const { data } = await ctx.curl(`${iService2Host}/wkzwy-appointment`, {
        method: 'POST',
        dataType: 'json',
        data: {
          appointmentId,
        },
      });
      ctx.auditLog('体检系统对接 同步预约单', data, 'info');
    } catch (e) {
      ctx.auditLog('体检系统对接 同步预约单失败', e.message, 'error');
    }
  }
  // 获取体检结果中的CwithO
  getCwithO(EXAM_CONCLUSION_LIST = [], EXAM_ITEM_RESULT_LIST = []) {
    const data = EXAM_CONCLUSION_LIST[0];
    if (data.YSZYB_CODE) return '疑似职业病';
    if (data.ZYJJZ_NAME) return '禁忌证';
    if (data.QTJB_NAME) return '其他疾病或异常';
    if (EXAM_ITEM_RESULT_LIST[0] && EXAM_ITEM_RESULT_LIST[0].ABNORMAL === '1') return '目前未见异常';
    return EXAM_ITEM_RESULT_LIST[0] ? EXAM_ITEM_RESULT_LIST[0].EXAM_RESULT : '目前未见异常';
  }
  // 浙江标准 创建/更新suspect
  async handleZjSuspect(healthcheck, personInfo, item) {
    const userInfo = item.userInfo;
    const jobCode = userInfo.jobCode || userInfo.otherJobName || '';
    const workType = await this.getWorkTypeCode(jobCode);
    let suspect = await this.ctx.model.Suspect.findOne({ employeeId: personInfo._id, batch: healthcheck._id });
    const harmFactors = []; // 危害因素
    const riskFactorsOfPhysicalExaminations = []; // 体检危害因素详情
    if (item.hazardFactorList.length === 1 && Array.isArray(item.hazardFactorList[0])) item.hazardFactorList = item.hazardFactorList[0];
    for (let i = 0; i < item.hazardFactorList.length; i++) {
      const ele = item.hazardFactorList[i];
      let harmFactor = await this.getHarmFactorsByCode(ele.hazardCode);
      if (!harmFactor) harmFactor = ele.otherHazardName;
      if (harmFactor) {
        harmFactors.push(harmFactor);
        riskFactorsOfPhysicalExaminations.push({ harmFactor });
      }
    }
    let workYears = '0'; // 工龄
    if (+item.cardInfo.exposureYear || +item.cardInfo.exposureMonth) {
      workYears = +item.cardInfo.exposureYear ? item.cardInfo.exposureYear + '年' : (+item.cardInfo.exposureMonth ? item.cardInfo.exposureMonth + '个月' : '');
    }
    const abnormalIndex = item.itemList.filter(ele => ele.mark && (ele.mark !== '1')).map(ele => `${ele.otherItemName || ele.itemId}检查结果为${ele.result || ''}，${ele.checkResult || ''}`).join('；'); // 异常指标
    const checkType = [ '0', '1', '2', '5', '4', '6' ][item.cardInfo.bodyCheckType - 1];
    const newData = {
      reportCode: item.cardInfo.code || '',
      organization: item.orgInfo.orgName, // 检查机构名称
      name: personInfo.name,
      age: personInfo.age,
      gender: personInfo.gender,
      workType,
      workYears,
      harmFactors: harmFactors.join('；'),
      otherHarmFactors: item.hazardFactorList.map(ele => ele.otherHazardName).filter(ele => ele).join('；'), // 其他危害因素
      opinion: item.cardInfo.checkResultCode || '', // 意见
      CwithO: item.diagnosisList.map(ele => [ '目前未见异常', '复查', '疑似职业病', '职业禁忌证', '其他疾病或异常' ][ele.conclusion - 1]).filter(ele => ele).join('；'), // 结论
      dedicalAdvice: item.cardInfo.suggest || '无', // 医学建议
      batch: healthcheck._id,
      employeeId: personInfo._id,
      IDCard: personInfo.IDNum,
      EnterpriseID: personInfo.EnterpriseID,
      checkType: checkType || healthcheck.checkType,
      checkDate: item.cardInfo.checkTime,
      recheck: item.cardInfo.checkType === '21' ? '是' : '否',
      abnormalIndex: abnormalIndex || '-', // 异常指标
      riskFactorsOfPhysicalExaminations,
      bhkSubList: item.itemList.map(ele => {
        return {
          itmcod: ele.itemId || '',
          name: ele.otherItemName || '',
          classify: ele.itemGroupName || '',
          msrunt: ele.unit || '', // 计量单位
          itemStdValue: `${ele.min || ''} - ${ele.max || ''}`,
          result: ele.result || '',
          chkdat: this.getTrueTime(ele.checkDate),
          minVal: ele.min || '',
          maxVal: ele.max || '',
          diagRest: ele.checkResult || '',
          rstFlag: ele.mark ? [ '异常', '未见异常', '尘肺样改变', '其他异常' ][+ele.mark] : '',
          jdgptn: ele.type ? +ele.type : 1,
          rgltag: ele.mark && ele.mark === '1' ? 1 : 0,
        };
      }),
    };
    if (suspect) {
      await this.ctx.model.Suspect.updateOne({ _id: suspect._id }, newData);
      this.ctx.auditLog('体检系统对接 上传体检报告-更新suspect', newData, 'info');
    } else {
      suspect = await this.ctx.model.Suspect.create(newData);
      this.ctx.auditLog('体检系统对接 上传体检报告-创建suspect', suspect, 'info');
    }
  }
  // 浙江创建/更新体检项目(体检机构已存在)
  async handleZjHealthCheck(physicalExamOrgDetail, item, EnterpriseDetail, appointment) {
    const { ctx } = this;
    const physicalExaminationOrgID = physicalExamOrgDetail._id,
      EnterpriseID = EnterpriseDetail._id,
      year = new Date(item.cardInfo.checkTime).getFullYear() + '';
    const checkType = [ '0', '1', '2', '5', '4', '6' ][item.cardInfo.bodyCheckType - 1];
    const recheck = item.cardInfo.checkType === '21'; // 是否复查
    let healthchecke;
    if (appointment && appointment.healthcheckId) {
      healthchecke = await ctx.model.Healthcheck.findOne({ _id: appointment.healthcheckId });
    } else {
      ctx.auditLog('体检系统对接 上传体检报告-体检项目编号', item.projectNumber, 'info');
      const healthcheckeList = await ctx.model.Healthcheck.find({
        projectNumber: item.projectNumber,
        physicalExaminationOrgID,
        EnterpriseID,
        source: 'oapi-tj',
      });
      if (healthcheckeList.length === 1) {
        healthchecke = healthcheckeList[0];
      } else if (healthcheckeList.length > 1) {
        ctx.auditLog('体检系统对接 上传体检报告-查询体检项目数量', healthcheckeList.length, 'info');
        const checkTime = new Date(item.cardInfo.checkTime);
        healthchecke = await ctx.model.Healthcheck.findOne({
          projectNumber: item.projectNumber,
          physicalExaminationOrgID,
          EnterpriseID,
          year,
          // checkType,
          // recheck,
          checkDate: {
            $gte: moment(checkTime).startOf('day').toDate(),
            $lte: moment(checkTime).endOf('day').toDate(),
          },
          source: 'oapi-tj',
        });
        if (!healthchecke) {
          healthchecke = healthcheckeList[0];
        }
      }
    }
    if (!healthchecke) {
      const newData = {
        organization: item.orgInfo.orgName || physicalExamOrgDetail.name, // 检查机构名称
        physicalExaminationOrgID,
        enterpriseName: item.empInfoEmployer.employerNameEmployer || EnterpriseDetail.cname,
        EnterpriseID,
        enterpriseContactsName: EnterpriseDetail.contract || '',
        enterpriseContactsPhonNumber: EnterpriseDetail.phoneNum || '',
        enterpriseAddr: EnterpriseDetail.districtRegAdd || [],
        workAddress: EnterpriseDetail.workAddress || [],
        year,
        projectNumber: item.projectNumber,
        checkDate: appointment ? appointment.startTime : item.cardInfo.checkTime,
        checkEndDate: appointment ? appointment.endTime : item.cardInfo.checkTime,
        checkPlace: (physicalExamOrgDetail.regAddr ? physicalExamOrgDetail.regAddr.join('') : '') + physicalExamOrgDetail.address || '',
        approvalDate: item.cardInfo.writeDate || item.cardInfo.checkTime,
        applyTime: new Date(),
        checkType,
        shouldCheckNum: appointment ? appointment.employeeIds.length || appointment.peopleNum : 0,
        actuallNum: 0,
        recheck,
        reportStatus: true,
        source: appointment ? 'oapi-appointment' : 'oapi-tj',
      };
      healthchecke = await ctx.model.Healthcheck.create(newData);
      ctx.auditLog('体检系统对接 上传体检报告-创建体检项目成功', healthchecke, 'info');
    } else {
      const updateData = {
        organization: item.orgInfo.orgName || physicalExamOrgDetail.name,
        applyTime: new Date(),
        checkDate: item.cardInfo.checkTime < healthchecke.checkDate ? item.cardInfo.checkTime : healthchecke.checkDate,
        checkEndDate: item.cardInfo.checkTime > healthchecke.checkEndDate ? item.cardInfo.checkTime : healthchecke.checkEndDate,
      };
      if (item.projectNumber) updateData.projectNumber = item.projectNumber;
      await ctx.model.Healthcheck.updateOne({ _id: healthchecke._id }, updateData);
      ctx.auditLog('体检系统对接 上传体检报告-更新体检项目成功', updateData, 'info');
    }
    return healthchecke;
  }
  // 创建/更新人员信息employee、user
  async handlePersonInfo(personInfo) {
    const { ctx } = this;
    const { EnterpriseID, IDNum, phoneNum = '' } = personInfo;
    if (!IDNum) throw new Error('身份证号不能为空');
    let employeeInfo = await ctx.model.Employee.findOne({ EnterpriseID, IDNum });
    if (!employeeInfo && phoneNum) employeeInfo = await ctx.model.Employee.findOne({ EnterpriseID, phoneNum });
    if (!employeeInfo) {
      employeeInfo = await ctx.model.Employee.create(personInfo);
      ctx.auditLog('体检系统对接 上传体检报告-创建人员信息成功', employeeInfo, 'info');
    } else {
      await ctx.model.Employee.updateOne({ _id: employeeInfo._id }, { $set: personInfo });
      ctx.auditLog('体检系统对接 上传体检报告-更新人员信息成功', personInfo, 'info');
    }
    if (phoneNum) {
      const flag = /^1[3-9][0-9]{9}$/.test(phoneNum); // 手机号校验
      if (!flag) throw new Error('手机号格式错误');
      let user = await ctx.model.User.findOne({ phoneNum });
      if (!user) {
        user = await ctx.model.User.create({ name: employeeInfo.name, phoneNum, employeeId: employeeInfo._id, idNo: IDNum, companyId: [ EnterpriseID ], companyStatus: 2, birth: personInfo.birthDate });
      }
      if (user && user._id) {
        await ctx.model.Employee.updateOne({ _id: employeeInfo._id }, { userId: user._id });
      }
    }
    if (employeeInfo.departs.length === 0) {
      let dingtrees = await ctx.model.Dingtree.findOne({ EnterpriseID });
      if (!dingtrees) {
        dingtrees = await ctx.model.Dingtree.create({ EnterpriseID, name: '企业部门', type: '1' });
      }
      await ctx.model.Employee.updateOne({ _id: employeeInfo._id }, { departs: [[ dingtrees._id ]] });
      await ctx.model.Dingtree.updateOne({ _id: dingtrees._id }, { $addToSet: { staff: employeeInfo._id } });
    }
    const options = {
      returnOptions: {
        name: {
          returnPlaintext: true, // 返回明文
        },
        IDNum: {
          returnPlaintext: true, // 返回明文
        },
        phoneNum: {
          returnPlaintext: true, // 返回明文
        },
      },
    };
    const res = ctx.model.Employee.findOne({ _id: employeeInfo._id }).setOptions(options);
    ctx.auditLog('体检系统对接 上传体检报告-获取人员信息', res, 'info');
    return res;
  }

  // 获取体检预约列表
  async listV1_0(physicalExamOrgId, query = {}) {
    const { page = 1, limit = 10, status, startTime, endTime } = query;
    const condition = { physicalExamOrgId };
    if (status) {
      if (![ 1, 2, 3, 4, 5, 6, 7 ].includes(+status)) {
        throw new Error('status参数错误');
      }
      condition.status = +status;
    }
    if (startTime)condition.startTime = { $gte: new Date(startTime) };
    if (endTime)condition.endTime = { $lte: new Date(endTime) };
    let list = await this.ctx.model.HealthCheckAppointment.find(condition, { physicalExamOrgId: 0, files: 0 }).skip((page - 1) * limit).limit(+limit)
      .sort({ createdAt: -1 })
      .populate('EnterpriseID', 'cname code regType industryCategory companyScale contract districtRegAdd phoneNum workAddress regAdd workAddress');
    list = JSON.parse(JSON.stringify(list));
    for (let i = 0; i < list.length; i++) {
      const item = list[i];
      if (item.EnterpriseID && typeof item.EnterpriseID === 'object') {
        const Enterprise = item.EnterpriseID;
        const areaEmployer = await this.ctx.model.District.findOne({ level: '3', name: Enterprise.districtRegAdd[Enterprise.districtRegAdd.length - 1] }, { area_code: 1 });
        item.employerInfo = {
          EnterpriseID: Enterprise._id,
          creditCode: Enterprise.code,
          employerName: Enterprise.cname,
          economicTypeCode: Enterprise.regType,
          industryCategoryCode: Enterprise.industryCategory && Array.isArray(Enterprise.industryCategory[0]) ? Enterprise.industryCategory[0] : Enterprise.industryCategory,
          enterpriseSizeCode: Enterprise.companyScale,
          areaCodeEmployer: areaEmployer ? areaEmployer.area_code : '',
          areaNameEmployer: Enterprise.districtRegAdd.join,
          contactPerson: Enterprise.contract,
          employerPhone: Enterprise.phoneNum,
          address: Enterprise.regAdd,
          workAddress: Enterprise.workAddress,
        };
        delete item.EnterpriseID;
      }
    }
    const count = await this.ctx.model.HealthCheckAppointment.countDocuments(condition);
    return { list, count };
  }

  // 确认体检日期
  async confirmDateV1_0(physicalExamOrgId, body) {
    const { appointmentId, startTime, endTime } = body;
    if (!appointmentId) {
      throw new Error('appointmentId不能为空');
    }
    const healthCheckAppointment = await this.ctx.model.HealthCheckAppointment.findOne({ _id: appointmentId, physicalExamOrgId })
      .populate('physicalExamOrgId', 'name shortName');
    if (!healthCheckAppointment) {
      throw new Error('体检预约单不存在');
    }
    const statusName = [ '', '提交申请', '已确认体检日期', '已修改体检日期', '已确认名单', '体检中', '已完成', '已取消' ];
    if ([ 5, 6, 7 ].includes(healthCheckAppointment.status)) {
      throw new Error(`当前体检预约单状态为${statusName[healthCheckAppointment.status]}，不允许确认体检日期`);
    }
    const updateData = {
      status: 2,
      startTime: startTime ? new Date(startTime) : healthCheckAppointment.startTime,
      endTime: endTime ? new Date(endTime) : healthCheckAppointment.endTime,
    };
    if (updateData.startTime > updateData.endTime) {
      throw new Error('结束时间不能小于开始时间');
    }
    if (updateData.startTime <= new Date()) {
      throw new Error('开始时间不能小于当前时间');
    }
    const result = await this.ctx.model.HealthCheckAppointment.updateOne({ _id: appointmentId }, updateData);
    if (result.nModified) {
      this.synchronizeAppointment(appointmentId); // 同步mongodb中的预约单到sqlserver中
      this.sendSMS(healthCheckAppointment.EnterpriseID, {
        cname: healthCheckAppointment.physicalExamOrgId.shortName || healthCheckAppointment.physicalExamOrgId.name,
        date: `${moment(updateData.startTime).format('YYYY年MM月DD日')}-${moment(updateData.endTime).format('YYYY年MM月DD日')}`,
      });
      return {
        appointmentId,
        ...updateData,
      };
    }
    throw new Error('体检时间确认失败');
  }
  // 发送短信提醒
  async sendSMS(EnterpriseID, params = { cname: '', date: '' }) {
    const Enterprise = await this.ctx.model.Adminorg.findOne({ _id: EnterpriseID }, { contract: 1, phoneNum: 1 }).populate('adminUserId', 'name phoneNum');
    if (!Enterprise) {
      this.ctx.auditLog('体检系统对接 短信发送失败', Enterprise + '用人单位不存在', 'error');
      return;
    }
    const PhoneNumbers = Enterprise.phoneNum || Enterprise.adminUserId ? Enterprise.adminUserId.phoneNum : '';
    if (!PhoneNumbers) {
      this.ctx.auditLog('体检系统对接 短信发送失败', Enterprise + '用人单位联系电话不存在', 'error');
      return;
    }
    const { data } = await this.ctx.curl(`${this.config.iServiceHost}/api/sendSMS`, {
      method: 'POST',
      dataType: 'json',
      data: {
        templateCodeName: 'confirmAppointment',
        TemplateParam: JSON.stringify(params),
        PhoneNumbers,
      },
    });
    if (data.data && data.data.Code === 'OK') {
      this.ctx.auditLog('体检系统对接 短信发送成功', data.message, 'info');
    } else {
      this.ctx.auditLog('体检系统对接 短信发送失败', data.message, 'error');
    }
  }

  // 获取单个体检预约单详情及其体检信息
  async getOneV1_0(physicalExamOrgId, query) {
    const { _id } = query;
    if (!_id) {
      throw new Error('_id不能为空');
    }
    let healthCheckAppointment = await this.ctx.model.HealthCheckAppointment.findOne({ _id, physicalExamOrgId }, { physicalExamOrgId: 0, EnterpriseCode: 0 })
      .populate('EnterpriseID', 'cname code regType industryCategory contract phoneNum districtRegAdd companyScale regAdd workAddress')
      .populate('employeeIds', 'name gender IDNum phoneNum workType marriage age');
    if (!healthCheckAppointment) {
      throw new Error('体检预约单不存在');
    }
    healthCheckAppointment = JSON.parse(JSON.stringify(healthCheckAppointment));
    let healthcheckInfo = null;
    if (healthCheckAppointment.healthcheckId) {
      healthcheckInfo = await this.ctx.model.Healthcheck.findOne({ _id: healthCheckAppointment.healthcheckId }, [ 'projectNumber', 'workAddress', 'shouldCheckNum', 'actuallNum', 'normal', 're_examination', 'suspected', 'forbid', 'otherDisease', 'createTime' ]);
    }
    if (healthCheckAppointment.EnterpriseID && typeof healthCheckAppointment.EnterpriseID === 'object') {
      const Enterprise = healthCheckAppointment.EnterpriseID;
      const areaEmployer = await this.ctx.model.District.findOne({ level: '3', name: Enterprise.districtRegAdd[Enterprise.districtRegAdd.length - 1] }, { area_code: 1 });
      healthCheckAppointment.employerInfo = {
        _id: Enterprise._id,
        creditCode: Enterprise.code,
        employerName: Enterprise.cname,
        economicTypeCode: Enterprise.regType,
        industryCategoryCode: Enterprise.industryCategory && Array.isArray(Enterprise.industryCategory[0]) ? Enterprise.industryCategory[0] : Enterprise.industryCategory,
        enterpriseSizeCode: Enterprise.companyScale,
        areaCodeEmployer: areaEmployer ? areaEmployer.area_code : '',
        areaNameEmployer: Enterprise.districtRegAdd.join,
        contactPerson: Enterprise.contract,
        employerPhone: Enterprise.phoneNum,
        address: Enterprise.regAdd,
        workAddress: Enterprise.workAddress,
      };
      delete healthCheckAppointment.EnterpriseID;
    }
    if (healthCheckAppointment.employeeIds && healthCheckAppointment.employeeIds.length > 0) {
      const task = healthCheckAppointment.employeeIds.map(async item => {
        const harmFactors = await this.getEmployeeharmFactors(healthCheckAppointment.employerInfo._id, item._id);
        const res = await this.getExamItem(harmFactors, this.changeCheckType(healthCheckAppointment.checkType));
        let examItem = '';
        res.forEach(option => {
          examItem += option.status.examItem;
        });
        return {
          _id: item._id,
          name: item.name,
          idcardType: '1',
          idcardCode: item.IDNum,
          // sexCode: item.gender === '1' ? '2' : '1',
          sexCode: item.gender === '1' ? '女' : '男',
          birthday: item.IDNum ? item.IDNum.substring(6, 14) : '',
          telPhone: item.phoneNum,
          jobCode: item.workType || '', // 工种编码
          maritalStatusCode: item.marriage,
          age: item.age || this.getAgeFromIdCard(item.IDNum),
          harmFactors,
          examItem,
        };
      });
      healthCheckAppointment.employeeIds = await Promise.all(task);
    }
    if (healthCheckAppointment.files) {
      const outPutPrefix = `/static${this.app.config.enterprise_http_path}/${healthCheckAppointment.EnterpriseID._id}/`;
      const files = healthCheckAppointment.files;
      healthCheckAppointment.files = Object.keys(files).reduce((result, key) => {
        if (files[key])result[key] = outPutPrefix + files[key];
        return result;
      }, {});
    }
    return {
      healthCheckAppointment,
      healthcheckInfo,
    };
  }
  // 获取一个员工的危害因素, 返回一个字符串
  async getEmployeeharmFactors(EnterpriseID = '', employeeId = '') {
    const { ctx } = this;
    let harmFactors = await this.findHarmFactorsByEmployeeId({ EnterpriseID, employeeId });
    if (!harmFactors || !harmFactors.length) {
      const suspect = await ctx.model.Suspect.findOne({ employeeId }, { harmFactors: 1 });
      harmFactors = suspect ? suspect.harmFactors || '' : '';
    } else {
      harmFactors = harmFactors.join('、');
    }
    return harmFactors;
  }
  // 根据厂房车间工种和员工id查找危害因素 返回危害因素数组
  async findHarmFactorsByEmployeeId(query = {
    workshopName: '',
    workspaceName: '',
    workTypeName: '',
    employeeId: '',
    EnterpriseID: '',
  }) {
    if (!query.EnterpriseID) {
      query.EnterpriseID = this.ctx.session.adminUserInfo.EnterpriseID;
    }
    if (query.employeeId) {
      query['employees.employeeId'] = query.employeeId;
      delete query.employeeId;
    }
    const list = await this.ctx.model.Workspace.find(query, { stations: 1 });
    if (list.length === 0) return [];
    const harmFactors = [];
    for (let i = 0; i < list.length; i++) {
      const { stations } = list[i];
      for (let j = 0; j < stations.length; j++) {
        harmFactors.push(...stations[j].harmFactors);
      }
    }
    return [ ...new Set(harmFactors) ];
  }
  // 根据危害因素获取体检项目
  async getExamItem(item, status) {
    const arr = item.split('、');
    const res = await this.ctx.model.PhysicalExamination.aggregate([
      { $unwind: '$status' },
      { $match:
          { 'status.statusCode': status,
            newKeyWord: { $in: arr },
          },
      },
    ]);
    return res;
  }
  changeCheckType(type) {
    let status = '';
    if (type === '01' || type === '1') {
      status = '上岗前';
    } else if (type === '02' || type === '2') {
      status = '在岗';
    } else if (type === '03' || type === '3') {
      status = '离岗';
    } else if (type === '04' || type === '4') {
      status = '在岗';
    } else {
      status = '';
    }
    return status;
  }

  // 根据token获取体检机构id并校验
  async getOrgId(token = '') {
    token = token.replace(/\\\"/g, '\"');
    const agentId = token ? JSON.parse(token).agentId : '';
    if (!agentId) throw new Error('token数据错误');
    const apiUser = await this.ctx.model.ApiUser.findOne({ _id: agentId, status: 1 }, { orgId: 1 });
    if (apiUser) {
      const physicalExamOrgId = apiUser.orgId;
      if (!physicalExamOrgId) {
        throw new Error('token错误或者权限不足');
      }
      const physicalExamOrg = await this.ctx.model.PhysicalExamOrg.findOne({ _id: physicalExamOrgId });
      if (!physicalExamOrg) {
        throw new Error('体检机构不存在');
      }
      return physicalExamOrgId;
    }
    throw new Error('体检机构找不到');
  }

  // 根据code获取工种代码名称
  async getWorkTypeCode(code) {
    if (!code) return '';
    const data = await this.ctx.model.ZjWorkTypeCode.findOne({ code });
    return data ? data.name : code;
  }

  // 添加数据接收日志
  async addInterfaceLog(requestUrl = '', physicalExamOrgId = '', data = {}) {
    await this.ctx.model.InterfaceLog.create({
      orgId: physicalExamOrgId,
      requestUrl,
      requestBody: data,
      handledTime: new Date(),
    });
  }
  // 时间转化
  getTrueTime(dateString) { // dateString = '20200101'
    if (!dateString || dateString.length !== 8) return '';
    const year = dateString.substring(0, 4);
    const month = dateString.substring(4, 6);
    const day = dateString.substring(6, 8);
    return new Date(year, month - 1, day);
  }

  // 体检机构对接 上传用人单位信息 国家标准
  async enterpriseInfoV1_0(physicalExamOrgId, item) {
    try {
      if (!item.CREDIT_CODE) {
        throw new Error('CREDIT_CODE不能为空');
      }
      if (item.CREDIT_CODE.length !== 18) {
        throw new Error('CREDIT_CODE长度错误');
      }
      if (item.BUSINESS_SCALE_CODE && ![ '01', '02', '03', '04' ].includes(item.BUSINESS_SCALE_CODE)) {
        throw new Error('BUSINESS_SCALE_CODE参数错误, 只能为01, 02, 03, 04');
      }
      const { ctx } = this;
      const physicalExamOrg = await ctx.model.PhysicalExamOrg.findOne({ _id: physicalExamOrgId });
      if (item.ORG_CODE && item.ORG_CODE !== physicalExamOrg.organization) {
        throw new Error('您的token跟上传的创建机构代码不匹配');
      }
      const EnterpriseCode = item.CREDIT_CODE.trim().toUpperCase();
      let Enterprise = await this.ctx.model.Adminorg.findOne({ code: EnterpriseCode, isDelete: false });
      const { districtRegAdd = [], point = [] } = await this.ctx.service.district.getParentByCode(item.ADDRESS_CODE);
      const industryCategory = item.INDUSTRY_CATEGORY_CODE;
      const companyScaleObj = { '01': '大型', '02': '中型', '03': '小型', '04': '微型' };
      if (!Enterprise) {
        // 创建用人单位
        const enterpriseData = {
          cname: item.ENTERPRISE_NAME || '',
          code: EnterpriseCode,
          regType: item.ECONOMIC_TYPE_CODE || '',
          industryCategory: industryCategory ? [ industryCategory ] : [],
          companyScale: companyScaleObj.item.BUSINESS_SCALE_CODE || '',
          districtRegAdd,
          regAdd: item.ADDRESS_DETAIL || '',
          workAddress: [{ districts: districtRegAdd, point, address: item.ADDRESS_DETAIL || '' }],
          isactive: '1',
          leadIn: '3',
          physicalExaminationOrgID: [{
            _id: physicalExamOrg._id,
            ServiceContractName: physicalExamOrg.contract || '',
            ServicePhoneNum: physicalExamOrg.phoneNum || '',
          }],
          contract: item.ENTERPRISE_CONTACT || '',
          phoneNum: item.CONTACT_TELPHONE || '',
        };
        Enterprise = await ctx.model.Adminorg.create(enterpriseData);
      } else { // 更新用人单位
        const updateData = {
          cname: item.ENTERPRISE_NAME || '',
          regType: item.ECONOMIC_TYPE_CODE || '',
          industryCategory: industryCategory ? [ industryCategory ] : [],
          companyScale: item.BUSINESS_SCALE_CODE || '',
          districtRegAdd,
          isactive: '1',
          leadIn: '3',
          $push: {},
          contract: item.ENTERPRISE_CONTACT || '',
          phoneNum: item.CONTACT_TELPHONE || '',
        };
        if (Enterprise.physicalExaminationOrgID.map(ele => ele._id).indexOf(physicalExamOrg._id) === -1) {
          updateData.$push.physicalExaminationOrgID = {
            _id: physicalExamOrg._id,
            EnterpriseContractName: item.ENTERPRISE_CONTACT || '',
            EnterprisePhoneNum: item.CONTACT_TELPHONE || '',
            ServiceContractName: physicalExamOrg.contract || '',
            ServicePhoneNum: physicalExamOrg.phoneNum || '',
          };
        }
        const addressTemp = Enterprise.workAddress.map(ele => ele.districts.join('') + (ele.address || ''));
        if (!addressTemp.includes(districtRegAdd.join('') + (item.ADDRESS_DETAIL || ''))) {
          updateData.$push.workAddress = { districts: districtRegAdd, point, address: item.ADDRESS_DETAIL || '' };
        }
        await ctx.model.Adminorg.updateOne({ _id: Enterprise._id }, updateData);
        ctx.auditLog('体检机构对接 更新用人单位信息成功', updateData, 'info');
      }
      // 处理人员信息
      await this.handlePersonInfo2({
        EnterpriseID: Enterprise._id,
        name: item.ENTERPRISE_CONTACT,
        phoneNum: item.CONTACT_TELPHONE,
      });
      return {
        status: 200,
        message: `用人单位：${item.ENTERPRISE_NAME}的信息${Enterprise ? '更新' : '添加'}成功`,
      };
    } catch (e) {
      this.ctx.auditLog('体检机构对接 上传用人单位信息失败', e, 'error');
      return {
        status: 500,
        message: `ID为${item.id || item.ID || item.CREDIT_CODE}的用人单位信息上传失败：${e.message}`,
      };
    }
  }
  // 创建/更新人员信息employee、user、adminUser
  async handlePersonInfo2(personInfo = { EnterpriseID: '', name: '', phoneNum: '' }) {
    const { ctx } = this;
    const { EnterpriseID, name, phoneNum } = personInfo;
    if (!phoneNum) return;
    const flag = /^1[3-9][0-9]{9}$/.test(phoneNum); // 手机号校验
    if (!flag) throw new Error('手机号格式错误');
    let employeeInfo = await ctx.model.Employee.findOne({ EnterpriseID, phoneNum });
    if (!employeeInfo) {
      employeeInfo = await ctx.model.Employee.create(personInfo);
    }
    let user = await ctx.model.User.findOne({ phoneNum });
    if (!user) {
      user = await ctx.model.User.create({ name, phoneNum, userName: phoneNum, employeeId: employeeInfo._id, companyId: [ EnterpriseID ], companyStatus: 2 });
    }
    if (user && user._id && !employeeInfo.userId) {
      await ctx.model.Employee.updateOne({ _id: employeeInfo._id }, { userId: user._id });
    }
    if (employeeInfo.departs.length === 0) {
      let dingtrees = await ctx.model.Dingtree.findOne({ EnterpriseID });
      if (!dingtrees) {
        dingtrees = await ctx.model.Dingtree.create({ EnterpriseID, name: '企业部门', type: '1' });
      }
      await ctx.model.Employee.updateOne({ _id: employeeInfo._id }, { departs: [[ dingtrees._id ]] });
      await ctx.model.Dingtree.updateOne({ _id: dingtrees._id }, { $addToSet: { staff: employeeInfo._id } });
    }
    let adminUser = await ctx.model.AdminUser.findOne({ phoneNum, state: '1' });
    if (!adminUser) {
      adminUser = await ctx.model.AdminUser.create({ phoneNum, name, userName: phoneNum, state: 1, newAddEnterpriseID: EnterpriseID, userId: user._id, employees: [ employeeInfo._id ] });
    } else {
      await ctx.model.AdminUser.updateOne({ _id: adminUser._id }, { newAddEnterpriseID: EnterpriseID, name, $addToSet: { employees: employeeInfo._id } });
    }
    const adminorg = await ctx.model.Adminorg.findOne({ _id: EnterpriseID });
    if (adminorg.adminUserId) {
      await ctx.model.Adminorg.updateOne({ _id: EnterpriseID }, { $addToSet: { adminArray: adminUser._id } });
    } else {
      await ctx.model.Adminorg.updateOne({ _id: EnterpriseID }, { adminUserId: adminUser._id });
    }
  }
  // 根据统一社会信用代码创建adminUser (userName: EnterpriseCode) 密码是userName后6位
  async handlePersonInfo3(personInfo = { EnterpriseID: '', name: '', userName: '' }) {
    const { ctx } = this;
    const { EnterpriseID, name, userName } = personInfo;
    if (!userName) return;
    let adminUser = await ctx.model.AdminUser.findOne({ userName, state: '1' });
    if (!adminUser) {
      adminUser = await ctx.model.AdminUser.create({ name, userName, state: 1, newAddEnterpriseID: EnterpriseID, password: userName.slice(-6) });
      ctx.auditLog('温州体检机构对接 创建adminUser成功', adminUser, 'info');
    } else {
      await ctx.model.AdminUser.updateOne({ _id: adminUser._id }, { newAddEnterpriseID: EnterpriseID });
      ctx.auditLog('温州体检机构对接 更新adminUser成功', personInfo, 'info');
    }
    const adminorg = await ctx.model.Adminorg.findOne({ _id: EnterpriseID }, { adminUserId: 1 });
    if (adminorg.adminUserId) {
      await ctx.model.Adminorg.updateOne({ _id: EnterpriseID }, { $addToSet: { adminArray: adminUser._id } });
      ctx.auditLog('温州体检机构对接 更新用人单位信息成功', { _id: EnterpriseID, $addToSet: { adminArray: adminUser._id } }, 'info');
    } else {
      await ctx.model.Adminorg.updateOne({ _id: EnterpriseID }, { adminUserId: adminUser._id });
      ctx.auditLog('温州体检机构对接 更新用人单位信息成功', { _id: EnterpriseID, adminUserId: adminUser._id }, 'info');
    }
  }

  // 体检机构对接 上传用人单位信息 浙江标准
  async zjEmployerListV1_0(physicalExamOrgId, item) {
    try {
      if (!item.creditCode) throw new Error('creditCode不能为空');
      if (!item.employerName) throw new Error('employerName不能为空');
      if (!/^[^IOZSV]{18}$/.test(item.creditCode)) throw new Error('creditCode格式错误');
      if (!item.areaCode) throw new Error('areaCode不能为空');
      if (!item.orgName) throw new Error('orgName不能为空');
      if (!item.address) throw new Error('address不能为空');
      if (!item.contactPerson) throw new Error('contactPerson不能为空');
      if (!item.employerPhone) throw new Error('employerPhone不能为空');
      if (!/^1[3-9]\d{9}$/.test(item.employerPhone)) throw new Error('employerPhone格式错误');
      if (item.enterpriseSizeCode && ![ '10000', '10001', '10002', '10004' ].includes(item.enterpriseSizeCode)) {
        throw new Error('enterpriseSizeCode参数错误, 只能为10000, 10001, 10002, 10004');
      }
      const { ctx } = this;
      const physicalExamOrg = await ctx.model.PhysicalExamOrg.findOne({ _id: physicalExamOrgId });
      if (item.orgName && !physicalExamOrg.name.includes(item.orgName)) {
        throw new Error('您的orgName跟token不匹配');
      }
      const EnterpriseCode = item.creditCode.trim().toUpperCase();
      let Enterprise = await this.ctx.model.Adminorg.findOne({ code: EnterpriseCode, isDelete: false });
      const updateFlag = !!Enterprise;
      const { districtRegAdd = [], point = [] } = await this.ctx.service.district.getParentByCode(item.areaCode);
      const industryCategory = [];
      if (item.industryCategoryCode) { // 行业类别
        const industryCategoryDeatil = await ctx.model.IndustryCategory.findOne({ 'children.children.children.value': item.industryCategoryCode });
        if (industryCategoryDeatil) {
          industryCategoryDeatil.children.forEach(child1 => {
            child1.children.forEach(child2 => {
              child2.children.forEach(child3 => {
                if (child3.value === item.industryCategoryCode) {
                  industryCategory.push(industryCategoryDeatil.value, child1.value, child2.value, child3.value);
                }
              });
            });
          });
        } else {
          throw new Error('industryCategoryCode参数错误');
        }
      }
      const companyScaleObj = { 10000: '大型', 10001: '中型', 10002: '小型', 10004: '微型' };
      if (!item.address) item.address = '';
      const baseData = {
        cname: item.employerName.trim(),
        code: EnterpriseCode,
        regType: item.economicTypeCode ? economicTypeCode[item.economicTypeCode] : '',
        industryCategory,
        companyScale: item.enterpriseSizeCode ? companyScaleObj[item.enterpriseSizeCode] : '',
        districtRegAdd,
        regAdd: item.address,
        isactive: '1',
        leadIn: '3',
        contract: item.contactPerson || '',
        phoneNum: item.employerPhone || '',
      };
      if (!Enterprise) {
        // 创建用人单位
        const enterpriseData = {
          ...baseData,
          workAddress: [{ districts: districtRegAdd, point, address: item.address }],
          physicalExaminationOrgID: [{
            _id: physicalExamOrg._id,
            ServiceContractName: physicalExamOrg.contract || '',
            ServicePhoneNum: physicalExamOrg.phoneNum || '',
          }],
        };
        Enterprise = await ctx.model.Adminorg.create(enterpriseData);
        ctx.auditLog('温州体检机构对接 创建用人单位信息成功', Enterprise, 'info');
      } else { // 更新用人单位
        const updateData = {
          ...baseData,
          $push: {},
        };
        if (Enterprise.physicalExaminationOrgID.map(ele => ele._id).indexOf(physicalExamOrg._id) === -1) {
          updateData.$push.physicalExaminationOrgID = {
            _id: physicalExamOrg._id,
            EnterpriseContractName: item.contactPerson || '',
            EnterprisePhoneNum: item.employerPhone || '',
            ServiceContractName: physicalExamOrg.contract || '',
            ServicePhoneNum: physicalExamOrg.phoneNum || '',
          };
        }
        const addressTemp = Enterprise.workAddress.map(ele => ele.districts.join('') + (ele.address || ''));
        if (!addressTemp.includes(districtRegAdd.join('') + item.address)) {
          updateData.$push.workAddress = { districts: districtRegAdd, point, address: item.address };
        }
        await ctx.model.Adminorg.updateOne({ _id: Enterprise._id }, updateData);
        ctx.auditLog('温州体检机构对接 更新用人单位信息成功', updateData, 'info');
      }
      // 处理人员信息
      await this.handlePersonInfo2({
        EnterpriseID: Enterprise._id,
        name: item.contactPerson,
        phoneNum: item.employerPhone,
      });
      this.ctx.auditLog('体检机构对接 上传用人单位信息成功', item, 'info');
      return {
        status: 200,
        message: `用人单位：${item.employerName}的信息${updateFlag ? '更新' : '添加'}成功`,
      };
    } catch (e) {
      this.ctx.auditLog('体检机构对接 上传用人单位信息失败', e, 'error');
      return {
        status: 400,
        message: `用人单位 - ${item.employerName}的信息上传失败：${e.message}`,
      };
    }
  }

  // 创建/更新企业车间岗位信息表（浙江）
  async updateWorkspace(personInfo, item, physicalExamOrgId) {
    const { ctx } = this;
    const { EnterpriseID, workType, _id } = personInfo;
    if (!workType) return;
    const workspaceName = item.userInfo.workshop || personInfo.station || workType;
    let workspace = await ctx.model.Workspace.findOne({ EnterpriseID, workshopName: '', workTypeName: workType, workspaceName });
    let hazardFactorList = item.hazardFactorList && item.hazardFactorList.length ? item.hazardFactorList : item.contactHazardFactorList || [];
    if (hazardFactorList.length === 1 && Array.isArray(hazardFactorList[0])) hazardFactorList = hazardFactorList[0];
    const harmFactors = []; // 危害因素
    for (let i = 0; i < hazardFactorList.length; i++) {
      const hazardFactor = hazardFactorList[i];
      const hazardFactorName = (await this.getHarmFactorsByCode(hazardFactor.hazardCode)) || hazardFactor.otherHazardName;
      if (hazardFactorName) harmFactors.push(hazardFactorName);
    }
    const stationInfo = { // 点位信息
      stationName: workType,
      harmFactors,
    };
    if (!workspace) {
      workspace = await ctx.model.Workspace.create({
        EnterpriseID,
        physicalExamOrgId,
        workTypeName: workType,
        workspaceName,
        employees: [{ employeeId: _id }],
        stations: [ stationInfo ],
      });
      ctx.auditLog('体检机构对接 创建企业车间岗位信息表成功', workspace, 'info');
    } else {
      const stations = workspace.stations;
      const stationIndex = stations.findIndex(ele => ele.stationName === workType);
      if (stationIndex === -1) {
        stations.push(stationInfo);
      } else {
        stations[stationIndex].harmFactors = stationInfo.harmFactors;
      }
      const updateObj = { $set: { stations } };
      const employeeIds = workspace.employees.map(ele => ele.employeeId);
      if (employeeIds.indexOf(_id) === -1) {
        updateObj.$push = { employees: { employeeId: _id } };
      }
      await ctx.model.Workspace.updateOne(
        { _id: workspace._id },
        updateObj
      );
      ctx.auditLog('体检机构对接 更新企业车间岗位信息表成功', { _id: workspace._id, stationInfo, employeeId: _id }, 'info');
    }
  }


}

module.exports = HealthCheckAppointmentService;
