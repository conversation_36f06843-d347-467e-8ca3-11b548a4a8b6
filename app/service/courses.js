const Service = require('egg').Service;

const {
  _item,
} = require('./general');

class courses extends Service {
  async getListByName(query, current, pageSize = 10) {
    const list = await this.ctx.model.Courses.find(query).populate([{
      path: 'authorID',
      select: 'name _id logo',
    }, {
      path: 'classification',
      select: 'name _id explain',
    }]).skip((current - 1) * pageSize)
      .limit(pageSize)
      .sort({
        date: -1,
      });
    const count = await this.ctx.model.Courses.countDocuments(query).exec();
    return {
      list,
      count,
    };
  }
  async getListByClass(query, current, pageSize = 10) {
    const list = await this.ctx.model.Courses.find(query).skip((current - 1) * pageSize)
      .limit(pageSize)
      .sort({
        date: -1,
      });
    const count = await this.ctx.model.Courses.countDocuments(query).exec();
    return {
      list,
      count,
    };
  }
  async getCourseOne(_id) {
    const course = await this.ctx.model.Courses.findOne({
      _id,
    }).populate([{
      path: 'authorID',
      select: 'name _id logo',
    }, {
      path: 'videoInfos',
      select: 'VideoId _id Size times author date',
    }, {
      path: 'documents',
    }]);
    return course;
  }
  async updateCourse(id, newData) {
    const backData = await this.ctx.model.Courses.updateOne({
      _id: id,
    }, newData);
    return backData;
  }
  async getClassification(level, parentID = 0) {
    // console.log(level, parentID)
    const list = await this.ctx.model.CourseClassification.find({
      level,
      parentID,
    });
    return list;
  }

  async getHotCourses() {
    const list = await this.ctx.model.Courses.find({
      allowToOpen: true,
    }, {
      cover: 1,
      name: 1,
      views: 1,
    })
      .limit(5).sort({
        views: -1,
      });
    return {
      list,
    };
  }

  async getCourseProgress(res, {
    query = {},
    populate = [],
    files = null,
  } = {}) {
    // console.log(this.ctx.model)
    // return;
    return _item(res, this.ctx.model.PersonalTraining, {
      query,
      populate,
      files,
    });
  }

  async createCourseProgress(newData) {
    const backData = await this.ctx.model.CoursesProgress.create(newData);
    return backData;
  }

  async updatePersonalTraining(query, newFile) {
    try {
      return this.ctx.model.PersonalTraining.updateOne(query, newFile);
    } catch (error) {
      console.error(error);
      return -1;
    }
  }

  async searchCourses(keyJSON) {
    keyJSON.allowToOpen = true;
    const course = await this.ctx.model.Courses.find(keyJSON, {
      _id: 1,
      explain: 1,
      name: 1,
      views: 1,
      cover: 1,
    });
    return course;
  }


  async createComment(newData) {
    const backData = await this.ctx.model.CoursesComments.create(newData);
    return backData;
  }

  async updateComment(_id, filed) {
    // console.log(filed)
    const backData = await this.ctx.model.CoursesComments.updateOne({
      _id,
    }, filed);
    return backData;
  }

  async getComments(courseID, current = 1) {
    try {
      const pageSize = 10;
      const query = {
        courseID,
        allowToOpen: true,
        level: 1,
      };
      const backData = await this.ctx.model.CoursesComments.find(query).populate([{
        path: 'userID',
        select: 'name _id logo',
      }, {
        path: 'replys',
        // select: 'content, userID',
        populate: {
          path: 'userID',
          select: 'name _id logo',
        },
        options: {
          skip: 0,
          limit: 2,
          sort: {
            createDate: -1,
          },
        },
      }])
        .sort({ createDate: -1 })
        .skip((current - 1) * pageSize)
        .limit(pageSize);
      // console.log()
      const totalComments = await this.ctx.model.CoursesComments.count(query);
      return {
        commentsList: backData,
        totalComments,
      };
    } catch (error) {
      console.error(error);
      return -1;
    }
  }

  async item(res, params = {}) {
    return _item(res, this.ctx.model.AdminTraining, params);
  }

  async getAllVideos() {
    const {
      ctx,
    } = this;
    try {
      const list = await ctx.model.VideoInfos.find();
      ctx.helper.renderSuccess(ctx, {
        data: {
          list,
          message: 'OK',
        },
      });
    } catch (error) {
      ctx.logger.error(error);
      console.error(error);
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

}

module.exports = courses;
