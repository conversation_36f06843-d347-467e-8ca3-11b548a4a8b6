
const Service = require('egg').Service;
const path = require('path');
const moment = require('moment');
const fs = require('fs');

class radiateqlcProject extends Service {
  getVal(projectInfo, filePathField) {
    const filePathFileds = filePathField.split('.');
    let filePath = projectInfo[filePathFileds[0]];
    let index = 1;
    while (index < filePathFileds.length) {
      filePath = filePath[filePathFileds[index]];
      index++;
    }
    return filePath;
  }
  // 给pdf添加签名
  async signPdf({ originatorEmployeeId, statusField, processInstanceField, processInstanceId, signIndex, currentEmployeeId, isComplete = false } = {}) {
    const { config } = this.app;
    // 获取机构ID
    const projectInfo = await this.ctx.model.RadiateqlcProject.findOne({ [processInstanceField]: processInstanceId }, { EnterpriseName: 1, createdAt: 1, serviceOrgId: 1, projectSN: 1, wordFileName: 1, firstDraftFileName: 1 }).lean();
    const { serviceOrgId, createdAt, projectSN, EnterpriseName } = projectInfo;
    // 获取机构签章图片
    const serviceOrgInfo = await this.ctx.model.ServiceOrg.findOne({ _id: serviceOrgId }, { signatures: 1, qualifies: 1 }).lean();
    let signatureUrl = '';
    const signatures = serviceOrgInfo.signatures || [];
    signatures.forEach(item => {
      let url = item.url.replace('/static' + config.enterpriseUpload_http_path, '');
      url = path.join(config.enterpriseUpload_path, url);
      if (item.usage.includes('放射卫生')) {
        signatureUrl = url;
      }
    });
    // 钉钉审核和文件信息
    const approvalSignFileIno = {
      reportReview: {
        filePthStart: 'static',
        filePathField: 'firstDraftFileName.url', // `${app.config.static.prefix}${app.config.report_http_path}/${jcqlcID}/${projectYear}/${projectSN}/${fileName}`
        outPathFile: 'officialReportUploadFileName.url',
        outPathFileName2: 'officialReportUploadFileName.name',
        outPathFileName: '正式稿',
        noStampFiled: 'officialReportUploadFileName.noStampUrl',
        nodeField: 'officialReportUploadFile',
        // mergeFiles: [ 'reportUploadFile.url', 'wordFileName.signedUrl' ],
        userSignConfig: [
          { text: '检测人：',
            offsetX: 80, // 偏移量 图片的位置放置在text所在位置+偏移量
            offsetY: 17,
            isSignFromApproval: true, // 签名是否来自于审批
            approvalNodeIndex: 0 },
          { text: '审核',
            offsetX: 80, // 偏移量 图片的位置放置在text所在位置+偏移量
            offsetY: 17,
            isSignFromApproval: true, // 签名是否来自于审批
            approvalNodeIndex: 1 },
          { text: '批准人：',
            offsetX: 80, // 偏移量 图片的位置放置在text所在位置+偏移量
            offsetY: 17,
            isSignFromApproval: true, // 签名是否来自于审批
            approvalNodeIndex: 2 }],
        signConfig: [{ // 签章位置
          y: 50,
          x: 'center', // x轴居中
        }, {
          text: '检测机构（盖章',
          offsetY: 0, // 偏移量
        }],
        textConfig: [{
          y: 100,
          x: 'center', // x轴居中
          offsetY: 0, // 偏移量
          width: 50,
          value: moment(new Date()).format('YYYY-MM'),
        }, {
          text: '检测机构（盖章',
          offsetY: 5,
          value: moment(new Date()).format('YYYY-MM-DD'),
        }],
        signImagePath: signatureUrl,
      },
    };
    const configInfo = approvalSignFileIno[statusField];
    if (!configInfo) return;
    // 配置文件
    // 获取机构ID
    const projectYear = (new Date(createdAt).getFullYear()) + '';
    const pathDir = path.resolve(path.join(config.report_path, serviceOrgId, projectYear, projectSN));
    let filePath = this.getVal(projectInfo, configInfo.filePathField);
    const staticPath = filePath;
    // 未签名的文件数据库存储路径
    if (configInfo.filePthStart === 'static') {
      filePath = path.basename(filePath);
    }
    filePath = path.join(pathDir, filePath);


    let signConfig = configInfo.signConfig;
    let signImagePath = configInfo.signImagePath;
    if (filePath.indexOf('?') !== -1) {
      const temp = filePath.split('?');
      filePath = temp[0];
    }
    // 原文件就是pdf文件,防止在待审批文件中修改
    if (path.extname(filePath) === '.pdf') {
      const { name } = path.parse(filePath);
      const signFileName = name + '_sign';
      const originFilePath = filePath;
      filePath = filePath.replace(name, signFileName);
      if (signIndex === 0) {
        fs.writeFileSync(filePath, fs.readFileSync(originFilePath));
      }
    }
    if (!isComplete) {
      if (path.extname(filePath) === '.docx') {
        if (!fs.existsSync(filePath.replace('docx', 'pdf'))) {
        // 转换成pdf
          await this.ctx.helper.docx2pdf(staticPath, filePath.replace('docx', 'pdf'));
        }
      }
    }

    filePath = filePath.replace('docx', 'pdf');
    console.log(filePath, fs.existsSync(filePath), '是否存在文件', signIndex);
    // 获取性能报告的开始页码
    const { page, lastPage } = await this.ctx.helper.extractTextWithCoordinates(filePath, projectSN + ' (X)');
    console.log(9999, page, lastPage);
    if (!isComplete) { // 添加每个审批人的签字
      signConfig = configInfo.userSignConfig[signIndex];
      signConfig.pages = [ page - 1, lastPage ];
      // 获取该审批节点签名
      let employeeInfo = null;
      if (signConfig.signField === 'origin') {
        // 发起人
        // 获取发起人的签名
        employeeInfo = await this.ctx.model.ServiceEmployee.findOne({ _id: originatorEmployeeId }, { signPath: 1 });
      } else {
        employeeInfo = await this.ctx.model.ServiceEmployee.findOne({ _id: currentEmployeeId }, { signPath: 1 });
      }

      // 获取签名路径
      signImagePath = path.resolve(path.join(config.sign_path, serviceOrgId, employeeInfo.signPath)); // 文件目录;
      await this.ctx.helper.drawImageToPdfAndSign(filePath, signImagePath, false, false, signConfig);
    } else { // 审批完成进行盖章和电子签名

      const textConfig = configInfo.textConfig;
      signConfig.forEach(item => {
        item.pages = [ 0, page ];
      });
      signConfig[0].pages = [ 0, page ];
      signConfig[1].pages = [ page - 1, lastPage ];
      textConfig[0].pages = [ 0, page ];
      textConfig[1].pages = [ page - 1, lastPage ];
      const fileData = await this.ctx.helper.drawImageToPdfAndSign(filePath, signImagePath, true, false, signConfig, textConfig);
      let { noStampFilePath2 } = fileData;


      // 先对防护部分盖骑缝章
      const { PDFDocument } = require('pdf-lib');
      let pdfDoc = await PDFDocument.load(fs.readFileSync(filePath));
      pdfDoc = await this.ctx.helper.addSeamStampToPDF(pdfDoc, signImagePath, 50, 0, page - 1);
      // 再对性能部分盖骑缝章
      pdfDoc = await this.ctx.helper.addSeamStampToPDF(pdfDoc, signImagePath, 50, page, 0);
      // 保存文件
      fs.writeFileSync(filePath, await pdfDoc.save());
      // 添加数字签名
      noStampFilePath2 = path.join(config.report_path, noStampFilePath2);
      await this.ctx.helper.signPdf(noStampFilePath2);
      await this.ctx.helper.signPdf(filePath);

      // 创建盖章记录
      await this.ctx.model.StampRecord.create({
        serviceOrgId,
        modelId: projectInfo._id,
        modelName: 'RadiateqlcProject',
        stampFile: configInfo.outPathFileName,
        stampFilePath: '/' + fileData.filePath,
        optServiceEmployeeId: currentEmployeeId,
      });
      const setFields = {
        [configInfo.outPathFile]: '/' + fileData.filePath,
      };
      if (configInfo.noStampFiled && fileData.noStampFilePath) {
        setFields[configInfo.noStampFiled] = '/' + fileData.noStampFilePath;
      }
      if (configInfo.outPathFileName2) {
        setFields[configInfo.outPathFileName2] = `${projectSN}_${EnterpriseName}_${configInfo.outPathFileName}.pdf`;
      }
      // 更新签名后的文件
      await this.ctx.model.RadiateqlcProject.updateOne({ _id: projectInfo._id }, { $set: setFields });
      if (configInfo.nodeField) {
        console.log(configInfo.nodeField, '更新正式稿状态？？？？');
        // 审批完成 修改报告状态
        await this.updateProgress({ field: configInfo.nodeField, status: 2, radiateqlcProjectId: projectInfo._id });
      }
    }
  }
  // 更新审批记录表
  async updateOne(query, setFieldObj) {
    await this.ctx.model.RadiateqlcProject.updateOne(query, { $set: setFieldObj });
  }
  async updateapproveSign(res) {
    const {
      ctx,
    } = this;
    // const res = await this.handleApprove(radapproveInfo, obj, AppKey, AppSecret, 'approved');
    if (res.qlcProjectStatus > 1) { // 处理审批人
      // 删除发起人和检测人的信息
      res.info.operation_records = res.info.operation_records.slice(2);
      // 审批结束处理签名
      let signinfo = res.info.operation_records.map(item => {
        return item.serviceEmployeeId;
      });
      signinfo = await this.getSignInfo(signinfo);
      if (res.info.operation_records[signinfo.length - 1].operation_type === 'PROCESS_CC') {
        signinfo.pop();
      }
      // 评审组长
      signinfo = JSON.parse(JSON.stringify(signinfo));
      signinfo[signinfo.length - 1].signType = 'reviewTeamLeader';
      await ctx.model.RadiateqlcProject.updateOne({
        process_instance_id: res.info.processInstanceId,
      }, {
        $set: {
          contractApprovedUsers: signinfo,
        },
      });
    }
    // const res = await ctx.service.dingTalkWorkFlow.getProcessinstance(obj.processInstanceId, AppKey, AppSecret);
    // if (res.errmsg) {
    //   console.log('钉钉获取审批详情错误返回', JSON.stringify(res.errmsg));
    //   return;
    // }
    // res.info.processInstanceId = obj.processInstanceId;
    // if (obj.type === 'start') {
    //   await ctx.service.dingTalkWorkFlow.createApproveRecord(res.info);
    // } else {
    //   // 更新审批状态数据
    //   await ctx.service.dingTalkWorkFlow.updateApproveRecord({ processInstanceId: obj.processInstanceId }, res.info);
    //   if (res.qlcProjectStatus > 1) {
    //     // await ctx.service.radiateqlcProject.updateOne({ [radapproveInfo[0]._idField]: obj.processInstanceId }, {
    //     //   [`progress.${radapproveInfo[0].statusField}`]: {
    //     //     status: res.qlcProjectStatus,
    //     //     completedTime: obj.finishTime,
    //     //   },
    //     // });
    //     const radiateqlcProjectId = await ctx.model.RadiateqlcProject.findOne({ reportProcessInstanceId: obj.processInstanceId });
    //     await this.updateProgress({ field: 'approved', status: 2, radiateqlcProjectId: radiateqlcProjectId._id });
    //     // // 删除发起人和检测人的信息
    //     // res.info.operation_records = res.info.operation_records.slice(2);
    //     // // 审批结束处理签名
    //     // let signinfo = res.info.operation_records.map(item => {
    //     //   return item.serviceEmployeeId;
    //     // });
    //     // signinfo = await this.getSignInfo(signinfo);
    //     // if (res.info.operation_records[signinfo.length - 1].operation_type === 'PROCESS_CC') {
    //     //   signinfo.pop();
    //     // }
    //     // // 评审组长
    //     // signinfo = JSON.parse(JSON.stringify(signinfo));
    //     // signinfo[signinfo.length - 1].signType = 'reviewTeamLeader';
    //     // await ctx.model.RadiateqlcProject.updateOne({
    //     //   process_instance_id: obj.processInstanceId,
    //     // }, {
    //     //   $set: {
    //     //     contractApprovedUsers: signinfo,
    //     //   },
    //     // });
    //   }
    // }
  }
  // 签名 审批人员信息 process_instance_id 数据库名
  async getSignInfo(signinfo) {
    const {
      ctx,
    } = this;
    const serviceEmployee = await ctx.model.ServiceEmployee.find({ _id: { $in: signinfo } },
      {
        _id: 0,
        name: 1,
        fileName: '$signPath',
        signType: 'approvedUsers',
        serviceEmployeeId: '$_id',
      }
    );
    return serviceEmployee;
  }
  // 更新修改审批状态
  async updateModifystatus(applyInfo) {
    const { ctx } = this;
    const info = applyInfo.info;
    const samplingSchemes = info.form_component_values.filter(item => item.value === '方案');
    const field = samplingSchemes.length ? 'samplingSchemes' : 'spotRecord';
    if (applyInfo.qlcProjectStatus > 1) {
      // 更新状态
      await ctx.model.RadiateqlcProject.updateOne({ [`${field}MApplyId`]: info.processInstanceId }, { $set: {
        [`${field}MApplyStatus`]: 2,
        [`${field}MApplyTime`]: new Date(),
      } });
    }
  }
  async handleApprove(radapproveInfo, obj, processField, res) {
    const {
      ctx,
    } = this;
    // const res = await ctx.service.dingTalkWorkFlow.getProcessinstance(obj.processInstanceId, AppKey, AppSecret);
    if (res.errmsg) {
      console.log('钉钉获取审批详情错误返回', JSON.stringify(res.errmsg));
      return;
    }
    res.info.processInstanceId = obj.processInstanceId;
    if (!await ctx.model.ApproveRecord.findOne({ processInstanceId: obj.processInstanceId })) {
      await ctx.service.dingTalkWorkFlow.createApproveRecord(res.info);
    }
    if (obj.type === 'finish') {
      await ctx.service.dingTalkWorkFlow.updateApproveRecord({ processInstanceId: obj.processInstanceId }, res.info);
      if (res.qlcProjectStatus > 1) {
        // 审批完成 修改报告状态
        const radiateqlcProjectId = await ctx.model.RadiateqlcProject.findOne({ [radapproveInfo._idField]: obj.processInstanceId }, { _id: 1 });
        if (processField !== 'MApplyStatus') {
          if (processField === 'approved') {
            await ctx.service.radiateqlcProject.updateOne({ [radapproveInfo._idField]: obj.processInstanceId }, {
              [`progress.${radapproveInfo.statusField}`]: {
                status: res.qlcProjectStatus,
                completedTime: obj.finishTime,
              },
            });
          } else {
            await this.updateProgress({ field: processField, status: 2, radiateqlcProjectId: radiateqlcProjectId._id });
          }
          if (processField === 'reportReview') {
            // 报告审核通过后，更新报告审核记录单状态为已完成
            await this.updateProgress({ field: 'auditRecords', status: 2, radiateqlcProjectId: radiateqlcProjectId._id });
          }
        }
      }
    }
    return res;
  }
  async reportReviews(radapproveInfo, obj, AppKey, AppSecret) {
    const {
      ctx,
    } = this;
    const res = await ctx.service.dingTalkWorkFlow.getProcessinstance(obj.processInstanceId, AppKey, AppSecret);
    if (res.errmsg) {
      console.log('钉钉获取审批详情错误返回', JSON.stringify(res.errmsg));
      return;
    }
    res.info.processInstanceId = obj.processInstanceId;
    if (obj.type === 'start') {
      await ctx.service.dingTalkWorkFlow.createApproveRecord(res.info);
    } else {
      await ctx.service.dingTalkWorkFlow.updateApproveRecord({ processInstanceId: obj.processInstanceId }, res.info);
      if (res.qlcProjectStatus > 1) {
        // 审批完成 修改报告状态
        const radiateqlcProjectId = await ctx.model.RadiateqlcProject.findOne({ reportProcessInstanceId: obj.processInstanceId });
        await this.updateProgress({ field: 'reportReview', status: 2, radiateqlcProjectId: radiateqlcProjectId._id });
      }
    }
  }
  // 初始化项目节点,创建项目时需要同时创建项目状态信息表
  // 如果是可选节点，如果该过程不需要，那么应该删除该可选节点
  async initProjectProgress(radiateqlcProjectId, detectionType, serviceOrgId) {
    // const serviceOrgId = this.ctx.session.jcqlcInfo.org_id;
    let data = await this.ctx.model.RadiateqlcProgress.findOne({ serviceOrgId, isInit: true });
    data = JSON.parse(JSON.stringify(data));
    delete data._id;
    data.radiateqlcProjectId = radiateqlcProjectId;
    data.isInit = false;
    data.currentProgress = data.progresses[0].field;
    data.currentSortNum = data.progresses[0].sortNum;
    // await this.ctx.service.db.create('RadiateqlcProgress', data);
    await this.ctx.model.RadiateqlcProgress.create(data);
    if (!detectionType.includes('防护检测')) {
      // 删除防护检测子节点
      // await this.ctx.service.db.updateOne('RadiateqlcProgress', { radiateqlcProjectId }, { $pull: { progresses: { nodeName: '防护检测', parentField: 'spotRecord' } } });
      await this.ctx.model.RadiateqlcProgress.updateOne({ radiateqlcProjectId }, { $pull: { progresses: { nodeName: '防护检测', parentField: 'spotRecord' } } });
    }
    if (!detectionType.includes('性能检测')) {
      // 删除性能检测子节点
      // await this.ctx.service.db.updateOne('RadiateqlcProgress', { radiateqlcProjectId }, { $pull: { progresses: { nodeName: '性能检测', parentField: 'spotRecord' } } });
      await this.ctx.model.RadiateqlcProgress.updateOne({ radiateqlcProjectId }, { $pull: { progresses: { nodeName: '性能检测', parentField: 'spotRecord' } } });
    }
  }
  async updateProgress({ field, status, radiateqlcProjectId }) {
    console.log(field, status, radiateqlcProjectId, 8752283541785);
    // const serviceOrgId = this.ctx.session.jcqlcInfo.org_id;
    const newDate = new Date();
    const progressData = await this.ctx.model.RadiateqlcProgress.findOne({ radiateqlcProjectId, 'progresses.field': field });
    if (progressData) {
      // 更新节点状态
      let progressFieldIndex = -1;
      let sortNum = 0;
      /**
       * 父节点字段
       * 如果是并行的节点，那么不需要单独为父节点状态调用updateProgress来更新状态
       * 而是在更新并行子节点的时候同步更新父节点状态，当子节点都满足退出状态时，此时父节点也退出，流程进入到父节点的下一个节点
       */

      // 获取子节点和父节点的对应关系
      const childrenField = {};
      let maxSort = 0; // 最后一个流程节点序号
      progressData.progresses.forEach((item, i) => {
        if (item.parentField) {
          if (!childrenField[item.parentField]) {
            childrenField[item.parentField] = {
              parentIndex: -1, // 父节点索引
              children: [{ // 子节点信息，状态以及字段
                status: item.status,
                field: item.field,
                index: i,
              }],
            };
          } else {
            childrenField[item.parentField].children.push({
              status: item.status,
              field: item.field,
              index: i,
            });
          }
        }
        if (item.sortNum) {
          maxSort = item.sortNum;
        }
        if (item.field === field) {
          sortNum = item.sortNum;
        }
      });
      let parentStatus = 0;
      let curChildrenField = {};
      progressData.progresses.forEach((item, i) => {
        if (childrenField[item.field]) {
          childrenField[item.field].parentIndex = i;
        }
        if (item.field === field) {
          progressFieldIndex = i;
          item.status = status;
          item.completedTime = newDate;
          // 处理并行节点的父节点的状态
          // 如果所有的子节点状态都为2 那么父节点状态为2，否则父节点状态为1
          if (item.parentField) {
            parentStatus = status;
            curChildrenField = Object.keys(childrenField).filter(key => key === item.parentField);
            curChildrenField = childrenField[curChildrenField];
            curChildrenField.children.forEach(item2 => {
              if (item2.field !== item.field) {
                parentStatus = parentStatus !== 2 || item2.status !== 2 ? 1 : 2;
              }
            });
          }
        } else if (!item.parentField && item.sortNum && item.sortNum > sortNum && item.sortNum <= progressData.currentSortNum) {
          // 处理节点状态
          item.status = 1; // 进行中
          // 处理子节点状态
          if (childrenField[item.field]) {
            childrenField[item.field].children.forEach(item2 => {
              progressData.progresses[item2.index].status = 1;
            });
          }
        }

      });
      if (parentStatus === 2) {
        // 更新父节点状态和时间
        progressData.progresses[curChildrenField.parentIndex].status = 2;
        progressData.progresses[curChildrenField.parentIndex].completedTime = newDate;
        // 如果最后一个节点已完成，那么将项目状态更新为已完成
        if (maxSort === progressData.progresses[curChildrenField.parentIndex].sortNum) {
          // 更新项目状态
          await this.ctx.model.RadiateqlcProject.updateOne({ _id: radiateqlcProjectId }, { $set: { completedTime: newDate, completeStatus: 2 } });
        } else {
          // 进入到下一个节点
          // 如果满足退出的状态，进入到下一个节点
          // 更新父节点的下一个节点的状态和时间
          while (curChildrenField.parentIndex + 1 < progressData.progresses.length && !progressData.progresses[curChildrenField.parentIndex + 1].sortNum) {
            curChildrenField.parentIndex++;
          }
          curChildrenField.parentIndex = curChildrenField.parentIndex + 1;
          progressData.currentProgress = progressData.progresses[curChildrenField.parentIndex].field;
          progressData.currentSortNum = progressData.progresses[curChildrenField.parentIndex].sortNum;
          progressData.progresses[curChildrenField.parentIndex].status = 1;
          progressData.progresses[curChildrenField.parentIndex].completedTime = newDate;
        }
      } else if (parentStatus === 1) { // 进行中
        // 更新父节点状态和时间
        progressData.progresses[curChildrenField.parentIndex].status = 1;
        progressData.progresses[curChildrenField.parentIndex].completedTime = newDate;
        // console.log(progressData,99999999)
        // return
        progressData.currentProgress = progressData.progresses[curChildrenField.parentIndex].field;
        progressData.currentSortNum = progressData.progresses[curChildrenField.parentIndex].sortNum;
      } else if (status === 2) {
        // 如果最后一个节点已完成，那么将项目状态更新为已完成
        if (maxSort === sortNum) {
          // 更新项目状态
          await this.ctx.model.RadiateqlcProject.updateOne({ _id: radiateqlcProjectId }, { $set: { completedTime: newDate, completeStatus: 2 } });
        } else {
          // 如果满足退出的状态，进入到下一个节点
          while (progressFieldIndex + 1 < progressData.progresses.length && !progressData.progresses[progressFieldIndex + 1].sortNum) {
            progressFieldIndex++;
          }
          progressFieldIndex = progressFieldIndex + 1;
          progressData.currentSortNum = progressData.progresses[progressFieldIndex ].sortNum;
          progressData.currentProgress = progressData.progresses[progressFieldIndex].field;
          progressData.progresses[progressFieldIndex].status = 1;
          progressData.progresses[progressFieldIndex].completedTime = newDate;
        }
      } else {
        // 不满足退出条件
        progressData.currentSortNum = sortNum;
        progressData.currentProgress = field;
      }
      // 更新节点状态
      await this.ctx.model.RadiateqlcProgress.updateOne({ radiateqlcProjectId }, { $set: progressData });
    } else {
      throw new Error(`状态字段 ${field} 不在流程中，请检查`);
    }
  }
}

module.exports = radiateqlcProject;
