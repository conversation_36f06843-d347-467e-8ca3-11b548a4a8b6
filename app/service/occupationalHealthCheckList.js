const Service = require('egg').Service;
const shortid = require('shortid');
class OccupationalHealthCheckList extends Service {
  async updateGuideForm(data) {
    const { ctx } = this;
    try {
      const { _id, guideForm } = data;
      guideForm._id = shortid.generate();
      const res = await ctx.model.OccupationalHealthCheckList.findOneAndUpdate(
        { _id },
        { guideForm, isSubmitGuideForm: true },
        { new: true }
      ).lean();
      return res;
    } catch (error) {
      ctx.auditLog(
        '上传引导单失败',
        `${JSON.stringify(error)}`,
        'error'
      );
    }
  }

  async confirmSelectHospital(data) {
    const { _id, hospitalConfirm, confirmStatus = false } = data;
    const { ctx } = this;
    try {
      const res = await ctx.model.OccupationalHealthCheckList.updateOne(
        { _id },
        {
          hospitalConfirm: {
            _id: shortid.generate(),
            ...hospitalConfirm,
          },
          confirmStatus }
      );
      return res;
    } catch (error) {
      ctx.auditLog(
        '确认选择体检医院失败',
        `${JSON.stringify(error)}`,
        'error'
      );
    }
  }

  async deleteGuideForm(data) {
    await this.ctx.model.OccupationalHealthCheckList.updateOne(
      { _id: data._id },
      {
        'guideForm.originName': '',
        'guideForm.staticName': '',
        isSubmitGuideForm: false,
      }
    );
  }
}

module.exports = OccupationalHealthCheckList;
