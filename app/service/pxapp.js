// const certificate = require('../../lib/plugin/egg-jk-training/app/model/certificate');

const Service = require('egg').Service;

class PxappService extends Service {
  // 获取培训机构列表
  async getPxOrgList(params = {}) {
    const page = parseInt(params.page) || 1; // 默认为第一页
    const limit = parseInt(params.pageSize) || 10; // 默认每页10条
    const skip = (page - 1) * limit;
    return await this.ctx.model.PxOrg.aggregate([
      { $skip: skip },
      { $limit: limit },
      {
        $lookup: {
          from: 'trainingRecords',
          foreignField: 'pxOrgId',
          localField: '_id',
          as: 'trainingRecords',
        },
      },
      { $unwind: { path: '$trainingRecords' } },
      {
        $project: {
          name: 1,
          trainingFormat: 1,
          employees: '$trainingRecords.employees',
        },
      },
      { $addFields: { employeeslen: { $size: '$employees' } } },
      {
        $group: {
          _id: '$_id',
          peopleTimes: { $sum: '$employeeslen' },
          name: { $first: '$name' },
          trainingFormat: { $first: '$trainingFormat' },
        },
      },
    ]);
  }

  // 获取首页培训记录列表
  async getHomePxRecords() {
    const { ctx } = this;
    if (!ctx.session.user._id) {
      throw new Error('用户信息缺失');
    }
    const res = await ctx.model.PersonalTraining.aggregate([
      {
        $match: {
          userId: ctx.session.user._id,
          trainingRecordId: { $exists: true },
          status: true,
        },
      },
      {
        $lookup: {
          from: 'examSyllabus',
          foreignField: '_id',
          localField: 'examSyllabusId',
          as: 'examSyllabus',
        },
      },
      {
        $lookup: {
          from: 'trainingRecords',
          foreignField: '_id',
          localField: 'trainingRecordId',
          as: 'trainingRecord',
        },
      },
      {
        $project: {
          certificateType: {
            $arrayElemAt: [ '$examSyllabus.certificateType', 0 ],
          },
          trainingClassId: {
            $arrayElemAt: [ '$trainingRecord.trainingClassId', 0 ],
          },
          trainingStartDate: {
            $arrayElemAt: [ '$trainingRecord.trainingStartDate', 0 ],
          },
          trainingEndDate: {
            $arrayElemAt: [ '$trainingRecord.trainingEndDate', 0 ],
          },
        },
      },
      {
        $lookup: {
          from: 'trainingClass',
          foreignField: '_id',
          localField: 'trainingClassId',
          as: 'trainingClassId',
        },
      },
      {
        $project: {
          certificateType: 1,
          trainingStartDate: 1,
          trainingEndDate: 1,
          trainingClassName: {
            $arrayElemAt: [ '$trainingClassId.name', 0 ],
          },
          pxOrgId: {
            $arrayElemAt: [ '$trainingClassId.pxOrgId', 0 ],
          },
        },
      },
      {
        $lookup: {
          from: 'pxOrg',
          localField: 'pxOrgId',
          foreignField: '_id',
          as: 'PxOrg',
        },
      },
      {
        $project: {
          certificateType: 1,
          trainingClassName: 1,
          trainingStartDate: 1,
          trainingEndDate: 1,
          pxOrgname: {
            $arrayElemAt: [ '$PxOrg.name', 0 ],
          },
          pxOrgAddress: {
            $arrayElemAt: [ '$PxOrg.address', 0 ],
          },
        },
      },
    ]);
    const statistics = await this.getStatistics(); // 获取统计数据
    return { list: res, statistics };
  }

  // 获取培训记录列表
  async getPxRecords() {
    const { ctx } = this;
    if (!ctx.session.user._id) {
      throw new Error('用户信息缺失');
    }
    const res = await ctx.model.PersonalTraining.find({
      userId: ctx.session.user._id,
      trainingRecordId: { $exists: true },
      status: true,
    })
      .populate('examSyllabusId', 'name -_id')
      .populate('trainingRecordId', 'trainingStartDate')
      .populate('bigTestList', 'createdAt -_id')
      .populate('employeesId', 'name IDNum headImg')
      .select('examSyllabusId trainingRecordId bigTestList completeState employeesId');
    return res;
  }

  // 获取培训记录详情
  async getPxDetail(params) {
    const { ctx } = this;
    if (!ctx.session.user._id) {
      throw new Error('用户信息缺失');
    }
    const { id } = params;
    const res = await ctx.model.PersonalTraining.findOne({
      _id: id,
      userId: ctx.session.user._id,
      status: true,
    })
      .populate('userId', 'idNo name phoneNum company')
      .populate({
        path: 'trainingRecordId',
        select:
          'pxOrgId trainingStartDate trainingEndDate proofMaterial trainingPlace employees _id',
        populate: {
          path: 'pxOrgId',
          select: 'name trainingFormat _id',
        },
      })
      .populate('examSyllabusId', 'name certificateType -_id')
      .populate('bigTestList', 'createdAt -_id')
      .populate('certificateID', 'certificateStatus _id img')
      .select(
        'userId examSyllabusId trainingRecordId certificateID bigTestList'
      )
      .lean();

    res.trainingRecordId &&
      res.trainingRecordId.proofMaterial.length > 0 &&
      res.trainingRecordId.proofMaterial.forEach(item => {
        item.url = `static${ctx.app.config.upload_http_path}/${res.trainingRecordId.pxOrgId._id}/${item.staticName}`;
      });
    res.certificateID &&
      (res.certificateID.url = `static${ctx.app.config.certificate_http_path}/${res.certificateID.img}`);
    return res;
  }

  async getStatistics() {
    const { ctx } = this;
    if (!ctx.session.user._id) {
      throw new Error('用户信息缺失');
    }
    const companyCount = await ctx.model.Adminorg.count({
      isactive: '1',
      isDelete: false,
    });
    const shouldTrainedPeople = await ctx.model.Employee.count({
      status: 1,
      survivalStatus: true,
      EnterpriseID: { $exists: true },
    });
    let hasTrainedPeople = await ctx.model.PersonalTraining.find(
      { examSyllabusId: { $exists: true }, status: true },
      { employeesId: 1 }
    );
    hasTrainedPeople = new Set(hasTrainedPeople.map(item => item.employeesId))
      .size;

    return {
      companyCount, // 总企业数
      shouldTrainedPeople, // 应培训人数
      hasTrainedPeople, // 已培训人数
    };
  }
  async getMyStatistics() {
    const { ctx } = this;
    if (!ctx.session.user._id) {
      throw new Error('用户信息缺失');
    }
    const myPersonalTraining = await ctx.model.PersonalTraining.find(
      {
        userId: ctx.session.user._id,
        status: true,
        examSyllabusId: { $exists: true },
      },
      { _id: 1 }
    );
    const myPersonalTrainingIds = myPersonalTraining.map(item => item._id);
    return {
      myPersonalTrainingNum: myPersonalTraining.length, // 我参与的培训数
      myExamRecordsNum: await ctx.model.TestRecord.count({
        personalTrainingId: { $in: myPersonalTrainingIds },
      }), // 我的考试记录数
      myCertificateNum: await ctx.model.Certificate.count({
        personalTrainingId: { $in: myPersonalTrainingIds },
        certificateStatus: 2,
      }), // 我的证书数
    };
  }

  // 获取培训班
  async getTrainingClass(params) {
    const { ctx } = this;
    const { type, searchKey = {} } = params;
    if (type === 'all') {
      const roleToNumMap = {
        企业负责人初次培训: 1,
        企业负责人继续教育: 2,
        职业健康管理人员初次培训: 3,
        职业健康管理人员继续教育: 4,
        劳动者上岗前培训: 5,
        劳动者在岗培训: 6,
      };
      const matchQuery = { status: 2, 'pxOrg.trainingFormat': 'offline' };
      if (searchKey.address !== '') {
        matchQuery['pxOrg.regAddr'] = {
          $all: [
            { $elemMatch: { $eq: searchKey.address[0] } },
            { $elemMatch: { $eq: searchKey.address[1] } },
          ],
        };
      }
      if (searchKey.roles !== '') {
        const num = roleToNumMap[searchKey.roles] || null;
        matchQuery['examSyllabus.certificateType'] = num;
      }
      // if (searchKey.time !== '') {
      //   const roleToNumMap = {
      //     企业负责人初次培训: 1,
      //     企业负责人继续教育: 2,
      //     职业健康管理人员初次培训: 3,
      //     职业健康管理人员继续教育: 4,
      //     劳动者上岗前培训: 5,
      //     劳动者在岗培训: 6,
      //   };
      //   const num = roleToNumMap[searchKey.roles] || null;
      //   matchQuery['examSyllabus.certificateType'] = num;
      // }
      const res = await ctx.model.TrainingClass.aggregate([
        {
          $lookup: {
            from: 'pxOrg',
            localField: 'pxOrgId',
            foreignField: '_id',
            as: 'pxOrg',
          },
        },
        {
          $unwind: '$pxOrg',
        },
        {
          $lookup: {
            from: 'examSyllabus',
            localField: 'examSyllabusId',
            foreignField: '_id',
            as: 'examSyllabus',
          },
        },
        {
          $match: matchQuery,
        },
        {
          $sort: {
            updatedAt: -1,
          },
        },
        {
          $lookup: {
            from: 'registration',
            localField: '_id',
            foreignField: 'trainingClassId',
            as: 'registrations',
          },
        },
        {
          $project: {
            pxOrgId: '$pxOrg._id',
            cname: '$pxOrg.name',
            address: '$pxOrg.address',
            createdAt: 1,
            name: 1,
            trainer: 1,
            cover: 1,
            employees: {
              $reduce: {
                input: '$registrations.employees',
                initialValue: 0,
                in: { $add: [ '$$value', { $size: '$$this' }] },
              },
            },
            examSyllabus: {
              _id: 1,
              name: 1,
              certificateType: 1,
            },
          },
        },
      ]);
      if (res && res.length > 0) {
        res.forEach(item => {
          item.cover.staticName && (item.cover = [{ ...item.cover, url: '/static' + ctx.app.config.upload_http_path + '/' + item.pxOrgId + '/' + item.cover.staticName }]);
        });
      }
      return res;
    } else if (type === 'person') {
      const { id } = params;
      const res = await ctx.model.User.aggregate([
        {
          $match: {
            _id: id,
          },
        },
        {
          $lookup: {
            from: 'employees',
            localField: '_id',
            foreignField: 'userId',
            as: 'employee',
          },
        },
        { $unwind: '$employee' },
        {
          $lookup: {
            from: 'registration',
            localField: 'employee._id',
            foreignField: 'employees',
            as: 'registration',
          },
        },
        {
          $project: {
            employeeId: '$employee._id',
            registration: 1,
          },
        },
        { $unwind: '$registration' },
        {
          $lookup: {
            from: 'trainingClass',
            localField: 'registration.trainingClassId',
            foreignField: '_id',
            as: 'trainingClass',
          },
        },
        { $unwind: '$trainingClass' },
        {
          $addFields: {
            'registration.trainingClass': '$trainingClass',
          },
        },
        {
          $lookup: {
            from: 'pxOrg',
            localField: 'registration.pxOrgId',
            foreignField: '_id',
            as: 'pxOrg',
          },
        },
        { $unwind: '$pxOrg' },
        {
          $lookup: {
            from: 'pxOrgUsers',
            localField: 'registration.trainingClass.trainer',
            foreignField: '_id',
            as: 'pxOrgUser',
          },
        },
        { $unwind: '$pxOrgUser' },
        {
          $addFields: {
            'registration.pxOrgName': '$pxOrg.name',
            'registration.trainingClass.trainerName': '$pxOrgUser.name',
          },
        },
        {
          $group: {
            _id: '$_id',
            registration: { $push: '$registration' },
            employeeId: { $first: '$employeeId' },
          },
        },
        {
          $lookup: {
            from: 'personalTraining',
            localField: '_id',
            foreignField: 'userId',
            as: 'personalTraining',
          },
        },
        {
          $lookup: {
            from: 'certificate',
            localField: 'personalTraining.certificateID',
            foreignField: '_id',
            as: 'certificate',
          },
        },
        {
          $project: {
            registration: {
              _id: 1,
              status: 1,
              deleted: 1,
              pxOrgId: 1,
              pxOrgName: 1,
              trainingClass: {
                _id: 1,
                coursesList: 1,
                status: 1,
                trainingPlace: 1,
                name: 1,
                trainer: 1,
                trainerName: 1,
                trainingEndDate: 1,
                trainingStartDate: 1,
              },
            },
            employeeId: 1,
            personalTraining: {
              _id: 1,
              trainingClassId: 1,
              completeState: 1,
              certificateID: 1,
            },
            certificate: {
              _id: 1,
              img: 1,
            },
          },
        },
      ]);
      return res[0];
    }
    const { id } = params;
    const res = await ctx.model.TrainingClass.aggregate([
      { $match: { _id: id } },
      {
        $lookup: {
          from: 'pxOrg',
          localField: 'pxOrgId',
          foreignField: '_id',
          as: 'pxOrg',
        },
      },
      { $unwind: '$pxOrg' },
      {
        $lookup: {
          from: 'Courses',
          localField: 'coursesList',
          foreignField: '_id',
          as: 'coursesList',
        },
      },
      {
        $lookup: {
          from: 'pxOrgUsers',
          localField: 'trainer',
          foreignField: '_id',
          as: 'trainerName',
        },
      },
      { $unwind: '$trainerName' },
      {
        $lookup: {
          from: 'examSyllabus',
          localField: 'examSyllabusId',
          foreignField: '_id',
          as: 'examSyllabus',
        },
      },
      {
        $lookup: {
          from: 'registration',
          localField: '_id',
          foreignField: 'trainingClassId',
          as: 'registrations',
        },
      },
      {
        $project: {
          pxOrgId: '$pxOrg._id',
          cname: '$pxOrg.name',
          address: '$pxOrg.address',
          managers: '$pxOrg.managers',
          createdAt: 1,
          name: 1,
          cover: 1,
          trainer: 1,
          trainerName: '$trainerName.name',
          introduction: 1,
          price: 1,
          trainingStartDate: 1,
          trainingEndDate: 1,
          employees: {
            $reduce: {
              input: '$registrations.employees',
              initialValue: 0,
              in: { $add: [ '$$value', { $size: '$$this' }] },
            },
          },
          coursesList: {
            explain: 1,
            credit: 1,
            classHours: 1,
            labels: 1,
            allowToOpen: 1,
            complete: 1,
            cover: 1,
            views: 1,
            likes: 1,
            name: 1,
            openTime: 1,
            _id: 1,
          },
          examSyllabus: {
            _id: 1,
            name: 1,
            certificateType: 1,
          },
        },
      },
    ]);
    if (res.length > 0 && res[0].coursesList && res[0].coursesList.length > 0) {
      res[0].coursesList = res[0].coursesList.filter(item => item.complete);
      res[0].cover.staticName && (res[0].cover = [{ ...res[0].cover, url: '/static' + ctx.app.config.upload_http_path + '/' + res[0].pxOrgId + '/' + res[0].cover.staticName }]);
      const managerId = res[0].managers && res[0].managers[0];
      const manager = await ctx.model.PxOrgUser.findOne({ _id: managerId }, { name: 1, phoneNum: 1 });
      res[0].manager = manager;
    }
    return res[0];
  }

  // h5获取培训证书
  // async getCertificate(params) {
  // }
}

module.exports = PxappService;
