// 国家体检标准 体检系统对接
const Service = require('egg').Service;
const moment = require('moment');
class HealthCheckAppointmentService extends Service {
  // 上传职业健康档案(单个)
  async itemParameterVerification(item) {
    if (!item.WORKER_INFO || !item.WORKER_INFO.ID_CARD_TYPE_CODE) {
      throw new Error('WORKER_INFO和ID_CARD_TYPE_CODE不能为空');
    }
    if (!item.ENTERPRISE_INFO_EMPLOYER || !item.ENTERPRISE_INFO_EMPLOYER.CREDIT_CODE_EMPLOYER) {
      throw new Error('ENTERPRISE_INFO_EMPLOYER和CREDIT_CODE_EMPLOYER不能为空');
    }
    if (!item.EXAM_CONCLUSION_LIST || !item.EXAM_CONCLUSION_LIST.length) {
      throw new Error('EXAM_CONCLUSION_LIST不能为空');
    }
    if (!item.WORKER_INFO.WORKER_TELPHONE) {
      throw new Error('WORKER_TELPHONE不能为空');
    }
    if (!item.EXAM_DATE) {
      throw new Error('EXAM_DATE不能为空');
    }
    if (!item.REPORT_DATE) {
      throw new Error('REPORT_DATE不能为空');
    }
    if (!item.EXAM_TYPE_CODE) {
      throw new Error('EXAM_TYPE_CODE不能为空');
    }
    if ([ '01', '02', '03', '04', '05', '06' ].indexOf(item.EXAM_TYPE_CODE) === -1) {
      throw new Error('EXAM_TYPE_CODE参数错误');
    }
  }
  // 上传职业健康档案(国家标准)
  async healthExamRecordV1_0(physicalExamOrgId, item) {
    try {
      const { config, ctx } = this;
      const { branch } = config;
      if (branch === 'sxcc') {
        return await ctx.service.sxccPhysicalAppointment.healthExamRecord(physicalExamOrgId, item);
      }
      await this.itemParameterVerification(item);
      if (item.EXAM_DATE && item.EXAM_DATE.length === 8) {
        item.EXAM_DATE = this.getTrueTime(item.EXAM_DATE);
      }
      if (item.REPORT_DATE && item.REPORT_DATE.length === 8) {
        item.REPORT_DATE = this.getTrueTime(item.REPORT_DATE);
      }
      const physicalExamOrg = await ctx.model.PhysicalExamOrg.findOne({ _id: physicalExamOrgId });
      if (item.ORG_CODE && item.ORG_CODE !== physicalExamOrg.organization) {
        throw new Error('您的token跟上传的创建机构代码不匹配');
      }
      // 1、通过用工单位统一信用代码判断是否预约体检
      const EnterpriseCode = item.ENTERPRISE_INFO_EMPLOYER.CREDIT_CODE_EMPLOYER;
      const checkType = item.EXAM_TYPE_CODE;
      let appointment = await this.ctx.model.HealthCheckAppointment.findOne({ EnterpriseCode, physicalExamOrgId, checkType });
      if (appointment) {
        const checkDate = new Date(item.EXAM_DATE);
        if (checkDate < appointment.startTime || checkDate > appointment.endTime) {
          appointment = null;
        }
      }
      // 2、处理用工单位信息
      const Enterprise = await this.handleEnterprise(EnterpriseCode, item.ENTERPRISE_INFO_EMPLOYER, physicalExamOrg);
      // 3、处理用人单位信息
      if (item.ENTERPRISE_INFO && item.ENTERPRISE_INFO.CREDIT_CODE) {
        await this.handleEnterprise(item.ENTERPRISE_INFO.CREDIT_CODE, item.ENTERPRISE_INFO, physicalExamOrg);
      }
      // 4、人员信息
      const workerInfo = item.WORKER_INFO;
      const personInfo = await this.handlePersonInfo({
        EnterpriseID: Enterprise._id,
        name: workerInfo.WORKER_NAME,
        IDNum: workerInfo.ID_CARD,
        laborDispatching: !!(item.ENTERPRISE_INFO && item.ENTERPRISE_INFO.CREDIT_CODE),
        gender: workerInfo.GENDER_CODE ? String(workerInfo.GENDER_CODE - 1) : '',
        phoneNum: workerInfo.WORKER_TELPHONE,
        userId: '',
        source: 'oapi',
        status: [ '03', '05' ].includes(checkType) ? 0 : 1,
      });
      if (item.EXAM_TYPE_CODE === '06') {
        return {
          status: 200,
          message: '普通体检不创建体检项目, 企业信息和人员信息已创建成功',
        };
      }
      // 5、体检项目
      const healthcheck = await this.handleHealthCheck(physicalExamOrg, item, Enterprise, appointment);
      // 6、体检信息
      await this.handleSuspect(healthcheck, personInfo, item, appointment);
      // 7、更新预约单
      if (appointment) await this.updateAppointment(appointment, healthcheck._id, personInfo);
      // 8、更新体检项目中的人数以及企业表中的healcheckInfo
      await this.updateHealthCheck(healthcheck, appointment ? appointment._id : '', Enterprise);
      return {
        status: 200,
        message: `用工单位：${item.ENTERPRISE_INFO_EMPLOYER.ENTERPRISE_NAME_EMPLOYER || Enterprise.cname} - ${item.WORKER_INFO.WORKER_NAME}的职业健康档案上传成功`,
      };
    } catch (e) {
      this.ctx.auditLog('体检系统对接 上传职业健康档案失败', e.message, 'error');
      return {
        status: 500,
        message: (item.id || item.ID) + '上传失败：' + e.message,
      };
    }
  }
  // 更新HealthCheck中的人数以及企业表中的体检信息
  async updateHealthCheck(healthcheck, appointmentId, Enterprise) {
    const appointment = appointmentId ? await this.ctx.model.HealthCheckAppointment.findOne({ _id: appointmentId }) : {};
    const appointmentPeopleNum = (appointment.employeeIds ? appointment.employeeIds.length : appointment.peopleNum) || 0;
    const suspectList = await this.ctx.model.Suspect.find({ batch: healthcheck._id });
    const updateData = {
      shouldCheckNum: appointmentPeopleNum > suspectList.length ? appointmentPeopleNum : suspectList.length,
      actuallNum: suspectList.length,
      recheckNum: suspectList.filter(ele => ele.recheck === '是').length,
      normal: suspectList.filter(ele => ele.CwithO === '目前未见异常').length,
      suspected: suspectList.filter(ele => ele.CwithO === '疑似职业病').length,
      forbid: suspectList.filter(ele => ele.CwithO === '禁忌证').length,
      otherDisease: suspectList.filter(ele => ele.CwithO === '其他疾病或异常').length,
    };
    await this.ctx.model.Healthcheck.updateOne({ _id: healthcheck._id }, updateData);
    await this.ctx.model.Adminorg.updateOne({ _id: Enterprise._id }, { $set: { healcheckInfo: {
      actuallNum: String(updateData.actuallNum), // 实检人数
      recheck: String(updateData.recheckNum), // 复查
      suspected: String(updateData.suspected), // 疑似
      forbid: String(updateData.forbid), // 禁忌证
      occupational: String(Enterprise.occupational || 0), // 职业病
      recentDay: healthcheck.checkDate, // 体检时间
    } } });
  }
  // 处理企业信息(用工单位、用人单位)
  async handleEnterprise(EnterpriseCode, params, physicalExamOrg) {
    let Enterprise = await this.ctx.model.Adminorg.findOne({ code: EnterpriseCode, isDelete: false });
    if (!Enterprise) {
      const { districtRegAdd = [], point = [] } = await this.ctx.service.district.getParentByCode(params.ADDRESS_CODE_EMPLOYER || params.ADDRESS_CODE);
      const industryCategory = params.INDUSTRY_CATEGORY_CODE_EMPLOYER || params.INDUSTRY_CATEGORY_CODE;
      const enterpriseData = {
        cname: params.ENTERPRISE_NAME_EMPLOYER || params.ENTERPRISE_NAME || '',
        code: EnterpriseCode,
        regType: params.ECONOMIC_TYPE_CODE_EMPLOYER || params.ECONOMIC_TYPE_CODE || '',
        industryCategory: industryCategory ? [ industryCategory ] : [],
        companyScale: params.BUSINESS_SCALE_CODE_EMPLOYER || params.BUSINESS_SCALE_CODE || '',
        districtRegAdd,
        regAdd: params.ADDRESS_DETAIL || '',
        workAddress: [{ districts: districtRegAdd, point, address: params.ADDRESS_DETAIL || '' }],
        isactive: '1',
        leadIn: '3',
        physicalExaminationOrgID: [{
          _id: physicalExamOrg._id,
          ServiceContractName: physicalExamOrg.contract || '',
          ServicePhoneNum: physicalExamOrg.phoneNum || '',
        }],
      };
      Enterprise = await this.ctx.model.Adminorg.create(enterpriseData);
    }
    return Enterprise;
  }
  // 按照体检结果更新预约单
  async updateAppointment(appointment, healthcheckId, personInfo) {
    let updateData = { };
    if (appointment.employeeIds.indexOf(personInfo._id) === -1) {
      updateData = { $push: { employeeIds: personInfo._id }, $inc: { peopleNum: 1 } };
    }
    updateData.healthcheckId = healthcheckId;
    let jcRes = true;
    for (let i = 0; i < appointment.employeeIds.length; i++) {
      const employeeId = appointment.employeeIds[i];
      const res = await this.ctx.model.Suspect.findOne({ employeeId, batch: healthcheckId }, { name: 1 });
      if (!res) {
        jcRes = false;
        break;
      }
    }
    updateData.status = jcRes ? 6 : 5;
    const result = await this.ctx.model.HealthCheckAppointment.updateOne({ _id: appointment._id }, updateData);
    if (result.nModified) {
      this.ctx.auditLog('体检系统对接 上传体检报告-更新预约单成功', updateData, 'info');
      this.synchronizeAppointment(appointment._id); // 同步mongodb中的预约单到sqlserver中
    } else {
      this.ctx.auditLog('体检系统对接 上传体检报告-更新预约单失败', result, 'error');
    }
  }
  // 获取体检结果中的CwithO
  getCwithO(EXAM_CONCLUSION_LIST = [], EXAM_ITEM_RESULT_LIST = []) {
    const data = EXAM_CONCLUSION_LIST[0];
    if (data.YSZYB_CODE) return '疑似职业病';
    if (data.ZYJJZ_NAME) return '禁忌证';
    if (data.QTJB_NAME) return '其他疾病或异常';
    if (EXAM_ITEM_RESULT_LIST[0] && EXAM_ITEM_RESULT_LIST[0].ABNORMAL === '1') return '目前未见异常';
    return EXAM_ITEM_RESULT_LIST[0] ? EXAM_ITEM_RESULT_LIST[0].EXAM_RESULT : '目前未见异常';
  }
  // 创建/更新suspect
  async handleSuspect(healthcheck, personInfo, item) {
    const age = item.WORKER_INFO.BIRTH_DATE ? moment().diff(item.WORKER_INFO.BIRTH_DATE, 'years') : ''; // 根据出生日期计算年龄
    const workType = await this.getWorkTypeCode(item.WORK_TYPE_CODE || item.OTHER_WORK_TYPE);
    let suspect = await this.ctx.model.Suspect.findOne({ employeeId: personInfo._id, batch: healthcheck._id });
    const newData = {
      name: personInfo.name,
      age,
      gender: item.WORKER_INFO.GENDER_CODE ? item.WORKER_INFO.GENDER_CODE - 1 + '' : '',
      workType,
      harmFactors: item.EXAM_CONCLUSION_LIST[0].ITAM_NAME || item.EXAM_CONCLUSION_LIST[0].ITAM_CODE || item.CONTACT_FACTOR_CODE,
      otherHarmFactors: item.CONTACT_FACTOR_OTHER || item.FACTOR_OTHER || '',
      opinion: item.EXAM_CONCLUSION_LIST.map(ele => ele.EXAM_CONCLUSION_CODE).join('；'), // 意见
      CwithO: this.getCwithO(item.EXAM_CONCLUSION_LIST, item.EXAM_ITEM_RESULT_LIST),
      dedicalAdvice: '',
      batch: healthcheck._id,
      employeeId: personInfo._id,
      IDCard: item.WORKER_INFO.ID_CARD,
      EnterpriseID: personInfo.EnterpriseID,
      checkType: healthcheck.checkType,
      checkDate: new Date(item.EXAM_DATE),
      recheck: item.IS_REVIEW === '1' ? '是' : '否',
      riskFactorsOfPhysicalExaminations: item.EXAM_CONCLUSION_LIST.map(ele => {
        return {
          harmFactor: ele.ITAM_NAME || '',
          examConclusion: ele.EXAM_CONCLUSION_CODE || '',
          suspectedOccupationalDisease: ele.YSZYB_CODE || '',
          occupationalContraindications: ele.ZYJJZ_NAME || '',
          otherOrDes: ele.QTJB_NAME || '',
        };
      }),
      bhkSubList: item.EXAM_ITEM_RESULT_LIST.map(ele => {
        return {
          itmcod: ele.EXAM_ITEM_PNAME || '',
          name: ele.EXAM_ITEM_NAME || '',
          classify: ele.EXAM_RESULT_TYPE || '',
          msrunt: ele.EXAM_ITEM_UNIT_CODE || '', // 计量单位
          itemStdValue: `${ele.REFERENCE_RANGE_MIN || ''} - ${ele.REFERENCE_RANGE_MAX || ''}`,
          result: ele.EXAM_RESULT || '',
          chkdat: new Date(item.EXAM_DATE),
          jdgptn: +ele.EXAM_RESULT_TYPE || 1,
          minVal: ele.REFERENCE_RANGE_MIN || '',
          maxVal: ele.REFERENCE_RANGE_MAX || '',
          diagRest: ele.EXAM_RESULT || '',
          rstFlag: ele.ABNORMAL || '',
        };
      }),
    };
    if (suspect) {
      await this.ctx.model.Suspect.updateOne({ _id: suspect._id }, newData);
    } else {
      suspect = await this.ctx.model.Suspect.create(newData);
    }
  }
  // 创建/更新体检项目(体检机构已存在)
  async handleHealthCheck(physicalExamOrgDetail, item, EnterpriseDetail, appointment) {
    const { ctx } = this;
    const physicalExaminationOrgID = physicalExamOrgDetail._id,
      EnterpriseID = EnterpriseDetail._id,
      year = new Date(item.EXAM_DATE).getFullYear() + '';
    const healthcheckeCode = {
      '01': '0',
      '02': '1',
      '03': '2',
      '04': '4',
      '05': '5',
    };
    const checkType = healthcheckeCode[item.EXAM_TYPE_CODE];
    const recheck = !!(item.IS_REVIEW && item.IS_REVIEW === '1');
    let healthchecke = await ctx.model.Healthcheck.findOne({ physicalExaminationOrgID, EnterpriseID, year, checkType, recheck });
    if (!healthchecke) {
      const newData = {
        organization: item.REPORT_ORGAN_CREDIT_CODE || physicalExamOrgDetail.name, // 检查机构名称
        physicalExaminationOrgID,
        enterpriseName: item.ENTERPRISE_INFO_EMPLOYER.ENTERPRISE_NAME_EMPLOYER || EnterpriseDetail.cname,
        EnterpriseID,
        enterpriseContactsName: EnterpriseDetail.contract || '',
        enterpriseContactsPhonNumber: EnterpriseDetail.phoneNum || '',
        enterpriseAddr: EnterpriseDetail.districtRegAdd || [],
        workAddress: EnterpriseDetail.workAddress || [],
        year,
        projectNumber: 'WKZWY' + moment(item.EXAM_DATE).format('YYYYMMDD') + Math.random().toString().slice(2, 6),
        checkDate: appointment ? appointment.startTime : new Date(item.EXAM_DATE),
        checkEndDate: appointment ? appointment.endTime : new Date(item.EXAM_DATE),
        checkPlace: (physicalExamOrgDetail.regAddr ? physicalExamOrgDetail.regAddr.join('') : '') + physicalExamOrgDetail.address || '',
        approvalDate: new Date(item.REPORT_DATE),
        applyTime: new Date(),
        checkType,
        shouldCheckNum: appointment ? appointment.employeeIds.length || appointment.peopleNum : 0,
        actuallNum: 0,
        recheck: !!(item.IS_REVIEW && item.IS_REVIEW === '1'),
        reportStatus: true,
      };
      healthchecke = await ctx.model.Healthcheck.create(newData);
    } else {
      const updateData = {
        checkDate: new Date(item.EXAM_DATE) < healthchecke.checkDate ? new Date(item.EXAM_DATE) : healthchecke.checkDate,
        checkEndDate: new Date(item.EXAM_DATE) > healthchecke.checkEndDate ? new Date(item.EXAM_DATE) : healthchecke.checkEndDate,
      };
      await ctx.model.Healthcheck.updateOne({ _id: healthchecke._id }, updateData);
    }
    return healthchecke;
  }
  // 创建/更新人员信息employee、user
  async handlePersonInfo(personInfo) {
    const { ctx } = this;
    const { EnterpriseID, IDNum, phoneNum } = personInfo;
    if (!IDNum) throw new Error('身份证号不能为空');
    let employeeInfo = await ctx.model.Employee.findOne({ EnterpriseID, IDNum });
    if (!employeeInfo) employeeInfo = await ctx.model.Employee.findOne({ EnterpriseID, phoneNum });
    if (!employeeInfo) {
      employeeInfo = await ctx.model.Employee.create(personInfo);
      ctx.auditLog('体检系统对接 上传体检报告-创建人员信息成功', employeeInfo, 'info');
    } else {
      await ctx.model.Employee.updateOne({ _id: employeeInfo._id }, { $set: personInfo });
      ctx.auditLog('体检系统对接 上传体检报告-更新人员信息成功', personInfo, 'info');
    }
    if (phoneNum) {
      const flag = /^1[3-9][0-9]{9}$/.test(phoneNum); // 手机号校验
      if (!flag) throw new Error('手机号格式错误');
      let user = await ctx.model.User.findOne({ phoneNum });
      if (!user) {
        user = await ctx.model.User.create({ name: employeeInfo.name, phoneNum, employeeId: employeeInfo._id, idNo: IDNum, companyId: [ EnterpriseID ], companyStatus: 2, birth: personInfo.birthDate });
      }
      if (user && user._id) {
        await ctx.model.Employee.updateOne({ _id: employeeInfo._id }, { userId: user._id });
      }
    }
    if (employeeInfo.departs.length === 0) {
      let dingtrees = await ctx.model.Dingtree.findOne({ EnterpriseID });
      if (!dingtrees) {
        dingtrees = await ctx.model.Dingtree.create({ EnterpriseID, name: '企业部门', type: '1' });
      }
      await ctx.model.Employee.updateOne({ _id: employeeInfo._id }, { departs: [[ dingtrees._id ]] });
      await ctx.model.Dingtree.updateOne({ _id: dingtrees._id }, { $addToSet: { staff: employeeInfo._id } });
    }
    const options = {
      returnOptions: {
        name: {
          returnPlaintext: true, // 返回明文
        },
        IDNum: {
          returnPlaintext: true, // 返回明文
        },
        phoneNum: {
          returnPlaintext: true, // 返回明文
        },
      },
    };

    return ctx.model.Employee.findOne({ _id: employeeInfo._id }).setOptions(options);
  }

  // 根据token获取体检机构id并校验
  async getOrgId(token = '') {
    token = token.replace(/\\\"/g, '\"');
    const agentId = token ? JSON.parse(token).agentId : '';
    if (!agentId) throw new Error('token数据错误');
    const apiUser = await this.ctx.model.ApiUser.findOne({ _id: agentId, status: 1 }, { orgId: 1 });
    if (apiUser) {
      const physicalExamOrgId = apiUser.orgId;
      if (!physicalExamOrgId) {
        throw new Error('token错误或者权限不足');
      }
      const physicalExamOrg = await this.ctx.model.PhysicalExamOrg.findOne({ _id: physicalExamOrgId });
      if (!physicalExamOrg) {
        throw new Error('体检机构不存在');
      }
      return physicalExamOrgId;
    }
    throw new Error('体检机构找不到');
  }

  // 数组中时间格式化
  perseTime(arr = []) {
    const newArr = JSON.parse(JSON.stringify(arr));
    return newArr.map(item => {
      item.startTime = moment(item.startTime).format('YYYY-MM-DD HH:mm');
      item.endTime = moment(item.endTime).format('YYYY-MM-DD HH:mm');
      item.createdAt = moment(item.createdAt).format('YYYY-MM-DD HH:mm');
      item.updatedAt = moment(item.updatedAt).format('YYYY-MM-DD HH:mm');
      return item;
    });
  }

  // 根据code获取工种代码名称
  async getWorkTypeCode(code) {
    if (!code) return '';
    const data = await this.ctx.model.ZjWorkTypeCode.findOne({ code });
    return data ? data.name : code;
  }

  // 时间转化
  getTrueTime(dateString) { // dateString = '20200101'
    if (!dateString || dateString.length !== 8) return '';
    const year = dateString.substring(0, 4);
    const month = dateString.substring(4, 6);
    const day = dateString.substring(6, 8);
    return new Date(year, month - 1, day);
  }

  // 体检机构对接 上传用人单位信息 国家标准
  async enterpriseInfoV1_0(physicalExamOrgId, item) {
    try {
      if (!item.CREDIT_CODE) {
        throw new Error('CREDIT_CODE不能为空');
      }
      if (item.CREDIT_CODE.length !== 18) {
        throw new Error('CREDIT_CODE长度错误');
      }
      if (item.BUSINESS_SCALE_CODE && ![ '01', '02', '03', '04' ].includes(item.BUSINESS_SCALE_CODE)) {
        throw new Error('BUSINESS_SCALE_CODE参数错误, 只能为01, 02, 03, 04');
      }
      const { ctx } = this;
      const physicalExamOrg = await ctx.model.PhysicalExamOrg.findOne({ _id: physicalExamOrgId });
      if (item.ORG_CODE && item.ORG_CODE !== physicalExamOrg.organization) {
        throw new Error('您的token跟上传的创建机构代码不匹配');
      }
      const EnterpriseCode = item.CREDIT_CODE.trim().toUpperCase();
      let Enterprise = await this.ctx.model.Adminorg.findOne({ code: EnterpriseCode, isDelete: false });
      const { districtRegAdd = [], point = [] } = await this.ctx.service.district.getParentByCode(item.ADDRESS_CODE);
      const industryCategory = item.INDUSTRY_CATEGORY_CODE;
      const companyScaleObj = { '01': '大型', '02': '中型', '03': '小型', '04': '微型' };
      if (!Enterprise) {
        // 创建用人单位
        const enterpriseData = {
          cname: item.ENTERPRISE_NAME || '',
          code: EnterpriseCode,
          regType: item.ECONOMIC_TYPE_CODE || '',
          industryCategory: industryCategory ? [ industryCategory ] : [],
          companyScale: companyScaleObj.item.BUSINESS_SCALE_CODE || '',
          districtRegAdd,
          regAdd: item.ADDRESS_DETAIL || '',
          workAddress: [{ districts: districtRegAdd, point, address: item.ADDRESS_DETAIL || '' }],
          isactive: '1',
          leadIn: '3',
          physicalExaminationOrgID: [{
            _id: physicalExamOrg._id,
            ServiceContractName: physicalExamOrg.contract || '',
            ServicePhoneNum: physicalExamOrg.phoneNum || '',
          }],
          contract: item.ENTERPRISE_CONTACT || '',
          phoneNum: item.CONTACT_TELPHONE || '',
        };
        Enterprise = await ctx.model.Adminorg.create(enterpriseData);
      } else { // 更新用人单位
        const updateData = {
          cname: item.ENTERPRISE_NAME || '',
          regType: item.ECONOMIC_TYPE_CODE || '',
          industryCategory: industryCategory ? [ industryCategory ] : [],
          companyScale: item.BUSINESS_SCALE_CODE || '',
          districtRegAdd,
          isactive: '1',
          leadIn: '3',
          $push: {},
          contract: item.ENTERPRISE_CONTACT || '',
          phoneNum: item.CONTACT_TELPHONE || '',
        };
        if (Enterprise.physicalExaminationOrgID.map(ele => ele._id).indexOf(physicalExamOrg._id) === -1) {
          updateData.$push.physicalExaminationOrgID = {
            _id: physicalExamOrg._id,
            EnterpriseContractName: item.ENTERPRISE_CONTACT || '',
            EnterprisePhoneNum: item.CONTACT_TELPHONE || '',
            ServiceContractName: physicalExamOrg.contract || '',
            ServicePhoneNum: physicalExamOrg.phoneNum || '',
          };
        }
        const addressTemp = Enterprise.workAddress.map(ele => ele.districts.join('') + (ele.address || ''));
        if (!addressTemp.includes(districtRegAdd.join('') + (item.ADDRESS_DETAIL || ''))) {
          updateData.$push.workAddress = { districts: districtRegAdd, point, address: item.ADDRESS_DETAIL || '' };
        }
        await ctx.model.Adminorg.updateOne({ _id: Enterprise._id }, updateData);
        ctx.auditLog('体检机构对接 更新用人单位信息成功', updateData, 'info');
      }
      // 处理人员信息
      await this.handlePersonInfo2({
        EnterpriseID: Enterprise._id,
        name: item.ENTERPRISE_CONTACT,
        phoneNum: item.CONTACT_TELPHONE,
      });
      return {
        status: 200,
        message: `用人单位：${item.ENTERPRISE_NAME}的信息${Enterprise ? '更新' : '添加'}成功`,
      };
    } catch (e) {
      this.ctx.auditLog('体检机构对接 上传用人单位信息失败', e, 'error');
      return {
        status: 500,
        message: `ID为${item.id || item.ID || item.CREDIT_CODE}的用人单位信息上传失败：${e.message}`,
      };
    }
  }
  // 创建/更新人员信息employee、user、adminUser
  async handlePersonInfo2(personInfo = { EnterpriseID: '', name: '', phoneNum: '' }) {
    const { ctx } = this;
    const { EnterpriseID, name, phoneNum } = personInfo;
    if (!phoneNum) return;
    const flag = /^1[3-9][0-9]{9}$/.test(phoneNum); // 手机号校验
    if (!flag) throw new Error('手机号格式错误');
    let employeeInfo = await ctx.model.Employee.findOne({ EnterpriseID, phoneNum });
    if (!employeeInfo) {
      employeeInfo = await ctx.model.Employee.create(personInfo);
    }
    let user = await ctx.model.User.findOne({ phoneNum });
    if (!user) {
      user = await ctx.model.User.create({ name, phoneNum, userName: phoneNum, employeeId: employeeInfo._id, companyId: [ EnterpriseID ], companyStatus: 2 });
    }
    if (user && user._id && !employeeInfo.userId) {
      await ctx.model.Employee.updateOne({ _id: employeeInfo._id }, { userId: user._id });
    }
    if (employeeInfo.departs.length === 0) {
      let dingtrees = await ctx.model.Dingtree.findOne({ EnterpriseID });
      if (!dingtrees) {
        dingtrees = await ctx.model.Dingtree.create({ EnterpriseID, name: '企业部门', type: '1' });
      }
      await ctx.model.Employee.updateOne({ _id: employeeInfo._id }, { departs: [[ dingtrees._id ]] });
      await ctx.model.Dingtree.updateOne({ _id: dingtrees._id }, { $addToSet: { staff: employeeInfo._id } });
    }
    let adminUser = await ctx.model.AdminUser.findOne({ phoneNum, state: '1' });
    if (!adminUser) {
      adminUser = await ctx.model.AdminUser.create({ phoneNum, name, userName: phoneNum, state: 1, newAddEnterpriseID: EnterpriseID, userId: user._id, employees: [ employeeInfo._id ] });
    } else {
      await ctx.model.AdminUser.updateOne({ _id: adminUser._id }, { newAddEnterpriseID: EnterpriseID, name, $addToSet: { employees: employeeInfo._id } });
    }
    const adminorg = await ctx.model.Adminorg.findOne({ _id: EnterpriseID });
    if (adminorg.adminUserId) {
      await ctx.model.Adminorg.updateOne({ _id: EnterpriseID }, { $addToSet: { adminArray: adminUser._id } });
    } else {
      await ctx.model.Adminorg.updateOne({ _id: EnterpriseID }, { adminUserId: adminUser._id });
    }
  }


}

module.exports = HealthCheckAppointmentService;
