const _ = require('lodash');
const { siteFunc } = require('@utils');

// 关键操作记录日志
const _addActionUserInfo = (ctx, params = {}) => {

  let infoStr = '';

  if (!_.isEmpty(ctx.session.user)) {
    infoStr += 'actionUser: ' + JSON.stringify(ctx.session.user) + ',';
  }

  if (!_.isEmpty(params)) {
    infoStr += 'actionParams: ' + JSON.stringify(params) + ',';
  }

  return infoStr;
};

/**
 * 通用多表查询列表
 * @function  unionQuery
 * @param  {[Object]} Model     [和之前_list中的Model是一样的。]
 * @param  {[Object]} payload     [和之前_list中的payload是一样的。]
 * @param  {[Object]} {}       该方法入参的3个参数，
  *                            collections 表示需要联查的表名，
  *                            sort排序，
  *                            files字段名,
  *                            query是指定的查询参数，
  *                            searchKeys是查询字段名，可以指定多个字段；
  *                            statisticsFiles指统计数据]
 * @param  {[Array]} collections : [{
 *                                 name: 'adminusers',       集合名称
  *                                selfKey: 'adminUserId',   本集合关联字段（一般为关联ID）
  *                                foreignKey: '_id',        对应联表的字段，一般为_id
  *                                asKey: 'adminUserId',     显示在查询结果中的字段名
  *                              }]
  *                              表示需要联查的集合
  * @param  {[Array]} unwindArray : [ 'feild1', 'feild2', 'feild3' ] 表示需要拆分的字段数组，默认为[]
 * @param  {[Array]} searchKeys : [ 'feild1', 'feild2', 'feild3' ]
 * @param {[Array]} statisticsFiles  :  [{
  *                                        field: {                        这个field是固定死的，不用更改
  *                                          'fieldName': { $eq: '0' },    这里的fieldName是指需要统计的字段名称，比如userName，isactive之类；后面跟的是mongodb的运算符。可自行百度
  *                                        },
  *                                        keyname: 'fieldCount',          keyname固定死的，不用更改；fieldCount是自定义的返回统计字段，建议跟业务相关
  *                                        accumulator: { $sum: 1 },       accumulator固定死的，不用更改；意思是统计的方法，{ $sum: 1 } 表示对需要统计的字段记录条数进行累加。
  *}]
  *  @return {[Object]}     { docs, pageInfo, statisticsData }             statisticsData{ fieldCount0:2 ,fieldCount1:5 , fieldCount2:8 }
  *                                                                        这里的fieldCount0，fieldCount1，fieldCount2对应statisticsFiles中的keyname
 */

exports._unionQuery = async (Model, payload, {
  collections = [],
  unwindArray = [],
  sort = {
    date: -1,
  },
  files = null,
  query = {},
  searchKeys = [],
  statisticsFiles = [],
} = {}) => {
  let {
    current,
    pageSize,
    searchkey,
    isPaging,
    skip,
  } = payload;
  let docs = [];
  let count = 0;
  query = query || {};
  current = (current || 1);
  pageSize = Number(pageSize) || 10;
  isPaging = isPaging !== '0';
  const skipNum = skip ? skip : ((Number(current)) - 1) * Number(pageSize);
  sort = !_.isEmpty(sort) ? sort : {
    createTime: -1,
  };

  const aggpip = [];
  if (!_.isEmpty(collections) && collections.length > 0) {
    collections.forEach(collection => {
      const lookup = { $lookup: { from: collection.name, localField: collection.selfKey, foreignField: collection.foreignKey, as: collection.asKey } };
      aggpip.push(lookup);
    });
  }

  if (!_.isEmpty(unwindArray) && unwindArray.length > 0) {
    unwindArray.forEach(val => {
      const unwind = { $unwind: '$' + val };
      aggpip.push(unwind);
    });
  }

  if (!_.isEmpty(searchkey) || !_.isEmpty(query)) {
    const match = { $match: {} };
    if (!_.isEmpty(searchkey)) {
      if (typeof searchKeys === 'object' && searchKeys.length > 0) {
        const searchStr = [];
        searchKeys.forEach(keyname => {
          searchStr.push({
            [keyname]: {
              $regex: searchkey,
            },
          });
        });
        match.$match.$or = searchStr;
      }
    }
    if (!_.isEmpty(query)) {
      const and = [];
      Object.keys(query).forEach(keyname => {
        let item = {};
        if (_.isArray(query[keyname])) {
          item = { [keyname]: { $all: query[keyname] } };
        } else {
          item = { [keyname]: query[keyname] };
        }
        and.push(item);
      });
      match.$match.$and = and;
    }
    aggpip.push(match);
  }

  if (files) {
    const project = { $project: files };
    aggpip.push(project);
  }
  const countPip = [].concat(aggpip);
  if (isPaging) {
    aggpip.push({ $sort: sort }, { $skip: skipNum }, { $limit: pageSize });
  } else {
    if (payload.pageSize > 0) {
      aggpip.push({ $sort: sort }, { $skip: skipNum }, { $limit: pageSize });
    } else {
      aggpip.push({ $sort: sort }, { $skip: skipNum });
    }
  }
  docs = await Model.aggregate(aggpip);
  const statisticsPip = [].concat(countPip);
  countPip.push({ $group: { _id: null, count: { $sum: 1 } } });
  count = await Model.aggregate(countPip);
  count && count.length > 0 ? count = count[0].count : count = 0;
  let backData = { docs };
  if (isPaging) {
    const pageInfoParams = {
      totalItems: count || '',
      pageSize: Number(pageSize) || 0,
      current: Number(current) || 0,
      searchkey: searchkey || '',
      totalPage: Math.ceil(count / Number(pageSize)),
    };

    for (const querykey in query) {
      if (query.hasOwnProperty(querykey)) {
        const queryValue = query[querykey];
        _.assign(pageInfoParams, {
          [querykey]: queryValue || '',
        });
      }
    }
    backData = { docs, pageInfo: pageInfoParams };
  }

  return new Promise(function(resolve) {
    if (statisticsFiles && statisticsFiles.length > 0) {
      const statisticsData = {};
      let counter = 0;
      siteFunc.foreachAsync(statisticsFiles, async function(sta) {
        let itemStatistic = {};
        const match = { $match: sta.field };
        const group = { $group: { _id: null } };
        group.$group[sta.keyname] = sta.accumulator;
        const tempParam = [].concat(statisticsPip);
        tempParam.push(match);
        tempParam.push(group);
        itemStatistic = await Model.aggregate(tempParam);
        if (itemStatistic && itemStatistic.length > 0) {
          itemStatistic = itemStatistic[0];
          delete itemStatistic._id;
        } else {
          itemStatistic[sta.keyname] = 0;
        }
        statisticsData[sta.keyname] = itemStatistic[sta.keyname];
        counter++;
        if (counter === statisticsFiles.length) {
          backData.statisticsData = statisticsData;
          resolve(backData);
        }
      });
    } else {
      resolve(backData);
    }
  });

};

/**
 * 通用列表
 * @function list
 * @param  {[type]} req     [description]
 * @param  {[type]} res     [description]
 * @param  {[type]} Model   [description]
 * @param  {[type]} sort    排序
 * @return {[type]}         [description]
 */


exports._list = async (Model, payload, {
  sort = {
    date: -1,
  },
  files = null,
  query = {},
  searchKeys = [],
  populate = [],
  contentSort = null,
} = {}) => {
  let {
    current,
    pageSize,
    searchkey,
    isPaging,
    skip,
  } = payload;
  let docs = [];
  let count = 0;
  query = query || {};
  current = (current || 1);
  pageSize = Number(pageSize) || 10;
  isPaging = isPaging !== '0';

  current = current > 0 ? current : 1;
  const skipNum = skip ? skip : ((Number(current)) - 1) * Number(pageSize);
  sort = !_.isEmpty(sort) ? sort : {
    date: -1,
  };
  if (searchkey) {
    if (searchKeys) {
      if (typeof searchKeys === 'object' && searchKeys.length > 0) {
        const searchStr = [];
        for (let i = 0; i < searchKeys.length; i++) {
          const keyItem = searchKeys[i];
          searchStr.push({
            [keyItem]: {
              $regex: searchkey,
            },
          });
        }
        query.$or = searchStr;
      } else {
        query[searchKeys] = {
          $regex: new RegExp(searchkey, 'i'),
        };
      }
    }
  }
  // console.log('--query--', query);

  // console.log('\r\n位于app/service/general.js下的query参数：'); console.log(JSON.stringify(query));
  // console.log('\r\n位于app/service/general.js下的payload参数：'); console.log(JSON.stringify(payload));
  // console.log('\r\n位于app/service/general.js下的files参数：'); console.log(JSON.stringify(files)); console.log('\r\n');
  if (isPaging) {
    docs = await Model.find(query, files).skip(skipNum).limit(Number(pageSize))
      .sort(sort)
      .populate(populate)
      .exec();
  } else {
    if (payload.pageSize > 0) {
      docs = await Model.find(query, files).skip(skipNum).limit(pageSize)
        .sort(sort)
        .populate(populate)
        .exec();
    } else {
      docs = await Model.find(query, files).skip(skipNum).sort(sort)
        .populate(populate)
        .exec();
    }
  }
  count = await Model.countDocuments(query).exec();

  if (isPaging) {
    const pageInfoParams = {
      totalItems: count,
      pageSize: Number(pageSize),
      current: Number(current),
      searchkey: searchkey || '',
      totalPage: Math.ceil(count / Number(pageSize)),

    };

    if (contentSort) {
      pageInfoParams.sort = contentSort;
    }

    const searchKeyArray = [ '$or' ];
    for (const querykey in query) {
      if (query.hasOwnProperty(querykey)) {
        const queryValue = query[querykey];
        if (!searchKeyArray.includes('$or')) {
          _.assign(pageInfoParams, {
            [querykey]: queryValue || '',
          });
        }
      }
    }
    return {

      docs,
      pageInfo: pageInfoParams,

    };
  }
  return docs;


};


exports._count = async (Model, query = {}) => {
  return await Model.countDocuments(query);
};

exports._create = async (Model, payload, options = {}) => {
  if (!_.isEmpty(options)) {
    return (await Model.create([ payload ], options))[0];
  }
  return await Model.create(payload);
};

exports._insertMany = async (Model, array = [], options = {}) => {
  return await Model.insertMany(array, options);
};

/**
 * 通用单个
 * @function item
 * @param  {[type]} res [description]
 * @param  {[type]} Model [description]
 * @return {[type]}         [description]
 */

exports._item = async (ctx, Model, {
  files = null,
  query = {},
  populate = [],
} = {}, options = {}) => {
  // console.log(query._id)
  // if (query._id && !shortid.isValid(query._id)) {
  //   throw new Error(ctx.__('validate_error_params'));
  // }
  if (Object.keys(options).length > 0) {
    if (ctx.app.config.dbEncryption && options.returnOptions) {
      const keys = Object.keys(options.returnOptions)
        .map(key => `${key}ForStore`)
        .join(' ') + ' encryptionAlgorithm';
      files = files ? files + ' ' + keys : keys;
    }
    return await Model.findOne(query, files).populate(populate).setOptions(options)
      .exec();
  }
  if (options && options.session) {
    return await Model.findOne(query, files).populate(populate).session(options.session);
  }
  return await Model.findOne(query, files).populate(populate).exec();

};

/**
 * 通用多个
 * @function items
 * @param  {[type]} res [description]
 * @param  {[type]} Model [description]
 * @return {[type]}         [description]
 */

exports._items = async (ctx, Model, {
  files = null,
  query = {},
  populate = [],
} = {}) => {
  // console.log(query._id)
  // if (query._id && !shortid.isValid(query._id)) {
  //   throw new Error(ctx.__('validate_error_params'));
  // }

  // console.log('=========query')
  // console.log(query)
  return await Model.find(query, files).populate(populate).exec();
};

/**
 * 通用删除
 * @function deletes
 * @param  {[type]}   Model [description]
 * @param  {[type]}   ids [description]
 */

exports._removes = async (ctx, Model, ids, key) => {
  if (!global.checkCurrentId(ids)) {
    throw new Error(ctx.__('validate_error_params'));
  } else {
    ids = ids.split(',');
  }
  ctx.logger.warn(_addActionUserInfo(ctx, {
    ids,
    key,
  }));

  return await Model.deleteMany({
    [key]: {
      $in: ids,
    },
  });

};

exports._removes2 = async (ctx, Model, ids, key) => {
  if (!global.checkCurrentId(ids)) {
    throw new Error(ctx.__('validate_error_params'));
  }
  ctx.logger.warn(_addActionUserInfo(ctx, {
    ids,
    key,
  }));

  return await Model.deleteMany({
    [key]: {
      $in: ids,
    },
  });

};

/**
 * 通用删除
 * @function deletes
 * @param  {[type]}   Model [description]
 */

exports._removeAll = async Model => {

  return await Model.deleteMany({});

};

/**
 * 通用删除
 * @function deletes
 * @param  {[type]}   Model [description]
 * @param  {[type]}   ids [description]
 */

exports._safeDelete = async (ctx, Model, ids, updateObj = {}) => {

  if (!global.checkCurrentId(ids)) {
    throw new Error(ctx.__('validate_error_params'));
  } else {
    ids = ids.split(',');
  }

  let queryObj = {
    state: '0',
  };

  if (!_.isEmpty(updateObj)) {
    queryObj = updateObj;
  }

  return await Model.updateMany({
    _id: {
      $in: ids,
    },
  }, {
    $set: queryObj,
  });

};

/**
 * 通用编辑
 * @function update
 * @param  {[type]} Model [description]
 * @param  {[type]} _id     [description]
 * @param  {[type]} data    [description]
 */

exports._update = async (ctx, Model, _id, data, query = {}, options = {}) => {
  console.log(data);
  if (_id) {
    query = _.assign({}, query, {
      _id,
    });
  } else {
    if (_.isEmpty(query)) {
      throw new Error(ctx.__('validate_error_params'));
    }
  }
  console.log(query);
  if (!options || !options.session) {
    const user = await this._item(ctx, Model, {
      query,
    });

    if (_.isEmpty(user)) {
      throw new Error(ctx.__('validate_error_params'));
    }
  }
  console.log('\r\n\r\n6.app-service   update\r\n\r\n');
  // console.log(Model);
  // console.log(query);
  // console.log(data);
  return await Model.findOneAndUpdate(query, {
    $set: data,
  }, options);

};
/**
 * 通用编辑
 * @function update
 * @param  {[type]} Model [description]
 * @param  {[type]} ids     [description]
 * @param  {[type]} data    [description]
 */

exports._updateMany = async (ctx, Model, ids = '', data, query = {}) => {

  if (_.isEmpty(ids) && _.isEmpty(query)) {
    throw new Error(ctx.__('validate_error_params'));
  }

  if (!_.isEmpty(ids)) {
    if (!global.checkCurrentId(ids)) {
      throw new Error(ctx.__('validate_error_params'));
    } else {
      // 2020.4.13 修改 下面注释行代码有机率报split为空的错误
      // ids = ids.split(',');
      query = _.assign({}, query, {
        _id: {
          $in: ids.split(','),
        },
      });
    }
  }
  return await Model.updateMany(query, {
    $set: data,
  });

};

/**
 * 通用数组字段添加
 * @function update
 * @param  {[type]} Model [description]
 * @param  {[type]} id     [description]
 * @param  {[type]} data    [description]
 */

exports._addToSet = async (ctx, Model, id, data, query = {}) => {

  if (_.isEmpty(id) && _.isEmpty(query)) {
    throw new Error(ctx.__('validate_error_params'));
  }

  if (!_.isEmpty(id)) {
    query = _.assign({}, query, {
      _id: id,
    });
  }

  return await Model.updateMany(query, {
    $addToSet: data,
  });

};


/**
 * 通用数组字段删除
 * @function update
 * @param  {[type]} Model [description]
 * @param  {[type]} id     [description]
 * @param  {[type]} data    [description]
 */

exports._pull = async (ctx, Model, id, data, query = {}) => {

  if (_.isEmpty(id) && _.isEmpty(query)) {
    throw new Error(ctx.__('validate_error_params'));
  }

  if (!_.isEmpty(id)) {
    query = _.assign({}, query, {
      _id: id,
    });
  }

  return await Model.updateMany(query, {
    $pull: data,
  });

};

/**
 * 通用属性加值
 * @function update
 * @param  {[type]} Model [description]
 * @param  {[type]} id     [description]
 * @param  {[type]} data    [description]
 */

exports._inc = async (ctx, Model, id, data, {
  query = {},
} = {}) => {

  if (_.isEmpty(id) && _.isEmpty(query)) {
    throw new Error(ctx.__('validate_error_params'));
  }

  if (!_.isEmpty(id)) {
    query = _.assign({}, query);
  }

  return await Model.updateMany(query, {
    $inc: data,
  });

};
