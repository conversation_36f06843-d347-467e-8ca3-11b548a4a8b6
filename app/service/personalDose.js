
const Service = require('egg').Service;
const path = require('path');
const moment = require('moment');
// const fs = require('fs');
class personalDose extends Service {
  getVal(projectInfo, filePathField) {
    const filePathFileds = filePathField.split('.');
    let filePath = projectInfo[filePathFileds[0]];
    let index = 1;
    while (index < filePathFileds.length) {
      filePath = filePath[filePathFileds[index]];
      index++;
    }
    return filePath;
  }
  // 给pdf添加签名
  async signPdf({ originatorEmployeeId, statusField, processInstanceField, processInstanceId, signIndex, currentEmployeeId, isComplete = false } = {}) {
    const { config } = this.app;
    // 钉钉审核和文件信息
    const approvalSignFileIno = {
      reportApproved: {
        filePthStart: 'static',
        filePathField: [ 'personReportFileName.url' ], // `${app.config.static.prefix}${app.config.report_http_path}/${jcqlcID}/${projectYear}/${projectSN}/${fileName}`
        outPathFile: 'officialReportUploadFileName.url',
        noStampFiled: 'officialReportUploadFileName.noStampUrl',
        outPathFileName2: 'officialReportUploadFileName.name',
        nodeField: 'officialReportUpload',
        outPathFileName: '正式稿',
        // mergeFiles: [ 'reportUploadFile.url', 'wordFileName.signedUrl' ],
        userSignConfig: [
          { text: '检测人：',
            offsetX: 60, // 偏移量 图片的位置放置在text所在位置+偏移量
            offsetY: 10,
            isSignFromApproval: true, // 签名是否来自于审批
            approvalNodeIndex: 0 },
          { text: '审核人：',
            offsetX: 60, // 偏移量 图片的位置放置在text所在位置+偏移量
            offsetY: 10,
            isSignFromApproval: true, // 签名是否来自于审批
            approvalNodeIndex: 1 },
          { text: '批准人：',
            offsetX: 60, // 偏移量 图片的位置放置在text所在位置+偏移量
            offsetY: 10,
            isSignFromApproval: true, // 签名是否来自于审批
            approvalNodeIndex: 2 }],
        signConfig: [{ // 签章位置
          pages: [ 0 ],
          y: 80,
          x: 'center', // x轴居中
        }, {
          text: '检测机构(盖章)',
          offsetY: 0, // 偏移量
        }],
        textConfig: [{
          pages: [ 0 ],
          y: 100,
          x: 'center', // x轴居中
          offsetY: 0, // 偏移量
          width: 50,
          value: moment(new Date()).format('YYYY-MM'),
        }, {
          text: '检测机构(盖章)',
          offsetY: 5,
          value: moment(new Date()).format('YYYY-MM-DD'),
        }],
        signImagePath: process.cwd() + '/app/public/images/放射用章.png',
      },
    };
    const configInfo = approvalSignFileIno[statusField];
    if (!configInfo) return;
    let signFileInfo = {};
    // 配置文件
    // 获取机构ID
    const projectInfo = await this.ctx.model.PersonalDoseProject.findOne({ [processInstanceField]: processInstanceId }, { EnterpriseName: 1, createdAt: 1, serviceOrgId: 1, projectSN: 1, personReportFileName: 1 }).lean();
    const { serviceOrgId, createdAt, projectSN, EnterpriseName } = projectInfo;
    const projectYear = (new Date(createdAt).getFullYear()) + '';
    const pathDir = path.resolve(path.join(config.report_path, serviceOrgId, projectYear, projectSN));
    for (let i = 0; i < configInfo.filePathField.length; i++) {
      let filePath = this.getVal(projectInfo, configInfo.filePathField[i]);
      const staticPath = filePath;
      // 未签名的文件数据库存储路径
      if (configInfo.filePthStart === 'static') {
        filePath = path.basename(filePath);
      }
      filePath = path.join(pathDir, filePath);

      let signConfig = configInfo.signConfig;
      const addSeamStamp = configInfo.addSeamStamp;
      let signImagePath = configInfo.signImagePath;
      if (filePath.indexOf('?') !== -1) {
        const temp = filePath.split('?');
        filePath = temp[0];
      }
      if (!isComplete) { // 添加每个审批人的签字
        if (path.extname(filePath) === '.docx') {
          if (signIndex === 0) {
          // 转换成pdf
            await this.ctx.helper.docx2pdf(staticPath, filePath.replace('docx', 'pdf'));
          }
        }
        filePath = filePath.replace('docx', 'pdf');
        signConfig = configInfo.userSignConfig[signIndex];
        // 获取该审批节点签名
        let employeeInfo = null;
        if (signConfig.signField === 'origin') {
        // 发起人
        // 获取发起人的签名
          employeeInfo = await this.ctx.model.ServiceEmployee.findOne({ _id: originatorEmployeeId }, { signPath: 1 });
        } else {
          employeeInfo = await this.ctx.model.ServiceEmployee.findOne({ _id: currentEmployeeId }, { signPath: 1 });
        }

        // 获取签名路径
        signImagePath = path.resolve(path.join(config.sign_path, serviceOrgId, employeeInfo.signPath)); // 文件目录;
        signFileInfo = await this.ctx.helper.drawImageToPdfAndSign(filePath, signImagePath, false, false, signConfig);
      } else { // 审批完成进行盖章和电子签名
        filePath = filePath.replace('docx', 'pdf');
        signFileInfo = await this.ctx.helper.drawImageToPdfAndSign(filePath, signImagePath, true, addSeamStamp, signConfig, configInfo.textConfig);
      }
    }

    const outputName = `${projectSN}_${EnterpriseName}_${configInfo.outPathFileName}.pdf`;
    if (isComplete && (configInfo.mergeFiles || configInfo.addPage)) {
      if (configInfo.mergeFiles) {
        configInfo.mergeFiles = configInfo.mergeFiles.map(item => {
          item = this.getVal(projectInfo, item);
          item = path.basename(item);
          return path.join(pathDir, item).replace('.docx', '.pdf');
        });
      }
      const outputFilePath = path.join(pathDir, outputName);
      signFileInfo = await this.ctx.helper.mergeFileAndAddPage('', outputFilePath, configInfo.mergeFiles, configInfo.addPage);
    }
    if (signFileInfo) {
      // 创建盖章记录
      await this.ctx.model.StampRecord.create({
        serviceOrgId,
        modelId: projectInfo._id,
        modelName: 'PersonalDoseProject',
        stampFile: configInfo.outPathFileName,
        stampFilePath: '/' + signFileInfo.filePath,
        optServiceEmployeeId: currentEmployeeId,
      });
      // 更新项目信息
      const setFields = {
        [configInfo.outPathFile]: '/' + signFileInfo.filePath,
      };

      if (configInfo.noStampFiled && signFileInfo.noStampFilePath) {
        setFields[configInfo.noStampFiled] = '/' + signFileInfo.noStampFilePath;
      }
      if (configInfo.outPathFileName2) {
        setFields[configInfo.outPathFileName2] = outputName;
      }

      if (configInfo.nodeField) {
        setFields[`progress.${configInfo.nodeField}`] = {
          status: 2,
          completedTime: new Date(),
        };
      }
      await this.ctx.model.PersonalDoseProject.updateOne({ _id: projectInfo._id }, { $set: setFields });
    }

  }
  // 更新修改审批状态
  async updateModifystatus(condition, applyInfo, statusField) {
    const { ctx } = this;
    const info = applyInfo.info;
    const field = 'progress.' + statusField;
    if (applyInfo.qlcProjectStatus > 1) {
      // 更新状态
      await ctx.model.PersonalDoseProject.updateOne({ [condition]: info.processInstanceId }, { $set: {
        [field]: {
          status: applyInfo.qlcProjectStatus,
          completedTime: new Date(),
        },
      } });
    }
  }
}

module.exports = personalDose;
