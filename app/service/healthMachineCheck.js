const Service = require('egg').Service;
const { Readable } = require('stream');
const moment = require('moment');
const path = require('path');
/** 北元健康一体机对接Service
 * @class HealthMachineService
 * @augments {Service}
 * @description 接收健康一体机检测结果
 */
class HealthMachineService extends Service {
  async healCheckResult(data) {
    const { ctx } = this;
    try {
      const { sfz, deviceID, examNo } = data;
      if (!sfz || !sfz.idnumber) {
        throw new Error('缺少身份证信息');
      }
      if (!deviceID || !examNo) {
        throw new Error('缺少设备信息');
      }
      delete data.deviceID;
      delete data.examNo;
      const newData = {};
      for (const key in data) {
        if (data[key]) {
          // 判断this.【key】Deal处理方法是否存在
          if (this[key + 'Deal']) {
            // 进行key处理的方法
            newData[key] = await this[key + 'Deal'](data[key]);
          } else {
            console.log('不存在', key);
          }
        }
      }
      // 确定employee内是否有这个人 因为北元employee对应身份证暂时具有唯一性 findOne就可以了,查不到就不管他了
      const employee = await ctx.model.Employee.findOne(
        { IDNum: sfz.idnumber },
        { _id: 1, EnterpriseID: 1 }
      );
      const employeeId = employee ? employee._id : null;
      const EnterpriseID = employee ? employee.EnterpriseID : null;
      console.log('数据处理', newData);
      const fileName = sfz.idnumber + '_' + examNo + '_' + deviceID + '.pdf';
      const checkDate = moment(examNo.toString(), 'YYYYMMDDHHmmss');
      await ctx.model.HealthMachineCheck.create({
        employeeId,
        EnterpriseID,
        sfz,
        deviceID,
        examNo,
        checkDate,
        ...newData,
        fileName,
      });
      return 'ok';
    } catch (error) {
      ctx.auditLog(
        '北元健康一体机healCheckResult Service出错',
        `${error.stack} 。`,
        'error'
      );
      throw error;
    }
  }
  // 上传文件
  async healCheckFile(ctx) {
    const { app } = this;
    try {
      const parts = ctx.multipart({ autoFields: true });
      let stream;
      // 正则判断 身份证号_体检编号_设备号.pdf
      const filenameRegex = /^(\d+)_(\d+)_(\w+)\.pdf$/;
      while ((stream = await parts()) != null) {
        if (!stream.filename) {
          break;
        }
        const filename = stream.filename;
        const match = filename.match(filenameRegex);
        if (!match) {
          throw new Error('文件名格式错误');
        }
        // 定义目标路径和 bucket 配置
        const configFilePath = path.join(
          app.config.upload_path,
          'healthMachineCheck'
        );
        const target = path.resolve(configFilePath, filename);
        // 调用封装的 pipe 方法
        await ctx.helper.pipe({
          readableStream: stream,
          target,
        });
      }
      return true;
    } catch (error) {
      this.ctx.auditLog(
        '北元健康一体机healCheckFile上传文件信息出错',
        `${error.stack} 。`,
        'error'
      );
      return false;
    }
  }
  async sfzDeal(data) {
    const ctx = this.ctx;
    try {
      if (data.data) {
        const saveRes = await this.base64ToSave(data.data, 'bmp');
        data.fileName = saveRes;
      }
      return data;
    } catch (error) {
      ctx.auditLog(
        '北元健康一体机healCheckResult处理身份证信息出错',
        `${error.stack} 。`,
        'error'
      );
    }
  }
  // 处理身高
  async hwDeal(data) {
    const ctx = this.ctx;
    try {
      const hw = {};
      hw.height = await this.splitValue(data.height);
      hw.weight = await this.splitValue(data.weight);
      hw.bmi = await this.splitValue(data.bmi);
      return hw;
    } catch (error) {
      ctx.auditLog(
        '北元健康一体机healCheckResult处理身高数据出错',
        `${error.stack} 。`,
        'error'
      );
    }
  }
  // 处理fat 脂肪信息
  async fatDeal(data) {
    const ctx = this.ctx;
    try {
      const fat = {};
      for (const key in data) {
        fat[key] = await this.splitValue(data[key]);
      }
      return fat;
    } catch (error) {
      ctx.auditLog(
        '北元健康一体机healCheckResult处理脂肪信息出错',
        `${error.stack} 。`,
        'error'
      );
    }
  }
  // 处理spo2 脂肪信息
  async spo2Deal(data) {
    const ctx = this.ctx;
    try {
      const spo2 = {};
      for (const key in data) {
        spo2[key] = await this.splitValue(data[key]);
      }
      return spo2;
    } catch (error) {
      ctx.auditLog(
        '北元健康一体机healCheckResult处理血氧信息出错',
        `${error.stack} 。`,
        'error'
      );
    }
  }
  // 处理blood 血压信息
  async bloodDeal(data) {
    const ctx = this.ctx;
    try {
      const blood = {};
      for (const key in data) {
        if ([ 'result', 'data', 'len' ].includes(key)) {
          console.log('blood key no spilt', key);
          blood[key] = data[key];
          if (data.data) {
            blood.fileName = await this.base64ToSave(data.data, 'bmp');
          }
        } else {
          blood[key] = await this.splitValue(data[key]);
        }
      }
      return blood;
    } catch (error) {
      ctx.auditLog(
        '北元健康一体机healCheckResult处理脂肪信息出错',
        `${error.stack} 。`,
        'error'
      );
    }
  }
  // 处理tiwen 体温信息
  async tiwenDeal(data) {
    const ctx = this.ctx;
    try {
      const tiwenDeal = await this.splitValue(data);
      return tiwenDeal;
    } catch (error) {
      ctx.auditLog(
        '北元健康一体机healCheckResult处理体温信息出错',
        `${error.stack} 。`,
        'error'
      );
    }
  }
  // 处理ecg 心电结果
  async ecgDeal(data) {
    const ctx = this.ctx;
    try {
      const ecg = {};
      ecg.result = data.result;
      if (data.data) {
        ecg.data = data.data; // 图片存储
        ecg.fileName = await this.base64ToSave(data.data, 'bmp');
      }
      ecg.len = data.len;
    } catch (error) {
      ctx.auditLog(
        '北元健康一体机healCheckResult处理体温信息出错',
        `${error.stack} 。`,
        'error'
      );
    }
  }
  // 处理ecg12 心电结果 带图片
  async ecg12Deal(data) {
    const ctx = this.ctx;
    try {
      const ecg12 = {};
      for (const key in data) {
        if ([ 'data', 'len', 'ecg_result' ].includes(key)) {
          ecg12[key] = data[key];
          if (key === 'data' && data.data) {
            ecg12.fileName = await this.base64ToSave(data[key], 'jpg');
          }
        } else {
          ecg12[key] = await this.splitValue(data[key]);
        }
      }
      return ecg12;
    } catch (error) {
      ctx.auditLog(
        '北元健康一体机healCheckResult处理心电结果信息出错',
        `${error.stack} 。`,
        'error'
      );
    }
  }

  // 处理xt 心电结果
  async xtDeal(data) {
    const ctx = this.ctx;
    try {
      const xt = {};
      for (const key in data) {
        xt[key] = await this.splitValue(data[key]);
      }
      return xt;
    } catch (error) {
      ctx.auditLog(
        '北元健康一体机healCheckResult处理血糖结果信息出错',
        `${error.stack} 。`,
        'error'
      );
    }
  }
  // 处理ns 心电结果
  async nsDeal(data) {
    const ctx = this.ctx;
    try {
      const nsDeal = await this.splitValue(data);
      return nsDeal;
    } catch (error) {
      ctx.auditLog(
        '北元健康一体机healCheckResult处理尿酸结果信息出错',
        `${error.stack} 。`,
        'error'
      );
    }
  }
  async dgcDeal(data) {
    const ctx = this.ctx;
    try {
      const dgcDeal = await this.splitValue(data);
      return dgcDeal;
    } catch (error) {
      ctx.auditLog(
        '北元健康一体机healCheckResult处理尿酸结果信息出错',
        `${error.stack} 。`,
        'error'
      );
    }
  }
  // 处理xhdb 血红蛋白信息
  async xhdbDeal(data) {
    const ctx = this.ctx;
    try {
      const xhdb = {};
      for (const key in data) {
        xhdb[key] = await this.splitValue(data[key]);
      }
      return xhdb;
    } catch (error) {
      ctx.auditLog(
        '北元健康一体机healCheckResult处理血糖结果信息出错',
        `${error.stack} 。`,
        'error'
      );
    }
  }
  // 处理xzsx 血脂四项
  async xzsxDeal(data) {
    const ctx = this.ctx;
    try {
      const xzsx = {};
      for (const key in data) {
        xzsx[key] = await this.splitValue(data[key]);
      }
      return xzsx;
    } catch (error) {
      ctx.auditLog(
        '北元健康一体机healCheckResult处理脂肪信息出错',
        `${error.stack} 。`,
        'error'
      );
    }
  }
  // 处理 nyfx 尿液分析信息
  async nyfxDeal(data) {
    const ctx = this.ctx;
    try {
      const nyfx = {};
      for (const key in data) {
        nyfx[key] = await this.splitValue(data[key]);
      }
      return nyfx;
    } catch (error) {
      ctx.auditLog(
        '北元健康一体机healCheckResult处理尿液分析信息出错',
        `${error.stack} 。`,
        'error'
      );
    }
  }
  // 处理zybs
  async zybsDeal(data) {
    const ctx = this.ctx;
    try {
      const zybs = {};
      const arr = data.split('#');
      const keys = [
        'tcmConstitutionType',
        'yangDeficiencyQualityScore',
        'yinDeficiencyQualityScore',
        'qiDeficiencyQualityScore',
        'phlegmdampnessScore',
        'humidityAndHeatQualityScore',
        'bloodAndVesiculationQualityScore',
        'specialQualityScore',
        'qiYuQualityScore',
        'peaceQualityScore',
      ];
      for (let i = 0; i < keys.length; i++) {
        zybs[keys[i]] = arr[i] || '';
      }
      return zybs;
    } catch (error) {
      ctx.auditLog(
        '北元健康一体机healCheckResult处理zybs信息出错',
        `${error.stack} 。`,
        'error'
      );
    }
  }
  async ytbDeal(data) {
    const ctx = this.ctx;
    try {
      const ytb = {};
      for (const key in data) {
        ytb[key] = await this.splitValue(data[key]);
      }
      return ytb;
    } catch (error) {
      ctx.auditLog(
        '北元健康一体机healCheckResult处理ytb信息出错',
        `${error.stack} 。`,
        'error'
      );
    }
  }
  async fgnDeal(data) {
    const ctx = this.ctx;
    try {
      const fgn = {};
      for (const key in data) {
        fgn[key] = await this.splitValue(data[key]);
      }
      return fgn;
    } catch (error) {
      ctx.auditLog(
        '北元健康一体机healCheckResult处理fgn信息出错',
        `${error.stack} 。`,
        'error'
      );
    }
  }
  // 骨密度 带图片
  async gmdDeal(data) {
    const ctx = this.ctx;
    try {
      const gmd = {};
      for (const key in data) {
        if (key === 'data' && data.data) {
          gmd.fileName = await this.base64ToSave(data.data, 'png');
        }
        gmd[key] = data[key];
      }
      return gmd;
    } catch (error) {
      ctx.auditLog(
        '北元健康一体机healCheckResult处理gmd信息出错',
        `${error.stack} 。`,
        'error'
      );
    }
  }
  async shiliDeal(data) {
    const ctx = this.ctx;
    try {
      const shili = {};
      for (const key in data) {
        shili[key] = await this.splitValue(data[key]);
      }
      return shili;
    } catch (error) {
      ctx.auditLog(
        '北元健康一体机healCheckResult处理shili信息出错',
        `${error.stack} 。`,
        'error'
      );
    }
  }
  async semangDeal(data) {
    const ctx = this.ctx;
    try {
      return await this.splitValue(data);
    } catch (error) {
      ctx.auditLog(
        '北元健康一体机healCheckResult处理semang信息出错',
        `${error.stack} 。`,
        'error'
      );
    }
  }
  async alcoholDeal(data) {
    const ctx = this.ctx;
    try {
      return await this.splitValue(data);
    } catch (error) {
      ctx.auditLog(
        '北元健康一体机healCheckResult处理semang信息出错',
        `${error.stack} 。`,
        'error'
      );
    }
  }
  async xlcpDeal(data) {
    const ctx = this.ctx;
    try {
      const xlcp = {};
      for (const key in data) {
        xlcp[key] = await this.splitValue(data[key]);
      }
      return xlcp;
    } catch (error) {
      ctx.auditLog(
        '北元健康一体机healCheckResult处理shili信息出错',
        `${error.stack} 。`,
        'error'
      );
    }
  }
  // 传入值判断是否是string 如果是 按照#拆分
  async splitValue(value, type = 1) {
    // type 1 value tips standard
    const { ctx } = this;
    try {
      console.log('splitValue', value);
      if (typeof value === 'string') {
        const arr = value.split('#');
        console.log('splitValue arr', arr);
        if (type === 1) {
          return {
            value: arr[0],
            tips: arr[1],
            standard: arr[2],
          };
        }
        return;
      }
      console.log(value, 'value is not string');
      return null;
    } catch (error) {
      ctx.auditLog(
        '北元健康一体机分割字符串出错',
        `${error.stack} 。`,
        'error'
      );
    }
  }
  // 传入值判断是否是string 如果是去掉# 否则返回’‘
  async replaceValue(value) {
    if (typeof value === 'string') {
      return value.replace('#', '');
    }
    return '';
  }
  // 将base64 转换为bmp存到当前目录
  async base64ToSave(data, type = 'bmp') {
    const { app, ctx } = this;

    // 1. 解码 base64 数据
    const bufferData = Buffer.from(
      data.replace(/^data:image\/\w+;base64,/, ''),
      'base64'
    ).toString('utf8');
    const readableStream = new Readable();
    readableStream.push(Buffer.from(bufferData, 'base64'));
    readableStream.push(null);
    // 3. 定义目标路径和 bucket 配置
    const configFilePath = path.join(
      app.config.upload_path,
      'healthMachineCheck'
    );
    const fileName = (await this.randomFileName()) + `.${type}`;
    const target = path.resolve(configFilePath, fileName);
    // 4. 调用封装的 pipe 方法
    await ctx.helper.pipe({
      readableStream,
      target,
    });
    // 5. 返回结果
    return fileName;
  }
  // 随机生成文件名保证唯一
  async randomFileName() {
    const date = new Date();
    const time = date.getTime();
    const random = Math.floor(Math.random() * 1000);
    return `${time}_${random}`;
  }
}

module.exports = HealthMachineService;
