const Service = require('egg').Service;
const WxPay = require('wechatpay-node-v3'); // 微信支付
const AlipaySdk = require('alipay-sdk').default;
const AlipayFormData = require('alipay-sdk/lib/form').default;
class PayService extends Service {
  // 微信 调起支付，返回支付二维码链接
  async wxNativePay(data) {
    const { ctx } = this;
    try {
      const { wxpay } = this.config.pay;
      const pay = new WxPay(wxpay);
      const time = new Date();
      const overTime = new Date(time.setMinutes(time.getMinutes() + 5));
      const rfcTime = await this.timeToRFC(overTime);
      const params = {
        description: data.description,
        out_trade_no: data.out_trade_no,
        notify_url: this.config.wxpay_notify_url,
        amount: {
          total: +data.amount.total * 1000 / 10,
        },
        // scene_info: { //用户终端ip
        //   payer_client_ip: '127.0.0.1',
        // },
        time_expire: rfcTime,
      };
      const result = await pay.transactions_native(params);
      if (result.status === 200) {
        result.time_expire = overTime;
        return result;
      }
      throw result;
    } catch (error) {
      ctx.auditLog('微信调起支付wxNativePay错误', `${JSON.stringify(error)}`, 'error');
      return error;
    }
  }
  // 微信小程序付款
  async wxSpPay(data) {
    const { ctx } = this;
    try {
      const { wxpay } = this.config.pay;
      const pay = new WxPay(wxpay);
      const time = new Date();
      const overTime = new Date(time.setMinutes(time.getMinutes() + 5));
      const rfcTime = await this.timeToRFC(overTime);
      const params = {
        description: data.description,
        out_trade_no: data.out_trade_no,
        notify_url: this.config.wxpay_notify_url,
        amount: {
          total: +data.amount.total * 1000 / 10,
        },
        payer: {
          openid: data.openId,
        },
        time_expire: rfcTime,
      };
      const result = await pay.transactions_jsapi(params);
      if (result.status === 200) {
        result.time_expire = overTime;
        return result;
      }
      throw {
        result,
        data,
      };
    } catch (error) {
      ctx.auditLog('微信小程序调起支付wxSpPay错误', `${JSON.stringify(error)}`, 'error');
    }
  }
  // 微信 退款
  async wxRefunds(data) {
    const { wxpay } = this.config.pay;
    const pay = new WxPay(wxpay);
    const params = {
      out_trade_no: data.out_trade_no,
      out_refund_no: data.out_refund_no,
      notify_url: this.config.wxpay_refund_notify_url,
      reason: data.reason,
      amount: {
        refund: +data.amount.refund * 1000 / 10,
        total: +data.amount.total * 1000 / 10,
        currency: 'CNY',
      },
    };
    const result = await pay.refunds(params);
    return result;
  }
  // 微信关闭订单--out_trade_no
  async wxCloseOrder(data) {
    const { wxpay } = this.config.pay;
    const pay = new WxPay(wxpay);
    const result = await pay.close(data.out_trade_no);
    if (result.status === 204) {
      await this.wxpayCheck(data);
    }
    return result;
  }
  // 微信 回调通知 解密
  async checkWxSign(data) {
    const { wxpay } = this.config.pay;
    const pay = new WxPay(wxpay);
    const { ciphertext, associated_data, nonce } = data;
    const key = 'Zwykj365jdfgu33s49nrKdjwI82SDie8';
    // 解密回调信息
    const result = pay.decipher_gcm(ciphertext, associated_data, nonce, key);
    return result;
  }
  // 微信  查询订单状态
  async wxpayCheck(data) {
    const { wxpay } = this.config.pay;
    const pay = new WxPay(wxpay);
    const params = {
      out_trade_no: data.out_trade_no,
    };
    const result = await pay.query(params);
    await this.checkWxStatus(result);
    return result;
  }
  // 微信签名校验--好像没用
  async wxsignVerify(data) {
    const { payConfig } = this;
    const pay = new WxPay(payConfig.wxpay);
    return await pay.verifySign(data);
  }
  // 支付宝 调起支付，返回支付二维码链接
  async aliNativePay(data) {
    const { ctx } = this;
    try {
      const { alipay } = this.config.pay;
      const pay = new AlipaySdk(alipay);
      const formData = new AlipayFormData();
      const time = new Date();
      const overTime = new Date(time.setMinutes(time.getMinutes() + 5));
      const rfcTime = await this.getNowFormatDate(overTime);
      formData.setMethod('get');
      // 配置回调接口
      // 表示异步通知回调,
      formData.addField('notifyUrl', this.config.alipay_notify_url);
      // 付款成功的跳转页面
      formData.addField('returnUrl', 'https://www.zyws.cn');
      // 设置参数
      formData.addField('bizContent', {
        out_trade_no: data.out_trade_no, // 商户订单号,64个字符以内、可包含字母、数字、下划线,且不能重复
        total_amount: +data.amount.total, // 订单总金额，单位为元，精确到小数点后两位
        subject: data.description, // 订单标题
        time_expire: rfcTime,
      });
      // 请求接口
      const result = (await pay.exec(
        'alipay.trade.precreate',
        {},
        { formData }
      ));
      let response = await ctx.helper.reqJsonData(result);
      response = JSON.parse(JSON.stringify(response));
      if (response.alipay_trade_precreate_response.code === '10000') {
        response.time_expire = overTime;
        return response;
      }
      throw {
        result,
        data,
        response: response && response.alipay_trade_precreate_response ? response.alipay_trade_precreate_response : '出错了',
      };
    } catch (error) {
      ctx.auditLog('支付宝调起支付aliNativePay错误', `${JSON.stringify(error)}`, 'error');
      return error;
    }
  }
  // 支付宝 回调通知 校验签名
  async checkAliSign(data) {
    const { alipay } = this.config.pay;
    const pay = new AlipaySdk(alipay);
    const result = await pay.checkNotifySign(data);
    return result;
  }
  // 支付宝 发起退款
  async alipayRefunds(data) {
    const { alipay } = this.config.pay;
    const { ctx } = this;
    const pay = new AlipaySdk(alipay);
    const formData = new AlipayFormData();
    formData.setMethod('get');
    formData.addField('bizContent', {
      out_trade_no: data.out_trade_no,
      out_request_no: data.out_refund_no,
      refund_amount: +data.amount.refund,
      refund_reason: data.reason,
    });
    const result = (await pay.exec(
      'alipay.trade.refund', {}, {
        formData,
      }));
    const response = await ctx.helper.reqJsonData(result);
    await this.checkAliStatus(response.alipay_trade_refund_response);
    return response.alipay_trade_refund_response;
  }
  // 支付宝 关闭订单
  async alipayCloseOrder(data) {
    const { alipay } = this.config.pay;
    const { ctx } = this;
    const pay = new AlipaySdk(alipay);
    const formData = new AlipayFormData();
    formData.setMethod('get');
    formData.addField('bizContent', {
      out_trade_no: data.out_trade_no,
    });
    const result = (await pay.exec(
      'alipay.trade.close', {}, {
        formData,
      }));
    const response = await ctx.helper.reqJsonData(result);
    response.alipay_trade_close_response.out_trade_no = data.out_trade_no;
    await this.checkAliStatus(response.alipay_trade_close_response);
    return response.alipay_trade_close_response;
  }
  // 支付宝 查询订单状态
  async alipayCheck(data) {
    const { ctx } = this;
    const { alipay } = this.config.pay;
    const pay = new AlipaySdk(alipay);
    const formData = new AlipayFormData();
    formData.setMethod('get');
    formData.addField('bizContent', {
      out_trade_no: data.out_trade_no,
    });
    // 通过该接口主动查询订单状态
    // 查询订单状态：https://opendocs.alipay.com/apis/api_1/alipay.trade.query?scene=23
    const result = await pay.exec(
      'alipay.trade.query', {}, {
        formData,
      });
    const response = await ctx.helper.reqJsonData(result);
    await this.checkAliStatus(response.alipay_trade_query_response);
    return response.alipay_trade_query_response;
  }
  // 支付宝 检查订单状态通用，可做逻辑处理
  async checkAliStatus(data) {
    switch (data.trade_status) {
      case 'WAIT_BUYER_PAY':
        // console.log('交易创建，等待买家付款', data.out_trade_no, 0);
        await this.ctx.model.PayInfo.updateOne({ out_trade_no: data.out_trade_no }, { payStatus: 0 });
        break;
      case 'TRADE_CLOSED':
        // console.log('未付款交易超时关闭，或支付完成后全额退款');
        await this.ctx.model.PayInfo.updateOne({ out_trade_no: data.out_trade_no }, { payStatus: 3 });
        break;
      case 'TRADE_SUCCESS':
        // console.log('交易支付成功', data.out_trade_no, 1);
        await this.ctx.model.PayInfo.updateOne({ out_trade_no: data.out_trade_no }, { payStatus: 1, paySuccessTime: new Date() });

        if (this.config.subscriptionVerification) { // 进行会员升级逻辑
          await this.ctx.service.adminorg.upgradeVip(data.out_trade_no);
        } else {
          // 进行代理收益分配逻辑
          await this.ctx.service.agent.triggerProfit(data.out_trade_no);
        }
        return '交易支付成功';
      case 'TRADE_FINISHED':
        await this.ctx.model.PayInfo.updateOne({ out_trade_no: data.out_trade_no }, { payStatus: 5 });
        // console.log('交易结束，不可退款');
        break;
      default:
        if (data.sub_code && data.sub_code === 'ACQ.TRADE_NOT_EXIST' && data.out_trade_no && data.point_amount !== '0.00') {
          await this.ctx.model.PayInfo.updateOne({ out_trade_no: data.out_trade_no }, { payStatus: 3 });
        }
        break;
    }
  }
  // 微信 检查订单状态通用，可做逻辑处理
  async checkWxStatus(data) {
    // console.log('进来了微信订单状态更改');
    switch (data.trade_state) {
      case 'SUCCESS':
        await this.ctx.model.PayInfo.updateOne({ out_trade_no: data.out_trade_no }, { payStatus: 1, paySuccessTime: new Date() });
        if (this.config.subscriptionVerification) { // 进行会员升级逻辑
          await this.ctx.service.adminorg.upgradeVip(data.out_trade_no);
        } else {
          // 进行代理收益分配逻辑
          await this.ctx.service.agent.triggerProfit(data.out_trade_no);
        }
        break;
      case 'REFUND': // 转入退款
        // console.log('未付款交易超时关闭，或支付完成后全额退款');
        // await this.ctx.model.PayInfo.updateOne({ out_trade_no: data.out_trade_no }, {payStatus:3})
        break;
      case 'NOTPAY':// 未支付
        await this.ctx.model.PayInfo.updateOne({ out_trade_no: data.out_trade_no }, { payStatus: 0 });
        return '交易支付成功';
      case 'CLOSED':// 已关闭
        await this.ctx.model.PayInfo.updateOne({ out_trade_no: data.out_trade_no }, { payStatus: 3 });
        break;
      case 'REVOKED':// 已撤销（付款码支付）
        await this.ctx.model.PayInfo.updateOne({ out_trade_no: data.out_trade_no }, { payStatus: 3 });
        break;
      case 'USERPAYING':// 用户支付中（付款码支付）
        await this.ctx.model.PayInfo.updateOne({ out_trade_no: data.out_trade_no }, { payStatus: 0 });
        break;
      case 'PAYERROR':// 支付失败(其他原因，如银行返回失败)
        await this.ctx.model.PayInfo.updateOne({ out_trade_no: data.out_trade_no }, { payStatus: 2 });
        break;
      default: break;
    }
  }
  // 微信 退款状态
  async checkWxRefundsStatus(data) {
    switch (data.refund_status) {
      case 'CLOSED':
        // console.log('退款关闭通知', data);
        await this.ctx.model.PayInfo.updateOne({ out_trade_no: data.out_trade_no }, { payStatus: 7 });
        break;
      case 'ABNORMAL':
        // console.log('退款异常', data);
        await this.ctx.model.PayInfo.updateOne({ out_trade_no: data.out_trade_no }, { payStatus: 6 });
        break;
      case 'SUCCESS':
        // console.log('退款成功', data);
        await this.ctx.model.PayInfo.updateOne({ out_trade_no: data.out_trade_no }, { payStatus: 4 });
        await this.ctx.service.agent.triggerRefund(data.out_trade_no);
        return '交易支付成功';
      default: break;
    }
  }
  // 将时间转化为rfc格式 微信订单截止有用到
  async timeToRFC(time) {
    const y = time.getFullYear();
    const m =
        time.getMonth() + 1 < 10
          ? '0' + (time.getMonth() + 1)
          : time.getMonth() + 1;
    const d = time.getDate() < 10 ? '0' + time.getDate() : time.getDate();
    const hh = time.getHours() < 10 ? '0' + time.getHours() : time.getHours();
    const mm =
        time.getMinutes() < 10 ? '0' + time.getMinutes() : time.getMinutes();
    const ss =
        time.getSeconds() < 10 ? '0' + time.getSeconds() : time.getSeconds();
    const endDate = y + '-' + m + '-' + d + ' ' + hh + ':' + mm + ':' + ss;
    return endDate.replace(/\s+/g, 'T') + '+08:00';
    // 2020-08-14T11:33:26-07:00
  }
  async getNowFormatDate(date) {
    const seperator1 = '-';
    const seperator2 = ':';
    let month = date.getMonth() + 1;
    let strDate = date.getDate();
    if (month >= 1 && month <= 9) {
      month = '0' + month;
    }
    if (strDate >= 0 && strDate <= 9) {
      strDate = '0' + strDate;
    }
    const currentdate = date.getFullYear() + seperator1 + month + seperator1 + strDate
            + ' ' + date.getHours() + seperator2 + date.getMinutes()
            + seperator2 + date.getSeconds();
    return currentdate;
  }
  // 主动查询订单状态混合，供业务调用封装
  async orderCheck(data) {
    if (data.type === 'wxpay') {
      const res = await this.wxpayCheck(data);
      return await this.checkWxStatus(res);// 可以注释
    } else if (data.type === 'alipay') {
      const res = await this.alipayCheck(data);
      return await this.checkAliStatus(res);// 可以注释
    }
  }
  // 关闭订单混合，供业务调用封装
  async closeOrder(data) {
    if (data.type === 'wxpay') {
      await this.wxCloseOrder(data);
    } else if (data.type === 'alipay') {
      await this.alipayCloseOrder(data);
    }
  }
}

module.exports = PayService;
