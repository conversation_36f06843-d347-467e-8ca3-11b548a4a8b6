const Service = require('egg').Service;
/** ImportToDbService 类用于导入到数据库中。
 * @class ImportToDbService
 * @augments {Service}
 * @example
 * const importToDbService = new Service.ImportToDbService(ctx);
 * importToDbService.createPhysicalExamOrgFlows(params);
 */
class ImportToDbService extends Service {
  /** 校验参数是否为空的方法
   * @param {Object} params - 参数对象
   * @param {Array} requiredFields - 必填字段数组
   * @param {String} requiredFields.field - 必填字段
   * @param {String} requiredFields.message - 必填字段的中文名称
   * @param {RegExp} requiredFields.format - 必填字段的格式
   * @return {Boolean} - 返回校验结果
   * @throws {Error} - 如果参数为空，则抛出错误
   * @example
   * validateParams({ name: '张三' }, [{ field: 'name', message: '姓名' }])
   * // => true
   * validateParams({ name: '' }, [{ field: 'name', message: '姓名' }])
   * // => Error: 姓名不能为空
   * validateParams({ name: '张三' }, [{ field: 'name', message: '姓名', format: /^[\u4e00-\u9fa5]{2,4}$/ }])
   * // => true
   * validateParams({ name: '张三' }, [{ field: 'name', message: '姓名', format: /^[\u4e00-\u9fa5]{2,4}$/ }])
   * // => Error: 姓名格式不正确/^[\u4e00-\u9fa5]{2,4}$/
   */
  validateParams(params, requiredFields) {
    const errorMessage = '%s不能为空';
    for (const { field, message, format } of requiredFields) {
      if (!params[field] && !params[field] === 0) {
        throw new Error(errorMessage.replace('%s', message));
      }
      if (format && !format.test(params[field])) {
        throw new Error(
          errorMessage.replace('%s', message) +
            `或格式不正确${format.toString().slice(1, -1)}`
        );
      }
    }
    return true;
  }
  // #region tj 相关
  /** 创建体检用户
   * @param {Object} params - 参数对象
   * @param {String} params.password - 密码 必填
   * @return {Object} - 返回创建的体检用户的信息
   * @throws {Error} - 如果体检用户为空，或者创建体检用户失败，则抛出错误
   */
  async createPhysicalExamUser(params) {
    const { ctx } = this;
    const requiredFields = [{ field: 'password', message: '密码' }];
    try {
      this.validateParams(params, requiredFields);
      return await ctx.model.PhysicalExamUser.create(params);
    } catch (error) {
      throw new Error('创建体检用户失败' + error);
    }
  }
  /** 创建体检机构信息
   * @param {Object} params - 参数对象
   * @param {String} params.name - 机构名称 必填
   * @param {String} params.organization - 社会统一信用代码 必填
   * @param {String} params.unitCode - 编码 必填
   * @return {Object} - 返回创建的体检机构的信息
   * @throws {Error} - 如果体检机构为空，或者创建体检机构失败，则抛出错误
   */
  async createPhysicalExamOrgs(params) {
    const { ctx } = this;
    const requiredFields = [{ field: 'name', message: '机构名称' }];
    try {
      this.validateParams(params, requiredFields);
      if (!params.organization && !params.unitCode) {
        throw new Error('社会统一信用代码和编码不能为空');
      }
      const res = await ctx.model.PhysicalExamOrg.create(params);
      return res;
    } catch (error) {
      console.log('error', error);
      throw new Error('创建体检用户失败');
    }
  }
  /** 创建可登录的体检机构 params 包含 physicalExamUser 和 physicalExamOrg
   * @param {Object} params - 参数对象
   * @param {Object} params.physicalExamUser - 体检用户信息
   * @param {Object} params.physicalExamOrg - 体检机构
   * @return {Object} - 返回创建的体检机构和用户的信息id
   * @throws {Error} - 如果体检用户或体检机构为空，或者创建体检用户或体检机构失败，则抛出错误
   */
  async createPhysicalExamOrgFlow(params) {
    const { ctx } = this;
    const { physicalExamUser, physicalExamOrg } = params;
    let physicalExamUserRes,
      physicalExamOrgsRes;
    try {
      if (!physicalExamUser) {
        throw new Error('体检用户信息不能为空');
      }
      if (!physicalExamOrg) {
        throw new Error('体检机构信息不能为空');
      }
      // 将体检机构的状态设置为已审核
      physicalExamOrg.status = 3;
      // 1.先创建体检管理员用户
      physicalExamUserRes = await this.createPhysicalExamUser(physicalExamUser);
      if (!physicalExamUserRes._id) {
        throw new Error('创建体检用户失败');
      }
      if (!physicalExamOrg.managers) {
        physicalExamOrg.managers = [];
      }
      physicalExamOrg.administrator = physicalExamUserRes._id;
      physicalExamOrg.managers.push(physicalExamUserRes._id);
      // 2.创建体检机构
      physicalExamOrgsRes = await this.createPhysicalExamOrgs(physicalExamOrg);
      if (!physicalExamOrgsRes) {
        throw new Error('创建体检机构失败');
      }
      // 3.更新体检用户的机构信息
      await ctx.model.PhysicalExamUser.updateOne(
        { _id: physicalExamUserRes._id },
        {
          $set: {
            org_id: physicalExamOrgsRes._id,
          },
        }
      );
      const log = {
        physicalExamUser: physicalExamUserRes._id,
        physicalExamOrg: physicalExamOrgsRes._id,
      };
      ctx.auditLog('创建体检机构和用户成功', `${JSON.stringify(log)}`, 'info');
      return log;
    } catch (error) {
      // 如果创建体检机构失败，则删除创建的体检用户
      if (physicalExamUserRes && physicalExamUserRes._id) {
        await ctx.model.PhysicalExamUser.deleteOne({
          _id: physicalExamUserRes._id,
        });
      }
      ctx.auditLog('创建体检机构和用户失败', `${error}`, 'error');
      throw new Error('创建体检机构和用户失败');
    }
  }
  /** 创建healthcheck体检报告
   * @param {Object} params - 参数对象
   * @param {String} params.physicalExaminationOrgID - 体检机构id 必填
   * @param {String} params.EnterpriseID - 企业id 必填
   * @return {Object} - 返回创建的体检机构和用户的信息
   * @throws {Error} - 如果体检用户或体检机构为空，或者创建体检用户或体检机构失败，则抛出错误
   */
  async createHealthCheck(params) {
    const { ctx } = this;
    const { physicalExaminationOrgID, EnterpriseID } = params;
    try {
      if (!physicalExaminationOrgID) {
        throw new Error('体检机构id不能为空');
      }
      if (!EnterpriseID) {
        throw new Error('企业id不能为空');
      }
      return await ctx.model.Healthcheck.create(params);
    } catch (error) {
      console.log('error', error);
      throw new Error('创建体检报告失败');
    }
  }
  /** 创建suspect表
   * @param params.ctx
   * @param {Object} params - 参数对象
   * @param {String} params.name - 名称 必填
   * @param {String} params.age - 年龄 必填
   * @param {String} params.gender - 性别 必填 ['0', '1'] 男女
   * @param {String} params.workType - 工种 必填
   * @param {String} params.harmFactors - 危害因素 必填
   * @param {String} params.opinion - 意见 必填
   * @param {String} params.CwithO - 结论 必填 ['疑似职业病','禁忌证','复查','其他疾病或异常','目前未见异常]
   * @param {String} params.dedicalAdvice - 医学建议 必填
   * @param {String} params.batch - 体检项目id 必填
   * @param {String} params.IDCard - 身份证号  最好必填
   * @param {String} params.EnterpriseID - 企业id 必填
   * @param {String} params.checkType - 体检类型 必填 ['0', '1', '2', '3', '4'] 0上岗前 1在岗 2离岗 3复查 4应急
   * @param {String} params.employeeId - 人员id 必填 没有建去
   * @param {String} params.checkDate - 体检时间 必填
   * @return {Object} - 返回创建的体检机构和用户的信息id
   * @throws {Error} - 如果体检用户或体检机构为空，或者创建体检用户或体检机构失败，则抛出错误
   */
  async createSuspect(params) {
    const { ctx, validateParams } = this;
    const requiredFields = [
      { field: 'name', message: '姓名' },
      { field: 'age', message: '年龄' },
      { field: 'gender', message: '性别', format: /^(0|1)$/ },
      // { field: 'workType', message: '工种' },
      // { field: 'harmFactors', message: '危害因素' },
      // { field: 'opinion', message: '意见' },
      // { field: 'CwithO', message: '结论' },
      // { field: 'dedicalAdvice', message: '医学建议' },
      { field: 'batch', message: '体检项目id' },
      { field: 'EnterpriseID', message: '企业id' },
      { field: 'checkType', message: '体检类型', format: /^(0|1|2|3|4)$/ },
      { field: 'checkDate', message: '体检时间' },
    ];
    try {
      console.log('params', params.checkType);
      validateParams(params, requiredFields);
      return await ctx.model.Suspect.create(params);
    } catch (error) {
      console.log('error', error);
      throw new Error('创建体检报告失败' + error);
    }
  }
  /** 根据体检机构编号或者组织机构代码查询 并返回体检机构整体信息
   * @param {Object} params - 参数对象
   * @param {String} params.searchKey - 搜索关键字 必填
   * @return {Object} - 返回体检机构信息 如果没有则为null
   */
  async getOnePhysicalExamOrgBySearchKey(params) {
    const { ctx } = this;
    const { searchKey } = params;
    try {
      if (!searchKey) {
        throw new Error('搜索关键字不能为空');
      }
      return await ctx.model.PhysicalExamOrg.findOne({
        $or: [{ organization: searchKey }, { unitCode: searchKey }],
      });
    } catch (error) {
      console.log('error', error);
      throw new Error('查询体检机构失败' + error);
    }
  }
  /** 根据体检编号查询体检报告
   * @param {Object} params - 参数对象
   * @param {String} params.searchKey - 搜索关键字 必填
   * @return {Object} - 返回体检报告
   * @throws {Error} - 出错抛出错误
   * @example
   * const importToDbService = new Service.ImportToDbService(ctx);
   * importToDbService.getHealthCheckByCode({ searchKey: 'tj123' });
   * // => { _id: 'xxxx', ... }
   */
  async getOneHealthCheckBySearchKey(params) {
    const { ctx } = this;
    const { searchKey } = params;
    try {
      if (!searchKey) {
        throw new Error('搜索关键字不能为空');
      }
      return await ctx.model.Healthcheck.findOne({
        $or: [
          {
            projectNumber: searchKey,
            unitCode: searchKey,
          },
        ],
      });
    } catch (error) {
      console.log('error', error);
      throw new Error('查询体检报告失败' + error);
    }
  }
  /** 根据体检机构、体检时间、体检类型、被检企业查询体检报告
   * @param {Object} params - 参数对象
   * @param {String} params.physicalExaminationOrgID - 搜索关键字 必填
   * @return {Object} - 返回体检报告
   * @throws {Error} - 出错抛出错误
   * @example
   * const importToDbService = new Service.ImportToDbService(ctx);
   * importToDbService.getHealthCheckByCode({ searchKey: 'tj123' });
   * // => { _id: 'xxxx', ... }
   */
  async getOneHealthCheckBySearchType(params) {
    const { ctx } = this;
    const { physicalExaminationOrgID, EnterpriseID, checkDate, checkType } = params;
    try {
      if (!physicalExaminationOrgID || !EnterpriseID || !checkDate) {
        throw new Error('关键参数不能为空');
      }
      return await ctx.model.Healthcheck.findOne({
        physicalExaminationOrgID,
        EnterpriseID,
        checkDate,
        checkType,
      });
    } catch (error) {
      console.log('error', error);
      throw new Error('查询体检报告失败' + error);
    }
  }

  /**
   * 根据体检项目编号查询 并返回体整个体检项目信息
   * @param {Object} params - 参数对象
   * @param {String} params.projectNumber - 体检项目编号 必填
   */
  async getOneHealthCheckByProjectNumber(params) {
    const { ctx } = this;
    const { projectNumber, EnterpriseID } = params;
    try {
      if (!projectNumber) {
        throw new Error('体检项目编号不能为空');
      }
      return await ctx.model.Healthcheck.findOne({
        projectNumber,
        EnterpriseID,
      });
    } catch (error) {
      console.log('error', error);
      throw new Error('查询体检项目失败' + error);
    }
  }
  /** 根据员工id，体检项目id，体检结论查询Suspect
   * @param {Object} params - 参数对象
   * @param {String} params.physicalExaminationOrgID - 搜索关键字 必填
   * @return {Object} - 返回结果
   * @throws {Error} - 出错抛出错误
   * @example
   * const importToDbService = new Service.ImportToDbService(ctx);
   * importToDbService.getOneHealthCheckBySearchType({ searchKey: 'tj123' });
   * // => { _id: 'xxxx', ... }
   */
  async getSuspectByQuery(params) {
    const { ctx } = this;
    try {
      return await ctx.model.Suspect.findOne(params);
    } catch (err) {
      console.log('error', err);
      return null;
    }
  }
  /** 更新suspect表
   * @param {Object} params - 参数对象
   * @param {String} params._id - 主键id 必填
   * @return {Object} - 返回更新后的suspect信息
   */
  async updateSuspect(params) {
    const { ctx } = this;
    const { _id, ...rest } = params;
    try {
      return await ctx.model.Suspect.updateOne({ _id }, { $set: rest });
    } catch (error) {
      console.log('error', error);
      throw new Error('更新suspect表失败' + error);
    }
  }
  /** 创建诊断表Odisease
   * @param {Object} params - 参数对象
   * @return {Object} - 返回创建诊断是否成功
   */
  async createDiagnosis(params) {
    const { ctx } = this;
    try {
      const res = await ctx.model.Odisease.create(params);
      try {
        await ctx.service.healthcheck.dealWithHealcheckInfo(res._id, 2);
      } catch (error) {
        ctx.auditLog('处理诊断数据统计失败', `${error}`, 'error');
      }
      return res;
    } catch (error) {
      console.log('error', error);
      throw new Error('创建诊断表Odisease失败' + error);
    }
  }
  /** 更新诊断表Odisease
   * @param {Object} params - 参数对象
   * @param {String} params._id - 主键id 必填
   * @return {Object} - 返回更新后的诊断信息
   */
  async updateDiagnosis(params) {
    const { ctx } = this;
    const { _id, ...rest } = params;
    try {
      return await ctx.model.Odisease.updateOne({ _id }, { $set: rest });
    } catch (error) {
      console.log('error', error);
      throw new Error('更新诊断表Odisease失败' + error);
    }
  }
  /** 职业病史创建
   * @param {Object} params - 参数对象
   * @param {String} params.employeeId - 员工id 必填
   * @param type - 类型 默认one one: 单个创建  many: 批量创建
   * @return {Object} - 返回职业病史
   * @throws {Error} - 出错抛出错误
   */
  async createOccupationalHistory(params, type = 'one') {
    const { ctx } = this;
    try {
      if (type === 'one') {
        const { employeeId } = params;
        if (!employeeId) {
          throw new Error('员工id不能为空');
        }
        return await ctx.model.OccupationalHistory.create(params);
      }
      // 循环params 将paramsItem 中employeeid 为空的过滤掉 并使用log记录下来
      const log = [];
      const paramsFilter = params.filter(paramsItem => {
        if (!paramsItem.employeeId) {
          log.push(paramsItem);
          return false;
        }
        return true;
      });
      if (log.length > 0) {
        ctx.auditLog('职业病史创建失败 缺少employeedID', `${log}`, 'error');
      }
      return await ctx.model.OccupationalHistory.insertMany(paramsFilter);
    } catch (error) {
      console.log('error', error);
      throw new Error('创建职业病史失败' + error);
    }
  }
  // #endregion

  // #region qy 相关
  /**
   * 创建企业信息 adminorgs
   * @param params
   */
  async createAdminorg(params) {
    const { ctx } = this;
    const requiredFields = [{ field: 'name', message: '机构名称' }];
    try {
      this.validateParams(params, requiredFields);
      return await ctx.model.Adminorg.create(params);
    } catch (error) {
      console.log('error', error);
      throw new Error('创建企业信息adminorgs失败' + error);
    }
  }
  /** 创建dingTrees
   *
  * @param params
  */
  async createDingTrees(params) {
    const { ctx } = this;
    const requiredFields = [
      { field: 'name', message: '部门名称' },
      { field: 'EnterpriseID', message: '企业ID' },
    ];
    try {
      this.validateParams(params, requiredFields);
      return await ctx.model.Dingtree.create(params);
    } catch (error) {
      console.log('error', error);
      throw new Error('创建企业信息dingTrees失败' + error);
    }
  }
  /** 创建adminuser
   * @param {Object} params - 参数对象
   */
  async createAdminuser(params) {
    const { ctx } = this;
    const requiredFields = [{ field: 'name', message: '姓名' }];
    try {
      this.validateParams(params, requiredFields);
      params.enable = true;
      return await ctx.model.AdminUser.create(params);
    } catch (error) {
      console.log('error', error);
      throw new Error('创建企业信息adminuser失败' + error);
    }
  }
  /** 创建employee
   * @param {Object} params - 参数对象
   * @param {String} params.EnterpriseID - 企业ID 必填
   */
  async createEmployee(params) {
    const { ctx } = this;
    const requiredFields = [{ field: 'EnterpriseID', message: '企业ID' }];
    try {
      this.validateParams(params, requiredFields);
      return await ctx.model.Employee.create(params);
    } catch (error) {
      console.log('error', error);
      throw new Error('创建企业信息employee失败' + error);
    }
  }
  /** 根据身份证号获取员工信息
   * @param {Object} params - 参数对象
   */
  async getOneEmployeeBySearchKey(params) {
    const { ctx } = this;
    const { searchKey } = params;
    try {
      if (!searchKey) {
        throw new Error('搜索关键字不能为空');
      }
      return await ctx.model.Employee.findOne({
        $or: [{ IDNum: searchKey }],
      });
    } catch (error) {
      console.log('error', error);
      throw new Error('查询员工信息失败' + error);
    }
  }
  /** 创建user
   * @param {Object} params - 参数对象
   */
  async createUser(params) {
    const { ctx } = this;
    const requiredFields = [{ field: 'name', message: '姓名' }];
    try {
      this.validateParams(params, requiredFields);
      params.enable = true;
      return await ctx.model.User.create(params);
    } catch (error) {
      console.log('error', error);
      throw new Error('创建企业信息user失败' + error);
    }
  }
  /** 创建employee+ user work flows
   * @param {Object} params - 参数对象
   * @param {Object} params.employee - 员工信息
   * @param {String} params.employee.EnterpriseID  必填
   * @param {Object} params.user - 用户信息
   * @param {Array.<string>} params.user.companyId  必填
   * @param {Number} params.user.companyStatus  必填 2 企业是否确定为员工  0 是未绑定 1是已绑定待企业审核  2是企业审核通过 3是企业审核未通过
   */
  async createEmployeeAndUserWorkFlows(params) {
    const { ctx } = this;
    const { employee, user } = params;
    let employeeRes,
      userRes;
    try {
      if (!employee) {
        throw new Error('员工信息不能为空');
      }
      if (!user) {
        throw new Error('用户信息不能为空');
      }
      // 1.创建员工信息
      employeeRes = await this.createEmployee(employee);
      if (!employeeRes._id) {
        throw new Error('创建员工信息失败');
      }
      // 2.创建用户信息
      user.employeeId = employeeRes._id;
      userRes = await this.createUser(user);
      if (!userRes._id) {
        throw new Error('创建用户信息失败');
      }
      const log = {
        employee: employeeRes._id,
        user: userRes._id,
      };
      ctx.auditLog(
        '创建员工信息和用户信息成功',
        `${JSON.stringify(log)}`,
        'info'
      );
      return {
        employee: employeeRes,
        user: userRes,
      };
    } catch (error) {
      // 如果创建员工信息失败，则删除创建的用户信息
      if (employeeRes && employeeRes._id) {
        await ctx.model.Employee.deleteOne({
          _id: employeeRes._id,
        });
      }
      ctx.auditLog('创建员工信息和用户信息失败', `${error}${JSON.stringify(params)}`, 'error');
      throw new Error('创建员工信息和用户信息失败');
    }
  }
  /** 根据企业组织机构代码获取企业信息
   * @param {Object} params - 参数对象
   * @param {String} params.searchKey - 搜索关键字 必填
   * @return {Object} - 返回企业信息
   */
  async getOneCompanyBySearchKey(params) {
    const { ctx } = this;
    const { searchKey } = params;
    try {
      if (!searchKey) {
        throw new Error('搜索关键字不能为空');
      }
      return await ctx.model.Adminorg.findOne({
        $or: [{ code: searchKey }, { unitCode: searchKey }, { cname: { $regex: searchKey } }],
      });
    } catch (error) {
      console.log('error', error);
      throw new Error('查询企业失败' + error);
    }
  }
  /** 处理企业行业分类字段
   * @param industryField
   * @params industryField 行业分类 必填
   * @return {Array} - 返回处理后的行业分类
   */
  async handleIndustryField(industryField) {
    try {
      const indusJsonData = [
        {
          key: '租赁和商务服务业',
          value: [ 'L' ],
        },
        {
          key: '商务服务业',
          value: [ 'L', '72' ],
        },
        {
          key: '人力资源服务',
          value: [ 'L', '72', '726' ],
        },
        {
          key: '公共就业服务',
          value: [ 'L', '72', '726', '7261' ],
        },
        {
          key: '交通运输、仓储和邮政业',
          value: [ 'G' ],
        },
        {
          key: '航空运输业',
          value: [ 'G', '56' ],
        },
        {
          key: '通用航空服务',
          value: [ 'G', '56', '562' ],
        },
        {
          key: '其他通用航空服务',
          value: [ 'G', '56', '562', '5629' ],
        },
        {
          key: '制造业',
          value: [ 'C' ],
        },
        {
          key: '化学原料和化学制品制造业',
          value: [ 'C', '26' ],
        },
        {
          key: '涂料、油墨、颜料及类似产品制造',
          value: [ 'C', '26', '264' ],
        },
        {
          key: '工艺美术颜料制造',
          value: [ 'C', '26', '264', '2644' ],
        },
        {
          key: '采矿业',
          value: [ 'B' ],
        },
        {
          key: '石油和天然气开采业',
          value: [ 'B', '07' ],
        },
        {
          key: '天然气开采',
          value: [ 'B', '07', '072' ],
        },
        {
          key: '海洋天然气及可燃冰开采',
          value: [ 'B', '07', '072', '0722' ],
        },
        {
          key: '农副食品加工业',
          value: [ 'C', '13' ],
        },
        {
          key: '水产品加工',
          value: [ 'C', '13', '136' ],
        },
        {
          key: '鱼糜制品及水产品干腌制加工',
          value: [ 'C', '13', '136', '1362' ],
        },
        {
          key: '电力、热力、燃气及水生产和供应业',
          value: [ 'D' ],
        },
        {
          key: '电力、热力生产和供应业',
          value: [ 'D', '44' ],
        },
        {
          key: '电 力生产',
          value: [ 'D', '44', '441' ],
        },
        {
          key: '生物质能发电',
          value: [ 'D', '44', '441', '4417' ],
        },
        {
          key: '非金属矿物制品业',
          value: [ 'C', '30' ],
        },
        {
          key: '玻璃纤维和玻璃纤维增强塑料制品制',
          value: [ 'C', '30', '306' ],
        },
        {
          key: '玻璃纤维及制品制造',
          value: [ 'C', '30', '306', '3061' ],
        },
        {
          key: '纺织业',
          value: [ 'C', '17' ],
        },
        {
          key: '家用纺织制成品制造',
          value: [ 'C', '17', '177' ],
        },
        {
          key: '其他家用纺织制成品制造',
          value: [ 'C', '17', '177', '1779' ],
        },
        {
          key: '租赁业',
          value: [ 'L', '71' ],
        },
        {
          key: '文体设备和用品出 租',
          value: [ 'L', '71', '712' ],
        },
        {
          key: '图书出租',
          value: [ 'L', '71', '712', '7124' ],
        },
        {
          key: '科学研究和技术服务业',
          value: [ 'M' ],
        },
        {
          key: '专业技术服务业',
          value: [ 'M', '74' ],
        },
        {
          key: '地震服务',
          value: [ 'M', '74', '742' ],
        },
        {
          key: '科技推广和应用服务业',
          value: [ 'M', '75' ],
        },
        {
          key: '技术推广服务',
          value: [ 'M', '75', '751' ],
        },
        {
          key: '新能源技术推广服务',
          value: [ 'M', '75', '751', '7515' ],
        },
        {
          key: '文化、体育和娱乐业',
          value: [ 'R' ],
        },
        {
          key: '娱乐业',
          value: [ 'R', '90' ],
        },
        {
          key: '彩票活动',
          value: [ 'R', '90', '904' ],
        },
        {
          key: '福利彩票服务',
          value: [ 'R', '90', '904', '9042' ],
        },
        {
          key: '国际组织',
          value: [ 'T' ],
        },
        {
          key: '质检技术服务',
          value: [ 'M', '74', '745' ],
        },
        {
          key: '其他质检技术服务',
          value: [ 'M', '74', '745', '7459' ],
        },
        {
          key: '谷物磨制',
          value: [ 'C', '13', '131' ],
        },
        {
          key: '玉米加工',
          value: [ 'C', '13', '131', '1313' ],
        },
        {
          key: '建筑业',
          value: [ 'E' ],
        },
        {
          key: '土木工程建筑业',
          value: [ 'E', '48' ],
        },
        {
          key: '架线和管道工程建筑',
          value: [ 'E', '48', '485' ],
        },
        {
          key: '管道工程建筑',
          value: [ 'E', '48', '485', '4852' ],
        },
        {
          key: '化纤织造及印染精加工',
          value: [ 'C', '17', '175' ],
        },
        {
          key: '化纤织物染整精加工',
          value: [ 'C', '17', '175', '1752' ],
        },
        {
          key: '居民服务、修理和其他服务业',
          value: [ 'O' ],
        },
        {
          key: '其他服务业',
          value: [ 'O', '82' ],
        },
        {
          key: '清洁服务',
          value: [ 'O', '82', '821' ],
        },
        {
          key: '其他清洁服务',
          value: [ 'O', '82', '821', '8219' ],
        },
        {
          key: '农林牧渔技术推广服务',
          value: [ 'M', '75', '751', '7511' ],
        },
        {
          key: '地下综合管廊工程建筑',
          value: [ 'E', '48', '485', '4853' ],
        },
        {
          key: '航空客货运输',
          value: [ 'G', '56', '561' ],
        },
        {
          key: '航空货物运输',
          value: [ 'G', '56', '561', '5612' ],
        },
        {
          key: '农、林、牧、渔业',
          value: [ 'A' ],
        },
        {
          key: '农业',
          value: [ 'A', '01' ],
        },
        {
          key: '谷物种植',
          value: [ 'A', '01', '011' ],
        },
        {
          key: '玉米种植',
          value: [ 'A', '01', '011', '0113' ],
        },
        {
          key: '日用化学产品制造',
          value: [ 'C', '26', '268' ],
        },
        {
          key: '香料、香精制造',
          value: [ 'C', '26', '268', '2684' ],
        },
        {
          key: '住宿和 餐饮业',
          value: [ 'H' ],
        },
        {
          key: '餐饮业',
          value: [ 'H', '62' ],
        },
        {
          key: '饮料及冷饮服务',
          value: [ 'H', '62', '623' ],
        },
        {
          key: '咖 啡馆服务',
          value: [ 'H', '62', '623', '6232' ],
        },
        {
          key: '卫生和社会工作',
          value: [ 'Q' ],
        },
        {
          key: '卫生',
          value: [ 'Q', '84' ],
        },
        {
          key: '医院',
          value: [ 'Q', '84', '841' ],
        },
        {
          key: '民族医院',
          value: [ 'Q', '84', '841', '8414' ],
        },
        {
          key: '坚果、含油果、香料和饮料 作物种植',
          value: [ 'A', '01', '016' ],
        },
        {
          key: '坚果种植',
          value: [ 'A', '01', '016', '0161' ],
        },
        {
          key: '航空运输辅助活动',
          value: [ 'G', '56', '563' ],
        },
        {
          key: '空中交通管理',
          value: [ 'G', '56', '563', '5632' ],
        },
        {
          key: '石油、煤炭及其他燃料加工业',
          value: [ 'C', '25' ],
        },
        {
          key: '煤炭加工',
          value: [ 'C', '25', '252' ],
        },
        {
          key: '煤制液体燃料生产',
          value: [ 'C', '25', '252', '2523' ],
        },
        {
          key: '信息传输、软件和信息技术服务业',
          value: [ 'I' ],
        },
        {
          key: '电信、广播电视和卫星传输服务',
          value: [ 'I', '63' ],
        },
        {
          key: '卫星传输服务',
          value: [ 'I', '63', '633' ],
        },
        {
          key: '广播电视卫星传输服务',
          value: [ 'I', '63', '633', '6331' ],
        },
        {
          key: '毛纺织及染整精 加工',
          value: [ 'C', '17', '172' ],
        },
        {
          key: '毛织造加工',
          value: [ 'C', '17', '172', '1722' ],
        },
        {
          key: '涂料制造',
          value: [ 'C', '26', '264', '2641' ],
        },
        {
          key: '非金属矿采选业',
          value: [ 'B', '10' ],
        },
        {
          key: '采盐',
          value: [ 'B', '10', '103' ],
        },
        {
          key: '砖瓦、石材等建筑材料制造',
          value: [ 'C', '30', '303' ],
        },
        {
          key: '其他建筑材料制造',
          value: [ 'C', '30', '303', '3039' ],
        },
        {
          key: '创业空间服务',
          value: [ 'M', '75', '754' ],
        },
        {
          key: '机械设备经营租赁',
          value: [ 'L', '71', '711' ],
        },
        {
          key: '其他机械与设备经营租赁',
          value: [ 'L', '71', '711', '7119' ],
        },
        {
          key: '金属制品业',
          value: [ 'C', '33' ],
        },
        {
          key: '搪瓷制品制造',
          value: [ 'C', '33', '337' ],
        },
        {
          key: '生产专用搪瓷制品制造',
          value: [ 'C', '33', '337', '3371' ],
        },
        {
          key: '通用设备制造业',
          value: [ 'C', '34' ],
        },
        {
          key: '物料搬运设备 制造',
          value: [ 'C', '34', '343' ],
        },
        {
          key: '生产专用起重机制造',
          value: [ 'C', '34', '343', '3432' ],
        },
        {
          key: '批发和零售业',
          value: [ 'F' ],
        },
        {
          key: '零售业',
          value: [ 'F', '52' ],
        },
        {
          key: '纺织、服装及日用品专门零售',
          value: [ 'F', '52', '523' ],
        },
        {
          key: '钟表、眼镜零售',
          value: [ 'F', '52', '523', '5236' ],
        },
        {
          key: '水利、环境和公共设施管理业',
          value: [ 'N' ],
        },
        {
          key: '水利管理业',
          value: [ 'N', '76' ],
        },
        {
          key: '防洪除涝设施管理',
          value: [ 'N', '76', '761' ],
        },
        {
          key: '专用设备制造业',
          value: [ 'C', '35' ],
        },
        {
          key: '纺织、服装和皮革加工专用设备制造',
          value: [ 'C', '35', '355' ],
        },
        {
          key: '洗涤机械制造',
          value: [ 'C', '35', '355', '3554' ],
        },
        {
          key: '其他日用化学产品制造',
          value: [ 'C', '26', '268', '2689' ],
        },
        {
          key: '公共管理、社会保障和社会组织',
          value: [ 'S' ],
        },
        {
          key: ' 群众团体、社会团体和其他成员组织',
          value: [ 'S', '95' ],
        },
        {
          key: '群众团体',
          value: [ 'S', '95', '951' ],
        },
        {
          key: '共青团',
          value: [ 'S', '95', '951', '9513' ],
        },
        {
          key: '建筑工程机械与设备经营租赁',
          value: [ 'L', '71', '711', '7113' ],
        },
        {
          key: '批发业',
          value: [ 'F', '51' ],
        },
        {
          key: '纺织、服装及家庭用品批发',
          value: [ 'F', '51', '513' ],
        },
        {
          key: '家用视听设备批发',
          value: [ 'F', '51', '513', '5137' ],
        },
        {
          key: '农、林、牧、渔专业及辅助性活动',
          value: [ 'A', '05' ],
        },
        {
          key: '林业专业及辅助性活动',
          value: [ 'A', '05', '052' ],
        },
        {
          key: '林业有害生物防治活动',
          value: [ 'A', '05', '052', '0521' ],
        },
        {
          key: '文化、办公用机械制造',
          value: [ 'C', '34', '347' ],
        },
        {
          key: '幻灯及投影设备制造',
          value: [ 'C', '34', '347', '3472' ],
        },
        {
          key: '计算机、通信和其他电子设备制造业',
          value: [ 'C', '39' ],
        },
        {
          key: '智能消费设备制造',
          value: [ 'C', '39', '396' ],
        },
        {
          key: '智能车载设备制造',
          value: [ 'C', '39', '396', '3962' ],
        },
        {
          key: '电气机械和器材制造业',
          value: [ 'C', '38' ],
        },
        {
          key: '输配电及控制设备制造',
          value: [ 'C', '38', '382' ],
        },
        {
          key: '电力电子元器件制造',
          value: [ 'C', '38', '382', '3824' ],
        },
        {
          key: '采矿、冶金、建筑专用设备制造',
          value: [ 'C', '35', '351' ],
        },
        {
          key: ' 矿山机械制造',
          value: [ 'C', '35', '351', '3511' ],
        },
        {
          key: '研究和试验发展',
          value: [ 'M', '73' ],
        },
        {
          key: '自然科学研究和试验发 展',
          value: [ 'M', '73', '731' ],
        },
        {
          key: '公共设施管理业',
          value: [ 'N', '78' ],
        },
        {
          key: '游览景区管理',
          value: [ 'N', '78', '786' ],
        },
        {
          key: '森林公园管理',
          value: [ 'N', '78', '786', '7862' ],
        },
        {
          key: '金融业',
          value: [ 'J' ],
        },
        {
          key: '资本市场服务',
          value: [ 'J', '67' ],
        },
        {
          key: '证券市场服务',
          value: [ 'J', '67', '671' ],
        },
        {
          key: '证券市场管理服务',
          value: [ 'J', '67', '671', '6711' ],
        },
        {
          key: '食品制造业',
          value: [ 'C', '14' ],
        },
        {
          key: '其他食品制造',
          value: [ 'C', '14', '149' ],
        },
        {
          key: '冷冻饮品及食用冰制造',
          value: [ 'C', '14', '149', '1493' ],
        },
        {
          key: '地质勘查',
          value: [ 'M', '74', '747' ],
        },
        {
          key: '基础地质勘查',
          value: [ 'M', '74', '747', '7474' ],
        },
        {
          key: '道路运输业',
          value: [ 'G', '54' ],
        },
        {
          key: '城市公共交通运输',
          value: [ 'G', '54', '541' ],
        },
        {
          key: '公共自行车服 务',
          value: [ 'G', '54', '541', '5414' ],
        },
        {
          key: '建筑、安全用金属制品制造',
          value: [ 'C', '33', '335' ],
        },
        {
          key: '其他建筑、安全 用金属制品制造',
          value: [ 'C', '33', '335', '3359' ],
        },
        {
          key: '社会保障',
          value: [ 'S', '94' ],
        },
        {
          key: '基本保险',
          value: [ 'S', '94', '941' ],
        },
        {
          key: '失业保险',
          value: [ 'S', '94', '941', '9413' ],
        },
        {
          key: '农、林、牧、渔产品批发',
          value: [ 'F', '51', '511' ],
        },
        {
          key: '棉、麻批发',
          value: [ 'F', '51', '511', '5114' ],
        },
        {
          key: '海洋服务',
          value: [ 'M', '74', '743' ],
        },
        {
          key: '其他海洋服务',
          value: [ 'M', '74', '743', '7439' ],
        },
        {
          key: '教育',
          value: [ 'P' ],
        },
        {
          key: '技能培训、教育辅助及其他教育',
          value: [ 'P', '83', '839' ],
        },
        {
          key: '其他未列明教育',
          value: [ 'P', '83', '839', '8399' ],
        },
        {
          key: '轴承、齿轮和传动部件制造',
          value: [ 'C', '34', '345' ],
        },
        {
          key: '其他传动部件制造',
          value: [ 'C', '34', '345', '3459' ],
        },
        {
          key: '道路货物运输',
          value: [ 'G', '54', '543' ],
        },
        {
          key: '集 装箱道路运输',
          value: [ 'G', '54', '543', '5433' ],
        },
        {
          key: '其他商务服务业',
          value: [ 'L', '72', '729' ],
        },
        {
          key: '信用服务',
          value: [ 'L', '72', '729', '7295' ],
        },
        {
          key: '软件和信息技术服务业',
          value: [ 'I', '65' ],
        },
        {
          key: '软件开发',
          value: [ 'I', '65', '651' ],
        },
        {
          key: '应用软件开发',
          value: [ 'I', '65', '651', '6513' ],
        },
        {
          key: '高等教育',
          value: [ 'P', '83', '834' ],
        },
        {
          key: '成人高等教 育',
          value: [ 'P', '83', '834', '8342' ],
        },
        {
          key: '其他娱乐业',
          value: [ 'R', '90', '909' ],
        },
        {
          key: '机动车、电子产品和日用产品修 理业',
          value: [ 'O', '81' ],
        },
        {
          key: '家用电器修理',
          value: [ 'O', '81', '813' ],
        },
        {
          key: '家用电子产品修理',
          value: [ 'O', '81', '813', '8131' ],
        },
        {
          key: '可穿戴智能设备制造',
          value: [ 'C', '39', '396', '3961' ],
        },
        {
          key: '生态保护和环境治理业',
          value: [ 'N', '77' ],
        },
        {
          key: '生态保护',
          value: [ 'N', '77', '771' ],
        },
        {
          key: '动物园、水族馆管理服务',
          value: [ 'N', '77', '771', '7715' ],
        },
        {
          key: '国家机构',
          value: [ 'S', '92' ],
        },
        {
          key: '国家行政机构',
          value: [ 'S', '92', '922' ],
        },
        {
          key: '对外事务管理机构',
          value: [ 'S', '92', '922', '9222' ],
        },
        {
          key: '医药制造业',
          value: [ 'C', '27' ],
        },
        {
          key: '化学药品制剂制造',
          value: [ 'C', '27', '272' ],
        },
        {
          key: '机械设备、五金产品及电子产品批发',
          value: [ 'F', '51', '517' ],
        },
        {
          key: '计算机、软件及辅助设备批发',
          value: [ 'F', '51', '517', '5176' ],
        },
        {
          key: '组织管理服务',
          value: [ 'L', '72', '721' ],
        },
        {
          key: '企业总部管理',
          value: [ 'L', '72', '721', '7211' ],
        },
        {
          key: '水力发电',
          value: [ 'D', '44', '441', '4413' ],
        },
        {
          key: '含油果种植',
          value: [ 'A', '01', '016', '0162' ],
        },
        {
          key: '棉纺织及印染精加工',
          value: [ 'C', '17', '171' ],
        },
        {
          key: '棉纺纱加工',
          value: [ 'C', '17', '171', '1711' ],
        },
        {
          key: '通用零部件制造',
          value: [ 'C', '34', '348' ],
        },
        {
          key: '其他通用零部件制造',
          value: [ 'C', '34', '348', '3489' ],
        },
        {
          key: '草种植及割草',
          value: [ 'A', '01', '018' ],
        },
        {
          key: '天然草原割草',
          value: [ 'A', '01', '018', '0182' ],
        },
        {
          key: '医药及医疗器材批发',
          value: [ 'F', '51', '515' ],
        },
        {
          key: '中 药批发',
          value: [ 'F', '51', '515', '5152' ],
        },
        {
          key: '名胜风景区管理',
          value: [ 'N', '78', '786', '7861' ],
        },
        {
          key: '方便食品制造',
          value: [ 'C', '14', '143' ],
        },
        {
          key: '方便面制造',
          value: [ 'C', '14', '143', '1433' ],
        },
        {
          key: '橡胶和塑料制品业',
          value: [ 'C', '29' ],
        },
        {
          key: '橡胶制品业',
          value: [ 'C', '29', '291' ],
        },
        {
          key: '运动场地用塑胶制造',
          value: [ 'C', '29', '291', '2916' ],
        },
        {
          key: '非电力家用器具制造',
          value: [ 'C', '38', '386' ],
        },
        {
          key: '燃气及类似能源家用器具制造',
          value: [ 'C', '38', '386', '3861' ],
        },
        {
          key: '铁路、船舶、航空航天和其他运输设备制',
          value: [ 'C', '37' ],
        },
        {
          key: '摩托车制造',
          value: [ 'C', '37', '375' ],
        },
        {
          key: '摩托 车整车制造',
          value: [ 'C', '37', '375', '3751' ],
        },
        {
          key: '地质勘查技术服务',
          value: [ 'M', '74', '747', '7475' ],
        },
        {
          key: '肥料制造',
          value: [ 'C', '26', '262' ],
        },
        {
          key: '有机肥料及微生物肥料制造',
          value: [ 'C', '26', '262', '2625' ],
        },
        {
          key: '皮革、毛皮、羽毛 及其制品和制鞋业',
          value: [ 'C', '19' ],
        },
        {
          key: '毛皮鞣制及制品加工',
          value: [ 'C', '19', '193' ],
        },
        {
          key: '毛皮服装加工',
          value: [ 'C', '19', '193', '1932' ],
        },
        {
          key: '咨询与调查',
          value: [ 'L', '72', '724' ],
        },
        {
          key: '健康咨询',
          value: [ 'L', '72', '724', '7244' ],
        },
        {
          key: '滚动轴承制造',
          value: [ 'C', '34', '345', '3451' ],
        },
        {
          key: '水泥、石灰和石膏制造',
          value: [ 'C', '30', '301' ],
        },
        {
          key: '石灰和石膏制造',
          value: [ 'C', '30', '301', '3012' ],
        },
        {
          key: '纺织服装、服饰业',
          value: [ 'C', '18' ],
        },
        {
          key: '服饰制造',
          value: [ 'C', '18', '183' ],
        },
        {
          key: '居民服务业',
          value: [ 'O', '80' ],
        },
        {
          key: '摄影扩印服务',
          value: [ 'O', '80', '806' ],
        },
        {
          key: '制鞋业',
          value: [ 'C', '19', '195' ],
        },
        {
          key: '纺织面料鞋制造',
          value: [ 'C', '19', '195', '1951' ],
        },
        {
          key: '玻璃制造',
          value: [ 'C', '30', '304' ],
        },
        {
          key: '平板玻璃制造',
          value: [ 'C', '30', '304', '3041' ],
        },
        {
          key: '农、林、牧、渔专用机械制造',
          value: [ 'C', '35', '357' ],
        },
        {
          key: '营林及木竹采伐机械制造',
          value: [ 'C', '35', '357', '3573' ],
        },
        {
          key: '照相机及器材制造',
          value: [ 'C', '34', '347', '3473' ],
        },
        {
          key: '速冻食品制造',
          value: [ 'C', '14', '143', '1432' ],
        },
        {
          key: '互联网和相关服务',
          value: [ 'I', '64' ],
        },
        {
          key: '互联网平台',
          value: [ 'I', '64', '643' ],
        },
        {
          key: '互联网公共服务平台',
          value: [ 'I', '64', '643', '6434' ],
        },
        {
          key: '中药材种植',
          value: [ 'A', '01', '017' ],
        },
        {
          key: '中草药种植',
          value: [ 'A', '01', '017', '0171' ],
        },
        {
          key: '保健食品制造',
          value: [ 'C', '14', '149', '1492' ],
        },
        {
          key: '保险业',
          value: [ 'J', '68' ],
        },
        {
          key: '人身保险',
          value: [ 'J', '68', '681' ],
        },
        {
          key: '人寿保险',
          value: [ 'J', '68', '681', '6811' ],
        },
        {
          key: '陶瓷制品制造',
          value: [ 'C', '30', '307' ],
        },
        {
          key: '卫生陶瓷制品制造',
          value: [ 'C', '30', '307', '3072' ],
        },
        {
          key: '拖拉机制造',
          value: [ 'C', '35', '357', '3571' ],
        },
        {
          key: '汽车制造业',
          value: [ 'C', '36' ],
        },
        {
          key: '汽车 用发动机制造',
          value: [ 'C', '36', '362' ],
        },
        {
          key: '食品、饮料及烟草制品批发',
          value: [ 'F', '51', '512' ],
        },
        {
          key: '烟草制品批发',
          value: [ 'F', '51', '512', '5128' ],
        },
        {
          key: '文教、工美、体育和娱乐用品制造业',
          value: [ 'C', '24' ],
        },
        {
          key: '体育用品制造',
          value: [ 'C', '24', '244' ],
        },
        {
          key: '球类制造',
          value: [ 'C', '24', '244', '2441' ],
        },
        {
          key: '汽车租赁',
          value: [ 'L', '71', '711', '7111' ],
        },
        {
          key: '新材料技术推广服务',
          value: [ 'M', '75', '751', '7513' ],
        },
        {
          key: '城市轨道交通',
          value: [ 'G', '54', '541', '5412' ],
        },
        {
          key: '金属制品、机械和设备修理业',
          value: [ 'C', '43' ],
        },
        {
          key: '铁路、船舶、航空航天等运输设备修理',
          value: [ 'C', '43', '434' ],
        },
        {
          key: '船舶修理',
          value: [ 'C', '43', '434', '4342' ],
        },
        {
          key: '法律服务',
          value: [ 'L', '72', '723' ],
        },
        {
          key: '公证服务',
          value: [ 'L', '72', '723', '7232' ],
        },
        {
          key: '节能技术推广服务',
          value: [ 'M', '75', '751', '7514' ],
        },
        {
          key: '黑色金属矿采选业',
          value: [ 'B', '08' ],
        },
        {
          key: '其他黑色金属矿采选',
          value: [ 'B', '08', '089' ],
        },
        {
          key: '土砂石开采',
          value: [ 'B', '10', '101' ],
        },
        {
          key: '粘土及其他土砂石开采',
          value: [ 'B', '10', '101', '1019' ],
        },
        {
          key: '食品、饮料、烟草及饲料生产专用设备制造',
          value: [ 'C', '35', '353' ],
        },
        {
          key: '饲料生产专用设备制造',
          value: [ 'C', '35', '353', '3534' ],
        },
        {
          key: '羽毛(绒)加工及制品制造',
          value: [ 'C', '19', '194' ],
        },
        {
          key: '羽毛（绒）制品加工',
          value: [ 'C', '19', '194', '1942' ],
        },
        {
          key: '电机制造',
          value: [ 'C', '38', '381' ],
        },
        {
          key: '电动机制造',
          value: [ 'C', '38', '381', '3812' ],
        },
        {
          key: '电线、电缆、光缆及电工器材制造',
          value: [ 'C', '38', '383' ],
        },
        {
          key: '绝缘制品制造',
          value: [ 'C', '38', '383', '3834' ],
        },
        {
          key: '广播、电视、电影和录音制作业',
          value: [ 'R', '87' ],
        },
        {
          key: '广播',
          value: [ 'R', '87', '871' ],
        },
        {
          key: '货币金融服务',
          value: [ 'J', '66' ],
        },
        {
          key: '银行理财服务',
          value: [ 'J', '66', '664' ],
        },
        {
          key: '日用品出租',
          value: [ 'L', '71', '713' ],
        },
        {
          key: '家用电器及电子产品专门零售',
          value: [ 'F', '52', '527' ],
        },
        {
          key: '日用家电零售',
          value: [ 'F', '52', '527', '5272' ],
        },
        {
          key: '装卸搬运和仓储业',
          value: [ 'G', '59' ],
        },
        {
          key: '装卸搬运',
          value: [ 'G', '59', '591' ],
        },
        {
          key: '专业公共卫生服务',
          value: [ 'Q', '84', '843' ],
        },
        {
          key: '急救中心（站）服务',
          value: [ 'Q', '84', '843', '8434' ],
        },
        {
          key: '林业',
          value: [ 'A', '02' ],
        },
        {
          key: '森林经营、管护和改培',
          value: [ 'A', '02', '023' ],
        },
        {
          key: '森林经营和 管护',
          value: [ 'A', '02', '023', '0231' ],
        },
        {
          key: '安全、消防用金属制品制造',
          value: [ 'C', '33', '335', '3353' ],
        },
        {
          key: '汽车、摩托车、零配件和燃料及其他动力销售',
          value: [ 'F', '52', '526' ],
        },
        {
          key: '汽车新车零售',
          value: [ 'F', '52', '526', '5261' ],
        },
        {
          key: '公路旅客运输',
          value: [ 'G', '54', '542' ],
        },
        {
          key: '其他公路客运',
          value: [ 'G', '54', '542', '5429' ],
        },
        {
          key: '智能无人飞行器制造',
          value: [ 'C', '39', '396', '3963' ],
        },
        {
          key: '财产保险',
          value: [ 'J', '68', '682' ],
        },
        {
          key: '船舶及相关装置制造',
          value: [ 'C', '37', '373' ],
        },
        {
          key: '非金属船舶制造',
          value: [ 'C', '37', '373', '3732' ],
        },
        {
          key: '机动车燃油零售',
          value: [ 'F', '52', '526', '5265' ],
        },
        {
          key: '其他金融业',
          value: [ 'J', '69' ],
        },
        {
          key: '金融信托与管理服务',
          value: [ 'J', '69', '691' ],
        },
        {
          key: '信托 公司',
          value: [ 'J', '69', '691', '6911' ],
        },
        {
          key: '热电联产',
          value: [ 'D', '44', '441', '4412' ],
        },
        {
          key: '雷达及配套设备制造',
          value: [ 'C', '39', '394' ],
        },
        {
          key: '城乡市容管理',
          value: [ 'N', '78', '783' ],
        },
        {
          key: '其他游览景区管理',
          value: [ 'N', '78', '786', '7869' ],
        },
        {
          key: '水果种植',
          value: [ 'A', '01', '015' ],
        },
        {
          key: '葡萄种植',
          value: [ 'A', '01', '015', '0152' ],
        },
        {
          key: '橡胶板、管、带制造',
          value: [ 'C', '29', '291', '2912' ],
        },
        {
          key: '其他谷物种植',
          value: [ 'A', '01', '011', '0119' ],
        },
        {
          key: '弹簧制造',
          value: [ 'C', '34', '348', '3483' ],
        },
        {
          key: '其他电子设备制造',
          value: [ 'C', '39', '399' ],
        },
        {
          key: '银行监管服务',
          value: [ 'J', '66', '665' ],
        },
        {
          key: '鞋帽批发',
          value: [ 'F', '51', '513', '5133' ],
        },
        {
          key: '金属丝绳及其制品制造',
          value: [ 'C', '33', '334' ],
        },
        {
          key: '农业机械经营租赁',
          value: [ 'L', '71', '711', '7112' ],
        },
        {
          key: '其他橡胶制品制造',
          value: [ 'C', '29', '291', '2919' ],
        },
        {
          key: '电子器件制造',
          value: [ 'C', '39', '397' ],
        },
        {
          key: '光电子器件制造',
          value: [ 'C', '39', '397', '3976' ],
        },
        {
          key: '环境与生 态监测检测服务',
          value: [ 'M', '74', '746' ],
        },
        {
          key: '生态资源监测',
          value: [ 'M', '74', '746', '7462' ],
        },
        {
          key: '其他卫星传输服 务',
          value: [ 'I', '63', '633', '6339' ],
        },
        {
          key: '粘土砖瓦及建筑砌块制造',
          value: [ 'C', '30', '303', '3031' ],
        },
        {
          key: '其他文化、办公用机械制造',
          value: [ 'C', '34', '347', '3479' ],
        },
        {
          key: '疗养院',
          value: [ 'Q', '84', '841', '8416' ],
        },
        {
          key: '木材加工和木、竹、藤、棕、草制品业',
          value: [ 'C', '20' ],
        },
        {
          key: '木质制品制造',
          value: [ 'C', '20', '203' ],
        },
        {
          key: '木制容器制造',
          value: [ 'C', '20', '203', '2035' ],
        },
        {
          key: '玻璃制品制造',
          value: [ 'C', '30', '305' ],
        },
        {
          key: '制镜及类似品加工',
          value: [ 'C', '30', '305', '3057' ],
        },
        {
          key: '其他彩票服务',
          value: [ 'R', '90', '904', '9049' ],
        },
        {
          key: '建筑装饰、装修和其他建筑业',
          value: [ 'E', '50' ],
        },
        {
          key: '提供施工设备服务',
          value: [ 'E', '50', '503' ],
        },
        {
          key: '其他科技推广服务业',
          value: [ 'M', '75', '759' ],
        },
        {
          key: '货 摊、无店铺及其他零售业',
          value: [ 'F', '52', '529' ],
        },
        {
          key: '生活用燃料零售',
          value: [ 'F', '52', '529', '5296' ],
        },
        {
          key: '其他 居民服务业',
          value: [ 'O', '80', '809' ],
        },
        {
          key: '玩具制造',
          value: [ 'C', '24', '245' ],
        },
        {
          key: '儿童乘骑玩耍的童车类产品制造',
          value: [ 'C', '24', '245', '2456' ],
        },
        {
          key: '仪器仪表制造业',
          value: [ 'C', '40' ],
        },
        {
          key: '通用仪器仪表制造',
          value: [ 'C', '40', '401' ],
        },
        {
          key: '供应用仪器仪表制造',
          value: [ 'C', '40', '401', '4016' ],
        },
        {
          key: '采供血机构服务',
          value: [ 'Q', '84', '843', '8435' ],
        },
        {
          key: '石膏、水泥制品及类似制品制造',
          value: [ 'C', '30', '302' ],
        },
        {
          key: '砼结构构件制造',
          value: [ 'C', '30', '302', '3022' ],
        },
        {
          key: '其他玻璃制品制造',
          value: [ 'C', '30', '305', '3059' ],
        },
        {
          key: '基层医疗卫生服务',
          value: [ 'Q', '84', '842' ],
        },
        {
          key: '街道卫生院',
          value: [ 'Q', '84', '842', '8422' ],
        },
        {
          key: '船舶拆除',
          value: [ 'C', '37', '373', '3736' ],
        },
        {
          key: '学前教育',
          value: [ 'P', '83', '831' ],
        },
        {
          key: '煤炭开采和洗选业',
          value: [ 'B', '06' ],
        },
        {
          key: '褐煤开采洗选',
          value: [ 'B', '06', '062' ],
        },
        {
          key: '铁路运输设备制造',
          value: [ 'C', '37', '371' ],
        },
        {
          key: '铁路机车车辆制造',
          value: [ 'C', '37', '371', '3712' ],
        },
        {
          key: '航标器材及其他相关装置制造',
          value: [ 'C', '37', '373', '3739' ],
        },
        {
          key: '其他电工器材制造',
          value: [ 'C', '38', '383', '3839' ],
        },
        {
          key: '电池制造',
          value: [ 'C', '38', '384' ],
        },
        {
          key: '铅蓄电池制造',
          value: [ 'C', '38', '384', '3843' ],
        },
        {
          key: '电子元件及电 子专用材料制造',
          value: [ 'C', '39', '398' ],
        },
        {
          key: '电子电路制造',
          value: [ 'C', '39', '398', '3982' ],
        },
        {
          key: '连续搬运设备制 造',
          value: [ 'C', '34', '343', '3434' ],
        },
        {
          key: '木门窗制造',
          value: [ 'C', '20', '203', '2032' ],
        },
        {
          key: '其他玻璃制造',
          value: [ 'C', '30', '304', '3049' ],
        },
        {
          key: '互联网生产服务平台',
          value: [ 'I', '64', '643', '6431' ],
        },
        {
          key: '航空、航天器及设备制造',
          value: [ 'C', '37', '374' ],
        },
        {
          key: '航天器及运载火箭制造',
          value: [ 'C', '37', '374', '3742' ],
        },
        {
          key: '其他制造业',
          value: [ 'C', '41' ],
        },
        {
          key: '核辐射加工',
          value: [ 'C', '41', '412' ],
        },
        {
          key: '基本医疗保险',
          value: [ 'S', '94', '941', '9412' ],
        },
        {
          key: '竹、藤、棕、草等制品制造',
          value: [ 'C', '20', '204' ],
        },
        {
          key: '竹制品制造',
          value: [ 'C', '20', '204', '2041' ],
        },
        {
          key: '锌锰电池制 造',
          value: [ 'C', '38', '384', '3844' ],
        },
        {
          key: '计算机制造',
          value: [ 'C', '39', '391' ],
        },
        {
          key: '计算机零部件制造',
          value: [ 'C', '39', '391', '3912' ],
        },
        {
          key: '纺织专用设备制造',
          value: [ 'C', '35', '355', '3551' ],
        },
        {
          key: '木材和竹材采运',
          value: [ 'A', '02', '024' ],
        },
        {
          key: '竹材采运',
          value: [ 'A', '02', '024', '0242' ],
        },
        {
          key: '其他电气机械及器材制造',
          value: [ 'C', '38', '389' ],
        },
        {
          key: '其他未列明电气机械及器材制造',
          value: [ 'C', '38', '389', '3899' ],
        },
        {
          key: '其他运输设备修理',
          value: [ 'C', '43', '434', '4349' ],
        },
        {
          key: '鞋帽零售',
          value: [ 'F', '52', '523', '5233' ],
        },
        {
          key: '冷藏车道路运输',
          value: [ 'G', '54', '543', '5432' ],
        },
        {
          key: '渔业',
          value: [ 'A', '04' ],
        },
        {
          key: '水产养殖',
          value: [ 'A', '04', '041' ],
        },
        {
          key: '海水养殖',
          value: [ 'A', '04', '041', '0411' ],
        },
        {
          key: '五金、家具及室内装饰材料专门零售',
          value: [ 'F', '52', '528' ],
        },
        {
          key: '陶瓷、石材装饰材料零售',
          value: [ 'F', '52', '528', '5287' ],
        },
        {
          key: '普通高等教育',
          value: [ 'P', '83', '834', '8341' ],
        },
        {
          key: '针织或钩针编织服装制造',
          value: [ 'C', '18', '182' ],
        },
        {
          key: '运动休闲针织服装制造',
          value: [ 'C', '18', '182', '1821' ],
        },
        {
          key: '文化、体育用品及器材专门零售',
          value: [ 'F', '52', '524' ],
        },
        {
          key: '乐器零售',
          value: [ 'F', '52', '524', '5247' ],
        },
        {
          key: '隧道施工专用机械制造',
          value: [ 'C', '35', '351', '3517' ],
        },
        {
          key: '铁路运输业',
          value: [ 'G', '53' ],
        },
        {
          key: '铁路旅客运输',
          value: [ 'G', '53', '531' ],
        },
        {
          key: '城际铁路旅客运输',
          value: [ 'G', '53', '531', '5312' ],
        },
        {
          key: '专用化学产品制造',
          value: [ 'C', '26', '266' ],
        },
        {
          key: '环境污染处理专用药 剂材料制造',
          value: [ 'C', '26', '266', '2666' ],
        },
        {
          key: '中等教育',
          value: [ 'P', '83', '833' ],
        },
        {
          key: '成人初中教育',
          value: [ 'P', '83', '833', '8333' ],
        },
        {
          key: '农业专业及辅助性活动',
          value: [ 'A', '05', '051' ],
        },
        {
          key: '农业机械活动',
          value: [ 'A', '05', '051', '0512' ],
        },
        {
          key: '其他通用设备制造业',
          value: [ 'C', '34', '349' ],
        },
        {
          key: '其他未列明通用设备制造业',
          value: [ 'C', '34', '349', '3499' ],
        },
        {
          key: '显示器件制造',
          value: [ 'C', '39', '397', '3974' ],
        },
        {
          key: '仪器仪表修理',
          value: [ 'C', '43', '436' ],
        },
        {
          key: '货币银行服务',
          value: [ 'J', '66', '662' ],
        },
        {
          key: '政策性银行服务',
          value: [ 'J', '66', '662', '6622' ],
        },
        {
          key: '会议、展览及相关服务',
          value: [ 'L', '72', '728' ],
        },
        {
          key: '体育会展服务',
          value: [ 'L', '72', '728', '7283' ],
        },
        {
          key: '科技会展服务',
          value: [ 'L', '72', '728', '7281' ],
        },
        {
          key: '生物技术推广服务',
          value: [ 'M', '75', '751', '7512' ],
        },
        {
          key: '米、面制品制造',
          value: [ 'C', '14', '143', '1431' ],
        },
        {
          key: '罐头食品制造',
          value: [ 'C', '14', '145' ],
        },
        {
          key: '蔬菜、水果罐头制造',
          value: [ 'C', '14', '145', '1453' ],
        },
        {
          key: '人造板制造',
          value: [ 'C', '20', '202' ],
        },
        {
          key: '胶合板制造',
          value: [ 'C', '20', '202', '2021' ],
        },
        {
          key: '矿产品、建材及化工产品批发',
          value: [ 'F', '51', '516' ],
        },
        {
          key: '农用薄膜批发',
          value: [ 'F', '51', '516', '5168' ],
        },
        {
          key: ' 风力发电',
          value: [ 'D', '44', '441', '4415' ],
        },
        {
          key: '鱼油提取及制品制造',
          value: [ 'C', '13', '136', '1363' ],
        },
        {
          key: '木材采运',
          value: [ 'A', '02', '024', '0241' ],
        },
        {
          key: '有色金属冶炼和压延加工业',
          value: [ 'C', '32' ],
        },
        {
          key: '稀有稀土金属冶炼',
          value: [ 'C', '32', '323' ],
        },
        {
          key: '钨钼冶炼',
          value: [ 'C', '32', '323', '3231' ],
        },
        {
          key: '环保、邮政、社会公共服务及其他专用',
          value: [ 'C', '35', '359' ],
        },
        {
          key: '社会公共安全设备及器材制造',
          value: [ 'C', '35', '359', '3595' ],
        },
        {
          key: '烘炉、风机、包装等设 备制造',
          value: [ 'C', '34', '346' ],
        },
        {
          key: '风机、风扇制造',
          value: [ 'C', '34', '346', '3462' ],
        },
        {
          key: '医疗设备经营租赁',
          value: [ 'L', '71', '711', '7115' ],
        },
        {
          key: '音像制品、电子和数字出版物零售',
          value: [ 'F', '52', '524', '5244' ],
        },
        {
          key: '建筑安装 业',
          value: [ 'E', '49' ],
        },
        {
          key: '管道和设备安装',
          value: [ 'E', '49', '492' ],
        },
        {
          key: '其他未列明金融业',
          value: [ 'J', '69', '699' ],
        },
        {
          key: '货币经纪公司服务',
          value: [ 'J', '69', '699', '6991' ],
        },
        {
          key: '数字内容服务',
          value: [ 'I', '65', '657' ],
        },
        {
          key: '地理遥感信息服务',
          value: [ 'I', '65', '657', '6571' ],
        },
        {
          key: '文教办公用品制造',
          value: [ 'C', '24', '241' ],
        },
        {
          key: '墨水、墨汁制造',
          value: [ 'C', '24', '241', '2414' ],
        },
        {
          key: '磷肥制造',
          value: [ 'C', '26', '262', '2622' ],
        },
        {
          key: '谷物、豆及薯类批发',
          value: [ 'F', '51', '511', '5111' ],
        },
        {
          key: '期货市场服务',
          value: [ 'J', '67', '674' ],
        },
        {
          key: '期货市场管理服务',
          value: [ 'J', '67', '674', '6741' ],
        },
        {
          key: '运行维护服务',
          value: [ 'I', '65', '654' ],
        },
        {
          key: '化学试剂和助剂制造',
          value: [ 'C', '26', '266', '2661' ],
        },
        {
          key: '有色金属矿采选业',
          value: [ 'B', '09' ],
        },
        {
          key: '稀有稀土金属矿采选',
          value: [ 'B', '09', '093' ],
        },
        {
          key: '钨 钼矿采选',
          value: [ 'B', '09', '093', '0931' ],
        },
        {
          key: '林产化学产品制造',
          value: [ 'C', '26', '266', '2663' ],
        },
        {
          key: '环保技术推广服务',
          value: [ 'M', '75', '751', '7516' ],
        },
        {
          key: '餐饮配送及外卖送餐服务',
          value: [ 'H', '62', '624' ],
        },
        {
          key: '餐饮配送服务',
          value: [ 'H', '62', '624', '6241' ],
        },
        {
          key: '建筑物清洁服务',
          value: [ 'O', '82', '821', '8211' ],
        },
        {
          key: '服装零售',
          value: [ 'F', '52', '523', '5232' ],
        },
        {
          key: '水上运输业',
          value: [ 'G', '55' ],
        },
        {
          key: '水上货物运输',
          value: [ 'G', '55', '552' ],
        },
        {
          key: '内河货物运输',
          value: [ 'G', '55', '552', '5523' ],
        },
        {
          key: '特种陶瓷制品制造',
          value: [ 'C', '30', '307', '3073' ],
        },
        {
          key: '机动车充电销售',
          value: [ 'F', '52', '526', '5267' ],
        },
        {
          key: '烟草制品业',
          value: [ 'C', '16' ],
        },
        {
          key: '卷烟制造',
          value: [ 'C', '16', '162' ],
        },
        {
          key: '蔬菜、菌类、水果和坚果加工',
          value: [ 'C', '13', '137' ],
        },
        {
          key: '蔬菜加工',
          value: [ 'C', '13', '137', '1371' ],
        },
        {
          key: '锅炉及原动设备制造',
          value: [ 'C', '34', '341' ],
        },
        {
          key: '锅炉及辅助设备制造',
          value: [ 'C', '34', '341', '3411' ],
        },
        {
          key: '水轮机及辅机制造',
          value: [ 'C', '34', '341', '3414' ],
        },
        {
          key: '轻质建筑材料制造',
          value: [ 'C', '30', '302', '3024' ],
        },
        {
          key: ' 野生动物保护',
          value: [ 'N', '77', '771', '7713' ],
        },
        {
          key: '安全保护服务',
          value: [ 'L', '72', '727' ],
        },
        {
          key: '其他安全保护服务',
          value: [ 'L', '72', '727', '7279' ],
        },
        {
          key: '金属加工机械制造',
          value: [ 'C', '34', '342' ],
        },
        {
          key: '铸造机械制造',
          value: [ 'C', '34', '342', '3423' ],
        },
        {
          key: '其他烟草制品制造',
          value: [ 'C', '16', '169' ],
        },
        {
          key: '铁路机车车辆配件制造',
          value: [ 'C', '37', '371', '3715' ],
        },
        {
          key: '航天相关设备制造',
          value: [ 'C', '37', '374', '3743' ],
        },
        {
          key: '试验机制造',
          value: [ 'C', '40', '401', '4015' ],
        },
        {
          key: '专用仪器仪表制造',
          value: [ 'C', '40', '402' ],
        },
        {
          key: '教学专用仪器制造',
          value: [ 'C', '40', '402', '4026' ],
        },
        {
          key: '专用设备修理',
          value: [ 'C', '43', '433' ],
        },
        {
          key: '其他计算机制造',
          value: [ 'C', '39', '391', '3919' ],
        },
        {
          key: '农村集体经济组织管理',
          value: [ 'L', '72', '721', '7215' ],
        },
        {
          key: '造林和更新',
          value: [ 'A', '02', '022' ],
        },
        {
          key: '海洋工程建筑',
          value: [ 'E', '48', '483' ],
        },
        {
          key: '海底设施铺设工程建筑',
          value: [ 'E', '48', '483', '4834' ],
        },
        {
          key: '邮政业',
          value: [ 'G', '60' ],
        },
        {
          key: '快递服务',
          value: [ 'G', '60', '602' ],
        },
        {
          key: '集成电路设计',
          value: [ 'I', '65', '652' ],
        },
        {
          key: '化工、木材、非金属加工专用设备制造',
          value: [ 'C', '35', '352' ],
        },
        {
          key: '橡胶加工专用设备制造',
          value: [ 'C', '35', '352', '3522' ],
        },
        {
          key: '电车制 造',
          value: [ 'C', '36', '365' ],
        },
        {
          key: '其他铁路运输设备制造',
          value: [ 'C', '37', '371', '3719' ],
        },
        {
          key: '体育',
          value: [ 'R', '89' ],
        },
        {
          key: '体育场地设施管理',
          value: [ 'R', '89', '892' ],
        },
        {
          key: '体育场馆管理',
          value: [ 'R', '89', '892', '8921' ],
        },
        {
          key: '医药及医疗器材专门零售',
          value: [ 'F', '52', '525' ],
        },
        {
          key: '动物用药品零售',
          value: [ 'F', '52', '525', '5253' ],
        },
        {
          key: '城 市配送',
          value: [ 'G', '54', '543', '5437' ],
        },
        {
          key: '文化艺术业',
          value: [ 'R', '88' ],
        },
        {
          key: '博物馆',
          value: [ 'R', '88', '885' ],
        },
        {
          key: '日用电器修理',
          value: [ 'O', '81', '813', '8132' ],
        },
        {
          key: '复印和胶印设备制造',
          value: [ 'C', '34', '347', '3474' ],
        },
        {
          key: '药用辅料及包装材料',
          value: [ 'C', '27', '278' ],
        },
        {
          key: '公共电汽车客运',
          value: [ 'G', '54', '541', '5411' ],
        },
        {
          key: '非货币银行服务',
          value: [ 'J', '66', '663' ],
        },
        {
          key: '财务公司服务',
          value: [ 'J', '66', '663', '6632' ],
        },
        {
          key: '酒、饮料和精制茶 制造业',
          value: [ 'C', '15' ],
        },
        {
          key: '饮料制造',
          value: [ 'C', '15', '152' ],
        },
        {
          key: '碳酸饮料制造',
          value: [ 'C', '15', '152', '1521' ],
        },
        {
          key: '其他机械和设备修理业',
          value: [ 'C', '43', '439' ],
        },
        {
          key: '贵金属冶炼',
          value: [ 'C', '32', '322' ],
        },
        {
          key: '金 冶炼',
          value: [ 'C', '32', '322', '3221' ],
        },
        {
          key: '初等教育',
          value: [ 'P', '83', '832' ],
        },
        {
          key: '成人小学教育',
          value: [ 'P', '83', '832', '8322' ],
        },
        {
          key: '小额贷款公司服务',
          value: [ 'J', '66', '663', '6635' ],
        },
        {
          key: '综合事务管理机构',
          value: [ 'S', '92', '922', '9221' ],
        },
        {
          key: '宠物服务',
          value: [ 'O', '82', '822' ],
        },
        {
          key: '宠物医院服务',
          value: [ 'O', '82', '822', '8222' ],
        },
        {
          key: '新闻和出版业',
          value: [ 'R', '86' ],
        },
        {
          key: '出版业',
          value: [ 'R', '86', '862' ],
        },
        {
          key: '电子出版物出版',
          value: [ 'R', '86', '862', '8625' ],
        },
        {
          key: '国家权力机构',
          value: [ 'S', '92', '921' ],
        },
        {
          key: '其他批发业',
          value: [ 'F', '51', '519' ],
        },
        {
          key: '再生物资回收与批发',
          value: [ 'F', '51', '519', '5191' ],
        },
        {
          key: '谷物、棉花等农产品仓储',
          value: [ 'G', '59', '595' ],
        },
        {
          key: '其 他农产品仓储',
          value: [ 'G', '59', '595', '5959' ],
        },
        {
          key: '产业用纺织制成品制造',
          value: [ 'C', '17', '178' ],
        },
        {
          key: '非织造布 制造',
          value: [ 'C', '17', '178', '1781' ],
        },
        {
          key: '西药批发',
          value: [ 'F', '51', '515', '5151' ],
        },
        {
          key: '经济事务管理机构',
          value: [ 'S', '92', '922', '9225' ],
        },
        {
          key: '木质装饰材料零售',
          value: [ 'F', '52', '528', '5286' ],
        },
        {
          key: '生产专用车辆制造',
          value: [ 'C', '34', '343', '3433' ],
        },
        {
          key: '房地产业',
          value: [ 'K' ],
        },
        {
          key: '房地产开发经营',
          value: [ 'K', '70', '701' ],
        },
        {
          key: '其他农副食品加工',
          value: [ 'C', '13', '139' ],
        },
        {
          key: '其他未列明农副食品加工',
          value: [ 'C', '13', '139', '1399' ],
        },
        {
          key: '塑料 制品业',
          value: [ 'C', '29', '292' ],
        },
        {
          key: '人造草坪制造',
          value: [ 'C', '29', '292', '2928' ],
        },
        {
          key: '牲畜批发',
          value: [ 'F', '51', '511', '5116' ],
        },
        {
          key: '搬家运输',
          value: [ 'G', '54', '543', '5438' ],
        },
        {
          key: '广播电视设备制造',
          value: [ 'C', '39', '393' ],
        },
        {
          key: '广播电视专用配件制造',
          value: [ 'C', '39', '393', '3933' ],
        },
        {
          key: '电力工程施工',
          value: [ 'E', '48', '487' ],
        },
        {
          key: '风能发电工程施工',
          value: [ 'E', '48', '487', '4874' ],
        },
        {
          key: '食品、饮料及烟草制品专门零售',
          value: [ 'F', '52', '522' ],
        },
        {
          key: '果品、蔬菜零售',
          value: [ 'F', '52', '522', '5223' ],
        },
        {
          key: '住宿业',
          value: [ 'H', '61' ],
        },
        {
          key: '民宿服务',
          value: [ 'H', '61', '613' ],
        },
        {
          key: '妇联',
          value: [ 'S', '95', '951', '9512' ],
        },
        {
          key: '互联网信息服务',
          value: [ 'I', '64', '642' ],
        },
        {
          key: ' 互联网游戏服务',
          value: [ 'I', '64', '642', '6422' ],
        },
        {
          key: '电信',
          value: [ 'I', '63', '631' ],
        },
        {
          key: '其他电信服务',
          value: [ 'I', '63', '631', '6319' ],
        },
        {
          key: '能源矿产地质勘查',
          value: [ 'M', '74', '747', '7471' ],
        },
        {
          key: '其他卫生活动',
          value: [ 'Q', '84', '849' ],
        },
        {
          key: '其他未列明卫生服务',
          value: [ 'Q', '84', '849', '8499' ],
        },
        {
          key: '盐加工',
          value: [ 'C', '14', '149', '1494' ],
        },
        {
          key: '其他人造板制造',
          value: [ 'C', '20', '202', '2029' ],
        },
        {
          key: '体育用品及器材零售',
          value: [ 'F', '52', '524', '5242' ],
        },
        {
          key: '信息处理和存储支持服务',
          value: [ 'I', '65', '655' ],
        },
        {
          key: '道路运输辅助活动',
          value: [ 'G', '54', '544' ],
        },
        {
          key: '公 路管理与养护',
          value: [ 'G', '54', '544', '5443' ],
        },
        {
          key: '电视',
          value: [ 'R', '87', '872' ],
        },
        {
          key: '石墨及其他非金属矿物制品 制造',
          value: [ 'C', '30', '309' ],
        },
        {
          key: '石墨及碳素制品制造',
          value: [ 'C', '30', '309', '3091' ],
        },
        {
          key: '汽车整车制造',
          value: [ 'C', '36', '361' ],
        },
        {
          key: '新能源车整车制造',
          value: [ 'C', '36', '361', '3612' ],
        },
        {
          key: '其他非电力家用器具制造',
          value: [ 'C', '38', '386', '3869' ],
        },
        {
          key: '电子真空器件制造',
          value: [ 'C', '39', '397', '3971' ],
        },
        {
          key: '动物胶制造',
          value: [ 'C', '26', '266', '2667' ],
        },
        {
          key: '汽车、摩托车等修理与维护',
          value: [ 'O', '81', '811' ],
        },
        {
          key: '汽车修理与维护',
          value: [ 'O', '81', '811', '8111' ],
        },
        {
          key: '社会人文科学研究',
          value: [ 'M', '73', '735' ],
        },
        {
          key: '金属及金属矿批发',
          value: [ 'F', '51', '516', '5164' ],
        },
        {
          key: '热力生产和供应',
          value: [ 'D', '44', '443' ],
        },
        {
          key: '其他水产品加工',
          value: [ 'C', '13', '136', '1369' ],
        },
        {
          key: '铸造及其他金属制品制造',
          value: [ 'C', '33', '339' ],
        },
        {
          key: '有色金属铸造',
          value: [ 'C', '33', '339', '3392' ],
        },
        {
          key: '羽毛（ 绒）加工',
          value: [ 'C', '19', '194', '1941' ],
        },
        {
          key: '光纤制造',
          value: [ 'C', '38', '383', '3832' ],
        },
        {
          key: '畜牧业',
          value: [ 'A', '03' ],
        },
        {
          key: '其他畜牧业',
          value: [ 'A', '03', '039' ],
        },
        {
          key: '蜜蜂饲养',
          value: [ 'A', '03', '039', '0392' ],
        },
        {
          key: '渔业 专业及辅助性活动',
          value: [ 'A', '05', '054' ],
        },
        {
          key: '鱼苗及鱼种场活动',
          value: [ 'A', '05', '054', '0541' ],
        },
        {
          key: '文化、体 育用品及器材批发',
          value: [ 'F', '51', '514' ],
        },
        {
          key: '文具用品批发',
          value: [ 'F', '51', '514', '5141' ],
        },
        {
          key: '棉印染精加工',
          value: [ 'C', '17', '171', '1713' ],
        },
        {
          key: '计量服务',
          value: [ 'M', '74', '745', '7453' ],
        },
        {
          key: '客运汽车站',
          value: [ 'G', '54', '544', '5441' ],
        },
        {
          key: '工艺美术及礼仪用品制造',
          value: [ 'C', '24', '243' ],
        },
        {
          key: '漆器工艺品制造',
          value: [ 'C', '24', '243', '2433' ],
        },
        {
          key: '化学纤维制造业',
          value: [ 'C', '28' ],
        },
        {
          key: '生物基材料制造',
          value: [ 'C', '28', '283' ],
        },
        {
          key: '生物 基化学纤维制造',
          value: [ 'C', '28', '283', '2831' ],
        },
        {
          key: '稀土金属冶炼',
          value: [ 'C', '32', '323', '3232' ],
        },
        {
          key: '泵、阀门、压缩机及类似机械制造',
          value: [ 'C', '34', '344' ],
        },
        {
          key: '气压动力机械及元件制造',
          value: [ 'C', '34', '344', '3446' ],
        },
        {
          key: '其他饮料作物种植',
          value: [ 'A', '01', '016', '0169' ],
        },
        {
          key: '工程技术与设计服务',
          value: [ 'M', '74', '748' ],
        },
        {
          key: '工程设计活动',
          value: [ 'M', '74', '748', '7484' ],
        },
        {
          key: '果菜汁及果菜汁饮料制造',
          value: [ 'C', '15', '152', '1523' ],
        },
        {
          key: '铁路货物运输',
          value: [ 'G', '53', '532' ],
        },
        {
          key: '氮肥制造',
          value: [ 'C', '26', '262', '2621' ],
        },
        {
          key: '水产品冷冻加工',
          value: [ 'C', '13', '136', '1361' ],
        },
        {
          key: '焙烤食品制造',
          value: [ 'C', '14', '141' ],
        },
        {
          key: '饼干及其他焙烤食品制造',
          value: [ 'C', '14', '141', '1419' ],
        },
        {
          key: '麻纺织及染整精加工',
          value: [ 'C', '17', '173' ],
        },
        {
          key: '麻纤维纺前加工和纺纱',
          value: [ 'C', '17', '173', '1731' ],
        },
        {
          key: '珠宝首饰零售',
          value: [ 'F', '52', '524', '5245' ],
        },
        {
          key: '市政设施管理',
          value: [ 'N', '78', '781' ],
        },
        {
          key: '酒的制造',
          value: [ 'C', '15', '151' ],
        },
        {
          key: '白酒制造',
          value: [ 'C', '15', '151', '1512' ],
        },
        {
          key: '一般旅馆',
          value: [ 'H', '61', '612' ],
        },
        {
          key: '经济型连锁酒店',
          value: [ 'H', '61', '612', '6121' ],
        },
        {
          key: '机动车燃气零售',
          value: [ 'F', '52', '526', '5266' ],
        },
        {
          key: '水利和水运工程建筑',
          value: [ 'E', '48', '482' ],
        },
        {
          key: '河湖治理及防洪设施工程建筑',
          value: [ 'E', '48', '482', '4822' ],
        },
        {
          key: '娃娃玩具制造',
          value: [ 'C', '24', '245', '2455' ],
        },
        {
          key: '游乐园',
          value: [ 'R', '90', '902' ],
        },
        {
          key: '金属切割及焊接设备制造',
          value: [ 'C', '34', '342', '3424' ],
        },
        {
          key: '发电机及发电机组制造',
          value: [ 'C', '38', '381', '3811' ],
        },
        {
          key: '工业颜料制造',
          value: [ 'C', '26', '264', '2643' ],
        },
        {
          key: '支撑软件开发',
          value: [ 'I', '65', '651', '6512' ],
        },
        {
          key: '开采专业及辅助性活动',
          value: [ 'B', '11' ],
        },
        {
          key: '石油和天然气开采专业及辅助性活动',
          value: [ 'B', '11', '112' ],
        },
        {
          key: '其他肥料制造',
          value: [ 'C', '26', '262', '2629' ],
        },
        {
          key: '家用电力器具制造',
          value: [ 'C', '38', '385' ],
        },
        {
          key: '家用制冷电器具制造',
          value: [ 'C', '38', '385', '3851' ],
        },
        {
          key: '铁路运输辅助活动',
          value: [ 'G', '53', '533' ],
        },
        {
          key: '铁路运输维护活动',
          value: [ 'G', '53', '533', '5333' ],
        },
        {
          key: '建筑物拆除和场地准备活动',
          value: [ 'E', '50', '502' ],
        },
        {
          key: '场地准备活动',
          value: [ 'E', '50', '502', '5022' ],
        },
        {
          key: '游艺器材及娱乐用品制造',
          value: [ 'C', '24', '246' ],
        },
        {
          key: '露天游乐场所游乐设备制造',
          value: [ 'C', '24', '246', '2461' ],
        },
        {
          key: '电工仪器仪表制造',
          value: [ 'C', '40', '401', '4012' ],
        },
        {
          key: '生物基、淀粉基新材料制造',
          value: [ 'C', '28', '283', '2832' ],
        },
        {
          key: '互联网安全服务',
          value: [ 'I', '64', '644' ],
        },
        {
          key: '其他电力工程施工',
          value: [ 'E', '48', '487', '4879' ],
        },
        {
          key: '报刊批发',
          value: [ 'F', '51', '514', '5144' ],
        },
        {
          key: '电气安装',
          value: [ 'E', '49', '491' ],
        },
        {
          key: '印刷和记录媒介复制业',
          value: [ 'C', '23' ],
        },
        {
          key: '记录媒介复制',
          value: [ 'C', '23', '233' ],
        },
        {
          key: '含乳饮料和植物蛋白 饮料制造',
          value: [ 'C', '15', '152', '1524' ],
        },
        {
          key: '常用有色金属冶炼',
          value: [ 'C', '32', '321' ],
        },
        {
          key: '铜冶炼',
          value: [ 'C', '32', '321', '3211' ],
        },
        {
          key: '金属工具制造',
          value: [ 'C', '33', '332' ],
        },
        {
          key: '其他金属工具制造',
          value: [ 'C', '33', '332', '3329' ],
        },
        {
          key: '针织或钩针编织物及其制品制造',
          value: [ 'C', '17', '176' ],
        },
        {
          key: '针织或钩针编织物印染精加工',
          value: [ 'C', '17', '176', '1762' ],
        },
        {
          key: '木材加工',
          value: [ 'C', '20', '201' ],
        },
        {
          key: '其他木材加工',
          value: [ 'C', '20', '201', '2019' ],
        },
        {
          key: '票务代理服务',
          value: [ 'L', '72', '729', '7298' ],
        },
        {
          key: '喷枪及类似器具制造',
          value: [ 'C', '34', '346', '3466' ],
        },
        {
          key: '糕点、糖果及糖批发',
          value: [ 'F', '51', '512', '5122' ],
        },
        {
          key: '家具制造业',
          value: [ 'C', '21' ],
        },
        {
          key: '其他家具制造',
          value: [ 'C', '21', '219' ],
        },
        {
          key: '中药饮片加工',
          value: [ 'C', '27', '273' ],
        },
        {
          key: '纺织带和帘子布制造',
          value: [ 'C', '17', '178', '1783' ],
        },
        {
          key: '造纸和纸制品业',
          value: [ 'C', '22' ],
        },
        {
          key: '造纸',
          value: [ 'C', '22', '222' ],
        },
        {
          key: '手工纸制造',
          value: [ 'C', '22', '222', '2222' ],
        },
        {
          key: '洗染服务',
          value: [ 'O', '80', '803' ],
        },
        {
          key: '化妆品及卫生用品零售',
          value: [ 'F', '52', '523', '5234' ],
        },
        {
          key: '日用塑料制品制造',
          value: [ 'C', '29', '292', '2927' ],
        },
        {
          key: '其他土木工程建筑',
          value: [ 'E', '48', '489' ],
        },
        {
          key: '体育场地设施工程施工',
          value: [ 'E', '48', '489', '4892' ],
        },
        {
          key: '其他开采专业及辅助性活动',
          value: [ 'B', '11', '119' ],
        },
        {
          key: '航空旅客运输',
          value: [ 'G', '56', '561', '5611' ],
        },
        {
          key: '水的生产和供应业',
          value: [ 'D', '46' ],
        },
        {
          key: '其他水的处理、利用与分配',
          value: [ 'D', '46', '469' ],
        },
        {
          key: '其他农业',
          value: [ 'A', '01', '019' ],
        },
        {
          key: '木片加工',
          value: [ 'C', '20', '201', '2012' ],
        },
        {
          key: '宠物美容服务',
          value: [ 'O', '82', '822', '8223' ],
        },
        {
          key: '社会工作',
          value: [ 'Q', '85' ],
        },
        {
          key: '提供住宿社会工作',
          value: [ 'Q', '85', '851' ],
        },
        {
          key: '老年人、残疾人养护服务',
          value: [ 'Q', '85', '851', '8514' ],
        },
        {
          key: '贵金属矿采选',
          value: [ 'B', '09', '092' ],
        },
        {
          key: '其他贵金属矿采选',
          value: [ 'B', '09', '092', '0929' ],
        },
        {
          key: '非专业视听设备制造',
          value: [ 'C', '39', '395' ],
        },
        {
          key: '音响设备制造',
          value: [ 'C', '39', '395', '3952' ],
        },
        {
          key: '黑色金属冶炼和 压延加工业',
          value: [ 'C', '31' ],
        },
        {
          key: '钢压延加工',
          value: [ 'C', '31', '313' ],
        },
        {
          key: '汽车旧车零售',
          value: [ 'F', '52', '526', '5262' ],
        },
        {
          key: '刀剪及类似日用金属工具制造',
          value: [ 'C', '33', '332', '3324' ],
        },
        {
          key: '镍氢电池制造',
          value: [ 'C', '38', '384', '3842' ],
        },
        {
          key: '深海石油钻探设备制造',
          value: [ 'C', '35', '351', '3513' ],
        },
        {
          key: '稻谷加工',
          value: [ 'C', '13', '131', '1311' ],
        },
        {
          key: '旅游会展服务',
          value: [ 'L', '72', '728', '7282' ],
        },
        {
          key: '其他原动设备制造',
          value: [ 'C', '34', '341', '3419' ],
        },
        {
          key: '房屋建筑业',
          value: [ 'E', '47' ],
        },
        {
          key: '其他房屋建筑业',
          value: [ 'E', '47', '479' ],
        },
        {
          key: '架线及设备工程建 筑',
          value: [ 'E', '48', '485', '4851' ],
        },
        {
          key: '废弃资源综合利用业',
          value: [ 'C', '42' ],
        },
        {
          key: '金属废料和碎屑加工处理',
          value: [ 'C', '42', '421' ],
        },
        {
          key: '其他专业咨询与调查',
          value: [ 'L', '72', '724', '7249' ],
        },
        {
          key: '临终关怀服务',
          value: [ 'Q', '85', '851', '8515' ],
        },
        {
          key: '其他期货市场服务',
          value: [ 'J', '67', '674', '6749' ],
        },
        {
          key: '其他仪器仪表制造业',
          value: [ 'C', '40', '409' ],
        },
        {
          key: '酒精制造',
          value: [ 'C', '15', '151', '1511' ],
        },
        {
          key: '建筑装饰和装修业',
          value: [ 'E', '50', '501' ],
        },
        {
          key: '公共建筑装饰和装修',
          value: [ 'E', '50', '501', '5011' ],
        },
        {
          key: '行政监督检查机构',
          value: [ 'S', '92', '922', '9226' ],
        },
        {
          key: '社会经济咨询',
          value: [ 'L', '72', '724', '7243' ],
        },
        {
          key: '电子专用材料制造',
          value: [ 'C', '39', '398', '3985' ],
        },
        {
          key: '绳 、索、缆制造',
          value: [ 'C', '17', '178', '1782' ],
        },
        {
          key: '工业自动控制系统装置制造',
          value: [ 'C', '40', '401', '4011' ],
        },
        {
          key: '摩托车修理与维护',
          value: [ 'O', '81', '811', '8113' ],
        },
        {
          key: '固体饮料制造',
          value: [ 'C', '15', '152', '1525' ],
        },
        {
          key: '邮件包裹道路运输',
          value: [ 'G', '54', '543', '5436' ],
        },
        {
          key: '轮胎制造',
          value: [ 'C', '29', '291', '2911' ],
        },
        {
          key: '增材制造装备制造',
          value: [ 'C', '34', '349', '3493' ],
        },
        {
          key: '其他未列明批发业',
          value: [ 'F', '51', '519', '5199' ],
        },
        {
          key: '游艺用品及室内游艺器材制造',
          value: [ 'C', '24', '246', '2462' ],
        },
        {
          key: '节能环保工程施工',
          value: [ 'E', '48', '486' ],
        },
        {
          key: '节能工程施工',
          value: [ 'E', '48', '486', '4861' ],
        },
        {
          key: '其他水泥类似制品制造',
          value: [ 'C', '30', '302', '3029' ],
        },
        {
          key: '电玩具制造',
          value: [ 'C', '24', '245', '2451' ],
        },
        {
          key: '有色金属合金制造',
          value: [ 'C', '32', '324' ],
        },
        {
          key: '金属表面处理及热处理加工',
          value: [ 'C', '33', '336' ],
        },
        {
          key: '棉织造加工',
          value: [ 'C', '17', '171', '1712' ],
        },
        {
          key: '摩托车及零配件批发',
          value: [ 'F', '51', '517', '5173' ],
        },
        {
          key: '制糖业',
          value: [ 'C', '13', '134' ],
        },
        {
          key: '生物质燃料加工',
          value: [ 'C', '25', '254' ],
        },
        {
          key: '生物质致密成型燃料加工',
          value: [ 'C', '25', '254', '2542' ],
        },
        {
          key: '电梯、自动扶梯及升降机制造',
          value: [ 'C', '34', '343', '3435' ],
        },
        {
          key: '水力发电工程施工',
          value: [ 'E', '48', '487', '4872' ],
        },
        {
          key: '其他社会保障',
          value: [ 'S', '94', '949' ],
        },
        {
          key: '小麦 种植',
          value: [ 'A', '01', '011', '0112' ],
        },
        {
          key: '临床检验服务',
          value: [ 'Q', '84', '849', '8492' ],
        },
        {
          key: '毛皮鞣制加工',
          value: [ 'C', '19', '193', '1931' ],
        },
        {
          key: '衡器制造',
          value: [ 'C', '40', '405' ],
        },
        {
          key: '其他农牧产品批发',
          value: [ 'F', '51', '511', '5119' ],
        },
        {
          key: '通信设备零售',
          value: [ 'F', '52', '527', '5274' ],
        },
        {
          key: '建筑装饰用石开采',
          value: [ 'B', '10', '101', '1012' ],
        },
        {
          key: '危险品仓储',
          value: [ 'G', '59', '594' ],
        },
        {
          key: '其他危险品仓储',
          value: [ 'G', '59', '594', '5949' ],
        },
        {
          key: '搪 瓷日用品及其他搪瓷制品制造',
          value: [ 'C', '33', '337', '3379' ],
        },
        {
          key: '电视机制造',
          value: [ 'C', '39', '395', '3951' ],
        },
        {
          key: '污水处理及其再生利用',
          value: [ 'D', '46', '462' ],
        },
        {
          key: '灯具、装饰物品批发',
          value: [ 'F', '51', '513', '5136' ],
        },
        {
          key: ' 其他电子产品零售',
          value: [ 'F', '52', '527', '5279' ],
        },
        {
          key: '合成纤维制造',
          value: [ 'C', '28', '282' ],
        },
        {
          key: '其他合成纤维 制造',
          value: [ 'C', '28', '282', '2829' ],
        },
        {
          key: '计算机及通讯设备经营租赁',
          value: [ 'L', '71', '711', '7114' ],
        },
        {
          key: '其他产业用纺织制成品制造',
          value: [ 'C', '17', '178', '1789' ],
        },
        {
          key: '家用电力器具专用配件制造',
          value: [ 'C', '38', '385', '3857' ],
        },
        {
          key: '基础化学原料制造',
          value: [ 'C', '26', '261' ],
        },
        {
          key: '有机化学原料制造',
          value: [ 'C', '26', '261', '2614' ],
        },
        {
          key: ' 其他文化用品批发',
          value: [ 'F', '51', '514', '5149' ],
        },
        {
          key: '网络借贷服务',
          value: [ 'J', '66', '663', '6637' ],
        },
        {
          key: '石油及制品批发',
          value: [ 'F', '51', '516', '5162' ],
        },
        {
          key: '金属工艺品制造',
          value: [ 'C', '24', '243', '2432' ],
        },
        {
          key: '玻璃仪器制造',
          value: [ 'C', '30', '305', '3053' ],
        },
        {
          key: '耐火材料制品制造',
          value: [ 'C', '30', '308' ],
        },
        {
          key: '石棉制品制造',
          value: [ 'C', '30', '308', '3081' ],
        },
        {
          key: '文化体育娱乐活动与经纪代理服务',
          value: [ 'R', '90', '905' ],
        },
        {
          key: '文化娱乐经纪人',
          value: [ 'R', '90', '905', '9053' ],
        },
        {
          key: '职业技能培训',
          value: [ 'P', '83', '839', '8391' ],
        },
        {
          key: '通信设备制造',
          value: [ 'C', '39', '392' ],
        },
        {
          key: '通信系统设备制造',
          value: [ 'C', '39', '392', '3921' ],
        },
        {
          key: '工程监理服务',
          value: [ 'M', '74', '748', '7482' ],
        },
        {
          key: '贸易经纪与代理',
          value: [ 'F', '51', '518' ],
        },
        {
          key: '其他贸易经纪与代理',
          value: [ 'F', '51', '518', '5189' ],
        },
        {
          key: '综合零售',
          value: [ 'F', '52', '521' ],
        },
        {
          key: '百货零售',
          value: [ 'F', '52', '521', '5211' ],
        },
        {
          key: '房地产中介服务',
          value: [ 'K', '70', '703' ],
        },
        {
          key: '文化艺术培训',
          value: [ 'P', '83', '839', '8393' ],
        },
        {
          key: '化纤织造加工',
          value: [ 'C', '17', '175', '1751' ],
        },
        {
          key: '其他文化用品零售',
          value: [ 'F', '52', '524', '5249' ],
        },
        {
          key: '互联网接入及相关服务',
          value: [ 'I', '64', '641' ],
        },
        {
          key: '非金融机构支付服务',
          value: [ 'J', '69', '693' ],
        },
        {
          key: '农药批发',
          value: [ 'F', '51', '516', '5167' ],
        },
        {
          key: '录音制作',
          value: [ 'R', '87', '877' ],
        },
        {
          key: '再保险',
          value: [ 'J', '68', '683' ],
        },
        {
          key: '烘炉、熔炉及电炉制造',
          value: [ 'C', '34', '346', '3461' ],
        },
        {
          key: '潜水救捞及其他未列明运输设备制造',
          value: [ 'C', '37', '379' ],
        },
        {
          key: '潜水装备制造',
          value: [ 'C', '37', '379', '3791' ],
        },
        {
          key: '体育组织',
          value: [ 'R', '89', '891' ],
        },
        {
          key: '体育竞赛组织',
          value: [ 'R', '89', '891', '8911' ],
        },
        {
          key: '绿化管理',
          value: [ 'N', '78', '784' ],
        },
        {
          key: '盐及调味品批发',
          value: [ 'F', '51', '512', '5125' ],
        },
        {
          key: '渔业产品批发',
          value: [ 'F', '51', '511', '5117' ],
        },
        {
          key: '服装批发',
          value: [ 'F', '51', '513', '5132' ],
        },
        {
          key: '其他海洋工程建筑',
          value: [ 'E', '48', '483', '4839' ],
        },
        {
          key: '天然植物纤维编织工艺品制造',
          value: [ 'C', '24', '243', '2435' ],
        },
        {
          key: '云母制品制造',
          value: [ 'C', '30', '308', '3082' ],
        },
        {
          key: '风动和电动工具制造',
          value: [ 'C', '34', '346', '3465' ],
        },
        {
          key: '通用设备修理',
          value: [ 'C', '43', '432' ],
        },
        {
          key: '炼钢',
          value: [ 'C', '31', '312' ],
        },
        {
          key: '丙纶纤维制造',
          value: [ 'C', '28', '282', '2825' ],
        },
        {
          key: '大型车辆装备修理与维护',
          value: [ 'O', '81', '811', '8112' ],
        },
        {
          key: '锦纶纤维制造',
          value: [ 'C', '28', '282', '2821' ],
        },
        {
          key: '照明器具制造',
          value: [ 'C', '38', '387' ],
        },
        {
          key: '灯用电器附件及其他照明器具制造',
          value: [ 'C', '38', '387', '3879' ],
        },
        {
          key: '食品、酒、饮料及茶生产专用设备制     指主要用于食品、酒、饮料生产及茶制品加',
          value: [ 'C', '35', '353', '3531' ],
        },
        {
          key: '沿海货物运输',
          value: [ 'G', '55', '552', '5522' ],
        },
        {
          key: '水下救捞装备制造',
          value: [ 'C', '37', '379', '3792' ],
        },
        {
          key: '保险资产管理',
          value: [ 'J', '68', '686' ],
        },
        {
          key: '公开募集证券投资基金',
          value: [ 'J', '67', '672' ],
        },
        {
          key: '皮革制品制造',
          value: [ 'C', '19', '192' ],
        },
        {
          key: '皮箱、包（袋）制造',
          value: [ 'C', '19', '192', '1922' ],
        },
        {
          key: '保险监管服务',
          value: [ 'J', '68', '687' ],
        },
        {
          key: '乳制品制造',
          value: [ 'C', '14', '144' ],
        },
        {
          key: '其他乳制品制造',
          value: [ 'C', '14', '144', '1449' ],
        },
        {
          key: '金矿采选',
          value: [ 'B', '09', '092', '0921' ],
        },
        {
          key: '木质家具制造',
          value: [ 'C', '21', '211' ],
        },
        {
          key: '塑料家具制造',
          value: [ 'C', '21', '214' ],
        },
        {
          key: '机制纸及纸板制造',
          value: [ 'C', '22', '222', '2221' ],
        },
        {
          key: '室内娱乐活动',
          value: [ 'R', '90', '901' ],
        },
        {
          key: '歌舞厅娱乐活动',
          value: [ 'R', '90', '901', '9011' ],
        },
        {
          key: '其他电子元件制造',
          value: [ 'C', '39', '398', '3989' ],
        },
        {
          key: '医疗用品及器材批发',
          value: [ 'F', '51', '515', '5154' ],
        },
        {
          key: '宠物饲养',
          value: [ 'O', '82', '822', '8221' ],
        },
        {
          key: '珠宝首饰及有关物品制造',
          value: [ 'C', '24', '243', '2438' ],
        },
        {
          key: '加工纸制造',
          value: [ 'C', '22', '222', '2223' ],
        },
        {
          key: '炸药、火工及焰火产品制造',
          value: [ 'C', '26', '267' ],
        },
        {
          key: '焰火、鞭炮产品制造',
          value: [ 'C', '26', '267', '2672' ],
        },
        {
          key: ' 计算器及货币专用设备制造',
          value: [ 'C', '34', '347', '3475' ],
        },
        {
          key: '医疗仪器设备及器械制造',
          value: [ 'C', '35', '358' ],
        },
        {
          key: '眼镜制造',
          value: [ 'C', '35', '358', '3587' ],
        },
        {
          key: '变压器、整流器和电感器制造',
          value: [ 'C', '38', '382', '3821' ],
        },
        {
          key: '乐器批发',
          value: [ 'F', '51', '514', '5147' ],
        },
        {
          key: '固定电信服务',
          value: [ 'I', '63', '631', '6311' ],
        },
        {
          key: '综合管理 服务',
          value: [ 'L', '72', '722' ],
        },
        {
          key: '供应链管理服务',
          value: [ 'L', '72', '722', '7224' ],
        },
        {
          key: '非公开募集证券投资基金',
          value: [ 'J', '67', '673' ],
        },
        {
          key: '其他非公开募集证券投资基金',
          value: [ 'J', '67', '673', '6739' ],
        },
        {
          key: '健康体检服务',
          value: [ 'Q', '84', '849', '8491' ],
        },
        {
          key: '保健辅助治疗器材零售',
          value: [ 'F', '52', '525', '5255' ],
        },
        {
          key: '屠宰及肉类加工',
          value: [ 'C', '13', '135' ],
        },
        {
          key: '禽类屠宰',
          value: [ 'C', '13', '135', '1352' ],
        },
        {
          key: '再生橡胶制造',
          value: [ 'C', '29', '291', '2914' ],
        },
        {
          key: '文物及非物质文化遗产保护',
          value: [ 'R', '88', '884' ],
        },
        {
          key: '其他航空运输辅助活动',
          value: [ 'G', '56', '563', '5639' ],
        },
        {
          key: '高铁设备、配件制造',
          value: [ 'C', '37', '371', '3714' ],
        },
        {
          key: '其他水利管理业',
          value: [ 'N', '76', '769' ],
        },
        {
          key: '其他非金属加工专用设备制造',
          value: [ 'C', '35', '352', '3529' ],
        },
        {
          key: '工业与专业设计及其他专业技术服务',
          value: [ 'M', '74', '749' ],
        },
        {
          key: '专业设计服务',
          value: [ 'M', '74', '749', '7492' ],
        },
        {
          key: '其他酒制造',
          value: [ 'C', '15', '151', '1519' ],
        },
        {
          key: '三维（3D)打印技术推广服务',
          value: [ 'M', '75', '751', '7517' ],
        },
        {
          key: '集装箱及金属包装容器制造',
          value: [ 'C', '33', '333' ],
        },
        {
          key: '金属压力容器制造',
          value: [ 'C', '33', '333', '3332' ],
        },
        {
          key: '烟草制品零售',
          value: [ 'F', '52', '522', '5227' ],
        },
        {
          key: '铁路、道路、隧道和桥梁工程建筑',
          value: [ 'E', '48', '481' ],
        },
        {
          key: '其他道路、隧道和桥梁工程建筑',
          value: [ 'E', '48', '481', '4819' ],
        },
        {
          key: '家用视听设备零售',
          value: [ 'F', '52', '527', '5271' ],
        },
        {
          key: '水资源专用机械制造',
          value: [ 'C', '35', '359', '3597' ],
        },
        {
          key: '棉、麻、糖、烟草种植',
          value: [ 'A', '01', '013' ],
        },
        {
          key: '棉花种植',
          value: [ 'A', '01', '013', '0131' ],
        },
        {
          key: '软木制品及其他木制品制造',
          value: [ 'C', '20', '203', '2039' ],
        },
        {
          key: '其他一般旅馆',
          value: [ 'H', '61', '612', '6129' ],
        },
        {
          key: '职业初中教育',
          value: [ 'P', '83', '833', '8332' ],
        },
        {
          key: '铅锌冶炼',
          value: [ 'C', '32', '321', '3212' ],
        },
        {
          key: '食品及饲料添加剂制造',
          value: [ 'C', '14', '149', '1495' ],
        },
        {
          key: '环境监测专用仪器仪表制造',
          value: [ 'C', '40', '402', '4021' ],
        },
        {
          key: '社会团体',
          value: [ 'S', '95', '952' ],
        },
        {
          key: '专业性团体',
          value: [ 'S', '95', '952', '9521' ],
        },
        {
          key: '其他体育',
          value: [ 'R', '89', '899' ],
        },
        {
          key: '其他未列明体育',
          value: [ 'R', '89', '899', '8999' ],
        },
        {
          key: '五金零售',
          value: [ 'F', '52', '528', '5281' ],
        },
        {
          key: '其他提供住宿社会救助',
          value: [ 'Q', '85', '851', '8519' ],
        },
        {
          key: '水上运输辅助活动',
          value: [ 'G', '55', '553' ],
        },
        {
          key: '客运港口',
          value: [ 'G', '55', '553', '5531' ],
        },
        {
          key: '林产品采集',
          value: [ 'A', '02', '025' ],
        },
        {
          key: '木竹材林产品采集',
          value: [ 'A', '02', '025', '0251' ],
        },
        {
          key: '阀门和旋塞制造',
          value: [ 'C', '34', '344', '3443' ],
        },
        {
          key: '机场',
          value: [ 'G', '56', '563', '5631' ],
        },
        {
          key: '陈设艺术陶瓷制造',
          value: [ 'C', '30', '307', '3075' ],
        },
        {
          key: '木楼梯制造',
          value: [ 'C', '20', '203', '2033' ],
        },
        {
          key: '牲畜屠宰',
          value: [ 'C', '13', '135', '1351' ],
        },
        {
          key: '特种玻璃制造',
          value: [ 'C', '30', '304', '3042' ],
        },
        {
          key: '有色金属压延加工',
          value: [ 'C', '32', '325' ],
        },
        {
          key: '铜压延加工',
          value: [ 'C', '32', '325', '3251' ],
        },
        {
          key: '中等职业学校教育',
          value: [ 'P', '83', '833', '8336' ],
        },
        {
          key: '建筑材料生产专用机械制造',
          value: [ 'C', '35', '351', '3515' ],
        },
        {
          key: '其他宠物服务',
          value: [ 'O', '82', '822', '8229' ],
        },
        {
          key: '茶馆服务',
          value: [ 'H', '62', '623', '6231' ],
        },
        {
          key: '精神康复服务',
          value: [ 'Q', '85', '851', '8513' ],
        },
        {
          key: '检验检疫服务',
          value: [ 'M', '74', '745', '7451' ],
        },
        {
          key: '林业产品批发',
          value: [ 'F', '51', '511', '5115' ],
        },
        {
          key: '涂料零售',
          value: [ 'F', '52', '528', '5284' ],
        },
        {
          key: '燃气生产和供应业',
          value: [ 'D', '45' ],
        },
        {
          key: '煤气生产和供应业',
          value: [ 'D', '45', '451', '4513' ],
        },
        {
          key: '金融信息服务',
          value: [ 'J', '69', '694' ],
        },
        {
          key: '教育辅助服务',
          value: [ 'P', '83', '839', '8394' ],
        },
        {
          key: '律师及相关法律服务',
          value: [ 'L', '72', '723', '7231' ],
        },
        {
          key: '家用通风电器具制造',
          value: [ 'C', '38', '385', '3853' ],
        },
        {
          key: '牲畜饲养',
          value: [ 'A', '03', '031' ],
        },
        {
          key: '猪的饲养',
          value: [ 'A', '03', '031', '0313' ],
        },
        {
          key: '家禽饲养',
          value: [ 'A', '03', '032' ],
        },
        {
          key: '鸭的饲养',
          value: [ 'A', '03', '032', '0322' ],
        },
        {
          key: '生物药品制品制造',
          value: [ 'C', '27', '276' ],
        },
        {
          key: '生物药品制造',
          value: [ 'C', '27', '276', '2761' ],
        },
        {
          key: '正餐服务',
          value: [ 'H', '62', '621' ],
        },
        {
          key: '蔬菜、食用菌及园艺作物种植',
          value: [ 'A', '01', '014' ],
        },
        {
          key: '其他园艺作 物种植',
          value: [ 'A', '01', '014', '0149' ],
        },
        {
          key: '其他群众团体',
          value: [ 'S', '95', '951', '9519' ],
        },
        {
          key: '其他信息技术服务业',
          value: [ 'I', '65', '659' ],
        },
        {
          key: '其他未列明信息技术服务业',
          value: [ 'I', '65', '659', '6599' ],
        },
        {
          key: '常用有色金属矿 采选',
          value: [ 'B', '09', '091' ],
        },
        {
          key: '铝矿采选',
          value: [ 'B', '09', '091', '0916' ],
        },
        {
          key: '其他饮料及冷饮服务',
          value: [ 'H', '62', '623', '6239' ],
        },
        {
          key: '其他水果种植',
          value: [ 'A', '01', '015', '0159' ],
        },
        {
          key: '密封用填料及类似品制造',
          value: [ 'C', '26', '264', '2646' ],
        },
        {
          key: '工业设计服务',
          value: [ 'M', '74', '749', '7491' ],
        },
        {
          key: '煤炭及制品批发',
          value: [ 'F', '51', '516', '5161' ],
        },
        {
          key: '创业指导服务',
          value: [ 'L', '72', '726', '7264' ],
        },
        {
          key: '其他日用产品修理业',
          value: [ 'O', '81', '819' ],
        },
        {
          key: '自行车修理',
          value: [ 'O', '81', '819', '8191' ],
        },
        {
          key: '床上用品制造',
          value: [ 'C', '17', '177', '1771' ],
        },
        {
          key: '营养和保健品零售',
          value: [ 'F', '52', '522', '5225' ],
        },
        {
          key: '耐火陶瓷制品及其他耐火材料制造',
          value: [ 'C', '30', '308', '3089' ],
        },
        {
          key: '制冷、空调设备制造',
          value: [ 'C', '34', '346', '3464' ],
        },
        {
          key: '放射性金属矿采选',
          value: [ 'B', '09', '093', '0933' ],
        },
        {
          key: '海洋工程装备制造',
          value: [ 'C', '37', '373', '3737' ],
        },
        {
          key: '半导体照明器件制造',
          value: [ 'C', '39', '397', '3975' ],
        },
        {
          key: '互联网零售',
          value: [ 'F', '52', '529', '5292' ],
        },
        {
          key: '印刷、制药、日化及日用品生产专用设',
          value: [ 'C', '35', '354' ],
        },
        {
          key: '制药专用设备制造',
          value: [ 'C', '35', '354', '3544' ],
        },
        {
          key: '雕塑工艺品制造',
          value: [ 'C', '24', '243', '2431' ],
        },
        {
          key: '其他机械设备及电子产品批发',
          value: [ 'F', '51', '517', '5179' ],
        },
        {
          key: '成人高中教育',
          value: [ 'P', '83', '833', '8335' ],
        },
        {
          key: '烟叶复烤',
          value: [ 'C', '16', '161' ],
        },
        {
          key: '纸制品制造',
          value: [ 'C', '22', '223' ],
        },
        {
          key: '其他纸制品制造',
          value: [ 'C', '22', '223', '2239' ],
        },
        {
          key: '外卖送餐服务',
          value: [ 'H', '62', '624', '6242' ],
        },
        {
          key: '煤炭开采和洗选专业及辅助性活动',
          value: [ 'B', '11', '111' ],
        },
        {
          key: '邮政基本服务',
          value: [ 'G', '60', '601' ],
        },
        {
          key: '其他住宿业',
          value: [ 'H', '61', '619' ],
        },
        {
          key: '文化用信息化学品制造',
          value: [ 'C', '26', '266', '2664' ],
        },
        {
          key: '耐火土石开采',
          value: [ 'B', '10', '101', '1013' ],
        },
        {
          key: '麻织造加工',
          value: [ 'C', '17', '173', '1732' ],
        },
        {
          key: '马的饲养',
          value: [ 'A', '03', '031', '0312' ],
        },
        {
          key: '建筑幕墙装饰和装修',
          value: [ 'E', '50', '501', '5013' ],
        },
        {
          key: '工程管理服务',
          value: [ 'M', '74', '748', '7481' ],
        },
        {
          key: '休闲观光活动',
          value: [ 'R', '90', '903' ],
        },
        {
          key: '液力动力机械元件制造',
          value: [ 'C', '34', '344', '3445' ],
        },
        {
          key: '互联网数据服 务',
          value: [ 'I', '64', '645' ],
        },
        {
          key: '隔热和隔音材料制造',
          value: [ 'C', '30', '303', '3034' ],
        },
        {
          key: '海底隧道工程建筑',
          value: [ 'E', '48', '483', '4833' ],
        },
        {
          key: '塑料加工专用设备制造',
          value: [ 'C', '35', '352', '3523' ],
        },
        {
          key: '其他陶瓷制品制造',
          value: [ 'C', '30', '307', '3079' ],
        },
        {
          key: '水资源管理',
          value: [ 'N', '76', '762' ],
        },
        {
          key: '厨具卫具及日用杂品零售',
          value: [ 'F', '52', '523', '5235' ],
        },
        {
          key: '其他常用有色金属冶炼',
          value: [ 'C', '32', '321', '3219' ],
        },
        {
          key: '汽车零部件及配件制造',
          value: [ 'C', '36', '367' ],
        },
        {
          key: '互联网批发',
          value: [ 'F', '51', '519', '5193' ],
        },
        {
          key: '丝绢纺织及印染精加工',
          value: [ 'C', '17', '174' ],
        },
        {
          key: '丝印染精加工',
          value: [ 'C', '17', '174', '1743' ],
        },
        {
          key: '其他化工产品批发',
          value: [ 'F', '51', '516', '5169' ],
        },
        {
          key: '证券期货监管服务',
          value: [ 'J', '67', '675' ],
        },
        {
          key: '乐器制造',
          value: [ 'C', '24', '242' ],
        },
        {
          key: '西乐器制造',
          value: [ 'C', '24', '242', '2422' ],
        },
        {
          key: '货运火车站（场）',
          value: [ 'G', '53', '533', '5332' ],
        },
        {
          key: '其他农业专业及辅助 性活动',
          value: [ 'A', '05', '051', '0519' ],
        },
        {
          key: '其他未列明运输设备制造',
          value: [ 'C', '37', '379', '3799' ],
        },
        {
          key: '中药零售',
          value: [ 'F', '52', '525', '5252' ],
        },
        {
          key: '超级市场零售',
          value: [ 'F', '52', '521', '5212' ],
        },
        {
          key: '长途客运',
          value: [ 'G', '54', '542', '5421' ],
        },
        {
          key: '香蕉等亚热带水果种植',
          value: [ 'A', '01', '015', '0154' ],
        },
        {
          key: '其他未列明专业技术服务业',
          value: [ 'M', '74', '749', '7499' ],
        },
        {
          key: '信息技术咨询服务',
          value: [ 'I', '65', '656' ],
        },
        {
          key: '固体矿产地质勘查',
          value: [ 'M', '74', '747', '7472' ],
        },
        {
          key: '其他道路货物运输',
          value: [ 'G', '54', '543', '5439' ],
        },
        {
          key: '其他文体设备和用品出租',
          value: [ 'L', '71', '712', '7129' ],
        },
        {
          key: '环境治理业',
          value: [ 'N', '77', '772' ],
        },
        {
          key: '危险废物治理',
          value: [ 'N', '77', '772', '7724' ],
        },
        {
          key: '旅游客运',
          value: [ 'G', '54', '542', '5422' ],
        },
        {
          key: '稀土金属矿采选',
          value: [ 'B', '09', '093', '0932' ],
        },
        {
          key: '商业、饮食、服务专用设备制造',
          value: [ 'C', '35', '359', '3594' ],
        },
        {
          key: '人民政协、民主党派',
          value: [ 'S', '93' ],
        },
        {
          key: '人民政协',
          value: [ 'S', '93', '931' ],
        },
        {
          key: '林木育种和育苗',
          value: [ 'A', '02', '021' ],
        },
        {
          key: '林木育苗',
          value: [ 'A', '02', '021', '0212' ],
        },
        {
          key: '劳务派遣服务',
          value: [ 'L', '72', '726', '7263' ],
        },
        {
          key: '导航、测绘、气象及海洋专用仪器制',
          value: [ 'C', '40', '402', '4023' ],
        },
        {
          key: '印刷',
          value: [ 'C', '23', '231' ],
        },
        {
          key: '书、报刊印刷',
          value: [ 'C', '23', '231', '2311' ],
        },
        {
          key: '城市轨道交通工程建筑',
          value: [ 'E', '48', '481', '4814' ],
        },
        {
          key: '其他煤炭采选',
          value: [ 'B', '06', '069' ],
        },
        {
          key: '医疗用品及器材零售',
          value: [ 'F', '52', '525', '5254' ],
        },
        {
          key: '日用陶瓷制品制造',
          value: [ 'C', '30', '307', '3074' ],
        },
        {
          key: '电气设备批发',
          value: [ 'F', '51', '517', '5175' ],
        },
        {
          key: '畜牧渔业饲料批发',
          value: [ 'F', '51', '511', '5113' ],
        },
        {
          key: ' 普通初中教育',
          value: [ 'P', '83', '833', '8331' ],
        },
        {
          key: '肉、禽、蛋、奶及水产品零售',
          value: [ 'F', '52', '522', '5224' ],
        },
        {
          key: '糖果、巧克力及蜜饯制造',
          value: [ 'C', '14', '142' ],
        },
        {
          key: '蜜饯制作',
          value: [ 'C', '14', '142', '1422' ],
        },
        {
          key: '安全服 务',
          value: [ 'L', '72', '727', '7271' ],
        },
        {
          key: '植物油加工',
          value: [ 'C', '13', '133' ],
        },
        {
          key: '食用植物油加工',
          value: [ 'C', '13', '133', '1331' ],
        },
        {
          key: '其他未列明食品制造',
          value: [ 'C', '14', '149', '1499' ],
        },
        {
          key: '笔的制造',
          value: [ 'C', '24', '241', '2412' ],
        },
        {
          key: '粮油零售',
          value: [ 'F', '52', '522', '5221' ],
        },
        {
          key: '建筑工程用机械制造',
          value: [ 'C', '35', '351', '3514' ],
        },
        {
          key: '石棉水泥制品制造',
          value: [ 'C', '30', '302', '3023' ],
        },
        {
          key: '洗浴和保健养生服务',
          value: [ 'O', '80', '805' ],
        },
        {
          key: '洗浴服务',
          value: [ 'O', '80', '805', '8051' ],
        },
        {
          key: '其他未列明零售业',
          value: [ 'F', '52', '529', '5299' ],
        },
        {
          key: '其他 人力资源服务',
          value: [ 'L', '72', '726', '7269' ],
        },
        {
          key: '其他数字内容服务',
          value: [ 'I', '65', '657', '6579' ],
        },
        {
          key: '其他餐饮业',
          value: [ 'H', '62', '629' ],
        },
        {
          key: '其他未列明餐饮业',
          value: [ 'H', '62', '629', '6299' ],
        },
        {
          key: '贵金属压延加工',
          value: [ 'C', '32', '325', '3253' ],
        },
        {
          key: '网吧活动',
          value: [ 'R', '90', '901', '9013' ],
        },
        {
          key: '房地产租赁经营',
          value: [ 'K', '70', '704' ],
        },
        {
          key: '高速铁路旅客运输',
          value: [ 'G', '53', '531', '5311' ],
        },
        {
          key: '旧货零售',
          value: [ 'F', '52', '529', '5295' ],
        },
        {
          key: '铝冶炼',
          value: [ 'C', '32', '321', '3216' ],
        },
        {
          key: '期刊出版',
          value: [ 'R', '86', '862', '8623' ],
        },
        {
          key: '特殊作业机器 人制造',
          value: [ 'C', '34', '349', '3492' ],
        },
        {
          key: '种子种苗培育活动',
          value: [ 'A', '05', '051', '0511' ],
        },
        {
          key: '电子测量仪器制造',
          value: [ 'C', '40', '402', '4028' ],
        },
        {
          key: '水上旅客运输',
          value: [ 'G', '55', '551' ],
        },
        {
          key: '内河旅客运输',
          value: [ 'G', '55', '551', '5512' ],
        },
        {
          key: '其他稀有金属矿采选',
          value: [ 'B', '09', '093', '0939' ],
        },
        {
          key: '建筑物拆除活动',
          value: [ 'E', '50', '502', '5021' ],
        },
        {
          key: '纺织品、针织品及原料批发',
          value: [ 'F', '51', '513', '5131' ],
        },
        {
          key: '鸡的饲养',
          value: [ 'A', '03', '032', '0321' ],
        },
        {
          key: '水文服务',
          value: [ 'N', '76', '764' ],
        },
        {
          key: '助动车等修理与维护',
          value: [ 'O', '81', '811', '8114' ],
        },
        {
          key: '数字出版',
          value: [ 'R', '86', '862', '8626' ],
        },
        {
          key: '专项化学用品制造',
          value: [ 'C', '26', '266', '2662' ],
        },
        {
          key: '银冶炼',
          value: [ 'C', '32', '322', '3222' ],
        },
        {
          key: '集成电路制造',
          value: [ 'C', '39', '397', '3973' ],
        },
        {
          key: '手工具制造',
          value: [ 'C', '33', '332', '3322' ],
        },
        {
          key: '人民法院和人民检察院',
          value: [ 'S', '92', '923' ],
        },
        {
          key: '人民检察院',
          value: [ 'S', '92', '923', '9232' ],
        },
        {
          key: '汽车及零配件批发',
          value: [ 'F', '51', '517', '5172' ],
        },
        {
          key: '化肥批发',
          value: [ 'F', '51', '516', '5166' ],
        },
        {
          key: '天然气生产和供应业',
          value: [ 'D', '45', '451', '4511' ],
        },
        {
          key: '纸和纸板容器制造',
          value: [ 'C', '22', '223', '2231' ],
        },
        {
          key: '箱包零售',
          value: [ 'F', '52', '523', '5237' ],
        },
        {
          key: '商业养老金',
          value: [ 'J', '68', '684' ],
        },
        {
          key: '合成材料制造',
          value: [ 'C', '26', '265' ],
        },
        {
          key: '合成纤维单（聚合）体制造',
          value: [ 'C', '26', '265', '2653' ],
        },
        {
          key: '农副食品加 工专用设备制造',
          value: [ 'C', '35', '353', '3532' ],
        },
        {
          key: '高铁车组制造',
          value: [ 'C', '37', '371', '3711' ],
        },
        {
          key: '畜牧专业及辅助性活动',
          value: [ 'A', '05', '053' ],
        },
        {
          key: '畜牧良种繁殖活动',
          value: [ 'A', '05', '053', '0531' ],
        },
        {
          key: '炼油、化工生 产专用设备制造',
          value: [ 'C', '35', '352', '3521' ],
        },
        {
          key: '切削工具制造',
          value: [ 'C', '33', '332', '3321' ],
        },
        {
          key: '音像制品出租',
          value: [ 'L', '71', '712', '7125' ],
        },
        {
          key: '天使投资',
          value: [ 'J', '67', '673', '6732' ],
        },
        {
          key: '塑胶玩具制造',
          value: [ 'C', '24', '245', '2452' ],
        },
        {
          key: '翻译服务',
          value: [ 'L', '72', '729', '7294' ],
        },
        {
          key: '铁矿采选',
          value: [ 'B', '08', '081' ],
        },
        {
          key: '广播电视传输服务',
          value: [ 'I', '63', '632' ],
        },
        {
          key: '有线广播电视传输服务',
          value: [ 'I', '63', '632', '6321' ],
        },
        {
          key: '其他社会团体',
          value: [ 'S', '95', '952', '9529' ],
        },
        {
          key: '音像制品出版',
          value: [ 'R', '86', '862', '8624' ],
        },
        {
          key: '其他室 内装饰材料零售',
          value: [ 'F', '52', '528', '5289' ],
        },
        {
          key: '出租车客运',
          value: [ 'G', '54', '541', '5413' ],
        },
        {
          key: '水污染治理',
          value: [ 'N', '77', '772', '7721' ],
        },
        {
          key: '乡镇卫生院',
          value: [ 'Q', '84', '842', '8423' ],
        },
        {
          key: '普通货物道路运输',
          value: [ 'G', '54', '543', '5431' ],
        },
        {
          key: '弹射玩具制造',
          value: [ 'C', '24', '245', '2454' ],
        },
        {
          key: '无机碱制造',
          value: [ 'C', '26', '261', '2612' ],
        },
        {
          key: '日用玻璃制品制造',
          value: [ 'C', '30', '305', '3054' ],
        },
        {
          key: '危险化学品仓储',
          value: [ 'G', '59', '594', '5942' ],
        },
        {
          key: '镍钴冶炼',
          value: [ 'C', '32', '321', '3213' ],
        },
        {
          key: '其他互联网平台',
          value: [ 'I', '64', '643', '6439' ],
        },
        {
          key: '娱乐船和运动船制造',
          value: [ 'C', '37', '373', '3733' ],
        },
        {
          key: '其他未列明商务服务业',
          value: [ 'L', '72', '729', '7299' ],
        },
        {
          key: '其他家庭用品批发',
          value: [ 'F', '51', '513', '5139' ],
        },
        {
          key: '饲料加工',
          value: [ 'C', '13', '132' ],
        },
        {
          key: '其他饲料加工',
          value: [ 'C', '13', '132', '1329' ],
        },
        {
          key: '其他常用有色金属矿采选',
          value: [ 'B', '09', '091', '0919' ],
        },
        {
          key: '工业控制计算机及系统制造',
          value: [ 'C', '39', '391', '3914' ],
        },
        {
          key: '柑橘类种植',
          value: [ 'A', '01', '015', '0153' ],
        },
        {
          key: '淀粉及淀粉制品制造',
          value: [ 'C', '13', '139', '1391' ],
        },
        {
          key: '摩托车零部件及配件制造',
          value: [ 'C', '37', '375', '3752' ],
        },
        {
          key: '骆驼饲养',
          value: [ 'A', '03', '031', '0315' ],
        },
        {
          key: '烟草种植',
          value: [ 'A', '01', '013', '0134' ],
        },
        {
          key: '家用空气调节器制造',
          value: [ 'C', '38', '385', '3852' ],
        },
        {
          key: '啤酒制造',
          value: [ 'C', '15', '151', '1513' ],
        },
        {
          key: '缫丝加工',
          value: [ 'C', '17', '174', '1741' ],
        },
        {
          key: '金属制日用品制造',
          value: [ 'C', '33', '338' ],
        },
        {
          key: '金属制餐具和器皿制造',
          value: [ 'C', '33', '338', '3382' ],
        },
        {
          key: '环境卫生管理',
          value: [ 'N', '78', '782' ],
        },
        {
          key: '其他基础化学原料制造',
          value: [ 'C', '26', '261', '2619' ],
        },
        {
          key: '会计、审计及税务服务',
          value: [ 'L', '72', '724', '7241' ],
        },
        {
          key: '环保工程施工',
          value: [ 'E', '48', '486', '4862' ],
        },
        {
          key: '技术玻璃制品制造',
          value: [ 'C', '30', '305', '3051' ],
        },
        {
          key: '制浆和造纸专用设备制造',
          value: [ 'C', '35', '354', '3541' ],
        },
        {
          key: '快餐服务',
          value: [ 'H', '62', '622' ],
        },
        {
          key: '其他电机制造',
          value: [ 'C', '38', '381', '3819' ],
        },
        {
          key: '肥皂及洗涤剂制造',
          value: [ 'C', '26', '268', '2681' ],
        },
        {
          key: '锯材加工',
          value: [ 'C', '20', '201', '2011' ],
        },
        {
          key: '安全系统监控服务',
          value: [ 'L', '72', '727', '7272' ],
        },
        {
          key: '肉制品及副产品加工',
          value: [ 'C', '13', '135', '1353' ],
        },
        {
          key: '城市公园管理',
          value: [ 'N', '78', '785' ],
        },
        {
          key: '其他煤炭加工',
          value: [ 'C', '25', '252', '2529' ],
        },
        {
          key: '金属制厨房用器具制造',
          value: [ 'C', '33', '338', '3381' ],
        },
        {
          key: '机械治疗及病房护理设备制造',
          value: [ 'C', '35', '358', '3585' ],
        },
        {
          key: '土地管理业',
          value: [ 'N', '79' ],
        },
        {
          key: '土地登记代理服务',
          value: [ 'N', '79', '794' ],
        },
        {
          key: '自行车和残疾人座车制造',
          value: [ 'C', '37', '376' ],
        },
        {
          key: '自行车制造',
          value: [ 'C', '37', '376', '3761' ],
        },
        {
          key: '信息安全设备制造',
          value: [ 'C', '39', '391', '3915' ],
        },
        {
          key: ' 野生动物疫源疫病防控监测',
          value: [ 'M', '74', '746', '7463' ],
        },
        {
          key: '非木竹材林产品采集',
          value: [ 'A', '02', '025', '0252' ],
        },
        {
          key: '皮革服装制造',
          value: [ 'C', '19', '192', '1921' ],
        },
        {
          key: '呼叫中心',
          value: [ 'I', '65', '659', '6591' ],
        },
        {
          key: '油墨及类似产品制造',
          value: [ 'C', '26', '264', '2642' ],
        },
        {
          key: '生育保险',
          value: [ 'S', '94', '941', '9415' ],
        },
        {
          key: '环境保护监测',
          value: [ 'M', '74', '746', '7461' ],
        },
        {
          key: '专科疾病防治院（所、站）',
          value: [ 'Q', '84', '843', '8432' ],
        },
        {
          key: '工业机器人制造',
          value: [ 'C', '34', '349', '3491' ],
        },
        {
          key: '汽车车身、挂车制造',
          value: [ 'C', '36', '366' ],
        },
        {
          key: '日用杂品制造',
          value: [ 'C', '41', '411' ],
        },
        {
          key: '其他日用杂品制造',
          value: [ 'C', '41', '411', '4119' ],
        },
        {
          key: '鬃毛加工、制刷及清扫工具制造',
          value: [ 'C', '41', '411', '4111' ],
        },
        {
          key: '酒、饮料及茶叶批发',
          value: [ 'F', '51', '512', '5127' ],
        },
        {
          key: '自然遗迹保护管理',
          value: [ 'N', '77', '771', '7712' ],
        },
        {
          key: '便利店零售',
          value: [ 'F', '52', '521', '5213' ],
        },
        {
          key: '林木育种',
          value: [ 'A', '02', '021', '0211' ],
        },
        {
          key: '复混肥料制造',
          value: [ 'C', '26', '262', '2624' ],
        },
        {
          key: '其他综合零售',
          value: [ 'F', '52', '521', '5219' ],
        },
        {
          key: '自然生态系统保护管理',
          value: [ 'N', '77', '771', '7711' ],
        },
        {
          key: '自来水生产和供应',
          value: [ 'D', '46', '461' ],
        },
        {
          key: '图书馆与档案馆',
          value: [ 'R', '88', '883' ],
        },
        {
          key: '图书馆',
          value: [ 'R', '88', '883', '8831' ],
        },
        {
          key: '玻璃纤维增强塑 料制品制造',
          value: [ 'C', '30', '306', '3062' ],
        },
        {
          key: '皮革、毛皮及其制品加工专用设备制',
          value: [ 'C', '35', '355', '3552' ],
        },
        {
          key: '应用电视设备及其他广播电视设备',
          value: [ 'C', '39', '393', '3939' ],
        },
        {
          key: '灯具零售',
          value: [ 'F', '52', '528', '5282' ],
        },
        {
          key: '广告业',
          value: [ 'L', '72', '725' ],
        },
        {
          key: '其他广告服务',
          value: [ 'L', '72', '725', '7259' ],
        },
        {
          key: '风能原动设 备制造',
          value: [ 'C', '34', '341', '3415' ],
        },
        {
          key: '化学药品原料药制造',
          value: [ 'C', '27', '271' ],
        },
        {
          key: '内陆养殖',
          value: [ 'A', '04', '041', '0412' ],
        },
        {
          key: '光学玻璃制造',
          value: [ 'C', '30', '305', '3052' ],
        },
        {
          key: '内燃机及配件制造',
          value: [ 'C', '34', '341', '3412' ],
        },
        {
          key: '互联网生活服务平台',
          value: [ 'I', '64', '643', '6432' ],
        },
        {
          key: '其他寄递服务',
          value: [ 'G', '60', '609' ],
        },
        {
          key: '体育用品设备出租',
          value: [ 'L', '71', '712', '7122' ],
        },
        {
          key: '报纸出版',
          value: [ 'R', '86', '862', '8622' ],
        },
        {
          key: '园区管理服务',
          value: [ 'L', '72', '722', '7221' ],
        },
        {
          key: '其他毛皮制品加工',
          value: [ 'C', '19', '193', '1939' ],
        },
        {
          key: '其他医疗设备及器械制造',
          value: [ 'C', '35', '358', '3589' ],
        },
        {
          key: '广播电视节目制作及发射设备制造',
          value: [ 'C', '39', '393', '3931' ],
        },
        {
          key: '货运枢纽（站）',
          value: [ 'G', '54', '544', '5442' ],
        },
        {
          key: '皮手套及皮装饰制品制造',
          value: [ 'C', '19', '192', '1923' ],
        },
        {
          key: '计算机整机制造',
          value: [ 'C', '39', '391', '3911' ],
        },
        {
          key: '其他有色金属压延加工',
          value: [ 'C', '32', '325', '3259' ],
        },
        {
          key: '抽纱刺绣工艺品制造',
          value: [ 'C', '24', '243', '2436' ],
        },
        {
          key: '陆地天然气开采',
          value: [ 'B', '07', '072', '0721' ],
        },
        {
          key: '基因工程药物和疫苗制造',
          value: [ 'C', '27', '276', '2762' ],
        },
        {
          key: '其他电子器件制造',
          value: [ 'C', '39', '397', '3979' ],
        },
        {
          key: '宠物食品用品零售',
          value: [ 'F', '52', '529', '5297' ],
        },
        {
          key: '茶叶种植',
          value: [ 'A', '01', '016', '0164' ],
        },
        {
          key: '初级形态塑料及合成树脂制造',
          value: [ 'C', '26', '265', '2651' ],
        },
        {
          key: '硅冶炼',
          value: [ 'C', '32', '321', '3218' ],
        },
        {
          key: '土地登记服务',
          value: [ 'N', '79', '793' ],
        },
        {
          key: '通用仓储',
          value: [ 'G', '59', '592' ],
        },
        {
          key: '核子及核辐射测量 仪器制造',
          value: [ 'C', '40', '402', '4027' ],
        },
        {
          key: '婚姻服务',
          value: [ 'O', '80', '807' ],
        },
        {
          key: '其他食品批发',
          value: [ 'F', '51', '512', '5129' ],
        },
        {
          key: '首饰、工艺品及收藏品批发',
          value: [ 'F', '51', '514', '5146' ],
        },
        {
          key: '调味品、发酵制品制造',
          value: [ 'C', '14', '146' ],
        },
        {
          key: '其他调味品、发酵制品制造',
          value: [ 'C', '14', '146', '1469' ],
        },
        {
          key: '普通高中教育',
          value: [ 'P', '83', '833', '8334' ],
        },
        {
          key: '无机酸制造',
          value: [ 'C', '26', '261', '2611' ],
        },
        {
          key: '畜禽粪污处理活动',
          value: [ 'A', '05', '053', '0532' ],
        },
        {
          key: '体育航空运动服务',
          value: [ 'G', '56', '562', '5623' ],
        },
        {
          key: '保险中介服务',
          value: [ 'J', '68', '685' ],
        },
        {
          key: '保险公估服务',
          value: [ 'J', '68', '685', '6853' ],
        },
        {
          key: '炼焦',
          value: [ 'C', '25', '252', '2521' ],
        },
        {
          key: '纤维素纤维原料及纤维制造',
          value: [ 'C', '28', '281' ],
        },
        {
          key: '化纤浆粕制造',
          value: [ 'C', '28', '281', '2811' ],
        },
        {
          key: '其他建筑 安装业',
          value: [ 'E', '49', '499' ],
        },
        {
          key: '体育场地设施安装',
          value: [ 'E', '49', '499', '4991' ],
        },
        {
          key: '专科医院',
          value: [ 'Q', '84', '841', '8415' ],
        },
        {
          key: '酒吧服务',
          value: [ 'H', '62', '623', '6233' ],
        },
        {
          key: '集装箱制造',
          value: [ 'C', '33', '333', '3331' ],
        },
        {
          key: '测绘地理信息服务',
          value: [ 'M', '74', '744' ],
        },
        {
          key: '其他测绘地理信息服务',
          value: [ 'M', '74', '744', '7449' ],
        },
        {
          key: '火力发电工程施工',
          value: [ 'E', '48', '487', '4871' ],
        },
        {
          key: '保险经纪服务',
          value: [ 'J', '68', '685', '6851' ],
        },
        {
          key: '互联网广告服务',
          value: [ 'L', '72', '725', '7251' ],
        },
        {
          key: '石棉及其他非金属矿采选',
          value: [ 'B', '10', '109' ],
        },
        {
          key: '石墨、滑石采选',
          value: [ 'B', '10', '109', '1092' ],
        },
        {
          key: '黑色金属铸造',
          value: [ 'C', '33', '339', '3391' ],
        },
        {
          key: '家用厨房电器具制造',
          value: [ 'C', '38', '385', '3854' ],
        },
        {
          key: '家用清洁卫生电器具制造',
          value: [ 'C', '38', '385', '3855' ],
        },
        {
          key: '照相器材零售',
          value: [ 'F', '52', '524', '5248' ],
        },
        {
          key: '补充保险',
          value: [ 'S', '94', '942' ],
        },
        {
          key: '宗教组织',
          value: [ 'S', '95', '954' ],
        },
        {
          key: '宗教活动场所服务',
          value: [ 'S', '95', '954', '9542' ],
        },
        {
          key: '金属玩具制造',
          value: [ 'C', '24', '245', '2453' ],
        },
        {
          key: '运输设备及生产用计数仪表制造',
          value: [ 'C', '40', '402', '4022' ],
        },
        {
          key: '文艺创作与表演',
          value: [ 'R', '88', '881' ],
        },
        {
          key: '氨纶纤维制造',
          value: [ 'C', '28', '282', '2826' ],
        },
        {
          key: '特殊教育',
          value: [ 'P', '83', '835' ],
        },
        {
          key: '花卉 种植',
          value: [ 'A', '01', '014', '0143' ],
        },
        {
          key: '塑料包装箱及容器制造',
          value: [ 'C', '29', '292', '2926' ],
        },
        {
          key: '蛋品加工',
          value: [ 'C', '13', '139', '1393' ],
        },
        {
          key: '宗教团体服务',
          value: [ 'S', '95', '954', '9541' ],
        },
        {
          key: '工程和技术研究和试验发展',
          value: [ 'M', '73', '732' ],
        },
        {
          key: '资源与产权交易服务',
          value: [ 'L', '72', '721', '7213' ],
        },
        {
          key: '糕点、面包零售',
          value: [ 'F', '52', '522', '5222' ],
        },
        {
          key: '理发及美容服务',
          value: [ 'O', '80', '804' ],
        },
        {
          key: '米、面制品及食用油批发',
          value: [ 'F', '51', '512', '5121' ],
        },
        {
          key: '谷物仓储',
          value: [ 'G', '59', '595', '5951' ],
        },
        {
          key: '融资租赁服务',
          value: [ 'J', '66', '663', '6631' ],
        },
        {
          key: '中国共产党机关',
          value: [ 'S', '91' ],
        },
        {
          key: '灌溉活动',
          value: [ 'A', '05', '051', '0513' ],
        },
        {
          key: '葡萄酒制造',
          value: [ 'C', '15', '151', '1515' ],
        },
        {
          key: '其他家用电力器具制造',
          value: [ 'C', '38', '385', '3859' ],
        },
        {
          key: '石油钻采专用设备制造',
          value: [ 'C', '35', '351', '3512' ],
        },
        {
          key: '不提供住宿社会工作',
          value: [ 'Q', '85', '852' ],
        },
        {
          key: '社会看护与帮助服 务',
          value: [ 'Q', '85', '852', '8521' ],
        },
        {
          key: '建材批发',
          value: [ 'F', '51', '516', '5165' ],
        },
        {
          key: '露营地服务',
          value: [ 'H', '61', '614' ],
        },
        {
          key: '噪声与振动控制服务',
          value: [ 'N', '77', '772', '7727' ],
        },
        {
          key: '其他采矿业',
          value: [ 'B', '12' ],
        },
        {
          key: '其他专用仪器制造',
          value: [ 'C', '40', '402', '4029' ],
        },
        {
          key: '休闲娱乐用品设备出租',
          value: [ 'L', '71', '712', '7121' ],
        },
        {
          key: '其他自然保护',
          value: [ 'N', '77', '771', '7719' ],
        },
        {
          key: '专项运动器材及配件制造',
          value: [ 'C', '24', '244', '2442' ],
        },
        {
          key: '其他房地产业',
          value: [ 'K', '70', '709' ],
        },
        {
          key: '计算机和办公设备维修',
          value: [ 'O', '81', '812' ],
        },
        {
          key: '通讯设备修 理',
          value: [ 'O', '81', '812', '8122' ],
        },
        {
          key: '艺术表演场馆',
          value: [ 'R', '88', '882' ],
        },
        {
          key: '健康保险',
          value: [ 'J', '68', '681', '6813' ],
        },
        {
          key: '其他道路运输辅助活动',
          value: [ 'G', '54', '544', '5449' ],
        },
        {
          key: '玻璃保温容器制造',
          value: [ 'C', '30', '305', '3056' ],
        },
        {
          key: '托儿所服务',
          value: [ 'O', '80', '802' ],
        },
        {
          key: '其他输配电及控制设备制造',
          value: [ 'C', '38', '382', '3829' ],
        },
        {
          key: '生态保护工程施工',
          value: [ 'E', '48', '486', '4863' ],
        },
        {
          key: '金属包装容器及材料制造',
          value: [ 'C', '33', '333', '3333' ],
        },
        {
          key: '计算机、软件及辅助设备零售',
          value: [ 'F', '52', '527', '5273' ],
        },
        {
          key: '其他建筑安装',
          value: [ 'E', '49', '499', '4999' ],
        },
        {
          key: '文具制造',
          value: [ 'C', '24', '241', '2411' ],
        },
        {
          key: '中药材仓储',
          value: [ 'G', '59', '596' ],
        },
        {
          key: '大气污染治理',
          value: [ 'N', '77', '772', '7722' ],
        },
        {
          key: '林产品初级加工活动',
          value: [ 'A', '05', '052', '0523' ],
        },
        {
          key: '其他体育用品制造',
          value: [ 'C', '24', '244', '2449' ],
        },
        {
          key: '其他家禽饲养',
          value: [ 'A', '03', '032', '0329' ],
        },
        {
          key: '船用配套设备制造',
          value: [ 'C', '37', '373', '3734' ],
        },
        {
          key: '艺术品、收藏品拍卖',
          value: [ 'F', '51', '518', '5183' ],
        },
        {
          key: '航空相关设备制造',
          value: [ 'C', '37', '374', '3744' ],
        },
        {
          key: '助动车制造',
          value: [ 'C', '37', '377' ],
        },
        {
          key: '渔业机械制造',
          value: [ 'C', '35', '357', '3575' ],
        },
        {
          key: '土地调查评估服务',
          value: [ 'N', '79', '792' ],
        },
        {
          key: '规划设计管理',
          value: [ 'M', '74', '748', '7485' ],
        },
        {
          key: '物业管理',
          value: [ 'K', '70', '702' ],
        },
        {
          key: '搪瓷卫生洁具制造',
          value: [ 'C', '33', '337', '3373' ],
        },
        {
          key: '精炼石油产品制造',
          value: [ 'C', '25', '251' ],
        },
        {
          key: '原油加工及石油制品制造',
          value: [ 'C', '25', '251', '2511' ],
        },
        {
          key: '口腔科用设备及器具制造',
          value: [ 'C', '35', '358', '3582' ],
        },
        {
          key: '广播影视设备批发',
          value: [ 'F', '51', '517', '5178' ],
        },
        {
          key: '农作物病虫害防治活动',
          value: [ 'A', '05', '051', '0515' ],
        },
        {
          key: '配电开关控制设备制造',
          value: [ 'C', '38', '382', '3823' ],
        },
        {
          key: '包装服务',
          value: [ 'L', '72', '729', '7292' ],
        },
        {
          key: '农林牧渔机械配件制造',
          value: [ 'C', '35', '357', '3576' ],
        },
        {
          key: '客运索道制造',
          value: [ 'C', '34', '343', '3436' ],
        },
        {
          key: '护理机构服务',
          value: [ 'Q', '85', '851', '8512' ],
        },
        {
          key: '其他 城市公共交通运输',
          value: [ 'G', '54', '541', '5419' ],
        },
        {
          key: '体育表演服务',
          value: [ 'R', '90', '905', '9052' ],
        },
        {
          key: '体校及体育培训',
          value: [ 'P', '83', '839', '8392' ],
        },
        {
          key: '电线、电缆制造',
          value: [ 'C', '38', '383', '3831' ],
        },
        {
          key: '教学用模型及教具制造',
          value: [ 'C', '24', '241', '2413' ],
        },
        {
          key: '其他合成材料制造',
          value: [ 'C', '26', '265', '2659' ],
        },
        {
          key: '其他铁路运输辅助活动',
          value: [ 'G', '53', '533', '5339' ],
        },
        {
          key: '肉、禽、蛋、奶及水产品批发',
          value: [ 'F', '51', '512', '5124' ],
        },
        {
          key: '稀有稀土金属压延加工',
          value: [ 'C', '32', '325', '3254' ],
        },
        {
          key: '非金属矿及制品批发',
          value: [ 'F', '51', '516', '5163' ],
        },
        {
          key: '烟草生产专用设备制造',
          value: [ 'C', '35', '353', '3533' ],
        },
        {
          key: '营养和保健品批发',
          value: [ 'F', '51', '512', '5126' ],
        },
        {
          key: '其他不提供住宿社会工作',
          value: [ 'Q', '85', '852', '8529' ],
        },
        {
          key: '体育中介代理服务',
          value: [ 'R', '89', '899', '8991' ],
        },
        {
          key: '其他资本市场服务',
          value: [ 'J', '67', '679' ],
        },
        {
          key: '石油开采',
          value: [ 'B', '07', '071' ],
        },
        {
          key: '海洋石油开采',
          value: [ 'B', '07', '071', '0712' ],
        },
        {
          key: '遥感测绘服务',
          value: [ 'M', '74', '744', '7441' ],
        },
        {
          key: '火力发电',
          value: [ 'D', '44', '441', '4411' ],
        },
        {
          key: '其他未列明服务业',
          value: [ 'O', '82', '829' ],
        },
        {
          key: '瓶（罐）装饮用水制造',
          value: [ 'C', '15', '152', '1522' ],
        },
        {
          key: '村卫生室',
          value: [ 'Q', '84', '842', '8424' ],
        },
        {
          key: '其他未列明日用产品修理业',
          value: [ 'O', '81', '819', '8199' ],
        },
        {
          key: '其他金融信托与管理服务',
          value: [ 'J', '69', '691', '6919' ],
        },
        {
          key: '互联网搜索服务',
          value: [ 'I', '64', '642', '6421' ],
        },
        {
          key: '篷、帆布制造',
          value: [ 'C', '17', '178', '1784' ],
        },
        {
          key: '其他乐器及零件制造',
          value: [ 'C', '24', '242', '2429' ],
        },
        {
          key: '锻件及粉末冶金制品制造',
          value: [ 'C', '33', '339', '3393' ],
        },
        {
          key: '其他畜牧专业及辅助性活动',
          value: [ 'A', '05', '053', '0539' ],
        },
        {
          key: '非食用植物油加工',
          value: [ 'C', '13', '133', '1332' ],
        },
        {
          key: '糕点、面包制造',
          value: [ 'C', '14', '141', '1411' ],
        },
        {
          key: '毛染整精加工',
          value: [ 'C', '17', '172', '1723' ],
        },
        {
          key: '新闻业',
          value: [ 'R', '86', '861' ],
        },
        {
          key: '森林改培',
          value: [ 'A', '02', '023', '0232' ],
        },
        {
          key: '印刷专用设备制造',
          value: [ 'C', '35', '354', '3542' ],
        },
        {
          key: '图书 、报刊零售',
          value: [ 'F', '52', '524', '5243' ],
        },
        {
          key: '其他工艺美术及礼仪用品制造',
          value: [ 'C', '24', '243', '2439' ],
        },
        {
          key: '其他贵金属冶炼',
          value: [ 'C', '32', '322', '3229' ],
        },
        {
          key: '木地板制造',
          value: [ 'C', '20', '203', '2034' ],
        },
        {
          key: '地质勘查专用设备制造',
          value: [ 'C', '35', '359', '3592' ],
        },
        {
          key: '其他专用设备制造',
          value: [ 'C', '35', '359', '3599' ],
        },
        {
          key: '棉花仓储',
          value: [ 'G', '59', '595', '5952' ],
        },
        {
          key: '棕制品制造',
          value: [ 'C', '20', '204', '2043' ],
        },
        {
          key: '意外伤害保险',
          value: [ 'J', '68', '681', '6814' ],
        },
        {
          key: '兽用药品制造',
          value: [ 'C', '27', '275' ],
        },
        {
          key: '天然水收集与分配',
          value: [ 'N', '76', '763' ],
        },
        {
          key: '森林防火活动',
          value: [ 'A', '05', '052', '0522' ],
        },
        {
          key: '塑料零件及其他塑料制品制造',
          value: [ 'C', '29', '292', '2929' ],
        },
        {
          key: '体育健康服务',
          value: [ 'R', '89', '899', '8992' ],
        },
        {
          key: '通讯设备批发',
          value: [ 'F', '51', '517', '5177' ],
        },
        {
          key: '藤制品制造',
          value: [ 'C', '20', '204', '2042' ],
        },
        {
          key: '涤纶纤维制造',
          value: [ 'C', '28', '282', '2822' ],
        },
        {
          key: '果品 、蔬菜批发',
          value: [ 'F', '51', '512', '5123' ],
        },
        {
          key: '气体、液体分离及纯净设备制造',
          value: [ 'C', '34', '346', '3463' ],
        },
        {
          key: '机械化农业及园艺机具制造',
          value: [ 'C', '35', '357', '3572' ],
        },
        {
          key: '服务消费机器人制造',
          value: [ 'C', '39', '396', '3964' ],
        },
        {
          key: '宝石、玉石采选',
          value: [ 'B', '10', '109', '1093' ],
        },
        {
          key: '其他体育场地设施管理',
          value: [ 'R', '89', '892', '8929' ],
        },
        {
          key: '中乐器制造',
          value: [ 'C', '24', '242', '2421' ],
        },
        {
          key: '气象服务',
          value: [ 'M', '74', '741' ],
        },
        {
          key: '其他林业专 业及辅助性活动',
          value: [ 'A', '05', '052', '0529' ],
        },
        {
          key: '建筑、家具用金属配件制造',
          value: [ 'C', '33', '335', '3351' ],
        },
        {
          key: '园艺陶瓷制造',
          value: [ 'C', '30', '307', '3076' ],
        },
        {
          key: '汽柴油车整车制造',
          value: [ 'C', '36', '361', '3611' ],
        },
        {
          key: '农用及园林用金属工具制造',
          value: [ 'C', '33', '332', '3323' ],
        },
        {
          key: '非公路休闲车及零配件制造',
          value: [ 'C', '37', '378' ],
        },
        {
          key: '核电工程施工',
          value: [ 'E', '48', '487', '4873' ],
        },
        {
          key: '园林绿化工程施工',
          value: [ 'E', '48', '489', '4891' ],
        },
        {
          key: ' 地毯、挂毯制造',
          value: [ 'C', '24', '243', '2437' ],
        },
        {
          key: '其他会议、会展及相关服务',
          value: [ 'L', '72', '728', '7289' ],
        },
        {
          key: '金属船舶制造',
          value: [ 'C', '37', '373', '3731' ],
        },
        {
          key: '固体废物治理',
          value: [ 'N', '77', '772', '7723' ],
        },
        {
          key: '塑料人造革、合成革制造',
          value: [ 'C', '29', '292', '2925' ],
        },
        {
          key: '其他国家机构',
          value: [ 'S', '92', '929' ],
        },
        {
          key: '消防管理机构',
          value: [ 'S', '92', '929', '9291' ],
        },
        {
          key: '其他污染治理',
          value: [ 'N', '77', '772', '7729' ],
        },
        {
          key: '群众文体活动',
          value: [ 'R', '88', '887' ],
        },
        {
          key: '其他未列明非金属矿采选',
          value: [ 'B', '10', '109', '1099' ],
        },
        {
          key: '腈纶纤维制造',
          value: [ 'C', '28', '282', '2823' ],
        },
        {
          key: '其他未列明畜牧业',
          value: [ 'A', '03', '039', '0399' ],
        },
        {
          key: '麻染整精加工',
          value: [ 'C', '17', '173', '1733' ],
        },
        {
          key: '其他智能消费设备制造',
          value: [ 'C', '39', '396', '3969' ],
        },
        {
          key: '豆类、油料和薯类种植',
          value: [ 'A', '01', '012' ],
        },
        {
          key: '薯类种植',
          value: [ 'A', '01', '012', '0123' ],
        },
        {
          key: '其他未列明制造业',
          value: [ 'C', '41', '419' ],
        },
        {
          key: '投资与资产管理',
          value: [ 'L', '72', '721', '7212' ],
        },
        {
          key: '黄酒制造',
          value: [ 'C', '15', '151', '1514' ],
        },
        {
          key: '陆地石油开 采',
          value: [ 'B', '07', '071', '0711' ],
        },
        {
          key: '电子和电工机械专用设备制造',
          value: [ 'C', '35', '356' ],
        },
        {
          key: '电子元器件与 机电组件设备制造',
          value: [ 'C', '35', '356', '3563' ],
        },
        {
          key: '装订及印刷相关服务',
          value: [ 'C', '23', '232' ],
        },
        {
          key: '染料制 造',
          value: [ 'C', '26', '264', '2645' ],
        },
        {
          key: '镁矿采选',
          value: [ 'B', '09', '091', '0917' ],
        },
        {
          key: '锂离子电池制造',
          value: [ 'C', '38', '384', '3841' ],
        },
        {
          key: '其他法律服务',
          value: [ 'L', '72', '723', '7239' ],
        },
        {
          key: '其他土地管理服务',
          value: [ 'N', '79', '799' ],
        },
        {
          key: '客运火车站',
          value: [ 'G', '53', '533', '5331' ],
        },
        {
          key: '花画工艺品制造',
          value: [ 'C', '24', '243', '2434' ],
        },
        {
          key: '控股公司服务',
          value: [ 'J', '69', '692' ],
        },
        {
          key: '烈士陵园、纪念馆',
          value: [ 'R', '88', '886' ],
        },
        {
          key: '水产捕捞',
          value: [ 'A', '04', '042' ],
        },
        {
          key: '内陆捕捞',
          value: [ 'A', '04', '042', '0422' ],
        },
        {
          key: '家具和相关物品修理',
          value: [ 'O', '81', '819', '8193' ],
        },
        {
          key: '紧固件制造',
          value: [ 'C', '34', '348', '3482' ],
        },
        {
          key: '照明灯具制造',
          value: [ 'C', '38', '387', '3872' ],
        },
        {
          key: '工伤保险',
          value: [ 'S', '94', '941', '9414' ],
        },
        {
          key: '基金会',
          value: [ 'S', '95', '953' ],
        },
        {
          key: '摩托车及零配 件零售',
          value: [ 'F', '52', '526', '5264' ],
        },
        {
          key: '广播电视接收设备制造',
          value: [ 'C', '39', '393', '3932' ],
        },
        {
          key: '气体压缩机械制造',
          value: [ 'C', '34', '344', '3442' ],
        },
        {
          key: '海洋能源开发利用工程建筑',
          value: [ 'E', '48', '483', '4832' ],
        },
        {
          key: '建筑装饰及水暖管道零件制造',
          value: [ 'C', '33', '335', '3352' ],
        },
        {
          key: '民主党派',
          value: [ 'S', '93', '932' ],
        },
        {
          key: '电影机械 制造',
          value: [ 'C', '34', '347', '3471' ],
        },
        {
          key: '文化会展服务',
          value: [ 'L', '72', '728', '7284' ],
        },
        {
          key: '社区卫生服务中心（站）',
          value: [ 'Q', '84', '842', '8421' ],
        },
        {
          key: '窗帘、布艺类产品制造',
          value: [ 'C', '17', '177', '1773' ],
        },
        {
          key: '管道运输业',
          value: [ 'G', '57' ],
        },
        {
          key: '海底管道运输',
          value: [ 'G', '57', '571' ],
        },
        {
          key: '其他通用仪器制造',
          value: [ 'C', '40', '401', '4019' ],
        },
        {
          key: '环境保护专用设备制造',
          value: [ 'C', '35', '359', '3591' ],
        },
        {
          key: '基本养老保险',
          value: [ 'S', '94', '941', '9411' ],
        },
        {
          key: '职业中介服务',
          value: [ 'L', '72', '726', '7262' ],
        },
        {
          key: '其他食品零售',
          value: [ 'F', '52', '522', '5229' ],
        },
        {
          key: '杂粮加工',
          value: [ 'C', '13', '131', '1314' ],
        },
        {
          key: '基础软件开发',
          value: [ 'I', '65', '651', '6511' ],
        },
        {
          key: '医疗诊断 、监护及治疗设备制造',
          value: [ 'C', '35', '358', '3581' ],
        },
        {
          key: '干部休养所',
          value: [ 'Q', '85', '851', '8511' ],
        },
        {
          key: '科技中介服务',
          value: [ 'M', '75', '753' ],
        },
        {
          key: '铁路工程建筑',
          value: [ 'E', '48', '481', '4811' ],
        },
        {
          key: '工矿工程建筑',
          value: [ 'E', '48', '484' ],
        },
        {
          key: '针织或钩针编织物织造',
          value: [ 'C', '17', '176', '1761' ],
        },
        {
          key: '文化用品设备出租',
          value: [ 'L', '71', '712', '7123' ],
        },
        {
          key: '家具零售',
          value: [ 'F', '52', '528', '5283' ],
        },
        {
          key: '其他未列明国家机构',
          value: [ 'S', '92', '929', '9299' ],
        },
        {
          key: '其他原油制造',
          value: [ 'C', '25', '251', '2519' ],
        },
        {
          key: '其他技术推广服务',
          value: [ 'M', '75', '751', '7519' ],
        },
        {
          key: '基层群众自治组织及其他组织',
          value: [ 'S', '96' ],
        },
        {
          key: '村民自治组织',
          value: [ 'S', '96', '962' ],
        },
        {
          key: ' 门诊部（所）',
          value: [ 'Q', '84', '842', '8425' ],
        },
        {
          key: '邮购及电视、电话零售',
          value: [ 'F', '52', '529', '5293' ],
        },
        {
          key: '其他基本保险',
          value: [ 'S', '94', '941', '9419' ],
        },
        {
          key: '体育场馆建筑',
          value: [ 'E', '47', '472' ],
        },
        {
          key: '互联网科技创新平台',
          value: [ 'I', '64', '643', '6433' ],
        },
        {
          key: '其他互联网服务',
          value: [ 'I', '64', '649' ],
        },
        {
          key: '陆地管道运输',
          value: [ 'G', '57', '572' ],
        },
        {
          key: '太阳能发电',
          value: [ 'D', '44', '441', '4416' ],
        },
        {
          key: '孤残儿童收养和庇护服务',
          value: [ 'Q', '85', '851', '8516' ],
        },
        {
          key: '殡葬服务',
          value: [ 'O', '80', '808' ],
        },
        {
          key: '银矿采选',
          value: [ 'B', '09', '092', '0922' ],
        },
        {
          key: '电子乐 器制造',
          value: [ 'C', '24', '242', '2423' ],
        },
        {
          key: '煤制合成气生产',
          value: [ 'C', '25', '252', '2522' ],
        },
        {
          key: '其他日用品生产专用设备制造',
          value: [ 'C', '35', '354', '3549' ],
        },
        {
          key: '铁路运输设备修理',
          value: [ 'C', '43', '434', '4341' ],
        },
        {
          key: '多式联运和运输代理业',
          value: [ 'G', '58' ],
        },
        {
          key: '运输代理业',
          value: [ 'G', '58', '582' ],
        },
        {
          key: '货物运输代理',
          value: [ 'G', '58', '582', '5821' ],
        },
        {
          key: '泡沫塑料制造',
          value: [ 'C', '29', '292', '2924' ],
        },
        {
          key: '玻璃包装容器制造',
          value: [ 'C', '30', '305', '3055' ],
        },
        {
          key: '泵及真空设备制造',
          value: [ 'C', '34', '344', '3441' ],
        },
        {
          key: '水果和坚果加工',
          value: [ 'C', '13', '137', '1373' ],
        },
        {
          key: '液体乳制造',
          value: [ 'C', '14', '144', '1441' ],
        },
        {
          key: '观光游览航空服务',
          value: [ 'G', '56', '562', '5622' ],
        },
        {
          key: '社会事务管理机构',
          value: [ 'S', '92', '922', '9224' ],
        },
        {
          key: '合成橡胶制造',
          value: [ 'C', '26', '265', '2652' ],
        },
        {
          key: '城市轨道交通设备制造',
          value: [ 'C', '37', '372' ],
        },
        {
          key: '海洋环境服务',
          value: [ 'M', '74', '743', '7432' ],
        },
        {
          key: '茶饮料及其他饮料制造',
          value: [ 'C', '15', '152', '1529' ],
        },
        {
          key: '人民法院',
          value: [ 'S', '92', '923', '9231' ],
        },
        {
          key: '机械式停车设备制造',
          value: [ 'C', '34', '343', '3437' ],
        },
        {
          key: '旅客票务代理',
          value: [ 'G', '58', '582', '5822' ],
        },
        {
          key: '足浴服务',
          value: [ 'O', '80', '805', '8052' ],
        },
        {
          key: '宠物寄托收养服务',
          value: [ 'O', '82', '822', '8224' ],
        },
        {
          key: '创业投资基金',
          value: [ 'J', '67', '673', '6731' ],
        },
        {
          key: '炸药及火工产品制造',
          value: [ 'C', '26', '267', '2671' ],
        },
        {
          key: '电工机械专用设备制造',
          value: [ 'C', '35', '356', '3561' ],
        },
        {
          key: '动物用药品批发',
          value: [ 'F', '51', '515', '5153' ],
        },
        {
          key: '中西医结合医院',
          value: [ 'Q', '84', '841', '8413' ],
        },
        {
          key: '机床功能部件及附件制造',
          value: [ 'C', '34', '342', '3425' ],
        },
        {
          key: '其他文教办公用品制造',
          value: [ 'C', '24', '241', '2419' ],
        },
        {
          key: '智能照明器具制造',
          value: [ 'C', '38', '387', '3874' ],
        },
        {
          key: '窄轨机车车辆制造',
          value: [ 'C', '37', '371', '3713' ],
        },
        {
          key: '锑矿采选',
          value: [ 'B', '09', '091', '0915' ],
        },
        {
          key: '航空航天器修理',
          value: [ 'C', '43', '434', '4343' ],
        },
        {
          key: '影视节目制作',
          value: [ 'R', '87', '873' ],
        },
        {
          key: '牛的饲养',
          value: [ 'A', '03', '031', '0311' ],
        },
        {
          key: '铅锌矿采选',
          value: [ 'B', '09', '091', '0912' ],
        },
        {
          key: '其他谷物磨制',
          value: [ 'C', '13', '131', '1319' ],
        },
        {
          key: '纺织品及针织品零售',
          value: [ 'F', '52', '523', '5231' ],
        },
        {
          key: '体育保障组织',
          value: [ 'R', '89', '891', '8912' ],
        },
        {
          key: '肉、禽类罐头制造',
          value: [ 'C', '14', '145', '1451' ],
        },
        {
          key: '办公服务',
          value: [ 'L', '72', '729', '7293' ],
        },
        {
          key: '兔的饲养',
          value: [ 'A', '03', '039', '0391' ],
        },
        {
          key: '机织服装制造',
          value: [ 'C', '18', '181' ],
        },
        {
          key: '其他机织服装制造',
          value: [ 'C', '18', '181', '1819' ],
        },
        {
          key: '卫生材料及医药用品制造',
          value: [ 'C', '27', '277' ],
        },
        {
          key: '锡冶炼',
          value: [ 'C', '32', '321', '3214' ],
        },
        {
          key: '金 属密封件制造',
          value: [ 'C', '34', '348', '3481' ],
        },
        {
          key: '西药零售',
          value: [ 'F', '52', '525', '5251' ],
        },
        {
          key: '汽车金融公司服务',
          value: [ 'J', '66', '663', '6634' ],
        },
        {
          key: '其他保险活动',
          value: [ 'J', '68', '689' ],
        },
        {
          key: '交通安全、管制及类似专用设 备制造',
          value: [ 'C', '35', '359', '3596' ],
        },
        {
          key: '其他体育组织',
          value: [ 'R', '89', '891', '8919' ],
        },
        {
          key: '机械零部件加工',
          value: [ 'C', '34', '348', '3484' ],
        },
        {
          key: '农药制造',
          value: [ 'C', '26', '263' ],
        },
        {
          key: '生物化学农药及微生物农药制造',
          value: [ 'C', '26', '263', '2632' ],
        },
        {
          key: '轻小型起重设备制造',
          value: [ 'C', '34', '343', '3431' ],
        },
        {
          key: '旅游饭店',
          value: [ 'H', '61', '611' ],
        },
        {
          key: '环保咨询',
          value: [ 'L', '72', '724', '7245' ],
        },
        {
          key: '绢纺和丝织加工',
          value: [ 'C', '17', '174', '1742' ],
        },
        {
          key: '木竹材加工机械制造',
          value: [ 'C', '35', '352', '3524' ],
        },
        {
          key: '医疗、外科及兽医用器械制造',
          value: [ 'C', '35', '358', '3584' ],
        },
        {
          key: '非金属废料和碎屑加工处理',
          value: [ 'C', '42', '422' ],
        },
        {
          key: '知识产权服务',
          value: [ 'M', '75', '752' ],
        },
        {
          key: '一般物品拍卖',
          value: [ 'F', '51', '518', '5182' ],
        },
        {
          key: '塑料薄膜制造',
          value: [ 'C', '29', '292', '2921' ],
        },
        {
          key: '铝 压延加工',
          value: [ 'C', '32', '325', '3252' ],
        },
        {
          key: '纤维板制造',
          value: [ 'C', '20', '202', '2022' ],
        },
        {
          key: ' 生物质液体燃料 生产',
          value: [ 'C', '25', '254', '2541' ],
        },
        {
          key: '防水建筑材料制造',
          value: [ 'C', '30', '303', '3033' ],
        },
        {
          key: '文具用品零售',
          value: [ 'F', '52', '524', '5241' ],
        },
        {
          key: '冶金专用设备制造',
          value: [ 'C', '35', '351', '3516' ],
        },
        {
          key: '商业综合体管理服务',
          value: [ 'L', '72', '722', '7222' ],
        },
        {
          key: '电气信号设备装置制造',
          value: [ 'C', '38', '389', '3891' ],
        },
        {
          key: '其他皮革制品制造',
          value: [ 'C', '19', '192', '1929' ],
        },
        {
          key: '蔬菜种植',
          value: [ 'A', '01', '014', '0141' ],
        },
        {
          key: '农业机械批发',
          value: [ 'F', '51', '517', '5171' ],
        },
        {
          key: '远洋货物运输',
          value: [ 'G', '55', '552', '5521' ],
        },
        {
          key: '动漫、游戏数字内容服务',
          value: [ 'I', '65', '657', '6572' ],
        },
        {
          key: '食用菌加工',
          value: [ 'C', '13', '137', '1372' ],
        },
        {
          key: '中成药生产',
          value: [ 'C', '27', '274' ],
        },
        {
          key: '工程勘察活动',
          value: [ 'M', '74', '748', '7483' ],
        },
        {
          key: '半导体器件专用设备制造',
          value: [ 'C', '35', '356', '3562' ],
        },
        {
          key: '健身休闲活动',
          value: [ 'R', '89', '893' ],
        },
        {
          key: '半导体分立器件制造',
          value: [ 'C', '39', '397', '3972' ],
        },
        {
          key: '锑 冶炼',
          value: [ 'C', '32', '321', '3215' ],
        },
        {
          key: '液压动力机械及元件制造',
          value: [ 'C', '34', '344', '3444' ],
        },
        {
          key: '其他电力生产',
          value: [ 'D', '44', '441', '4419' ],
        },
        {
          key: '其他电池制造',
          value: [ 'C', '38', '384', '3849' ],
        },
        {
          key: '体育彩票服务',
          value: [ 'R', '90', '904', '9041' ],
        },
        {
          key: '单位后勤管理服务',
          value: [ 'L', '72', '721', '7214' ],
        },
        {
          key: '照明器具生产专用设备制造',
          value: [ 'C', '35', '354', '3545' ],
        },
        {
          key: '信息系统集成和物联网技术服务',
          value: [ 'I', '65', '653' ],
        },
        {
          key: '信息系统集成 服务',
          value: [ 'I', '65', '653', '6531' ],
        },
        {
          key: '其他日用品零售',
          value: [ 'F', '52', '523', '5239' ],
        },
        {
          key: '乳粉制造',
          value: [ 'C', '14', '144', '1442' ],
        },
        {
          key: '旅行社及相关服务',
          value: [ 'L', '72', '729', '7291' ],
        },
        {
          key: '计划生育技术服务活动',
          value: [ 'Q', '84', '843', '8436' ],
        },
        {
          key: '橡胶零件制造',
          value: [ 'C', '29', '291', '2913' ],
        },
        {
          key: '家用美容、保健护理电器具制造',
          value: [ 'C', '38', '385', '3856' ],
        },
        {
          key: '草种植',
          value: [ 'A', '01', '018', '0181' ],
        },
        {
          key: '计算机外围设备制造',
          value: [ 'C', '39', '391', '3913' ],
        },
        {
          key: '棉花加工机械制造',
          value: [ 'C', '35', '357', '3577' ],
        },
        {
          key: '其他出版业',
          value: [ 'R', '86', '862', '8629' ],
        },
        {
          key: '皮鞋制造',
          value: [ 'C', '19', '195', '1952' ],
        },
        {
          key: '维纶纤维制造',
          value: [ 'C', '28', '282', '2824' ],
        },
        {
          key: '消费金融公司服务',
          value: [ 'J', '66', '663', '6636' ],
        },
        {
          key: '其他软件开发',
          value: [ 'I', '65', '651', '6519' ],
        },
        {
          key: '塑料丝、绳及编织品制造',
          value: [ 'C', '29', '292', '2923' ],
        },
        {
          key: '市场管理服务',
          value: [ 'L', '72', '722', '7223' ],
        },
        {
          key: '包装专用设备制造',
          value: [ 'C', '34', '346', '3467' ],
        },
        {
          key: '医疗实验室及医用消毒设备和器具',
          value: [ 'C', '35', '358', '3583' ],
        },
        {
          key: '体育经纪人',
          value: [ 'R', '90', '905', '9054' ],
        },
        {
          key: '港口及航运设施工程建筑',
          value: [ 'E', '48', '482', '4823' ],
        },
        {
          key: '日用化工专用设备制造',
          value: [ 'C', '35', '354', '3543' ],
        },
        {
          key: '通用航空生产服务',
          value: [ 'G', '56', '562', '5621' ],
        },
        {
          key: '其他仓储业',
          value: [ 'G', '59', '599' ],
        },
        {
          key: '麻类种植',
          value: [ 'A', '01', '013', '0132' ],
        },
        {
          key: '其他玩具制造',
          value: [ 'C', '24', '245', '2459' ],
        },
        {
          key: '钾肥制造',
          value: [ 'C', '26', '262', '2623' ],
        },
        {
          key: '大型货物道路运输',
          value: [ 'G', '54', '543', '5434' ],
        },
        {
          key: '酱油、食醋及类似制品制造',
          value: [ 'C', '14', '146', '1462' ],
        },
        {
          key: '人造纤维（纤维素纤维）制造',
          value: [ 'C', '28', '281', '2812' ],
        },
        {
          key: '其他稀有金属冶炼',
          value: [ 'C', '32', '323', '3239' ],
        },
        {
          key: '其他未列明金属制品制造',
          value: [ 'C', '33', '339', '3399' ],
        },
        {
          key: '电声器件及零件制造',
          value: [ 'C', '39', '398', '3984' ],
        },
        {
          key: '食用菌种植',
          value: [ 'A', '01', '014', '0142' ],
        },
        {
          key: '建筑用木料及木材组件加工',
          value: [ 'C', '20', '203', '2031' ],
        },
        {
          key: '金属家具制造',
          value: [ 'C', '21', '213' ],
        },
        {
          key: '检测服务',
          value: [ 'M', '74', '745', '7452' ],
        },
        {
          key: '健身器材制造',
          value: [ 'C', '24', '244', '2443' ],
        },
        {
          key: '石灰石、石膏开采',
          value: [ 'B', '10', '101', '1011' ],
        },
        {
          key: '化学农药制造',
          value: [ 'C', '26', '263', '2631' ],
        },
        {
          key: '流动货摊零售',
          value: [ 'F', '52', '529', '5291' ],
        },
        {
          key: '移动电信服务',
          value: [ 'I', '63', '631', '6312' ],
        },
        {
          key: '文化活动服务',
          value: [ 'R', '90', '905', '9051' ],
        },
        {
          key: '飞机制造',
          value: [ 'C', '37', '374', '3741' ],
        },
        {
          key: '植物园管理服务',
          value: [ 'N', '77', '771', '7716' ],
        },
        {
          key: '水泥制造',
          value: [ 'C', '30', '301', '3011' ],
        },
        {
          key: '公路工程建筑',
          value: [ 'E', '48', '481', '4812' ],
        },
        {
          key: '互联网其他信息服务',
          value: [ 'I', '64', '642', '6429' ],
        },
        {
          key: '糖果、巧克力制造',
          value: [ 'C', '14', '142', '1421' ],
        },
        {
          key: '住宅装饰和装修',
          value: [ 'E', '50', '501', '5012' ],
        },
        {
          key: '交通及公共管理用金属标牌制造',
          value: [ 'C', '33', '339', '3394' ],
        },
        {
          key: '物联网技术服务',
          value: [ 'I', '65', '653', '6532' ],
        },
        {
          key: '结构性金属制品制造',
          value: [ 'C', '33', '331' ],
        },
        {
          key: '金属结构制造',
          value: [ 'C', '33', '331', '3311' ],
        },
        {
          key: '地质勘探和地震专用 仪器制造',
          value: [ 'C', '40', '402', '4025' ],
        },
        {
          key: '煤制品制造',
          value: [ 'C', '25', '252', '2524' ],
        },
        {
          key: '包装装潢及其他印刷',
          value: [ 'C', '23', '231', '2319' ],
        },
        {
          key: '妇幼保健院（所、站）',
          value: [ 'Q', '84', '843', '8433' ],
        },
        {
          key: '镁冶炼',
          value: [ 'C', '32', '321', '3217' ],
        },
        {
          key: '纸浆制造',
          value: [ 'C', '22', '221' ],
        },
        {
          key: '木竹浆制造',
          value: [ 'C', '22', '221', '2211' ],
        },
        {
          key: '其他电子专用设备制造',
          value: [ 'C', '35', '356', '3569' ],
        },
        {
          key: '低温仓储',
          value: [ 'G', '59', '593' ],
        },
        {
          key: '行业性团体',
          value: [ 'S', '95', '952', '9522' ],
        },
        {
          key: '锡矿采选',
          value: [ 'B', '09', '091', '0914' ],
        },
        {
          key: '其他罐头食品制 造',
          value: [ 'C', '14', '145', '1459' ],
        },
        {
          key: '口腔清洁用品制造',
          value: [ 'C', '26', '268', '2683' ],
        },
        {
          key: '家庭服务',
          value: [ 'O', '80', '801' ],
        },
        {
          key: '住宅房屋建筑',
          value: [ 'E', '47', '471' ],
        },
        {
          key: '鞋和皮革修理',
          value: [ 'O', '81', '819', '8192' ],
        },
        {
          key: '光缆制造',
          value: [ 'C', '38', '383', '3833' ],
        },
        {
          key: '金属制品修理',
          value: [ 'C', '43', '431' ],
        },
        {
          key: '光学仪器制造',
          value: [ 'C', '40', '404' ],
        },
        {
          key: '草及其他制品制造',
          value: [ 'C', '20', '204', '2049' ],
        },
        {
          key: '电力供应',
          value: [ 'D', '44', '442' ],
        },
        {
          key: '海洋油气资源开发利用工程建筑',
          value: [ 'E', '48', '483', '4831' ],
        },
        {
          key: '建筑用石加工',
          value: [ 'C', '30', '303', '3032' ],
        },
        {
          key: '无机盐制造',
          value: [ 'C', '26', '261', '2613' ],
        },
        {
          key: '商务代理代办服务',
          value: [ 'L', '72', '729', '7297' ],
        },
        {
          key: '其他中药材种植',
          value: [ 'A', '01', '017', '0179' ],
        },
        {
          key: '其他航空航天器制造',
          value: [ 'C', '37', '374', '3749' ],
        },
        {
          key: '玻璃、陶瓷和搪瓷制品生产专用设备',
          value: [ 'C', '35', '354', '3546' ],
        },
        {
          key: '影视录放设备制造',
          value: [ 'C', '39', '395', '3953' ],
        },
        {
          key: '水产品罐头制造',
          value: [ 'C', '14', '145', '1452' ],
        },
        {
          key: '本册印制',
          value: [ 'C', '23', '231', '2312' ],
        },
        {
          key: '货运港口',
          value: [ 'G', '55', '553', '5532' ],
        },
        {
          key: '兽医服务',
          value: [ 'M', '74', '749', '7493' ],
        },
        {
          key: '豆类种植',
          value: [ 'A', '01', '012', '0121' ],
        },
        {
          key: '标准化服务',
          value: [ 'M', '74', '745', '7454' ],
        },
        {
          key: '化学矿开采',
          value: [ 'B', '10', '102' ],
        },
        {
          key: '其他农、林、牧、渔业机械制造',
          value: [ 'C', '35', '357', '3579' ],
        },
        {
          key: '康复辅具制造',
          value: [ 'C', '35', '358', '3586' ],
        },
        {
          key: '电子游艺厅娱乐活动',
          value: [ 'R', '90', '901', '9012' ],
        },
        {
          key: '其他货币银行服务',
          value: [ 'J', '66', '662', '6629' ],
        },
        {
          key: '精制茶加工',
          value: [ 'C', '15', '153' ],
        },
        {
          key: '绘图、计算及测量仪器制造',
          value: [ 'C', '40', '401', '4013' ],
        },
        {
          key: '工会',
          value: [ 'S', '95', '951', '9511' ],
        },
        {
          key: '多式联运',
          value: [ 'G', '58', '581' ],
        },
        {
          key: '船舶改装',
          value: [ 'C', '37', '373', '3735' ],
        },
        {
          key: '农业科学研究和试验发展',
          value: [ 'M', '73', '733' ],
        },
        {
          key: '卫生洁具零售',
          value: [ 'F', '52', '528', '5285' ],
        },
        {
          key: '非木竹浆制造',
          value: [ 'C', '22', '221', '2212' ],
        },
        {
          key: '康复辅具适配服务',
          value: [ 'Q', '85', '852', '8522' ],
        },
        {
          key: '市政道路工程建筑',
          value: [ 'E', '48', '481', '4813' ],
        },
        {
          key: '其他室内娱乐活动',
          value: [ 'R', '90', '901', '9019' ],
        },
        {
          key: '五金产品批发',
          value: [ 'F', '51', '517', '5174' ],
        },
        {
          key: '残疾人座车制造',
          value: [ 'C', '37', '376', '3762' ],
        },
        {
          key: '铁合金冶炼',
          value: [ 'C', '31', '314' ],
        },
        {
          key: '敏感元件及传感器制造',
          value: [ 'C', '39', '398', '3983' ],
        },
        {
          key: '其他非金属矿物制品制造',
          value: [ 'C', '30', '309', '3099' ],
        },
        {
          key: '皮革鞣制加工',
          value: [ 'C', '19', '191' ],
        },
        {
          key: '养生保健服务',
          value: [ 'O', '80', '805', '8053' ],
        },
        {
          key: '通信终端设备制造',
          value: [ 'C', '39', '392', '3922' ],
        },
        {
          key: '核力发电',
          value: [ 'D', '44', '441', '4414' ],
        },
        {
          key: '电光源制造',
          value: [ 'C', '38', '387', '3871' ],
        },
        {
          key: '社区居民自 治组织',
          value: [ 'S', '96', '961' ],
        },
        {
          key: '水、二氧化碳等矿产地质勘查',
          value: [ 'M', '74', '747', '7473' ],
        },
        {
          key: '化妆品及 卫生用品批发',
          value: [ 'F', '51', '513', '5134' ],
        },
        {
          key: '生物质燃气生产和供应业',
          value: [ 'D', '45', '452' ],
        },
        {
          key: '邮政专 用机械及器材制造',
          value: [ 'C', '35', '359', '3593' ],
        },
        {
          key: '普通小学教育',
          value: [ 'P', '83', '832', '8321' ],
        },
        {
          key: '核燃料加工',
          value: [ 'C', '25', '253' ],
        },
        {
          key: '烟煤和无烟煤开采洗选',
          value: [ 'B', '06', '061' ],
        },
        {
          key: '刨花板制造',
          value: [ 'C', '20', '202', '2023' ],
        },
        {
          key: '稻谷种植',
          value: [ 'A', '01', '011', '0111' ],
        },
        {
          key: '石棉、云母矿采选',
          value: [ 'B', '10', '109', '1091' ],
        },
        {
          key: '模具制造',
          value: [ 'C', '35', '352', '3525' ],
        },
        {
          key: '农林牧渔专用仪器仪表制造',
          value: [ 'C', '40', '402', '4024' ],
        },
        {
          key: '铜矿采选',
          value: [ 'B', '09', '091', '0911' ],
        },
        {
          key: '电容器及其配套设备制造',
          value: [ 'C', '38', '382', '3822' ],
        },
        {
          key: '塑料鞋制造',
          value: [ 'C', '19', '195', '1953' ],
        },
        {
          key: '其他土木工程建筑施工',
          value: [ 'E', '48', '489', '4899' ],
        },
        {
          key: '电气设备修理',
          value: [ 'C', '43', '435' ],
        },
        {
          key: '认证认可服务',
          value: [ 'M', '74', '745', '7455' ],
        },
        {
          key: '商业银行服务',
          value: [ 'J', '66', '662', '6621' ],
        },
        {
          key: '塑料板、管、型材制造',
          value: [ 'C', '29', '292', '2922' ],
        },
        {
          key: '自行车等代步设备零售',
          value: [ 'F', '52', '523', '5238' ],
        },
        {
          key: '其他针织或钩针编织服装制造',
          value: [ 'C', '18', '182', '1829' ],
        },
        {
          key: '厨具卫具及日用杂品批发',
          value: [ 'F', '51', '513', '5135' ],
        },
        {
          key: '土地整治服务',
          value: [ 'N', '79', '791' ],
        },
        {
          key: '证券 经纪交易服务',
          value: [ 'J', '67', '671', '6712' ],
        },
        {
          key: '仁果类和核果类水果种植',
          value: [ 'A', '01', '015', '0151' ],
        },
        {
          key: '体育用品及器材批发',
          value: [ 'F', '51', '514', '5142' ],
        },
        {
          key: '其他文化艺术经纪代理',
          value: [ 'R', '90', '905', '9059' ],
        },
        {
          key: '汽轮机及辅机制造',
          value: [ 'C', '34', '341', '3413' ],
        },
        {
          key: '豆制品制造',
          value: [ 'C', '13', '139', '1392' ],
        },
        {
          key: '酒、饮料及茶叶零售',
          value: [ 'F', '52', '522', '5226' ],
        },
        {
          key: '微特电机及组件制造',
          value: [ 'C', '38', '381', '3813' ],
        },
        {
          key: '单板加工',
          value: [ 'C', '20', '201', '2013' ],
        },
        {
          key: '农产品初加工活动',
          value: [ 'A', '05', '051', '0514' ],
        },
        {
          key: '其他物料搬运设备制造',
          value: [ 'C', '34', '343', '3439' ],
        },
        {
          key: '运动防护用具制造',
          value: [ 'C', '24', '244', '2444' ],
        },
        {
          key: '其他专用化学产品制造',
          value: [ 'C', '26', '266', '2669' ],
        },
        {
          key: '客运轮渡运输',
          value: [ 'G', '55', '551', '5513' ],
        },
        {
          key: '香料作物种植',
          value: [ 'A', '01', '016', '0163' ],
        },
        {
          key: '工艺美术品及收藏品零售',
          value: [ 'F', '52', '524', '5246' ],
        },
        {
          key: '电影放映',
          value: [ 'R', '87', '876' ],
        },
        {
          key: '糖料种植',
          value: [ 'A', '01', '013', '0133' ],
        },
        {
          key: '其他方便食品制造',
          value: [ 'C', '14', '143', '1439' ],
        },
        {
          key: '海水捕捞',
          value: [ 'A', '04', '042', '0421' ],
        },
        {
          key: '广播电视集成播控',
          value: [ 'R', '87', '874' ],
        },
        {
          key: '野生植物保护',
          value: [ 'N', '77', '771', '7714' ],
        },
        {
          key: '金融资产管理公司',
          value: [ 'J', '69', '695' ],
        },
        {
          key: '无线广播电视传输服务',
          value: [ 'I', '63', '632', '6322' ],
        },
        {
          key: '滑动轴承制造',
          value: [ 'C', '34', '345', '3452' ],
        },
        {
          key: '营养食品制造',
          value: [ 'C', '14', '149', '1491' ],
        },
        {
          key: '图书批发',
          value: [ 'F', '51', '514', '5143' ],
        },
        {
          key: '市场调查',
          value: [ 'L', '72', '724', '7242' ],
        },
        {
          key: '医学研究和试验发展',
          value: [ 'M', '73', '734' ],
        },
        {
          key: '其他综合管理服务',
          value: [ 'L', '72', '722', '7229' ],
        },
        {
          key: '实验分析仪器制造',
          value: [ 'C', '40', '401', '4014' ],
        },
        {
          key: '小吃服务',
          value: [ 'H', '62', '629', '6291' ],
        },
        {
          key: '炼铁',
          value: [ 'C', '31', '311' ],
        },
        {
          key: '其他组织管理服务',
          value: [ 'L', '72', '721', '7219' ],
        },
        {
          key: '畜牧机械制 造',
          value: [ 'C', '35', '357', '3574' ],
        },
        {
          key: '低速汽车制造',
          value: [ 'C', '36', '364' ],
        },
        {
          key: '电阻电容电感元件制造',
          value: [ 'C', '39', '398', '3981' ],
        },
        {
          key: '其他制鞋业',
          value: [ 'C', '19', '195', '1959' ],
        },
        {
          key: '钟表与计时仪器制造',
          value: [ 'C', '40', '403' ],
        },
        {
          key: '艺术品代理',
          value: [ 'F', '51', '518', '5184' ],
        },
        {
          key: '中央银行服务',
          value: [ 'J', '66', '661' ],
        },
        {
          key: '土地规划服务',
          value: [ 'M', '74', '748', '7486' ],
        },
        {
          key: '运动机织服装制造',
          value: [ 'C', '18', '181', '1811' ],
        },
        {
          key: '种子批发',
          value: [ 'F', '51', '511', '5112' ],
        },
        {
          key: '中医医院',
          value: [ 'Q', '84', '841', '8412' ],
        },
        {
          key: '小麦加工',
          value: [ 'C', '13', '131', '1312' ],
        },
        {
          key: '光伏设备及元器件制造',
          value: [ 'C', '38', '382', '3825' ],
        },
        {
          key: '综合医院',
          value: [ 'Q', '84', '841', '8411' ],
        },
        {
          key: '土壤污染治理与修复服务',
          value: [ 'N', '77', '772', '7726' ],
        },
        {
          key: '危险货物道路运输',
          value: [ 'G', '54', '543', '5435' ],
        },
        {
          key: '宠物食品用品批发',
          value: [ 'F', '51', '519', '5192' ],
        },
        {
          key: '其他非货币银行服务',
          value: [ 'J', '66', '663', '6639' ],
        },
        {
          key: '化妆品制造',
          value: [ 'C', '26', '268', '2682' ],
        },
        {
          key: '典当',
          value: [ 'J', '66', '663', '6633' ],
        },
        {
          key: '体育咨询',
          value: [ 'L', '72', '724', '7246' ],
        },
        {
          key: '液化石油气生产和供应业',
          value: [ 'D', '45', '451', '4512' ],
        },
        {
          key: '金属切削机床制造',
          value: [ 'C', '34', '342', '3421' ],
        },
        {
          key: '海洋气象服务',
          value: [ 'M', '74', '743', '7431' ],
        },
        {
          key: ' 毛巾类制品制造',
          value: [ 'C', '17', '177', '1772' ],
        },
        {
          key: '橡胶鞋制造',
          value: [ 'C', '19', '195', '1954' ],
        },
        {
          key: '非融资担保服务',
          value: [ 'L', '72', '729', '7296' ],
        },
        {
          key: '其他娱乐用品制造',
          value: [ 'C', '24', '246', '2469' ],
        },
        {
          key: '计算机和辅助设备修理',
          value: [ 'O', '81', '812', '8121' ],
        },
        {
          key: '其他未列明建筑业',
          value: [ 'E', '50', '509' ],
        },
        {
          key: '其他金属加工机械制 造',
          value: [ 'C', '34', '342', '3429' ],
        },
        {
          key: '油气仓储',
          value: [ 'G', '59', '594', '5941' ],
        },
        {
          key: '游乐设施工程施工',
          value: [ 'E', '48', '489', '4893' ],
        },
        {
          key: '海上旅客运输',
          value: [ 'G', '55', '551', '5511' ],
        },
        {
          key: '水泥制品制造',
          value: [ 'C', '30', '302', '3021' ],
        },
        {
          key: '齿轮及齿轮减、变速箱制造',
          value: [ 'C', '34', '345', '3453' ],
        },
        {
          key: '信用合作社服务',
          value: [ 'J', '66', '662', '6623' ],
        },
        {
          key: '金属成形机床制造',
          value: [ 'C', '34', '342', '3422' ],
        },
        {
          key: '保险代理服务',
          value: [ 'J', '68', '685', '6852' ],
        },
        {
          key: '太阳能器具制造',
          value: [ 'C', '38', '386', '3862' ],
        },
        {
          key: '鹅的饲养',
          value: [ 'A', '03', '032', '0323' ],
        },
        {
          key: '其他办公设备维修',
          value: [ 'O', '81', '812', '8129' ],
        },
        {
          key: '狩猎和捕捉动物',
          value: [ 'A', '03', '033' ],
        },
        {
          key: '舞台及场地用灯制造',
          value: [ 'C', '38', '387', '3873' ],
        },
        {
          key: '铁路专用设备及器材、配件制造',
          value: [ 'C', '37', '371', '3716' ],
        },
        {
          key: '档案馆',
          value: [ 'R', '88', '883', '8832' ],
        },
        {
          key: '竹、藤家具制造',
          value: [ 'C', '21', '212' ],
        },
        {
          key: '金属门窗 制造',
          value: [ 'C', '33', '331', '3312' ],
        },
        {
          key: '改装汽车制造',
          value: [ 'C', '36', '363' ],
        },
        {
          key: '建筑陶瓷制品制造',
          value: [ 'C', '30', '307', '3071' ],
        },
        {
          key: '其他水上运输辅助活动',
          value: [ 'G', '55', '553', '5539' ],
        },
        {
          key: '疾病预防控制中心',
          value: [ 'Q', '84', '843', '8431' ],
        },
        {
          key: '放射性废物治理',
          value: [ 'N', '77', '772', '7725' ],
        },
        {
          key: '海水淡化处理',
          value: [ 'D', '46', '463' ],
        },
        {
          key: '其他运输代理业',
          value: [ 'G', '58', '582', '5829' ],
        },
        {
          key: '专业音响设备制造',
          value: [ 'C', '39', '393', '3934' ],
        },
        {
          key: '锰矿、铬矿采选',
          value: [ 'B', '08', '082' ],
        },
        {
          key: '建筑装饰搪瓷制品制造',
          value: [ 'C', '33', '337', '3372' ],
        },
        {
          key: '味精制造',
          value: [ 'C', '14', '146', '1461' ],
        },
        {
          key: '图书出版',
          value: [ 'R', '86', '862', '8621' ],
        },
        {
          key: '其他文化艺术业',
          value: [ 'R', '88', '889' ],
        },
        {
          key: '太阳能发电工程施工',
          value: [ 'E', '48', '487', '4875' ],
        },
        {
          key: '其他渔业专业及辅 助性活动',
          value: [ 'A', '05', '054', '0549' ],
        },
        {
          key: '年金保险',
          value: [ 'J', '68', '681', '6812' ],
        },
        {
          key: '针织或钩针编织品制造',
          value: [ 'C', '17', '176', '1763' ],
        },
        {
          key: '日用及医用橡胶制品制造',
          value: [ 'C', '29', '291', '2915' ],
        },
        {
          key: '贸易代理',
          value: [ 'F', '51', '518', '5181' ],
        },
        {
          key: '医学生产用信息化学品制造',
          value: [ 'C', '26', '266', '2665' ],
        },
        {
          key: '毛条和毛纱线加工',
          value: [ 'C', '17', '172', '1721' ],
        },
        {
          key: '其他牲畜饲养',
          value: [ 'A', '03', '031', '0319' ],
        },
        {
          key: '羊的饲养',
          value: [ 'A', '03', '031', '0314' ],
        },
        {
          key: '资本投资服务',
          value: [ 'J', '67', '676' ],
        },
        {
          key: '宠物饲料加工',
          value: [ 'C', '13', '132', '1321' ],
        },
        {
          key: '日用家电批发',
          value: [ 'F', '51', '513', '5138' ],
        },
        {
          key: '自动售货机零售',
          value: [ 'F', '52', '529', '5294' ],
        },
        {
          key: '其他未包括金融业',
          value: [ 'J', '69', '699', '6999' ],
        },
        {
          key: '公共安全管理机构',
          value: [ 'S', '92', '922', '9223' ],
        },
        {
          key: '农村资金互助社服务',
          value: [ 'J', '66', '662', '6624' ],
        },
        {
          key: '水源及供水设施工程建筑',
          value: [ 'E', '48', '482', '4821' ],
        },
        {
          key: '金属制卫生器具制造',
          value: [ 'C', '33', '338', '3383' ],
        },
        {
          key: '电影和广播电视节目发行',
          value: [ 'R', '87', '875' ],
        },
        {
          key: '缝制机械制造',
          value: [ 'C', '35', '355', '3553' ],
        },
        {
          key: '普通铁路旅客运输',
          value: [ 'G', '53', '531', '5313' ],
        },
        {
          key: '其他金属制日用品制造',
          value: [ 'C', '33', '338', '3389' ],
        },
        {
          key: '镍钴矿采选',
          value: [ 'B', '09', '091', '0913' ],
        },
        {
          key: '油料种植',
          value: [ 'A', '01', '012', '0122' ],
        },
        {
          key: '汽车零配件零售',
          value: [ 'F', '52', '526', '5263' ],
        },
        {
          key: '音像制品、 电子和数字出版物批发',
          value: [ 'F', '51', '514', '5145' ],
        },
      ];
      if (!industryField) {
        throw new Error('行业分类不能为空');
      }
      let realIndustry = [];
      for (let i = 0; i < indusJsonData.length; i++) {
        const item = indusJsonData[i];
        if (item.key === industryField) {
          realIndustry = item.value;
          break;
        }
      }
      return [ realIndustry ];
    } catch (error) {
      console.log('error', error);
      throw new Error('处理企业行业分类字段失败' + error);
    }
  }
  // #endregion
}

module.exports = ImportToDbService;
