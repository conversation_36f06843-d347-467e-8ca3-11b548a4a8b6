'use strict';

const Service = require('egg').Service;

/**
 * 基于Redis的任务队列管理服务
 */
class TaskQueueService extends Service {

  constructor(ctx) {
    super(ctx);
    this.redis = this.app.redis;
    this.TASK_PREFIX = 'task:';
    this.PROGRESS_PREFIX = 'progress:';
    this.QUEUE_PREFIX = 'queue:';
    this.RESULT_PREFIX = 'result:';
    this.CHANNEL_PREFIX = 'channel:';
  }

  /**
   * 创建任务
   * @param {String} taskType - 任务类型
   * @param {Object} payload - 任务数据
   * @param {Object} options - 任务选项
   * @return {Promise<String>} 任务ID
   */
  async createTask(taskType, payload = {}, options = {}) {
    const taskId = `${taskType}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    const taskData = {
      taskId,
      taskType,
      payload,
      status: 'pending',
      progress: 0,
      message: '任务已创建',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      ...options,
    };

    // 保存任务信息
    await this.redis.hset(`${this.TASK_PREFIX}${taskId}`, taskData);

    // 设置任务过期时间（24小时）
    await this.redis.expire(`${this.TASK_PREFIX}${taskId}`, 24 * 60 * 60);

    // 将任务加入队列
    await this.redis.lpush(`${this.QUEUE_PREFIX}${taskType}`, taskId);

    this.ctx.logger.info(`任务已创建: ${taskId}`, { taskType, payload });

    return taskId;
  }

  /**
   * 获取任务信息
   * @param {String} taskId - 任务ID
   * @return {Promise<Object|null>} 任务信息
   */
  async getTask(taskId) {
    const taskData = await this.redis.hgetall(`${this.TASK_PREFIX}${taskId}`);

    if (!taskData || Object.keys(taskData).length === 0) {
      return null;
    }

    // 解析JSON字段
    if (taskData.payload) {
      try {
        taskData.payload = JSON.parse(taskData.payload);
      } catch (e) {
        // 忽略解析错误
      }
    }

    return taskData;
  }

  /**
   * 更新任务状态
   * @param {String} taskId - 任务ID
   * @param {Object} updates - 更新数据
   */
  async updateTask(taskId, updates) {
    const updateData = {
      ...updates,
      updatedAt: new Date().toISOString(),
    };

    // 如果有payload，需要序列化
    if (updateData.payload && typeof updateData.payload === 'object') {
      updateData.payload = JSON.stringify(updateData.payload);
    }

    await this.redis.hset(`${this.TASK_PREFIX}${taskId}`, updateData);

    // 发布进度更新事件
    await this.publishProgress(taskId, updateData);
  }

  /**
   * 发布进度更新
   * @param {String} taskId - 任务ID
   * @param {Object} progressData - 进度数据
   */
  async publishProgress(taskId, progressData) {
    const channel = `${this.CHANNEL_PREFIX}${taskId}`;
    const message = JSON.stringify({
      taskId,
      timestamp: new Date().toISOString(),
      ...progressData,
    });

    await this.redis.publish(channel, message);
  }

  /**
   * 订阅任务进度
   * @param {String} taskId - 任务ID
   * @param {Function} callback - 回调函数
   * @return {Object} 订阅对象
   */
  async subscribeProgress(taskId, callback) {
    const subscriber = this.app.redis.duplicate();
    const channel = `${this.CHANNEL_PREFIX}${taskId}`;

    subscriber.on('message', (receivedChannel, message) => {
      if (receivedChannel === channel) {
        try {
          const data = JSON.parse(message);
          callback(data);
        } catch (error) {
          this.ctx.logger.error('解析进度消息失败', error);
        }
      }
    });

    await subscriber.subscribe(channel);

    return {
      unsubscribe: async () => {
        await subscriber.unsubscribe(channel);
        subscriber.disconnect();
      },
    };
  }

  /**
   * 从队列中获取任务
   * @param {String} taskType - 任务类型
   * @param {Number} timeout - 超时时间（秒）
   * @return {Promise<String|null>} 任务ID
   */
  async popTask(taskType, timeout = 10) {
    const result = await this.redis.brpop(`${this.QUEUE_PREFIX}${taskType}`, timeout);
    return result ? result[1] : null;
  }

  /**
   * 获取队列长度
   * @param {String} taskType - 任务类型
   * @return {Promise<Number>} 队列长度
   */
  async getQueueLength(taskType) {
    return await this.redis.llen(`${this.QUEUE_PREFIX}${taskType}`);
  }

  /**
   * 清理过期任务
   * @param {Number} maxAge - 最大年龄（小时）
   */
  async cleanupExpiredTasks(maxAge = 24) {
    const pattern = `${this.TASK_PREFIX}*`;
    const keys = await this.redis.keys(pattern);
    const expiredTime = Date.now() - (maxAge * 60 * 60 * 1000);

    for (const key of keys) {
      const taskData = await this.redis.hgetall(key);
      if (taskData.createdAt) {
        const createdTime = new Date(taskData.createdAt).getTime();
        if (createdTime < expiredTime) {
          await this.redis.del(key);
          this.ctx.logger.info(`清理过期任务: ${key}`);
        }
      }
    }
  }

  /**
   * 获取任务统计信息
   * @param {String} taskType - 任务类型（可选）
   * @return {Promise<Object>} 统计信息
   */
  async getTaskStats(taskType = null) {
    const pattern = taskType ? `${this.TASK_PREFIX}${taskType}_*` : `${this.TASK_PREFIX}*`;
    const keys = await this.redis.keys(pattern);

    const stats = {
      total: 0,
      pending: 0,
      processing: 0,
      success: 0,
      failed: 0,
    };

    for (const key of keys) {
      const taskData = await this.redis.hgetall(key);
      if (taskData.status) {
        stats.total++;
        stats[taskData.status] = (stats[taskData.status] || 0) + 1;
      }
    }

    return stats;
  }

  /**
   * 重试失败的任务
   * @param {String} taskId - 任务ID
   */
  async retryTask(taskId) {
    const task = await this.getTask(taskId);
    if (!task) {
      throw new Error('任务不存在');
    }

    if (task.status !== 'failed') {
      throw new Error('只能重试失败的任务');
    }

    // 重置任务状态
    await this.updateTask(taskId, {
      status: 'pending',
      progress: 0,
      message: '任务已重新加入队列',
      retryCount: (parseInt(task.retryCount) || 0) + 1,
    });

    // 重新加入队列
    await this.redis.lpush(`${this.QUEUE_PREFIX}${task.taskType}`, taskId);

    this.ctx.logger.info(`任务已重试: ${taskId}`);
  }

  /**
   * 取消任务
   * @param {String} taskId - 任务ID
   */
  async cancelTask(taskId) {
    await this.updateTask(taskId, {
      status: 'cancelled',
      message: '任务已取消',
      endTime: new Date().toISOString(),
    });

    this.ctx.logger.info(`任务已取消: ${taskId}`);
  }
}

module.exports = TaskQueueService;
