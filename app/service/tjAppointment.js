const Service = require('egg').Service;
const moment = require('moment');


class tjAppointmentService extends Service {

  // 获取当前员工的信息
  async getUserInfo(userId) {
    const { ctx } = this;
    try {
      if (!ctx.session.user._id) {
        throw new Error('用户信息缺失');
      }
      const res = await this.ctx.model.User.findOne({
        _id: userId || ctx.session.user._id,
      });
      ctx.session.user.gender = res.gender; // 0男 1女
      // 计算年龄
      const today = new Date();
      const birthday = new Date(res.birth);
      ctx.session.user.age = today.getFullYear() - birthday.getFullYear();
      const m = today.getMonth() - birthday.getMonth();
      if (m < 0 || (m === 0 && today.getDate() < birthday.getDate())) {
        ctx.session.user.age--;
      }
      ctx.session.user.name = res.name; // 姓名
      ctx.session.user.idNo = res.idNo; // 身份证
      ctx.session.user.phoneNum = res.phoneNum; // 手机号
      return res;
    } catch (error) {
      throw error;
    }
  }

  // 搜索公司旗下成员
  async getTjEmployee(params) {
    // 传进来计划id和员工姓名
    const { employeeName, tjPlanId } = params;
    const now = new Date();
    const appointDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const match = {
      $or: [
        { 'employee.phoneNum': { $regex: employeeName } },
        { 'employee.name': { $regex: employeeName } },
        { 'employee.IDNum': { $regex: employeeName } },
        { 'employee.unitCode': { $regex: employeeName } },
      ],
    };
    const pipeline = [
      {
        $match: {
          physicalExaminationOrgID: tjPlanId,
          checkEndDate: { $gte: appointDate },
        },
      },
      {
        $unwind: '$employees',
      },
      {
        $lookup: {
          from: 'employees',
          localField: 'employees.employeeId',
          foreignField: '_id',
          as: 'employee',
        },
      },
      {
        $unwind: '$employee',
      },
      {
        $match: match,
      },
      {
        $unwind: { path: '$employee.departs', preserveNullAndEmptyArrays: true },
      },
      {
        $graphLookup: {
          from: 'dingtrees',
          startWith: '$employee.departs',
          connectFromField: 'parentid',
          connectToField: '_id',
          as: 'departsGraph',
          depthField: 'depth',
        },
      },
      {
        $group: {
          _id: '$employee._id',
          physicalExaminationOrgID: { $first: '$physicalExaminationOrgID' },
          physicalExaminationOrgName: { $first: 'physicalExaminationOrgName' },
          EnterpriseID: { $first: 'EnterpriseID' },
          employee: { $first: '$employee' },
          departsGraph: { $first: '$departsGraph' },
          appointmentStatus: { $first: '$employees.appointmentStatus' },
          checkStatus: { $first: '$employees.checkStatus' },
        },
      },
      {
        $unwind: { path: '$departsGraph', preserveNullAndEmptyArrays: true },
      },
      { $sort: { 'departsGraph.depth': -1 } },
      {
        $group: {
          _id: '$employee._id',
          root: { $first: '$$ROOT' },
          departsGraph: { $push: '$departsGraph' },
        },
      },
      {
        $addFields: {
          'root.departsName': {
            $reduce: {
              input: {
                $map: {
                  input: '$departsGraph',
                  as: 'dep',
                  in: {
                    $cond: {
                      if: {
                        $eq: [
                          {
                            $type: '$$dep.shortName',
                          },
                          'missing',
                        ],
                      },
                      then: '$$dep.name',
                      else: '$$dep.shortName',
                    },
                  },
                },
              },
              initialValue: '',
              in: {
                $concat: [
                  '$$value',
                  {
                    $cond: {
                      if: { $eq: [ '$$value', '' ] },
                      then: '',
                      else: '-',
                    },
                  },
                  '$$this',
                ],
              },
            },
          },
        },
      },
      {
        $replaceRoot: { newRoot: '$root' },
      },
      {
        $project: {
          physicalExaminationOrgID: 1,
          physicalExaminationOrgName: 1,
          EnterpriseID: 1,
          departsName: 1,
          appointmentStatus: 1,
          checkStatus: 1,
          employee: {
            _id: 1,
            name: 1,
          },
        },
      },
      { $sort: { name: 1 } },
    ];
    const res = await this.ctx.model.TjPlan.aggregate(pipeline);
    return res;
  }

  async getTjPlan(params) {
    const now = new Date();
    const appointDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    if (params.userId && params.tjPlanId) {
      const { userId, tjPlanId } = params;

      const alreadyReserved = await this.ctx.model.AppointmentDetails.aggregate([
        { $sort: { appointDate: 1 } },
        {
          $match: { // 预约日期在今天及之后
            employeeId: userId,
            tjPlanId,
            // appointDate: { $gte: appointDate },
            status: '0',
          },
        },
        { $limit: 1 },
        {
          $lookup: {
            from: 'users',
            localField: 'employeeId',
            foreignField: '_id',
            as: 'employee',
          },
        },
        {
          $unwind: '$employee',
        },
        {
          $addFields: {
            name: '$employee.name',
            birth: '$employee.birth',
            phoneNum: '$employee.phoneNum',
            idNo: '$employee.idNo',
            gender: '$employee.gender',
          },
        },
        {
          $lookup: {
            from: 'tjPlan',
            localField: 'tjPlanId',
            foreignField: '_id',
            as: 'tjPlan',
          },
        },
        {
          $unwind: {
            path: '$tjPlan',
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $addFields: {
            selectionQuota: '$tjPlan.selectionQuota',
            selectionQuota2: '$tjPlan.selectionQuota2',
          },
        },
        {
          $project: {
            employee: 0,
            tjPlan: 0,
          },
        },
      ]);

      if (alreadyReserved && alreadyReserved.length > 0) {
        const result = alreadyReserved[0];
        result.age = moment().diff(moment(result.birth), 'years');
        result.idNo = result.idNo
          ? result.idNo.replace(/^(\d{6})\d{8}(\d+)/, '$1********$2')
          : '';
        result.phoneNum = result.phoneNum
          ? result.phoneNum.replace(/^(\d{3})\d{4}(\d+)/, '$1****$2')
          : '';
        return result;
      }
      const res = await this.ctx.model.TjPlan.aggregate([
        {
          $match: {
            // _id: tjPlanId,
            _id: tjPlanId,
            checkEndDate: { $gte: appointDate },
          },
        },
        {
          $unwind: '$employees',
        },
        {
          $match: {
            'employees.employeeId': userId,
          },
        },
        {
          $lookup: {
            from: 'users',
            localField: 'employees.employeeId',
            foreignField: '_id',
            as: 'employee',
          },
        },
        {
          $unwind: '$employee',
        },
        {
          $project: {
            EnterpriseID: 1,
            physicalExaminationOrgID: 1,
            physicalExaminationOrgName: 1,
            checkStartDate: 1,
            checkEndDate: 1,
            selectionQuota: 1,
            selectionQuota2: 1,
            EnterpriseInfo: '$EnterpriseInfo',
            harmFactors: '$employees.harmFactors',
            employeeId: '$employees.employeeId',
            name: '$employee.name',
            phoneNum: '$employee.phoneNum',
            idNo: '$employee.idNo',
            birth: '$employee.birth',
            gender: '$employee.gender',
            appointmentStatus: '$employees.appointmentStatus',
            checkStatus: '$employees.checkStatus',
          },
        },
        {
          $sort: {
            appointmentStatus: -1,
          },
        },
      ]);
      if (res && res.length > 0) {
        const result = res[0];
        result.age = moment().diff(moment(result.birth), 'years');
        result.idNo = result.idNo
          ? result.idNo.replace(/^(\d{6})\d{8}(\d+)/, '$1********$2')
          : '';
        result.phoneNum = result.phoneNum
          ? result.phoneNum.replace(/^(\d{3})\d{4}(\d+)/, '$1****$2')
          : '';
        return result;
      }
    } else {
      const userInfo = await this.getUserInfo();
      const EnterpriseID = userInfo.companyId[userInfo.companyId.length - 1];
      // 判断是否为admin管理员
      const adminUser = await this.ctx.model.AdminUser.findOne({ employees: userInfo.employeeId });
      let proxyReserve = [];
      if (adminUser) {
        proxyReserve = await this.ctx.model.PhysicalExamOrg.aggregate([
          {
            $lookup: {
              from: 'tjPlan',
              localField: '_id',
              foreignField: 'physicalExaminationOrgID',
              as: 'tjplan',
            },
          },
          {
            $unwind: '$tjplan',
          },
          {
            $match: {
              'tjplan.EnterpriseID': EnterpriseID,
              'tjplan.checkEndDate': { $gte: appointDate },
              'tjplan.status': 1,
            },
          },
          {
            $group: {
              org: { $first: '$$ROOT' },
              planid: { $push: '$$ROOT.tjplan._id' },
              _id: '$_id',
            },
          },
          {
            $project: {
              EnterpriseID: 1,
              physicalExaminationOrgID: '$_id',
              physicalExaminationOrgName: '$org.name',
              contract: '$org.contract',
              orgPhoneNum: '$org.phoneNum',
              address: '$org.address',
              planid: 1,
            },
          },
        ]);
      }
      const query = { // 结束日期大于等于今天
        EnterpriseID,
        checkEndDate: { $gte: appointDate },
        $or: [{
          employees: {
            $elemMatch: {
              employeeId: userInfo.employeeId,
            },
          }, status: 1,
        }, {
          status: 0,
          employees: {
            $elemMatch: {
              employeeId: userInfo.employeeId,
              appointmentStatus: 1,
            },
          },
        }],
      };
      const { age, gender } = this.ctx.session.user;
      const res = await this.ctx.model.TjPlan.aggregate([
        { $sort: { updatedAt: -1 } },
        { $match: query },
        { $limit: 1 },
        // {
        //   $lookup: {
        //     from: 'physicalExamOrg',
        //     localField: 'physicalExaminationOrgID',
        //     foreignField: '_id',
        //     as: 'PhysicalExamOrgInfo'
        //   }
        // },
        {
          $addFields: {
            employeeInfo: {
              $arrayElemAt: [
                {
                  $filter: {
                    input: '$employees',
                    as: 'item',
                    cond: { $eq: [ '$$item.employeeId', userInfo.employeeId ] },
                  },
                },
                0,
              ],
            },
          },
        },
        {
          $project: {
            EnterpriseID: 1,
            physicalExaminationOrgID: 1,
            physicalExaminationOrgName: 1,
            checkStartDate: 1,
            checkEndDate: 1,
            selectionQuota: 1,
            selectionQuota2: 1,
            EnterpriseInfo: '$EnterpriseInfo',
            harmFactors: '$employeeInfo.harmFactors',
            appointmentStatus: '$employeeInfo.appointmentStatus',
            checkStatus: '$employeeInfo.checkStatus',
            checkDates: 1,
            // PhysicalExamOrgInfo: 1, //
            // contract: { $arrayElemAt: ['$PhysicalExamOrgInfo.contract', 0] },
            // orgPhoneNum: { $arrayElemAt: ['$PhysicalExamOrgInfo.phoneNum', 0] },
            // address: { $arrayElemAt: ['$PhysicalExamOrgInfo.address', 0] },
            age: age + '',
            gender,
            employeeId: userInfo.employeeId,
          },
        },
      ]);
      // console.log(res, 'resresres');
      const org = await this.ctx.model.PhysicalExamOrg.findOne({ _id: res && res.length > 0 ? res[0].physicalExaminationOrgID : '' });
      if (res && res.length > 0) {
        res[0].isAdminUser = !!adminUser;
        res[0].contract = org.contract || '';
        res[0].orgPhoneNum = org.phoneNum || '';
        res[0].address = org.address || '';
        res[0].physicalExaminationOrgName = org.name || '';
        if (!!adminUser && proxyReserve.length > 0) {
          proxyReserve = proxyReserve.filter(item => item.physicalExaminationOrgID !== res[0].physicalExaminationOrgID);
          res[0].proxyReserve = proxyReserve;
        }
        return res;
      }
      return [{ isAdminUser: !!adminUser, proxyReserve: adminUser ? proxyReserve : [] }];
    }
    return [];
  }

  async getTjAppointment(params) {
    const { tjPlanId, employeeId } = params;
    const query = {
      // 预约日期不做限制，只要体检计划尚未结束并且未进行体检
      tjPlanId,
      employeeId: this.ctx.session.user._id,
      // appointDate: { $gte: appointDate },
      status: '0',
    };
    if (employeeId) {
      query.employeeId = employeeId;
      delete query.status;
    }
    const userInfo = await this.getUserInfo();
    // const now = new Date();
    // const appointDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    // const query = { // 预约日期不做限制，只要体检计划尚未结束并且未进行体检
    //   tjPlanId,
    //   employeeId: this.ctx.session.user._id,
    //   // appointDate: { $gte: appointDate },
    //   status: '0',
    // };
    const res = await this.ctx.model.AppointmentDetails.aggregate([
      { $sort: { appointDate: 1 } },
      { $match: query },
      { $limit: 1 },
      {
        $addFields: {
          gender: userInfo.gender,
          age: userInfo.age,
        },
      },
    ]) || [];
    const timeRangeOptions = this.generateTimeSlots('07:30', '10:00');// 可配置时间段 TjPlan传递数组
    if (res.length > 0) {
      res[0].timeRangeOptions = timeRangeOptions;
    }
    return res;
  }

  // 创建体检预约
  async createTjAppointment(params) {
    if (!params.hasOwnProperty('appointDate')) {
      throw new Error('必须提交预约日期');
    }
    let userInfo = {};
    if (params.userId !== '') {
      const res = await this.ctx.model.User.findOne({ _id: params.userId });
      if (res) {
        userInfo = res;
      }
    } else {
      await this.getUserInfo();
      userInfo = this.ctx.session.user;
    }
    const age = moment().diff(moment(userInfo.birth, 'YYYY-MM-DD'), 'years');
    const physicalExaminationName = (userInfo.gender === '0' ? '男' : '女') + (age < 40 ? ' 40岁以下' : ' 40岁以上');
    const tjPlan = await this.ctx.model.TjPlan.findOne({ _id: params.tjPlanId });
    const checkStartDate = tjPlan.checkStartDate;
    const appointName = new Date(checkStartDate).getFullYear() + '年体检预约';
    // const appointmentDetail = await this.ctx.model.AppointmentDetails.create({
    //   tjPlanId: params.tjPlanId,
    //   adminUserId: params.adminUserId || '',
    //   employeeId: userInfo._id,
    //   appointDate: new Date(params.appointDate),
    //   checkItems: params.checkItems,
    //   status: '0',
    //   physicalExaminationName,
    //   physicalExaminationOrgId: params.physicalExaminationOrgId,
    //   physicalExaminationOrgName: params.physicalExaminationOrgName,
    //   address: params.address,
    //   appointName,
    //   totalPrice: params.totalPrice,
    //   enterprisePay: params.enterprisePay,
    //   selfPay: params.selfPay,
    // });
    const appointmentDetail =
      await this.ctx.model.AppointmentDetails.findOneAndUpdate(
        {
          tjPlanId: params.tjPlanId,
          employeeId: userInfo._id,
        },
        {
          $set: {
            tjPlanId: params.tjPlanId,
            adminUserId: params.adminUserId || '',
            employeeId: userInfo._id,
            appointDate: new Date(params.appointDate),
            checkItems: params.checkItems,
            status: '0',
            physicalExaminationName,
            physicalExaminationOrgId: params.physicalExaminationOrgId,
            physicalExaminationOrgName: params.physicalExaminationOrgName,
            address: params.address,
            appointName,
            totalPrice: params.totalPrice,
            enterprisePay: params.enterprisePay,
            selfPay: params.selfPay,
            selectionQuota: params.selectionQuota,
            appointTime: params.selectIndex,
          },
        },
        { new: true, upsert: true, setDefaultsOnInsert: true }
      );
    if (params.adminUserId && params.adminUserId !== '') {
      // 发送消息
      const duration = moment(params.appointDate).format('MM月DD日');
      const cname = await this.ctx.model.User.findOne({ _id: params.adminUserId });
      await this.sendMessage({
        employee: userInfo._id,
        title: '管理员代预约体检',
        message: `体检安排通知，您好，${cname.name || ''}已为您预约了体检计划，体检时间：${duration}，请及时登录企业微信卫生健康体检预约模块确认体检日期。`,
      });
    }
    // 更新体检计划中的员工预约状态
    await this.ctx.model.TjPlan.updateOne(
      { _id: params.tjPlanId, 'employees.employeeId': userInfo._id },
      { $set: { 'employees.$.appointmentStatus': '1' } }
    ).catch(err => {
      console.log(err);
      throw new Error('更新体检中员工状态失败');
    });
    // 更新appointPeopleCount当天的已预约人数appointed
    const AppointPeopleCount = await this.ctx.model.AppointPeopleCount.findOne({ organizationId: params.physicalExaminationOrgId, appointDate: new Date(params.appointDate) });
    // 如果有这一天的预约人数记录，更新 appointed 字段使其加1
    if (AppointPeopleCount) {
      await this.ctx.model.AppointPeopleCount.updateOne({ _id: AppointPeopleCount._id }, { $inc: { [`appointed.${params.selectIndex}`]: 1 } }).catch(err => {
        console.log(err);
        throw new Error('更新预约人数失败');
      });
    } else {
      const physicalExamOrg = await this.ctx.model.PhysicalExamOrg.findOne({ _id: params.physicalExaminationOrgId }, { defaultAppointPeopleCount: 1 });
      const defaultAppointPeopleCount = physicalExamOrg.defaultAppointPeopleCount;
      // 没有则创建这一天的预约人数记录
      const timeRangeOptions = this.generateTimeSlots('07:30', '10:00');// 可配置时间段 TjPlan传递数组
      const arr = new Array(timeRangeOptions.length).fill(0);
      arr[params.selectIndex] = 1;
      this.ctx.model.AppointPeopleCount.create({
        appointDate: new Date(params.appointDate),
        organizationId: params.physicalExaminationOrgId,
        appointment: defaultAppointPeopleCount,
        flexible: 0,
        appointed: arr,
        physicalExamined: 0,
      }).catch(err => {
        console.log(err);
        throw new Error('创建预约人数失败');
      });
    }

    return appointmentDetail;
  }

  // 取消体检预约
  async cancelTjAppointment(params) {
    if (!params.hasOwnProperty('_id')) {
      throw new Error('必须提交体检预约id');
    }
    // 删除第一个符合条件的文档并返回它
    const res = await this.ctx.model.AppointmentDetails.findOneAndDelete({ _id: params._id }, function(err) {
      if (err) {
        console.error(err);
        throw new Error('取消预约失败');
      }
    });
    // 更新体检计划中的员工预约状态
    await this.ctx.model.TjPlan.updateOne(
      { _id: params.tjPlanId, 'employees.employeeId': this.ctx.session.user._id },
      { $set: { 'employees.$.appointmentStatus': '0' } }
    ).catch(err => {
      console.log(err);
      throw new Error('更新体检中员工状态失败');
    });
    // 更新appointPeopleCount当天的已预约人数appointed 使其减一
    await this.ctx.model.AppointPeopleCount.updateOne(
      { organizationId: params.physicalExaminationOrgId, appointDate: new Date(params.appointDate) },
      { $inc: { [`appointed.${params.appointTime}`]: -1 } }
    ).catch(err => {
      console.log(err);
      throw new Error('更新预约人数失败');
    });
    return res;
  }

  // 更新体检预约
  async updateTjAppointment(params) {
    const appointment = await this.ctx.model.AppointmentDetails.findOne({ _id: params._id });
    if (!appointment) {
      throw new Error('体检预约不存在');
    }
    if (moment(appointment.appointDate).isSame(moment(params.appointDate)) && appointment.appointTime !== params.selectIndex) {
      await this.ctx.model.AppointPeopleCount.updateOne(
        { organizationId: params.physicalExaminationOrgId, appointDate: new Date(appointment.appointDate) },
        { $inc: {
          [`appointed.${appointment.appointTime}`]: -1,
          [`appointed.${params.selectIndex}`]: 1,
        } }
      ).catch(err => {
        console.log(err);
        throw new Error('减少预约人数失败');
      });
    } else if (!moment(appointment.appointDate).isSame(moment(params.appointDate))) {
      // 更新旧的已预约人数appointed 使其减一
      await this.ctx.model.AppointPeopleCount.updateOne(
        { organizationId: params.physicalExaminationOrgId, appointDate: new Date(appointment.appointDate) },
        { $inc: { [`appointed.${appointment.appointTime}`]: -1 } })
        .catch(err => {
          console.log(err);
          throw new Error('减少预约人数失败');
        });
      // 更新appointPeopleCount当天的已预约人数appointed
      const AppointPeopleCount = await this.ctx.model.AppointPeopleCount.findOne({ organizationId: params.physicalExaminationOrgId, appointDate: new Date(params.appointDate) });
      // 如果有这一天的预约人数记录，更新 appointed 字段使其加1
      if (AppointPeopleCount) {
        await this.ctx.model.AppointPeopleCount.updateOne({ _id: AppointPeopleCount._id },
          { $inc: { [`appointed.${params.selectIndex}`]: 1 } })
          .catch(err => {
            console.log(err);
            throw new Error('更新预约人数失败');
          });
      } else {
        const physicalExamOrg = await this.ctx.model.PhysicalExamOrg.findOne({ _id: params.physicalExaminationOrgId }, { defaultAppointPeopleCount: 1 });
        const defaultAppointPeopleCount = physicalExamOrg.defaultAppointPeopleCount;
        const timeRangeOptions = this.generateTimeSlots('07:30', '10:00');// 可配置时间段 TjPlan传递数组
        const arr = new Array(timeRangeOptions.length).fill(0);
        arr[params.selectIndex] = 1;
        // 没有则创建这一天的预约人数记录
        this.ctx.model.AppointPeopleCount.create({
          appointDate: new Date(params.appointDate),
          organizationId: params.physicalExaminationOrgId,
          appointment: defaultAppointPeopleCount,
          flexible: 0,
          appointed: arr,
          physicalExamined: 0,
        }).catch(err => {
          console.log(err);
          throw new Error('创建预约人数失败');
        });
      }
    }

    let res = null;
    await this.ctx.model.AppointmentDetails.findOneAndUpdate(
      { _id: params._id },
      { ...params, appointTime: params.selectIndex },
      { new: true, useFindAndModify: false }, (err, doc) => {
        if (err) {
          console.error(err);
        } else {
          res = doc; // 将输出更新后的文档
        }
      });
    if (params.adminUserId && params.adminUserId !== '') {
      // 发送消息
      const duration = moment(params.appointDate).format('MM月DD日');
      const cname = await this.ctx.model.User.findOne({ _id: params.adminUserId });
      await this.sendMessage({
        employee: params.userId,
        title: '管理员代预约体检',
        message: `体检安排通知，您好，${cname.name || ''}已为您更新了体检计划，体检时间：${duration}，请及时登录企业微信卫生健康体检预约模块确认体检日期。`,
      });
    }
    return res;
  }

  // 获取必选检查项目列表 （每年一次普通体检，每年两次妇科项目检查）
  async getRequiredCheckItemList(params) {
    const { ctx } = this;
    const { organizationId, appointmentId, userId, tjPlanId } = params;
    const tjPlan = await ctx.model.TjPlan.findOne({ _id: tjPlanId });
    const checkStartDate = tjPlan.checkStartDate;
    // 查找当前人员今年是否有体检预约记录
    const yearStart = new Date(checkStartDate.getFullYear(), 0, 1);
    const yearEnd = new Date(checkStartDate.getFullYear() + 1, 0, 1);
    const query = {
      employeeId: userId,
      appointDate: {
        $gte: yearStart,
        $lt: yearEnd,
      },
      _id: { $ne: appointmentId },
    };
    const appointment = await ctx.model.AppointmentDetails.find(query);
    // 0-单位付费 1-自费
    let commonPayType = '0'; // 一般项目
    let womanPayType = '0'; // 妇科项目
    if (appointment.length > 0) { commonPayType = '1'; }
    if (appointment.length > 1) { womanPayType = '1'; }
    const { age, sex } = await this.getInfo(userId);
    let result = await ctx.model.CheckItems.aggregate([
      {
        $unwind: {
          path: '$packageList',
        },
      },
      {
        $lookup: {
          from: 'checkitemsdicts',
          localField: 'packageList',
          foreignField: '_id',
          as: 'checkItemDict',
        },
      },
      {
        $replaceRoot: {
          newRoot: {
            $mergeObjects: [
              {
                $arrayElemAt: [ '$checkItemDict', 0 ],
              },
              '$$ROOT',
            ],
          },
        },
      },
      {
        $project: {
          checkItemDict: 0,
        },
      },
      { $match: { sex, age, type: '0' } },
      {
        $lookup: {
          from: 'auditcheckitems', // 外键集合的名字
          let: { id: '$_id', organizationId }, // 定义本地变量
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: [ '$checkItemId', '$$id' ] }, // 外键集合中的关联字段
                    { $eq: [ '$organizationId', '$$organizationId' ] }, // 过滤条件
                  ],
                },
              },
            },
          ],
          as: 'auditInfo', // 查询结果将被放在这个字段中
        },
      },
      {
        $project: {
          name: 1,
          comments: 1,
          price: 1,
          oldPrice: { $arrayElemAt: [ '$auditInfo.oldPrice', 0 ] },
          payType: commonPayType,
          type: { $literal: '0' },
        },
      },
      {
        $lookup:
        {
          from: 'indicatordicts',
          localField: 'comments',
          foreignField: '_id',
          as: 'comments',
        },
      },
    ]);
    result = result.map(item => {
      const woman = [ '乳腺彩超检查', '阴道分泌物检查', '妇科彩超检查' ];
      if (woman.includes(item.name)) {
        return { ...item, payType: womanPayType };
      }
      return item;
    });
    return result;
  }

  // 获取选检检查项目列表 （肠胃镜检查三年一次免费）
  async getOptionalCheckItemList(params) {
    const { ctx } = this;
    const { organizationId, appointmentId, userId, tjPlanId } = params;
    const tjPlan = await ctx.model.TjPlan.findOne({ _id: tjPlanId });
    const checkStartDate = tjPlan.checkStartDate;
    // 三年一次肠胃镜检查
    // 如果三年内没有企业付费的肠胃镜体检记录，则本次体检由企业付费
    const yearStart = new Date(checkStartDate.getFullYear() - 2, 0, 1);
    const yearEnd = new Date(checkStartDate.getFullYear() + 1, 0, 1);
    const query = {
      employeeId: userId,
      appointDate: {
        $gte: yearStart,
        $lt: yearEnd,
      },
      checkItems: {
        $elemMatch: {
          name: { $regex: '^胃肠镜检查' },
          payType: '0',
        },
      },
      _id: { $ne: appointmentId },
    };
    const res = await ctx.model.AppointmentDetails.findOne(query);
    let payType = '0';
    if (res) {
      payType = '1';
    }

    const { age, sex, specialAge } = await this.getInfo(userId);
    let result = await ctx.model.CheckItems.aggregate([
      {
        $unwind: {
          path: '$packageList',
        },
      },
      {
        $lookup: {
          from: 'checkitemsdicts',
          localField: 'packageList',
          foreignField: '_id',
          as: 'checkItemDict',
        },
      },
      {
        $replaceRoot: {
          newRoot: {
            $mergeObjects: [
              {
                $arrayElemAt: [ '$checkItemDict', 0 ],
              },
              '$$ROOT',
            ],
          },
        },
      },
      {
        $project: {
          checkItemDict: 0,
        },
      },
      { $match: { $or: [{ age, type: '1', sex: { $in: [ sex, '2' ] } }, { sex: { $in: [ sex, '2' ] }, age: { $in: [ specialAge, '3' ] }, type: '2' }] } },
      {
        $lookup: {
          from: 'auditcheckitems', // 外键集合的名字
          let: { id: '$_id', organizationId }, // 定义本地变量
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: [ '$checkItemId', '$$id' ] }, // 外键集合中的关联字段
                    { $eq: [ '$organizationId', '$$organizationId' ] }, // 过滤条件
                  ],
                },
              },
            },
          ],
          as: 'auditInfo', // 查询结果将被放在这个字段中
        },
      },
      {
        $project: {
          name: 1,
          comments: 1,
          price: 1,
          oldPrice: { $arrayElemAt: [ '$auditInfo.oldPrice', 0 ] },
          payType: '1',
          type: { $literal: '1' },
        },
      },
      {
        $lookup:
        {
          from: 'indicatordicts',
          localField: 'comments',
          foreignField: '_id',
          as: 'comments',
        },
      },
    ]);

    if (payType === '0') {
      result = result.map(item => {
        if (item.name.startsWith('胃肠镜检查')) {
          item.payType = '0';
        }
        return item;
      });
    }

    return result;
  }

  // 获取职业病检测（危害因素）项目列表 （所有项目均为企业付费）
  async getOccupationalHealth(params) {
    const { ctx } = this;
    const { harmFactors, organizationId } = params;
    const regexPattern = new RegExp(JSON.parse(harmFactors).join('|'));
    // const regexPattern = new RegExp(harmFactors.join('|'));
    // console.log(333, regexPattern);
    const OccHealthCheckItems = await ctx.model.CheckItems.aggregate([
      {
        $unwind: {
          path: '$packageList',
        },
      },
      {
        $lookup: {
          from: 'checkitemsdicts',
          localField: 'packageList',
          foreignField: '_id',
          as: 'checkItemDict',
        },
      },
      {
        $replaceRoot: {
          newRoot: {
            $mergeObjects: [
              {
                $arrayElemAt: [ '$checkItemDict', 0 ],
              },
              '$$ROOT',
            ],
          },
        },
      },
      {
        $project: {
          checkItemDict: 0,
        },
      },
      { $match: { type: '3' } },
      {
        $lookup: {
          from: 'auditcheckitems', // 外键集合的名字
          let: { id: '$_id', organizationId }, // 定义本地变量
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: [ '$checkItemId', '$$id' ] }, // 外键集合中的关联字段
                    { $eq: [ '$organizationId', '$$organizationId' ] }, // 过滤条件
                  ],
                },
              },
            },
          ],
          as: 'auditInfo', // 查询结果将被放在这个字段中
        },
      },
      {
        $project: {
          name: 1,
          comments: 1,
          price: 1,
          oldPrice: { $arrayElemAt: [ '$auditInfo.oldPrice', 0 ] },
          payType: '0', // 0-单位付费 1-自费
        },
      },
      {
        $lookup:
        {
          from: 'indicatordicts',
          localField: 'comments',
          foreignField: '_id',
          as: 'comments',
        },
      },
    ]);
    const OccupationalHealth = await ctx.model.PhysicalExamination.aggregate([
      {
        $match: {
          keyword: { $regex: regexPattern },
        },
      },
      {
        $replaceRoot:
        {
          newRoot: {
            $arrayElemAt: [ '$status', 1 ],
          },
        },
      },
      {
        $addFields:
        {
          examItem: { $replaceAll: { input: '$examItem', find: '后前位X射线高仟伏胸片', replacement: '后前位X射线高仟伏胸片（别名：数字化摄影胸片（DR胸片））' } },
        },
      },
      {
        $addFields:
        {
          examItem: { $replaceAll: { input: '$examItem', find: '胸部X射线摄片', replacement: '胸部X射线摄片（别名：数字化摄影胸片（DR胸片））' } },
        },
      },
      {
        $project: {
          examItem: 1,
          _id: 0,
        },
      },
      {
        $addFields: {
          examItem: {
            $function: {
              body: `function(input) {
                  const regex = /、(?![^()]*\\))/g;
                  return input.split(regex).map(item => item.trim());
                }`,
              args: [ '$examItem' ],
              lang: 'js',
            },
          },
        },
      },
      {
        $unwind: {
          path: '$examItem',
        },
      },
      {
        $group: {
          _id: null,
          combinedExamItems: { $addToSet: '$examItem' },
        },
      },
      {
        $project: {
          _id: 0,
          combinedExamItems: 1,
        },
      },
    ]) || [{ combinedExamItems: [] }];
    let results = [];
    if (OccHealthCheckItems.length > 0 && OccupationalHealth.length > 0
      && OccupationalHealth[0].combinedExamItems.length > 0) {
      results = OccHealthCheckItems.filter(item => {
        const itemName = item.name;
        const regex = new RegExp(itemName);
        const isMatched = OccupationalHealth[0].combinedExamItems.some(subItem => regex.test(subItem));
        return isMatched;
      });
    }
    return results;
  }
  async getInfo(userId) {
    const { ctx } = this;
    const employeeId = userId || ctx.session.user._id;
    const userInfo = await ctx.model.User.findOne({ _id: employeeId }).select('birth gender').lean();
    // 使用moment计算年龄，而不是从字符串解析
    const age = moment().diff(moment(userInfo.birth, 'YYYY-MM-DD'), 'years');
    userInfo.age = age >= 40 ? '0' : '1';
    userInfo.specialAge = age >= 45 ? '2' : '3';
    userInfo.sex = userInfo.gender;
    return userInfo;
  }

  async getTjPlanCount(params) {
    if (!params.hasOwnProperty('_id')) {
      throw new Error('缺少体检计划id');
    }
    const tjPlan = await this.ctx.model.TjPlan.findOne({ _id: params._id });
    const physicalExamOrg = await this.ctx.model.PhysicalExamOrg.findOne({ _id: tjPlan.physicalExaminationOrgID }, { defaultAppointPeopleCount: 1 });
    const defaultAppointPeopleCount = physicalExamOrg.defaultAppointPeopleCount;
    const timeRangeOptions = this.generateTimeSlots('07:30', '10:00');// 可配置时间段 TjPlan传递数组
    const res = [];
    const startTime = new Date(tjPlan.checkStartDate);
    const endTime = new Date(tjPlan.checkEndDate);
    for (let date = new Date(startTime); date <= endTime; date.setDate(date.getDate() + 1)) {
      res.push({
        appointDate: moment(date).format('YYYY-MM-DD'),
        appointment: defaultAppointPeopleCount,
        flexible: 0,
        appointed: new Array(timeRangeOptions.length).fill(0),
      });
    }
    const appointDays = await this.ctx.model.AppointPeopleCount.find(
      {
        appointDate: { $gte: tjPlan.checkStartDate, $lte: tjPlan.checkEndDate },
        organizationId: tjPlan.physicalExaminationOrgID,
      });
    if (appointDays) {
      appointDays.forEach(async item => {
        const index = res.findIndex(i => {
          return moment(i.appointDate).format('YYYY-MM-DD') === moment(item.appointDate).format('YYYY-MM-DD');
        });
        if (index !== -1) {
          res[index].appointment = item.appointment;
          res[index].flexible = item.flexible;
          res[index].appointed = item.appointed.length > 0 ? item.appointed : new Array(timeRangeOptions.length).fill(0);
          !item.appointed.length && await this.ctx.model.AppointPeopleCount.updateOne({ _id: item._id }, { $set: { appointed: new Array(timeRangeOptions.length).fill(0) } });
        }
      });
    }

    return { data: res, timeRangeOptions };
  }

  async sendMessage(params) {
    const { ctx } = this;
    const { employee, title, message } = params;

    console.log(employee, 'employees');
    // 发送铃铛消息 给所有员工发消息
    const employeeIds = [
      {
        readerID: employee,
        readerGroup: '',
        isRead: 0,
      },
    ];
    const res = await this.ctx.model.User.findOne({
      _id: ctx.session.user._id,
    });

    const messageNotice = {
      title,
      message,
      reader: employeeIds, // 员工通知模板
      sendWay: 'systemMessage',
      templateCode: '',
      type: 1,
      SignName: '',
      files: [],
      authorID: ctx.session.user._id,
      authorGroup: ctx.session.user.group || '',
      informer: res.name || '', // 发通知的人名
    };
    if (employeeIds.length > 0) {
      await this.ctx.model.MessageNotification.create(messageNotice);
    }
  }
  // 获取问卷答卷地址
  async getQuestionnaireUrl() {
    const res = await this.ctx.model.SystemConfig.findOne().select('answerLink');
    return res.answerLink;
  }

  generateTimeSlots(start, end) {
    const timeSlots = [];
    start = new Date(`1970-01-01T${start}:00`);
    end = new Date(`1970-01-01T${end}:00`);

    while (start < end) {
      const next = new Date(start.getTime() + 30 * 60000); // 增加30分钟
      timeSlots.push({
        start: start.toTimeString().slice(0, 5),
        end: next.toTimeString().slice(0, 5),
        count: 0,
      });
      start = next;
    }
    return timeSlots;
  }
}

module.exports = tjAppointmentService;
