/**
 * @file 外部数据接口对接服务
 * @description 对接YTOutsideData接口，获取实时和历史数据
 * <AUTHOR>
 * @createDate 2023-12-15
 */

'use strict';

const Service = require('egg').Service;
const moment = require('moment');
const axios = require('axios');
const assert = require('assert');

class WhDataOnlineService extends Service {
  constructor(ctx) {
    super(ctx);
    // 根据环境配置不同的API基础URL
    this.baseUrl = ctx.app.config.whRequest.host + '/RESTAdapter/790/YTOutsideData';
    // this.app.config.env === 'dev'
    // ? 'https://aexdev.whchem.com:8091/RESTAdapter/790/YTOutsideData'
    // : 'http://jd1.whchem.com:8080/RESTAdapter/790/YTOutsideData';
    // 配置axios默认设置
    this.axiosConfig = {
      timeout: 30000,
      headers: {
        'content-type': 'application/json',
        Authorization: `Basic ${Buffer.from(
          `${ctx.app.config.whRequest.username}:${ctx.app.config.whRequest.password}`
        ).toString('base64')}`,
      },
    };

    // 如果有Basic认证信息，添加到默认配置中
    if (this.app.config.whRequest && this.app.config.whRequest.username && this.app.config.whRequest.password) {
      this.axiosConfig.headers.Authorization = `Basic ${Buffer.from(
        `${this.app.config.whRequest.username}:${this.app.config.whRequest.password}`
      ).toString('base64')}`;
    }
  }

  /**
   * @summary 获取当前实时值
   * @description 获取指定profile的当前实时数据
   * @param {String} profilename - 配置文件名称
   * @return {Promise<Object>} 返回实时数据
   */
  async getCurrentDataValue(profilename) {
    const { ctx } = this;

    try {
      console.log('profilename===============>', profilename);
      const response = await axios.post(
        `${this.baseUrl}/getCurrentDataValueByProfile`,
        { profilename },
        this.axiosConfig
      );

      ctx.auditLog('获取实时数据', `获取${profilename}实时数据成功 ${response.data.tagDataList.length}`, 'info');

      const result = response.data;
      console.log('result===============>', result.tagDataList.length, 'profilename', profilename);
      if (result.errorCode !== '0') {
        ctx.auditLog('获取实时数据', `获取${profilename}实时数据失败: ${result.errorMessage}`, 'error');
        return { success: false, message: result.errorMessage };
      }
      // 保存数据到数据库
      const saveRes = await this.saveMonitorData(result.tagDataList);
      return saveRes;
    } catch (error) {
      ctx.auditLog('获取实时数据', `获取${profilename}实时数据异常: ${error}`, 'error');
      return { success: false, message: '获取实时数据异常', error };
    }
  }

  /**
   * @summary 获取指定时间的历史数据
   * @description 获取指定profile在特定时间点的历史数据
   * @param {String} profilename - 配置文件名称
   * @param {String} time - 时间，格式：YYYY-MM-DD HH:mm:ss
   * @return {Promise<Object>} 返回历史数据
   */
  async getHistoryDataValue(profilename, time) {
    const { ctx } = this;

    try {
      const response = await axios.post(
        `${this.baseUrl}/getDataValueByProfile`,
        { profilename, time },
        this.axiosConfig
      );

      ctx.auditLog('获取历史数据', `获取${profilename}历史数据成功`, 'info');

      const result = response.data;
      if (result.errorCode !== '0') {
        ctx.auditLog('获取历史数据', `获取${profilename}历史数据失败: ${result.errorMessage}`, 'error');
        return { success: false, message: result.errorMessage };
      }

      // 保存数据到数据库
      await this.saveMonitorData(result.tagDataList);

      return { success: true, data: result };
    } catch (error) {
      ctx.auditLog('获取历史数据', `获取${profilename}历史数据异常: ${error}`, 'error');
      return { success: false, message: '获取历史数据异常', error };
    }
  }

  /**
   * @summary 获取时间范围内的历史压缩数据
   * @description 获取指定profile在时间范围内的历史压缩数据
   * @param {String} profilename - 配置文件名称
   * @param {String} startTime - 开始时间，格式：YYYY-MM-DD HH:mm:ss
   * @param {String} endTime - 结束时间，格式：YYYY-MM-DD HH:mm:ss
   * @param {Number} boundaryType - 边界处理类型，0:inside; 1:outside; 2:interplolated
   * @param {String} filterExpression - 过滤表达式
   * @return {Promise<Object>} 返回历史压缩数据
   */
  async getCompressDataValue(profilename, startTime, endTime, boundaryType = 0, filterExpression = '') {
    const { ctx } = this;
    try {
      const response = await axios.post(
        `${this.baseUrl}/getRecordeValueByProfile`,
        { profilename, startTime, endTime, boundaryType, filterExpression },
        this.axiosConfig
      );

      ctx.auditLog('获取历史压缩数据', `获取${profilename}历史压缩数据成功`, 'info');

      const result = response.data;
      if (result.errorCode !== '0') {
        ctx.auditLog('获取历史压缩数据', `获取${profilename}历史压缩数据失败: ${result.errorMessage}`, 'error');
        return { success: false, message: result.errorMessage };
      }

      // 保存数据到数据库
      await this.saveMonitorData(result.tagDataList);

      return { success: true, data: result };
    } catch (error) {
      ctx.auditLog('获取历史压缩数据', `获取${profilename}历史压缩数据异常: ${error}`, 'error');
      return { success: false, message: '获取历史压缩数据异常', error };
    }
  }

  /**
   * @summary 获取时间范围内的插值历史数据
   * @description 获取指定profile在时间范围内，按间隔进行插值的历史数据
   * @param {String} profilename - 配置文件名称
   * @param {String} startTime - 开始时间，格式：YYYY-MM-DD HH:mm:ss
   * @param {String} endTime - 结束时间，格式：YYYY-MM-DD HH:mm:ss
   * @param {Number} intervals - 间隔，单位：秒
   * @param {String} filterExpression - 过滤表达式
   * @return {Promise<Object>} 返回插值历史数据
   */
  async getInterpolatedDataValue(profilename, startTime, endTime, intervals = 600, filterExpression = '') {
    const { ctx } = this;
    assert(profilename, '配置文件名称不能为空');
    assert(startTime, '开始时间不能为空');
    assert(endTime, '结束时间不能为空');

    try {
      const response = await axios.post(
        `${this.baseUrl}/getInterpolatedValuesByProfile`,
        { profilename, startTime, endTime, intervals, filterExpression },
        this.axiosConfig
      );

      ctx.auditLog('获取插值历史数据', `获取${profilename}插值历史数据成功`, 'info');

      const result = response.data;
      if (result.errorCode !== '0') {
        ctx.auditLog('获取插值历史数据', `获取${profilename}插值历史数据失败: ${result.errorMessage}`, 'error');
        return { success: false, message: result.errorMessage };
      }

      // 保存数据到数据库
      await this.saveMonitorData(result.tagDataList);

      return { success: true, data: result };
    } catch (error) {
      ctx.auditLog('获取插值历史数据', `获取${profilename}插值历史数据异常: ${error}`, 'error');
      return { success: false, message: '获取插值历史数据异常', error };
    }
  }

  /**
   * @summary 获取时间范围内的绘图数据
   * @description 获取指定profile在时间范围内用于绘图的历史数据
   * @param {String} profilename - 配置文件名称
   * @param {String} startTime - 开始时间，格式：YYYY-MM-DD HH:mm:ss
   * @param {String} endTime - 结束时间，格式：YYYY-MM-DD HH:mm:ss
   * @param {Number} intervals - 间隔，单位：秒
   * @return {Promise<Object>} 返回绘图数据
   */
  async getPlotDataValue(profilename, startTime, endTime, intervals = 600) {
    const { ctx } = this;

    try {
      const response = await axios.post(
        `${this.baseUrl}/getPlotValueByProfile`,
        { profilename, startTime, endTime, intervals },
        this.axiosConfig
      );

      ctx.auditLog('获取绘图数据', `获取${profilename}绘图数据成功`, 'info');

      const result = response.data;
      if (result.errorCode !== '0') {
        ctx.auditLog('获取绘图数据', `获取${profilename}绘图数据失败: ${result.errorMessage}`, 'error');
        return { success: false, message: result.errorMessage };
      }

      // 保存数据到数据库
      await this.saveMonitorData(result.tagDataList);

      return { success: true, data: result };
    } catch (error) {
      ctx.auditLog('获取绘图数据', `获取${profilename}绘图数据异常: ${error}`, 'error');
      return { success: false, message: '获取绘图数据异常', error };
    }
  }

  /**
   * @summary 保存监控数据
   * @description 将获取到的数据保存到数据库
   * @param {Array} tagDataList - 标签数据列表
   * @return {Promise<void>}
   */
  async saveMonitorData(tagDataList) {
    const { ctx } = this;
    if (!tagDataList || !Array.isArray(tagDataList) || tagDataList.length === 0) {
      return;
    }

    try {
      const bulkOps = [];
      // 设置Redis缓存前缀，避免键名冲突
      const cachePrefix = 'whdata:device:';

      for (const tagData of tagDataList) {
        // 获取设备信息，如果Redis缓存没有，则查询数据库
        const cacheKey = `${cachePrefix}${tagData.tagName}`;
        let device;
        // 尝试从Redis获取设备信息
        const cachedDevice = await ctx.helper.getRedis(cacheKey);

        if (cachedDevice) {
          // Redis中有缓存，直接使用
          if ([ 'YT.66GT-01001A.PV', 'YT.01GT-36001.PV' ].includes(tagData.tagName)) {
            console.log('cachedDevice 查到了', cachedDevice);
          }
          device = JSON.parse(cachedDevice);
        } else {
          // Redis中没有缓存，查询数据库
          // device = await ctx.model.Devices.findOne({
          //   deviceID: tagData.tagName,
          //   connectionStatus: true,
          // });
          if (tagData.tagName === 'YT.66GT-01001A.PV') {
            console.log('cachedDevice 没有查到', tagData.tagName);
          }
          const deviceList = await ctx.model.Devices.aggregate([
            {
              $match: {
                deviceID: tagData.tagName,
                connectionStatus: true,
              },
            },
            {
              $lookup: {
                from: 'deviceTypes',
                localField: 'deviceType',
                foreignField: '_id',
                as: 'deviceType',
              },
            },
            {
              $unwind: {
                path: '$deviceType',
                preserveNullAndEmptyArrays: false,
              },
            },
            {
              $lookup: {
                from: 'deviceMonitorFactors',
                let: {
                  param: '$deviceType.parameters',
                  deviceData: '$deviceData',
                }, // 使用let定义一个变量param，它将用于lookup条件
                pipeline: [
                  {
                    $match: {
                      $expr: { $in: [ '$_id', '$$param' ] }, // 使用$expr和$in来匹配parameters数组中的元素
                    },
                  },
                  {
                    $addFields: {
                      kv: {
                        $arrayElemAt: [
                          {
                            $filter: {
                              input: {
                                $objectToArray: '$$deviceData',
                              },
                              as: 'item',
                              cond: {
                                $eq: [ '$$item.k', '$key' ],
                              },
                            },
                          },
                          0,
                        ],
                      },
                    },
                  },
                  {
                    $project: {
                      key: 1,
                      quality: 1,
                      isConversion: 1,
                    },
                  },
                ],
                as: 'deviceType.parameters', // 将匹配到的文档存储在matchedFactors字段
              },
            },
            {
              $unwind: {
                path: '$deviceType.parameters',
                preserveNullAndEmptyArrays: false,
              },
            },
            {
              $project: {
                deviceID: 1,
                key: '$deviceType.parameters.key',
              },
            },
          ]);
          if ([ 'YT.66GT-01001A.PV', 'YT.01GT-36001.PV' ].includes(tagData.tagName)) {
            console.log('cachedDevice 数据库查到数据===============>', deviceList);
          }
          if (deviceList.length > 0) {
            device = deviceList[0];
          }

          if (device) {
            // 将设备信息缓存到Redis，设置60分钟过期
            await ctx.helper.setRedis(cacheKey, JSON.stringify(device), 60 * 60);
          } else {
            if ([ 'YT.66GT-01001A.PV', 'YT.01GT-36001.PV' ].includes(tagData.tagName)) {
              console.log('cachedDevice 数据库没有查到数据', tagData.tagName);
            }
            continue; // 如果设备不存在或未启用，跳过此条数据
          }
        }
        // // 这个地方要做处理
        // const keyMonitor = await ctx.model.DeviceMonitorFactor.findOne({
        //   key: PIDeviceObj[item.Name],
        // }).lean();

        // 构建监控数据
        const monitorData = {
          deviceID: tagData.tagName,
          timestamp: moment(tagData.timeStamp, 'YYYY-MM-DD HH:mm:ss').toDate(),
          metadata: {
            plant: tagData.plant,
            tagName: tagData.tagName,
            tagDesc: tagData.tagDesc,
            timeStamp: tagData.timeStamp,
            value: tagData.value,
            unit: tagData.unit,
          },
          data: {
            [device.key]: tagData.value > 0 ? this.convertPPMtoMGM3(device, tagData.value) : 0,
          },
        };
        // 准备批量插入操作
        bulkOps.push({
          insertOne: {
            document: monitorData,
          },
        });
        // 更新设备的最新数据
        // await this.updateDeviceLatestData(device, parseFloat(tagData.value), monitorData.timestamp);
        console.log('更新设备最新数据', monitorData);
        // 当设备数据更新后，更新Redis缓存
        await ctx.helper.setRedis(cacheKey, JSON.stringify(device), 60 * 60);
      }

      // 批量插入监控数据
      if (bulkOps.length > 0) {
        await ctx.model.MonitorData.bulkWrite(bulkOps);
        ctx.auditLog('保存监控数据', `成功保存监控数据 ${bulkOps.length}`, 'info');
      }
      return {
        success: true,
        count: bulkOps.length,
        message: `成功保存监控数据 ${bulkOps.length}`,
      };
    } catch (error) {
      ctx.auditLog('保存监控数据', `保存监控数据失败: ${error}`, 'error');
      throw error;
    }
  }
  // 转换ppm到mg/m3
  convertPPMtoMGM3(item, ppm) {
    try {
      if (item.isConversion && item.quality) {
        const R = 22.4;
        // const T = 数字格式化item.quality
        const T = Number(item.quality);
        const result = (T / R) * ppm;
        const roundedResult = result.toFixed(2); // 保留两位小数
        return roundedResult;
      }
      return ppm;
    } catch (error) {
      console.log('error', error);
    }
    return ppm;
  }
  /**
   * @summary 更新设备最新数据
   * @description 更新设备的实时、小时、天、月数据
   * @param {Object} device - 设备对象
   * @param {Number} value - 数据值
   * @param {Date} timestamp - 时间戳
   * @return {Promise<void>}
   */
  async updateDeviceLatestData(device, value, timestamp) {
    const { ctx } = this;
    try {
      const now = moment(timestamp);
      const minuteTime = now.clone().startOf('minute').toDate();
      const hourTime = now.clone().startOf('hour').toDate();
      const dayTime = now.clone().startOf('day').toDate();
      const monthTime = now.format('YYYY-M');

      // 更新分钟数据
      const minuteIndex = device.minuteData.findIndex(
        item => moment(item.dateTime).isSame(minuteTime)
      );

      if (minuteIndex === -1) {
        // 如果不存在，添加新数据
        device.minuteData.push({
          dateTime: minuteTime,
          value,
        });
      } else {
        // 如果存在，更新数据
        device.minuteData[minuteIndex].value = value;
      }

      // 限制分钟数据数量
      if (device.minuteData.length > 60) {
        device.minuteData = device.minuteData.slice(-60);
      }

      // 更新小时数据（取平均值）
      let hourExisted = false;
      device.minuteData.forEach(data => {
        if (moment(data.dateTime).isSame(hourTime, 'hour')) {
          hourExisted = true;
        }
      });

      if (hourExisted) {
        const minuteDataInHour = device.minuteData.filter(
          data => moment(data.dateTime).isSame(hourTime, 'hour')
        );

        const hourValue = minuteDataInHour.reduce((sum, item) => sum + item.value, 0) / minuteDataInHour.length;

        const hourIndex = device.hourData.findIndex(
          item => moment(item.dateTime).isSame(hourTime)
        );

        if (hourIndex === -1) {
          device.hourData.push({
            dateTime: hourTime,
            value: hourValue,
          });
        } else {
          device.hourData[hourIndex].value = hourValue;
        }

        // 限制小时数据数量
        if (device.hourData.length > 24) {
          device.hourData = device.hourData.slice(-24);
        }
      }

      // 更新日数据（取平均值）
      let dayExisted = false;
      device.hourData.forEach(data => {
        if (moment(data.dateTime).isSame(dayTime, 'day')) {
          dayExisted = true;
        }
      });

      if (dayExisted) {
        const hourDataInDay = device.hourData.filter(
          data => moment(data.dateTime).isSame(dayTime, 'day')
        );

        const dayValue = hourDataInDay.reduce((sum, item) => sum + item.value, 0) / hourDataInDay.length;

        const dayIndex = device.dayData.findIndex(
          item => moment(item.dateTime).isSame(dayTime)
        );

        if (dayIndex === -1) {
          device.dayData.push({
            dateTime: dayTime,
            value: dayValue,
          });
        } else {
          device.dayData[dayIndex].value = dayValue;
        }

        // 限制日数据数量
        if (device.dayData.length > 31) {
          device.dayData = device.dayData.slice(-31);
        }
      }

      // 更新月数据（取平均值）
      let monthExisted = false;
      device.dayData.forEach(data => {
        if (moment(data.dateTime).format('YYYY-M') === monthTime) {
          monthExisted = true;
        }
      });

      if (monthExisted) {
        const dayDataInMonth = device.dayData.filter(
          data => moment(data.dateTime).format('YYYY-M') === monthTime
        );

        // 修复：确保避免NaN值产生
        let monthValue = 0;
        if (dayDataInMonth.length > 0) {
          // 过滤掉无效值（NaN或者非数字）
          const validValues = dayDataInMonth.filter(item => typeof item.value === 'number' && !isNaN(item.value));
          if (validValues.length > 0) {
            monthValue = validValues.reduce((sum, item) => sum + item.value, 0) / validValues.length;
          }
        }

        const monthIndex = device.monthData.findIndex(
          item => item.dateTime === monthTime
        );

        if (monthIndex === -1) {
          device.monthData.push({
            dateTime: monthTime,
            value: monthValue,
          });
        } else {
          device.monthData[monthIndex].value = monthValue;
        }

        // 限制月数据数量
        if (device.monthData.length > 12) {
          device.monthData = device.monthData.slice(-12);
        }
      }

      // 保存设备数据
      await this.ctx.model.Devices.updateOne({ _id: device._id }, { $set: { minuteData: device.minuteData, hourData: device.hourData, dayData: device.dayData, monthData: device.monthData } });
    } catch (error) {
      ctx.auditLog('更新设备数据', '更新设备最新数据失败', 'error');
      throw error;
    }
  }

  /**
   * @summary 同步所有在线设备数据
   * @description 定时任务使用，获取所有在线设备的最新数据
   * @param {String} profilename - 配置文件名称
   * @return {Promise<Object>} 同步结果
   */
  async syncAllDevicesData() {
    const { ctx } = this;
    try {
      // // 获取所有在线设备
      // const onlineDevices = await ctx.model.Devices.find({});

      // if (!onlineDevices || onlineDevices.length === 0) {
      //   ctx.auditLog('同步设备数据', '没有在线设备，无需同步数据', 'info');
      //   return { success: true, message: '没有在线设备，无需同步数据' };
      // }
      const profilename = [ 'YT_OHSYD', 'FJ_OHSYD', 'GD_OHSYD', 'NB_OHSYD', 'SC_OHSYD' ];
      // 获取当前实时数据
      let syncDeviceCount = 0;
      for (const item of profilename) {
        const result = await this.getCurrentDataValue(item);
        if (result.success) {
          syncDeviceCount += result.count;
        }
      }
      ctx.auditLog('同步设备数据', `成功同步${syncDeviceCount}个设备数据`, 'info');

      return { success: true, message: `成功同步${syncDeviceCount}个设备数据`, syncDeviceCount };
    } catch (error) {
      ctx.auditLog('同步设备数据', `同步所有设备数据失败: ${error}`, 'error');
      return { success: false, message: `同步所有设备数据失败: ${error}`, error };
    }
  }
}

module.exports = WhDataOnlineService;
