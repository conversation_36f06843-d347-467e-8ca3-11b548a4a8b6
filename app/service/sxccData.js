const Service = require('egg').Service;
class SxccService extends Service {
  async getIndicator() {
    try {
      const documents = await this.ctx.model.IndicatorDict.find({}).lean();
      const tree = this.buildForest(documents);
      return tree;
    } catch (error) {
      this.ctx.auditLog('获取指标字典报错', error.message, 'error');
    }
  }

  buildForest(data) {
    const forest = [];
    const lookup = {};

    data.forEach(item => {
      lookup[item._id] = { ...item, children: [] };
    });

    data.forEach(item => {
      if (item.parentId && lookup[item.parentId]) {
        lookup[item.parentId].children.push(lookup[item._id]);
      } else {
        forest.push(lookup[item._id]);
      }
    });

    return forest;
  }
}
module.exports = SxccService;
