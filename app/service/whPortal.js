'use strict';

const Service = require('egg').Service;
const moment = require('moment');
const WhPortalTaskUtil = require('@utils').whPortalTaskUtil;

class PortalTaskService extends Service {
  constructor(ctx) {
    super(ctx);
    this.whPortalTaskUtil = new WhPortalTaskUtil(ctx);
  }

  /**
   * 推送待办任务
   * @param {Object|Array} task - 待办任务数据或任务数组
   * @return {Promise<Object>} 发送结果
   */
  async pushPendingTask(task) {
    const aaa = this.whPortalTaskUtil.buildMultiLangField(
      '默认流程',
      '',
      ''
    ); // 流程名称，多语言
    console.log('aaa', aaa);
    // 处理单个任务或任务数组
    if (Array.isArray(task)) {
      // 处理任务数组
      const taskDataArray = task.map(item => ({
        OrderSource: 'OHS', // 来源系统
        OrderId: item.orderId, // 流程实例ID
        OrderCode: item.orderCode, // 单据编码，必填
        OrderTypeName: this.whPortalTaskUtil.buildMultiLangField(
          item.orderTypeNameZh || '默认流程',
          item.orderTypeNameEn || '',
          item.orderTypeNameHu || ''
        ), // 流程名称，多语言
        OrderUserId: item.orderUserId, // 申请人 ITCode
        OrderTime: item.orderTime || moment().format('YYYY-MM-DD HH:mm:ss'), // 发起时间
        OrderTaskId: item.orderTaskId, // 任务ID
        OrderTaskUrl: item.orderTaskUrl, // 超链接地址
        Colum2: item.colum2 || '', // 移动端URL，可选
        OrderTaskCurrentUserId: item.currentUserId, // 当前处理人 ITCode
        OrderTaskStatus: '1', // 待办状态
        OrderTaskName: this.whPortalTaskUtil.buildMultiLangField(
          item.taskNameZh || '默认环节',
          item.taskNameEn || '',
          item.taskNameHu || ''
        ), // 环节名称，多语言
        OrderTaskDesc: this.whPortalTaskUtil.buildMultiLangField(
          `${item.orderUserName || '某人'}提交了${
            item.orderTypeNameZh || '默认流程'
          },请您尽快处理`,
          `${item.orderUserName || 'Someone'} submitted ${
            item.orderTypeNameEn || 'Default Process'
          }, please handle it ASAP`,
          ''
        ), // 描述，多语言
        OrderTaskTime: moment().format('YYYY-MM-DD HH:mm:ss'), // 待办产生时间
      }));

      const payload = this.whPortalTaskUtil.buildTaskPayload(taskDataArray, 'OHS', taskDataArray.length);
      return await this.whPortalTaskUtil.sendTaskRequest(payload);
    }

    // 处理单个任务（保持原有逻辑）
    const taskData = {
      OrderSource: 'OHS', // 来源系统
      OrderId: task.orderId, // 流程实例ID
      OrderCode: task.orderCode, // 单据编码，必填
      OrderTypeName: this.whPortalTaskUtil.buildMultiLangField(
        task.orderTypeNameZh || '默认流程',
        task.orderTypeNameEn || '',
        task.orderTypeNameHu || ''
      ), // 流程名称，多语言
      OrderUserId: task.orderUserId, // 申请人 ITCode
      OrderTime: task.orderTime || moment().format('YYYY-MM-DD HH:mm:ss'), // 发起时间
      OrderTaskId: task.orderTaskId, // 任务ID
      OrderTaskUrl: task.orderTaskUrl, // 超链接地址
      Colum2: task.colum2 || '', // 移动端URL，可选
      OrderTaskCurrentUserId: task.currentUserId, // 当前处理人 ITCode
      OrderTaskStatus: '1', // 待办状态
      OrderTaskName: this.whPortalTaskUtil.buildMultiLangField(
        task.taskNameZh || '默认环节',
        task.taskNameEn || '',
        task.taskNameHu || ''
      ), // 环节名称，多语言
      OrderTaskDesc: this.whPortalTaskUtil.buildMultiLangField(
        `${task.orderUserName || '某人'}提交了${
          task.orderTypeNameZh || '默认流程'
        },请您尽快处理`,
        `${task.orderUserName || 'Someone'} submitted ${
          task.orderTypeNameEn || 'Default Process'
        }, please handle it ASAP`,
        ''
      ), // 描述，多语言
      OrderTaskTime: moment().format('YYYY-MM-DD HH:mm:ss'), // 待办产生时间
    };

    const payload = this.whPortalTaskUtil.buildTaskPayload(taskData);
    console.log('防护用品', payload);
    return await this.whPortalTaskUtil.sendTaskRequest(payload);
  }

  /**
   * 推送已办任务（消除待办）
   * @param {Object} task - 已办任务数据
   * @return {Promise<Object>} 发送结果
   */
  async pushCompletedTask(task) {
    // 默认已办状态为 2，最后一个已办为 3
    const taskData = {
      OrderSource: task.orderSource || 'HSE',
      OrderId: task.orderId,
      OrderCode: task.orderCode,
      OrderTypeName: this.whPortalTaskUtil.buildMultiLangField(
        task.orderTypeNameZh || '默认流程',
        task.orderTypeNameEn,
        task.orderTypeNameHu
      ),
      OrderUserId: task.orderUserId,
      OrderTime: task.orderTime || moment().format('YYYY-MM-DD HH:mm:ss'),
      OrderTaskId: task.orderTaskId,
      OrderTaskUrl: task.orderTaskUrl,
      Colum2: task.colum2 || '',
      OrderTaskCurrentUserId: task.currentUserId,
      OrderTaskStatus: task.isLast ? '3' : '2', // 是否最后一个已办
      OrderTaskName: this.whPortalTaskUtil.buildMultiLangField(
        task.taskNameZh || '默认环节',
        task.taskNameEn,
        task.taskNameHu
      ),
      OrderTaskDesc: this.whPortalTaskUtil.buildMultiLangField(
        `${task.orderUserName || '某人'}提交了${
          task.orderTypeNameZh || '默认流程'
        },请您尽快处理`,
        `${task.orderUserName || 'Someone'} submitted ${
          task.orderTypeNameEn || 'Default Process'
        }, please handle it ASAP`,
        ''
      ),
      OrderTaskTime: task.taskTime, // 待办产生时间
      OrderDoneTime: moment().format('YYYY-MM-DD HH:mm:ss'), // 已办处理时间
      OrderAction: task.action || 'submit', // 动作类型：submit, reject, involve, reassign
    };

    const payload = this.whPortalTaskUtil.buildTaskPayload(taskData);
    return await this.whPortalTaskUtil.sendTaskRequest(payload);
  }

  /**
   * 推送特殊状态任务（如注销、删除等）
   * @param {Object} task - 任务数据
   * @param {string} status - 特殊状态 (4, 5, 6, 7)
   * @return {Promise<Object>} 发送结果
   */
  async pushSpecialTask(task, status) {
    const taskData = {
      OrderSource: task.orderSource || 'HSE',
      OrderId: task.orderId,
      OrderTaskId: task.orderTaskId || '', // 视情况可选
      OrderTaskStatus: status, // 特殊状态：4-注销, 5-已办删除, 6-待办删除, 7-仅申请
    };

    const payload = this.whPortalTaskUtil.buildTaskPayload(taskData);
    return await this.whPortalTaskUtil.sendTaskRequest(payload);
  }
}

module.exports = PortalTaskService;
