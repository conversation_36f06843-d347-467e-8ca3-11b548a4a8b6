const Controller = require('egg').Controller;

class AddressController extends Controller {
  async getList() {
    const ctx = this.ctx;

    try {
      const payload = {
        current: 1,
        pageSize: 10000,
        isPaging: '0',
      };
      console.log('取地址列表，当前入参', ctx.query);
      let query = ctx.query;
      if (query.root) {
        query = {
          parent_code: 0,
        };
      } else {
        const target = await ctx.model.District.findOne({ id: query.id });
        query = {
          parent_code: target.area_code,
        };
      }
      const addlist = await ctx.service.district.find(payload, {
        sort: {
          id: -1,
        },
        files:
          'id parent_code name lng lat zip_code area_code level merger_name short_name',
        query,
      });
      // gs+++ 地址是三级无法选择
      // console.log(addlist, '77777777777');
      if (addlist.length > 0 && addlist[0].level === '2') {
        for (let i = 0; i < addlist.length; i++) {
          const res = await ctx.model.District.find({ parent_code: addlist[i].area_code }).count();
          // console.log(res, '5555555555');
          if (res === 0) {
            const item = JSON.parse(JSON.stringify(addlist[i]));
            item.hasChildren = true;
            addlist[i] = JSON.parse(JSON.stringify(item));
          }
        }
      }
      // console.log(addlist, '22222222222222');
      await ctx.helper.renderSuccess(ctx, {
        message: '地址列表',
        data: addlist,
      });
    } catch (e) {
      ctx.helper.renderFail(ctx, {
        message: e,
      });
    }
  }
  async addresssearch() {
    const { ctx } = this;
    const { keyWord } = ctx.query;
    const targtes = await ctx.model.District.find(
      { name: { $regex: keyWord }, merger_name: { $not: /台湾省/ } },
      { name: 1, merger_name: 1, level: 1, id: 1, area_code: 1, _id: 0 }
    );

    ctx.helper.renderSuccess(ctx, {
      data: targtes,
      message: 'success',
      status: 200,
    });
  }
  async getAddressByCode() {
    const { ctx } = this;
    const { adcode } = ctx.query;
    const addrePath = [];
    // 查询某地址的父节点
    const findAddress = async function(adcode, addrePath) {
      const res = await ctx.model.District.findOne({ area_code: adcode }, { level: 1, name: 1, area_code: 1, parent_code: 1, id: 1 });
      if (!res) {
        return;
      }
      addrePath.unshift(res);
      if (res.parent_code) {
        await findAddress(res.parent_code, addrePath);
      }
    };

    await findAddress(adcode, addrePath);

    // 格式化数据
    const addPathData = addrePath.map(item => {
      return {
        id: item.id,
        value: item.name,
        leaf: item.level >= 3,
      };
    });

    ctx.helper.renderSuccess(ctx, {
      data: {
        addPathData,
      },
      message: 'success',
      status: 200,
    });
  }
}

module.exports = AddressController;
