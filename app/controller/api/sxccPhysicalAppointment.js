const Controller = require('egg').Controller;
// const { XMLParser } = require('fast-xml-parser');
// 山西焦煤体检预约
class SxccPhysicalAppointmentController extends Controller {
  // json版本号检查
  checkAccept(ctx, accept) {
    const version = ctx.helper.getAPIVersion(accept, 'application/oapi.zyws.v', '+json');
    const versions = [ '1_0' ];
    if (!version || version.indexOf(versions) === -1) {
      throw new Error('版本号token错误，请检查');
    }
    return version;
  }


  async tjPlanList(ctx) {
    try {
      const { ctx } = this;
      const { query } = ctx;
      // console.log('Controller/tjPlanList山西焦煤体检预约列表', query);
      const accept = ctx.header.accept;
      const version = this.checkAccept(ctx, accept);
      const physicalExamOrgId = await ctx.service.healthCheckAppointment.getOrgId(ctx.header.token);
      console.log('physicalExamOrgId', physicalExamOrgId);
      const list = await ctx.service.sxccPhysicalAppointment['tjPlanListV' + version](physicalExamOrgId, query);
      const res = {
        list,
        count: list.length,
      };
      ctx.helper.renderCustom(ctx, {
        status: 200,
        data: res,
        message: '获取山西焦煤体检预约列表',
      });
    } catch (error) {
      console.log('获取山西焦煤体检预约列表报错', error);
      ctx.auditLog('获取山西焦煤体检预约列表报错', error, 'error');
      ctx.helper.renderCustom(ctx, {
        status: 404,
        message: error.message,
      });
    }
  }

  async tjPlanListDetails(ctx) {
    try {
      const { ctx } = this;
      const { query } = ctx;
      console.log('Controller/tjPlanListDetails山西焦煤体检预约详情', query);
      const accept = ctx.header.accept;
      const version = this.checkAccept(ctx, accept);
      const physicalExamOrgId = await ctx.service.healthCheckAppointment.getOrgId(ctx.header.token);
      console.log('physicalExamOrgId', physicalExamOrgId);
      const res = await ctx.service.sxccPhysicalAppointment['tjPlanListDetailsV' + version](physicalExamOrgId, query);
      ctx.helper.renderCustom(ctx, {
        status: 200,
        data: res,
        message: '获取山西焦煤体检预约详情列表',
      });
    } catch (error) {
      console.log('获取山西焦煤体检预约详情列表报错', error);
      ctx.auditLog('获取山西焦煤体检预约详情列表报错', error, 'error');
      ctx.helper.renderCustom(ctx, {
        status: 404,
        message: error.message,
      });
    }
  }

  async IndividualAppointment(ctx) {
    try {
      const { ctx } = this;
      const { query } = ctx;
      const accept = ctx.header.accept;
      const version = this.checkAccept(ctx, accept);
      // console.log('Controller/IndividualAppointment山西焦煤体检预约详情', query);
      const physicalExamOrgId = await ctx.service.healthCheckAppointment.getOrgId(ctx.header.token);
      console.log('physicalExamOrgId', physicalExamOrgId);
      const res = await ctx.service.sxccPhysicalAppointment['IndividualAppointmentV' + version](physicalExamOrgId, query);
      ctx.helper.renderCustom(ctx, {
        status: 200,
        data: res,
        message: '获取山西焦煤个人体检预约详情列表',
      });
    } catch (error) {
      console.log('获取山西焦煤个人体检预约详情列表报错', error);
      ctx.auditLog('获取山西焦煤个人体检预约详情列表报错', error, 'error');
      ctx.helper.renderCustom(ctx, {
        status: 404,
        message: error.message,
      });
    }
  }

  // 山西焦煤体检人员现场签到
  async IndividualCheckIn(ctx) {
    try {
      const { ctx } = this;
      const { query } = ctx;
      const accept = ctx.header.accept;
      const version = this.checkAccept(ctx, accept);
      // console.log('Controller/IndividualCheckIn山西焦煤体检人员现场签到', query);
      const physicalExamOrgId = await ctx.service.healthCheckAppointment.getOrgId(ctx.header.token);
      console.log('physicalExamOrgId', physicalExamOrgId);
      const res = await ctx.service.sxccPhysicalAppointment['IndividualCheckInV' + version](physicalExamOrgId, query);
      ctx.helper.renderCustom(ctx, {
        status: 200,
        data: res,
        message: '山西焦煤体检人员现场签到成功',
      });
    } catch (error) {
      console.log('山西焦煤体检人员现场签到报错', error);
      ctx.auditLog('山西焦煤体检人员现场签到报错', error, 'error');
      ctx.helper.renderCustom(ctx, {
        status: 404,
        message: error.message,
      });
    }
  }

  // 获取指标字典
  async getIndividualCheckIn(ctx) {
    try {
      const { ctx } = this;
      const res = await ctx.service.sxccData.getIndicator();
      ctx.helper.renderCustom(ctx, {
        status: 200,
        data: res,
        message: '获取指标字典',
      });
    } catch (error) {
      ctx.auditLog('获取指标字典报错', error.message, 'error');
      ctx.helper.renderCustom(ctx, {
        status: 404,
        message: error.message,
      });
    }
  }

  // 接收答卷完成信息
  async handleCompletePhysicalExam(ctx) {
    try {
      const { query } = ctx;
      const res = await ctx.service.sxccPhysicalAppointment.handleCompletePhysicalExam(query);
      ctx.helper.renderCustom(ctx, {
        status: 200,
        data: res,
        message: '体检已完成',
      });
    } catch (error) {
      ctx.auditLog('获取答卷完成信息报错', error.message, 'error');
      ctx.helper.renderCustom(ctx, {
        status: 404,
        message: error.message,
      });
    }
  }
}

module.exports = SxccPhysicalAppointmentController;
