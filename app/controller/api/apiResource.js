const shortid = require('shortid');
const _ = require('lodash');
const {
  apiResourceRule,
} = require('@validate/apiResource');
const fs = require('fs');
const Controller = require('egg').Controller;
class ApiResourceController extends Controller {

  async list() {
    const {
      ctx,
    } = this;
    try {
      const payload = ctx.query;
      _.assign(payload, {
        isPaging: '0',
      });
      const apiResourceList = await ctx.service.apiResource.find(payload);

      ctx.helper.renderSuccess(ctx, {
        data: apiResourceList,
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  }

  async alllist() {
    return await this.ctx.service.apiResource.find({
      isPaging: '0',
    }, {
      type: '1',
    });
  }

  async create() {

    const {
      ctx,
    } = this;
    try {

      const fields = ctx.request.body || {};
      const formObj = {
        _id: fields._id || shortid.generate(), // 测试用
        label: fields.label,
        type: fields.type,
        api: fields.api,
        parentId: fields.parentId,
        sortId: fields.sortId,
        routePath: fields.routePath,
        icon: fields.icon,
        componentPath: fields.componentPath,
        enable: fields.enable,
        comments: fields.comments,
      };


      ctx.validate(apiResourceRule.form(ctx), formObj);

      if (fields.type === '0' && !fields.label) {
        formObj.label = fields.routePath;
      } else if (fields.type === '1' && !fields.label) {
        formObj.label = fields.api;
      }

      const oldResource = await ctx.service.apiResource.item(ctx, {
        query: {
          label: formObj.label,
        },
      });
      if (!_.isEmpty(oldResource)) {
        throw new Error(ctx.__('user_action_tips_repeat', [ ctx.__('label_resourceName') ]));
      }

      await ctx.service.apiResource.create(formObj);

      ctx.helper.renderSuccess(ctx);
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  }


  async getOne() {
    const {
      ctx,
    } = this;
    try {
      const _id = ctx.query.id;

      const targetItem = await ctx.service.apiResource.item(ctx, {
        query: {
          _id,
        },
      });

      ctx.helper.renderSuccess(ctx, {
        data: targetItem,
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  }


  async update() {
    const {
      ctx,
    } = this;
    try {

      const fields = ctx.request.body || {};
      const formObj = {
        label: fields.label,
        type: fields.type,
        api: fields.api,
        parentId: fields.parentId,
        sortId: fields.sortId,
        routePath: fields.routePath,
        icon: fields.icon,
        componentPath: fields.componentPath,
        enable: fields.enable,
        comments: fields.comments,
      };

      ctx.validate(apiResourceRule.form(ctx), formObj);

      if (fields.type === '0' && fields.routePath) {
        formObj.label = fields.routePath;
      } else if (fields.type === '1') {
        formObj.label = fields.api;
      }

      const oldResource = await ctx.service.apiResource.item(ctx, {
        query: {
          label: formObj.label,
        },
      });

      if (!_.isEmpty(oldResource) && oldResource._id !== fields._id) {
        throw new Error(ctx.__('user_action_tips_repeat', [ ctx.__('label_resourceName') ]));
      }

      await ctx.service.apiResource.update(ctx, fields._id, formObj);
      ctx.helper.renderSuccess(ctx);
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  }


  async updateParentId() {
    const {
      ctx,
    } = this;

    try {

      const fields = ctx.request.body || {};

      const formObj = {
        parentId: fields.parentId,
      };

      const oldResource = await ctx.service.apiResource.item(ctx, {
        query: {
          label: fields.label,
        },
      });

      if (!_.isEmpty(oldResource) && oldResource._id !== fields._id) {
        throw new Error(ctx.__('user_action_tips_repeat', [ ctx.__('label_resourceName') ]));
      }

      await ctx.service.apiResource.update(ctx, fields._id, formObj);
      ctx.helper.renderSuccess(ctx);
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  }


  async removes() {
    const {
      ctx,
    } = this;
    try {
      const targetIds = ctx.query.ids;

      const oldResource = await ctx.service.apiResource.item(ctx, { query: { _id: targetIds }, files: { _id: 1, parentId: 1 } });
      if (oldResource.parentId === '0') {
        const cResources = await ctx.service.apiResource.find({ isPaging: '0' }, { query: { parentId: oldResource._id }, files: { _id: 1, parentId: 1 } });
        await ctx.service.apiResource.removes(ctx, oldResource._id);
        for (let i = 0; i < cResources.length; i++) {
          const cResourceId = cResources[i]._id;
          // 删除主类
          await ctx.service.apiResource.removes(ctx, cResourceId);
          // 删除子类
          await ctx.service.apiResource.removes(ctx, cResourceId, 'parentId');
        }
      } else {
        // 删除主类
        await ctx.service.apiResource.removes(ctx, oldResource._id);
        // 删除子类
        await ctx.service.apiResource.removes(ctx, oldResource._id, 'parentId');
      }
      ctx.helper.renderSuccess(ctx);
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  }
  // 判断数据库连接状态
  async adoConnection() {
    const { ctx } = this;
    try {
      const district = await ctx.model.District.findOne({}, { area_code: 1 });
      const version = fs.readFileSync('version', 'utf-8') || '';
      if (district && district.area_code) {
        ctx.helper.renderSuccess(ctx, {
          status: 200,
          message: 'mongoose connected',
          data: {
            version: version.replace(/(\n|VERSION=)/g, '').trim(),
          },
        });
      } else {
        ctx.helper.renderCustom(ctx, {
          status: 500,
          message: 'mongoose disconnected',
          data: version,
        });
      }
    } catch (err) {
      ctx.helper.renderCustom(ctx, {
        status: 500,
        message: JSON.stringify(err),
      });
    }
  }

}
module.exports = ApiResourceController;
