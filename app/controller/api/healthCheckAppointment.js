const Controller = require('egg').Controller;
const { XMLParser } = require('fast-xml-parser');
// 体检预约
class HealthCheckAppointmentController extends Controller {
  // 上传职业健康档案 - 国家标准
  async healthExamRecordList(ctx) {
    try {
      const accept = ctx.header.accept;
      const version = this.checkAccept(ctx, accept);
      const physicalExamOrgId = await ctx.service.healthCheckAppointment.getOrgId(ctx.header.token);
      let examRecordList;
      if (ctx.is('xml')) {
        examRecordList = await this.convertXmlData(ctx.request.body.DataExchange.EventBody[0], 'HEALTH_EXAM_RECORD_LIST', 'HEALTH_EXAM_RECORD');
        ctx.auditLog('体检系统对接 上传职业健康档案 xml转化后的数据', examRecordList, 'info');
      } else {
        examRecordList = ctx.request.body.HEALTH_EXAM_RECORD_LIST;
      }
      await ctx.service.healthCheckAppointment.addInterfaceLog(ctx.request.url, physicalExamOrgId, examRecordList);
      const result = { successful: [], failed: [] };
      for (let i = 0; i < examRecordList.length; i++) {
        const item = examRecordList[i];
        const res = await ctx.service.healthCheckAppointment_cn['healthExamRecordV' + version](physicalExamOrgId, item);
        if (res.status === 200) {
          result.successful.push(res.message);
        } else {
          result.failed.push(res.message);
        }
      }
      ctx.helper.renderCustom(ctx, {
        status: 200,
        data: result,
        message: '上传职业健康档案完成',
      });
    } catch (error) {
      console.log('上传职业健康档案报错', error);
      ctx.auditLog('上传职业健康档案报错', error, 'error');
      ctx.helper.renderCustom(ctx, {
        status: 404,
        message: error.message,
      });
    }
  }
  // 上传职业健康档案 - 浙江省标准
  async zjHealthExamRecordList(ctx) {
    try {
      const accept = ctx.header.accept;
      const version = this.checkAccept(ctx, accept);
      const physicalExamOrgId = await ctx.service.healthCheckAppointment.getOrgId(ctx.header.token);
      let examRecordList;
      if (ctx.is('xml')) {
        examRecordList = await this.convertXmlData(ctx.request.body.data.body[0], 'reportCardList', 'reportCard');
        ctx.auditLog('温州体检系统对接 上传职业健康档案 xml转化后的数据', examRecordList, 'info');
      } else {
        examRecordList = ctx.request.body.reportCardList;
      }
      await ctx.service.healthCheckAppointment.addInterfaceLog(ctx.request.url, physicalExamOrgId, examRecordList);
      const result = { successful: [], failed: [] };
      for (let i = 0; i < examRecordList.length; i++) {
        const item = examRecordList[i];
        const res = await ctx.service.healthCheckAppointment['zjHealthExamRecordV' + version](physicalExamOrgId, item);
        if (res.status === 200) {
          result.successful.push(res.message);
        } else {
          result.failed.push(res.message);
        }
      }
      ctx.helper.renderCustom(ctx, {
        status: result.successful.length ? 200 : 400,
        data: result,
        message: '上传职业健康档案完成',
      });
    } catch (error) {
      console.log('上传职业健康档案报错', error);
      ctx.auditLog('上传职业健康档案报错', error, 'error');
      ctx.helper.renderCustom(ctx, {
        status: 404,
        message: error.message,
      });
    }
  }
  // 处理xml数据
  async convertXmlData(body, listName, itemName) {
    const list = body[listName];
    if (!Array.isArray(list)) throw new Error('参数错误');
    for (let i = 0; i < list.length; i++) {
      list[i] = list[i][itemName][0];
      for (const key in list[i]) {
        if (list[i].hasOwnProperty(key)) {
          if (key === '$') {
            const idObj = list[i][key];
            if (idObj.id) {
              list[i].id = idObj.id;
            } else if (idObj.ID) {
              list[i].ID = idObj.ID;
            }
            delete list[i][key];
          } else {
            list[i][key] = this.normalizeData(list[i][key]);
            if ([ 'EXAM_ITEM_RESULT_LIST', 'EXAM_CONCLUSION_LIST', 'contactHazardFactorList', 'hazardFactorList', 'diagnosisList' ].includes(key)) {
              list[i][key] = Object.values(list[i][key]);
            } else if (key === 'itemList') {
              list[i][key] = (list[i][key].item).map(j => {
                const newItem = {};
                for (const [ key, value ] of Object.entries(j)) {
                  newItem[key] = value[0];
                }
                return newItem;
              });
            }
          }
        }
      }
    }
    return list;
  }
  // 数据规范化 递归
  normalizeData(data) {
    if (Array.isArray(data) && data.length === 1) {
      if (typeof data[0] === 'string') {
        return data[0].trim();
      } else if (typeof data[0] === 'object') {
        for (const key in data[0]) {
          if (data[0].hasOwnProperty(key)) {
            const res = this.normalizeData(data[0][key]);
            data[0][key] = res;
          }
        }
        return data[0];
      }
    }
    return data;
  }
  // 上传用人单位信息 - 浙江省标准
  async zjEmployerList(ctx) {
    try {
      const accept = ctx.header.accept;
      const version = this.checkAccept(ctx, accept);
      const physicalExamOrgId = await ctx.service.healthCheckAppointment.getOrgId(ctx.header.token);
      let list;
      if (ctx.is('xml')) {
        list = await this.convertXmlData(ctx.request.body.data.body[0], 'employerList', 'employer');
        ctx.auditLog('温州体检系统对接 上传用人单位信息 xml转化后的数据', list, 'info');
      } else {
        list = ctx.request.body.employerList;
      }
      await ctx.service.healthCheckAppointment.addInterfaceLog(ctx.request.url, physicalExamOrgId, list);
      const result = { successful: [], failed: [] };
      for (let i = 0; i < list.length; i++) {
        const item = list[i];
        const res = await ctx.service.healthCheckAppointment['zjEmployerListV' + version](physicalExamOrgId, item);
        if (res.status === 200) {
          result.successful.push(res.message);
        } else {
          result.failed.push(res.message);
        }
      }
      if (result.successful.length === 0) {
        return ctx.helper.renderCustom(ctx, {
          status: 400,
          data: result,
          message: '操作完成',
        });
      }
      ctx.helper.renderCustom(ctx, {
        status: 200,
        data: result,
        message: '操作完成',
      });
    } catch (error) {
      console.log('上传用人单位信息报错', error);
      ctx.auditLog('上传用人单位信息报错', error, 'error');
      ctx.helper.renderCustom(ctx, {
        status: 404,
        message: error.message,
      });
    }
  }
  // 上传用人单位信息 - 国家标准
  async enterpriseInfoList(ctx) {
    try {
      const accept = ctx.header.accept;
      const version = this.checkAccept(ctx, accept);
      const physicalExamOrgId = await ctx.service.healthCheckAppointment.getOrgId(ctx.header.token);
      let list;
      if (ctx.is('xml')) {
        list = await this.convertXmlData(ctx.request.body.DataExchange.EventBody[0], 'ENTERPRISE_INFO_LIST', 'ENTERPRISE_INFO');
        ctx.auditLog('体检系统对接 上传用人单位信息 xml转化后的数据', list, 'info');
      } else {
        list = ctx.request.body.ENTERPRISE_INFO_LIST;
      }
      await ctx.service.healthCheckAppointment.addInterfaceLog(ctx.request.url, physicalExamOrgId, list);
      const result = { successful: [], failed: [] };
      for (let i = 0; i < list.length; i++) {
        const item = list[i];
        const res = await ctx.service.healthCheckAppointment_cn['enterpriseInfoV' + version](physicalExamOrgId, item);
        if (res.status === 200) {
          result.successful.push(res.message);
        } else {
          result.failed.push(res.message);
        }
      }
      if (result.successful.length === 0) {
        return ctx.helper.renderCustom(ctx, {
          status: 400,
          data: result,
          message: '操作完成',
        });
      }
      ctx.helper.renderCustom(ctx, {
        status: 200,
        data: result,
        message: '操作完成',
      });
    } catch (error) {
      console.log('上传用人单位信息报错', error);
      ctx.auditLog('上传用人单位信息报错', error, 'error');
      ctx.helper.renderCustom(ctx, {
        status: 404,
        message: error.message,
      });
    }
  }

  // 机构获取其体检预约列表
  async list(ctx) {
    try {
      const accept = ctx.header.accept;
      const version = this.checkAccept(ctx, accept);
      const physicalExamOrgId = await ctx.service.healthCheckAppointment.getOrgId(ctx.header.token);
      const data = await ctx.service.healthCheckAppointment['listV' + version](physicalExamOrgId, ctx.query);
      ctx.helper.renderCustom(ctx, {
        status: 200,
        data,
        message: '获取体检预约列表',
      });
    } catch (error) {
      console.log('获取体检预约列表报错', error);
      ctx.auditLog('获取体检预约列表报错', error, 'error');
      ctx.helper.renderCustom(ctx, {
        status: 404,
        message: error.message,
      });
    }
  }

  // 确认体检日期
  async confirmDate(ctx) {
    try {
      ctx.auditLog('确认体检日期进来了', 11223366, 'info');
      const accept = ctx.header.accept;
      const version = this.checkAccept(ctx, accept);
      const physicalExamOrgId = await ctx.service.healthCheckAppointment.getOrgId(ctx.header.token);
      console.log(555555, physicalExamOrgId);
      const data = await ctx.service.healthCheckAppointment['confirmDateV' + version](physicalExamOrgId, ctx.request.body);
      ctx.helper.renderCustom(ctx, {
        status: 200,
        data,
        message: '体检日期确认成功',
      });
    } catch (error) {
      console.log('确认体检日期报错', error);
      ctx.auditLog('确认体检日期报错', error, 'error');
      ctx.helper.renderCustom(ctx, {
        status: 400,
        message: error.message,
      });
    }

  }
  // 获取单个体检预约单详情
  async getOne(ctx) {
    try {
      const accept = ctx.header.accept;
      const version = this.checkAccept(ctx, accept);
      const physicalExamOrgId = await ctx.service.healthCheckAppointment.getOrgId(ctx.header.token);
      const data = await ctx.service.healthCheckAppointment['getOneV' + version](physicalExamOrgId, ctx.query);
      ctx.helper.renderCustom(ctx, {
        status: 200,
        data,
        message: '获取体检预约单详情成功',
      });
    } catch (error) {
      ctx.logger.error('获取体检预约单详情', error.message, 'error');
      ctx.helper.renderCustom(ctx, {
        status: 404,
        message: error.message,
      });
    }
  }

  // json版本号检查
  checkAccept(ctx, accept) {
    const version = ctx.helper.getAPIVersion(accept, 'application/oapi.zyws.v', '+json');
    const versions = [ '1_0' ];
    if (!version || version.indexOf(versions) === -1) {
      throw new Error('版本号token错误，请检查');
    }
    return version;
  }
  // xml版本号检查
  checkAcceptXml(ctx, accept) {
    const version = ctx.helper.getAPIVersion(accept, 'application/oapi.zyws.v', '+xml');
    const versions = [ '1_0' ];
    if (!version || version.indexOf(versions) === -1) {
      throw new Error('版本号token错误，请检查');
    }
    return version;
  }

  // 上传职业健康档案 - 陕西标准（北元）
  async shaanxiHealthExamRecordList(ctx) {
    try {
      const accept = ctx.header.accept;
      const version = this.checkAcceptXml(ctx, accept);
      const physicalExamOrgId =
        await ctx.service.healthCheckAppointment.getOrgId(ctx.header.token);
      const physicalExamOrg = await ctx.model.PhysicalExamOrg.findOne({
        _id: physicalExamOrgId,
      });
      let convertedBody;
      if (ctx.is('xml')) {
        const xmlData = ctx.request.body;
        const parser = new XMLParser({
          ignoreDeclaration: true,
          ignorePiTags: true,
          numberParseOptions: { leadingZeros: false },
        });
        convertedBody = parser.parse(xmlData);
        ctx.auditLog(
          '体检系统对接 陕西上传职业健康档案 xml转换成功',
          'success',
          'info'
        );
      }
      await ctx.service.healthCheckAppointment.addInterfaceLog(
        ctx.request.url,
        physicalExamOrgId,
        convertedBody
      );
      const sendData = {
        convertedBody,
        physicalExamOrgId,
        version,
      };
      const requiredFields = [
        {
          field: 'INSTITUTION_CODE',
          message: '企业社会信用代码',
          format: /^(([0-9A-Za-z]{15})|([0-9A-Za-z]{18})|([0-9A-Za-z]{20}))$/,
        },
        { field: 'BHK_CODE', message: '个人体检编号' },
        { field: 'CRPT_NAME', message: '企业名称' },
        { field: 'PROJECT_NUM', message: '体检项目编号' },
        { field: 'PERSON_NAME', message: '员工姓名' },
        {
          field: 'IDC',
          message: '员工身份证号',
          format:
            /^[1-9]\d{5}(?:18|19|20)\d{2}(?:0[1-9]|10|11|12)(?:0[1-9]|[1-2]\d|30|31)\d{3}[\dXx]$/,
        },
        {
          field: 'LNKTEL',
          message: '联系电话',
          format: /^(?:(?:\+|00)86)?1[3-9]\d{9}$/,
        },
      ];
      const employeeAdminInfo = convertedBody.DATAS.PERSONS.PERSON;
      if (employeeAdminInfo && Array.isArray(employeeAdminInfo) && employeeAdminInfo.length > 0) {
        for (let i = 0; i < employeeAdminInfo.length; i++) {
          const item = employeeAdminInfo[i].TD_TJ_BHK;
          this.ctx.service.shaanxiData.validateParams(item, requiredFields);
          if (
            item.ORG_CODE &&
            item.ORG_CODE !== physicalExamOrg.organization
          ) {
            throw new Error('您的token跟上传的创建机构代码不匹配');
          }
        }
      } else if (
        Object.prototype.toString.call(employeeAdminInfo) === '[object Object]'
      ) {
        this.ctx.service.shaanxiData.validateParams(
          employeeAdminInfo.TD_TJ_BHK,
          requiredFields
        );
        if (
          employeeAdminInfo.TD_TJ_BHK.ORG_CODE &&
          employeeAdminInfo.TD_TJ_BHK.ORG_CODE !== physicalExamOrg.organization
        ) {
          throw new Error('您的token跟上传的创建机构代码不匹配');
        }
      }
      // this.ctx.service.rabbitmq.produce(
      //   sendData,
      //   'shaanxi',
      //   'shaanxiHealthExamRecordData'
      // );
      this.ctx.service.rabbitmq.produce(
        {
          message: sendData,
          exchange: 'shaanxi',
          routingKey: 'shaanxiHealthExamRecordData',
        }
      );
      // await ctx.service.shaanxiData.uploadShaanxiHealthExamRecordData(sendData);
      ctx.helper.renderCustom(ctx, {
        status: 200,
        message: 'ok',
      });
    } catch (error) {
      console.log('上传职业健康档案报错', error);
      ctx.auditLog('上传职业健康档案报错', error, 'error');
      ctx.helper.renderCustom(ctx, {
        status: 404,
        message: error.message,
      });
    }
  }

  // 上传个人体检信息（文件）（北元）
  async uploadHealthExamRecordFile(ctx) {
    try {
      const accept = ctx.header.accept;
      const version = this.checkAccept(ctx, accept);
      const physicalExamOrgId =
        await ctx.service.healthCheckAppointment.getOrgId(ctx.header.token);
      const data = await ctx.service.shaanxiData[
        'uploadSuspectFileV' + version
      ](physicalExamOrgId, ctx);
      ctx.helper.renderCustom(ctx, {
        ...data,
      });
    } catch (error) {
      ctx.logger.error('上传个人体检信息个案卡失败', error.message, 'error');
      ctx.helper.renderCustom(ctx, {
        status: 404,
        message: error.message,
      });
    }
  }


}

module.exports = HealthCheckAppointmentController;
