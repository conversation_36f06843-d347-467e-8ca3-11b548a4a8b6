const Controller = require('egg').Controller;
class WhDataController extends Controller {
  // 接收万华人员数据
  async getPersonData(ctx) {
    try {
      const { item = [] } = ctx.request.body;
      if (!item.length) {
        ctx.body = {
          code: '1',
          msg: '参数错误',
        };
      }
      await ctx.service.whData.getPersonData(item);
      // await ctx.service.whData.getPersonData();
      ctx.body = {
        code: '0',
        msg: '接收成功',
      };
    } catch (error) {
      ctx.auditLog('接收万华人员数据', `接收失败：${error.message}`, 'error');
      ctx.body = {
        code: '1',
        msg: '接收失败',
      };
    }
  }

  // 接收万华组织数据
  async getOrgData(ctx) {
    try {
      const data = ctx.request.body;
      const { uid = '' } = data;
      if (!uid) {
        ctx.body = {
          code: '1',
          msg: '参数错误',
        };
      }
      await ctx.service.whData.getOrgData(data);
      // await ctx.service.whData.getOrgData();
      ctx.body = {
        code: '0',
        msg: '接收成功',
      };
    } catch (error) {
      ctx.auditLog('接收万华组织数据', `接收失败：${error.message}`, 'error');
      ctx.body = {
        code: '1',
        msg: '接收失败',
      };
    }
  }

  // 接收万华账号数据
  async getAccountData(ctx) {
    try {
      const data = ctx.request.body;
      const { uid = '' } = data;
      if (!uid) {
        ctx.body = {
          code: '1',
          msg: '参数错误',
        };
      }
      await ctx.service.whData.getAccountData(data);
      // await ctx.service.whData.getAccountData();
      ctx.body = {
        code: '0',
        msg: '接收成功',
      };
    } catch (error) {
      ctx.auditLog('接收万华账号数据', `接收失败：${error.message}`, 'error');
      ctx.body = {
        code: '1',
        msg: '接收失败',
      };
    }
  }

  // 接收万华岗位(管理组)数据
  async getPostData(ctx) {
    try {
      const body = ctx.request.body;
      const { systemId = '', data = [] } = body;
      if (!systemId || systemId !== 'SUPOS' || systemId !== 'BPM') {
        ctx.body = {
          code: '1',
          msg: '参数错误',
        };
      }
      await ctx.service.whData.getPostData(data);
      ctx.body = {
        code: '0',
        msg: '接收成功',
      };
    } catch (error) {
      ctx.auditLog('接收万华岗位(管理组)数据', `接收失败：${error.message}`, 'error');
      ctx.body = {
        code: '1',
        msg: '接收失败',
      };
    }
  }

  // 接收万华岗位人员（管理组）数据
  async getPostAndPersonData(ctx) {
    try {
      const body = ctx.request.body;
      const { systemId = '', data = [], taskBillNo } = body;
      if (!systemId || systemId !== 'BPM' || !taskBillNo || !data.length) {
        ctx.body = {
          code: '1',
          msg: '参数错误',
        };
      }
      await ctx.service.whData.getPostAndPersonData(body);
      ctx.body = {
        code: '0',
        msg: '接收成功',
      };
    } catch (error) {
      ctx.auditLog('接收万华岗位人员(管理组)数据', `接收失败：${error.message}`, 'error');
      ctx.body = {
        code: '1',
        msg: '接收失败',
      };
    }
  }
  // 上传职业健康档案 - 国家标准
  async healthExamRecordList(ctx) {
    try {
      const accept = ctx.header.accept;
      const version = this.checkAccept(ctx, accept);
      const physicalExamOrgId =
        await ctx.service.healthCheckAppointment.getOrgId(ctx.header.token);
      let examRecordList;
      if (ctx.is('xml')) {
        examRecordList = await this.convertXmlData(
          ctx.request.body.DataExchange.EventBody[0],
          'HEALTH_EXAM_RECORD_LIST',
          'HEALTH_EXAM_RECORD'
        );
        ctx.auditLog(
          '体检系统对接 上传职业健康档案 xml转化后的数据',
          examRecordList,
          'info'
        );
      } else {
        examRecordList = ctx.request.body.HEALTH_EXAM_RECORD_LIST;
      }
      await ctx.service.healthCheckAppointment.addInterfaceLog(
        ctx.request.url,
        physicalExamOrgId,
        examRecordList
      );
      const result = { successful: [], failed: [] };
      for (let i = 0; i < examRecordList.length; i++) {
        const item = examRecordList[i];
        const res = await ctx.service.whData['healthExamRecordV' + version](
          physicalExamOrgId,
          item
        );
        if (res.status === 200) {
          result.successful.push(res.message);
        } else {
          result.failed.push(res.message);
        }
      }
      ctx.helper.renderCustom(ctx, {
        status: 200,
        data: result,
        message: '上传职业健康档案完成',
      });
    } catch (error) {
      console.log('上传职业健康档案报错', error);
      ctx.auditLog('上传职业健康档案报错', error, 'error');
      ctx.helper.renderCustom(ctx, {
        status: 404,
        message: error.message,
      });
    }
  }
  // 处理xml数据
  async convertXmlData(body, listName, itemName) {
    const list = body[listName];
    if (!Array.isArray(list)) throw new Error('参数错误');
    for (let i = 0; i < list.length; i++) {
      list[i] = list[i][itemName][0];
      for (const key in list[i]) {
        if (list[i].hasOwnProperty(key)) {
          if (key === '$') {
            const idObj = list[i][key];
            if (idObj.id) {
              list[i].id = idObj.id;
            } else if (idObj.ID) {
              list[i].ID = idObj.ID;
            }
            delete list[i][key];
          } else {
            list[i][key] = this.normalizeData(list[i][key]);
            if (
              [
                'EXAM_ITEM_RESULT_LIST',
                'EXAM_CONCLUSION_LIST',
                'contactHazardFactorList',
                'hazardFactorList',
                'diagnosisList',
              ].includes(key)
            ) {
              list[i][key] = Object.values(list[i][key]);
            } else if (key === 'itemList') {
              list[i][key] = list[i][key].item.map(j => {
                const newItem = {};
                for (const [ key, value ] of Object.entries(j)) {
                  newItem[key] = value[0];
                }
                return newItem;
              });
            }
          }
        }
      }
    }
    return list;
  }
  // 数据规范化 递归
  normalizeData(data) {
    if (Array.isArray(data) && data.length === 1) {
      if (typeof data[0] === 'string') {
        return data[0].trim();
      } else if (typeof data[0] === 'object') {
        for (const key in data[0]) {
          if (data[0].hasOwnProperty(key)) {
            const res = this.normalizeData(data[0][key]);
            data[0][key] = res;
          }
        }
        return data[0];
      }
    }
    return data;
  }

  // 机构获取其体检预约列表
  async list(ctx) {
    try {
      const accept = ctx.header.accept;
      const version = this.checkAccept(ctx, accept);
      const physicalExamOrgId =
        await ctx.service.healthCheckAppointment.getOrgId(ctx.header.token);
      const data = await ctx.service.healthCheckAppointment['listV' + version](
        physicalExamOrgId,
        ctx.query
      );
      ctx.helper.renderCustom(ctx, {
        status: 200,
        data,
        message: '获取体检预约列表',
      });
    } catch (error) {
      console.log('获取体检预约列表报错', error);
      ctx.auditLog('获取体检预约列表报错', error, 'error');
      ctx.helper.renderCustom(ctx, {
        status: 404,
        message: error.message,
      });
    }
  }

  // 确认体检日期
  async confirmDate(ctx) {
    try {
      ctx.auditLog('确认体检日期进来了', 11223366, 'info');
      const accept = ctx.header.accept;
      const version = this.checkAccept(ctx, accept);
      const physicalExamOrgId =
        await ctx.service.healthCheckAppointment.getOrgId(ctx.header.token);
      console.log(555555, physicalExamOrgId);
      const data = await ctx.service.healthCheckAppointment[
        'confirmDateV' + version
      ](physicalExamOrgId, ctx.request.body);
      ctx.helper.renderCustom(ctx, {
        status: 200,
        data,
        message: '体检日期确认成功',
      });
    } catch (error) {
      console.log('确认体检日期报错', error);
      ctx.auditLog('确认体检日期报错', error, 'error');
      ctx.helper.renderCustom(ctx, {
        status: 400,
        message: error.message,
      });
    }
  }
  // 获取单个体检预约单详情
  async getOne(ctx) {
    try {
      const accept = ctx.header.accept;
      const version = this.checkAccept(ctx, accept);
      const physicalExamOrgId =
        await ctx.service.healthCheckAppointment.getOrgId(ctx.header.token);
      const data = await ctx.service.healthCheckAppointment[
        'getOneV' + version
      ](physicalExamOrgId, ctx.query);
      ctx.helper.renderCustom(ctx, {
        status: 200,
        data,
        message: '获取体检预约单详情成功',
      });
    } catch (error) {
      ctx.logger.error('获取体检预约单详情', error.message, 'error');
      ctx.helper.renderCustom(ctx, {
        status: 404,
        message: error.message,
      });
    }
  }

  // json版本号检查
  checkAccept(ctx, accept) {
    const version = ctx.helper.getAPIVersion(
      accept,
      'application/oapi.zyws.v',
      '+json'
    );
    const versions = [ '1_0' ];
    if (!version || version.indexOf(versions) === -1) {
      throw new Error('版本号token错误，请检查');
    }
    return version;
  }
  // xml版本号检查
  checkAcceptXml(ctx, accept) {
    const version = ctx.helper.getAPIVersion(
      accept,
      'application/oapi.zyws.v',
      '+xml'
    );
    const versions = [ '1_0' ];
    if (!version || version.indexOf(versions) === -1) {
      throw new Error('版本号token错误，请检查');
    }
    return version;
  }
}

module.exports = WhDataController;
