const { tools } = require('@utils');
const shortid = require('shortid');
const {
  apiUserRule,
} = require('@validate/apiUser');
const Controller = require('egg').Controller;
class ApiUserController extends Controller {
  async getTypes() {
    const { ctx } = this;
    try {
      const resultArray = [
        { label: '企业用户', value: 1 },
        { label: '政府用户', value: 2 },
        { label: '机构用户', value: 3 },
        { label: '体检用户', value: 4 },
      ];
      ctx.helper.renderSuccess(ctx, {
        data: resultArray,
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  }
  async list() {
    const { ctx, config } = this;
    try {
      const params = ctx.query,
        payload = { current: params.current, pageSize: params.pageSize, searchkey: params.searchkey || '' },
        type = params.type,
        status = params.status ? parseInt(params.status) : 0;
      let collections = [];
      let query = {};
      let searchKeys = [];
      let files = {};
      switch (type) {
        case '2':
          collections = [{
            name: 'superusers',
            selfKey: 'orgId',
            foreignKey: '_id',
            asKey: 'orgId',
          }];
          searchKeys = [ 'orgId.cname', 'orgId.name', 'orgId.phoneNum' ];
          query = { status, group: config.groupID.superGroupID };
          files = {
            _id: 1,
            message: 1,
            status: 1,
            'orgId.cname': 1,
            'orgId.name': 1,
            'orgId.phoneNum': 1,
          };
          break;
        case '3':
          collections = [{
            name: 'serviceOrg',
            selfKey: 'orgId',
            foreignKey: '_id',
            asKey: 'orgId',
          }, {
            name: 'serviceUsers',
            selfKey: 'orgId.managers',
            foreignKey: '_id',
            asKey: 'managers',
          }];
          searchKeys = [ 'orgId.name', 'orgId.corp', 'managers.phoneNum' ];
          query = { status, group: config.groupID.serviceGroupID };
          files = {
            _id: 1,
            message: 1,
            status: 1,
            'orgId.name': 1,
            'orgId.corp': 1,
            'managers.phoneNum': 1,
          };
          break;
        case '4':
          collections = [{
            name: 'physicalExamOrgs',
            selfKey: 'orgId',
            foreignKey: '_id',
            asKey: 'orgId',
          }, {
            name: 'physicalExamUsers',
            selfKey: 'orgId.managers',
            foreignKey: '_id',
            asKey: 'managers',
          }];
          searchKeys = [ 'orgId.name', 'orgId.corp', 'managers.phoneNum' ];
          query = { status, group: config.groupID.physicalExamGroupID };
          files = {
            _id: 1,
            message: 1,
            status: 1,
            'orgId.name': 1,
            'orgId.corp': 1,
            'managers.phoneNum': 1,
          };
          break;
        case '1':
        default:
          collections = [{
            name: 'adminorgs',
            selfKey: 'orgId',
            foreignKey: '_id',
            asKey: 'orgId',
          }, {
            name: 'adminusers',
            selfKey: 'orgId.adminUserId',
            foreignKey: '_id',
            asKey: 'adminUserId',
          }];
          searchKeys = [ 'orgId.cname', 'orgId.corp', 'adminUserId.phoneNum' ];
          query = { status, group: config.groupID.adminGroupID };
          files = {
            _id: 1,
            message: 1,
            status: 1,
            'orgId.cname': 1,
            'orgId.corp': 1,
            'adminUserId.phoneNum': 1,
          };
          break;
      }
      const paramsA = {
        collections,
        query,
        searchKeys,
        files,
        sort: {
          date: -1,
        },
      };

      const apiUserList = await ctx.service.apiUser.unionQuery(payload, paramsA);

      if (apiUserList) {
        const docs = apiUserList.docs,
          docsCount = docs.length,
          array = [];
        let arrayJson = {};
        delete apiUserList.pageInfo.group;
        apiUserList.pageInfo.type = type;
        switch (type) {
          case '2':
            for (let i = 0; i < docsCount; i++) {
              const item = docs[i];
              const orgId = item.orgId,
                orgIdCount = orgId.length;
              arrayJson._id = item._id;
              arrayJson.message = item.message;
              arrayJson.status = item.status;
              for (let j = 0; j < orgIdCount; j++) {
                const org = orgId[j];
                arrayJson.name = org.name;
                arrayJson.cname = org.cname;
                arrayJson.phoneNum = org.phoneNum;
              }
              array.push(arrayJson);
              arrayJson = {};
            }
            break;
          case '3':
          case '4':
            for (let i = 0; i < docsCount; i++) {
              const item = docs[i];
              const orgId = item.orgId,
                orgIdCount = orgId.length,
                managers = item.managers,
                managersCount = managers.length;
              arrayJson._id = item._id;
              arrayJson.message = item.message;
              arrayJson.status = item.status;
              for (let j = 0; j < orgIdCount; j++) {
                const org = orgId[j];
                arrayJson.cname = org.name;
                arrayJson.name = org.corp;
              }
              let phone = '';
              for (let k = 0; k < managersCount; k++) {
                k === managersCount - 1 ? phone += managers[k].phoneNum : phone += managers[k].phoneNum + ',\r\n';
                arrayJson.phoneNum = phone;
              }
              array.push(arrayJson);
              arrayJson = {};
            }
            break;
          case '1':
          default:
            for (let i = 0; i < docsCount; i++) {
              const item = docs[i];
              const orgId = item.orgId,
                orgIdCount = orgId.length,
                adminUserId = item.adminUserId,
                adminUserIdCount = adminUserId.length;
              arrayJson._id = item._id;
              arrayJson.message = item.message;
              arrayJson.status = item.status;
              for (let j = 0; j < orgIdCount; j++) {
                const org = orgId[j];
                arrayJson.cname = org.cname;
                arrayJson.name = org.corp;
              }
              for (let k = 0; k < adminUserIdCount; k++) {
                arrayJson.phoneNum = adminUserId[k].phoneNum;
              }
              array.push(arrayJson);
              arrayJson = {};
            }
            break;
        }
        apiUserList.docs = array;
      }

      ctx.helper.renderSuccess(ctx, {
        data: apiUserList,
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  }

  async getOne() {
    const { ctx, config } = this;
    try {
      const _id = ctx.query.id;

      let targetItem = await ctx.service.apiUser.item(ctx, {
        files: {
          _id: 1,
          name: 1,
          corp: 1,
          organization: 1,
          regAddr: 1,
          address: 1,
          status: 1,
          img: 1,
          qualifies: 1,
        },
        query: {
          _id,
        },
      });

      if (targetItem) {
        targetItem = tools.convertToEditJson(targetItem);
        targetItem.img = config.static.prefix + config.upload_http_path + targetItem.img;
      } else {
        targetItem = {};
      }

      ctx.helper.renderSuccess(ctx, {
        data: targetItem,
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  }

  async create() {
    const { ctx, config } = this;
    try {
      const fields = ctx.request.body || {},
        agentId = shortid.generate(),
        orgType = fields.orgType || '',
        orgId = fields.orgId || '';
      let group = '';
      switch (orgType) {
        case '0': // 企业
          group = config.groupID.adminGroupID;
          break;
        case '1': // 行政
          group = config.groupID.superGroupID;
          break;
        case '2': // 机构
          group = config.groupID.serviceGroupID;
          break;
        case '3': // 体检
          group = config.groupID.physicalExamGroupID;
          break;
        default:
          group = '';
          break;
      }

      const formObj = {
        _id: agentId,
        orgId,
        group,
        enable: true,
        status: 1,
      };
      ctx.validate(apiUserRule.form(ctx), formObj);

      ctx.service.apiUser.create(formObj);
      ctx.helper.renderSuccess(ctx, {
        data: {
          agentId,
        },
        message: '开通API成功',
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  }

  async update() {
    const { ctx } = this;
    try {

      const fields = ctx.request.body || {},
        status = fields.status || '',
        phoneNum = fields.phoneNum || '',
        message = fields.message || '',
        _id = fields._id || '',
        editType = fields.editType || '';
      if (_id === '' || status === '' || phoneNum === '' || editType === '') {
        ctx.helper.renderFail(ctx, {
          message: phoneNum === '' ? '请先完善手机号！' : '哎呀！网络开小差了！要不，稍后再试试？',
        });
        return;
      }
      if (editType === '2' && message === '') {
        ctx.helper.renderFail(ctx, {
          message: '哎呀！网络开小差了！要不，稍后再试试？',
        });
        return;
      }
      const formObj = editType !== '2' ? { status } : { message, status };

      // const beforUpdate = await ctx.service.apiUser.item(ctx, {
      //   query: {
      //     _id,
      //   },
      // });

      await ctx.service.apiUser.update(ctx, _id, formObj);
      // 审核操作就发短信
      // if (formObj.status !== beforUpdate.status && formObj.status > 2) {
      //   ctx.service.apiUser.sendVerifyMassage(ctx, app.config.aliMessage, serviceUser, formObj);
      // }

      ctx.helper.renderSuccess(ctx);
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  }

  async removes() {
    const { ctx } = this;
    try {
      const targetIds = ctx.query.ids;
      await ctx.service.apiUser.removes(ctx, targetIds);
      ctx.helper.renderSuccess(ctx);
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  }

}
module.exports = ApiUserController;
