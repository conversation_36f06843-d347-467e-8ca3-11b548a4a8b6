const Controller = require('egg').Controller;
class TrainPayController extends Controller {
  // 培训当面付
  async trainNativePay() {
    const { ctx } = this;
    try {
      const data = ctx.request.body;
      if (!data.description) {
        throw '支付调起失败，缺少商品描述';
      }
      if (!data.out_trade_no) {
        throw '支付调起失败，缺少商品描述';
      }
      if (!data.amount.total) {
        throw '支付调起失败，缺少商品金额';
      }
      if (!data.type) {
        throw '支付调起失败，缺少支付方式';
      }
      const res = await ctx.service.trainPay.createCertificateOrder(data);
      ctx.helper.renderSuccess(ctx, {
        data: res,
        message: '支付调起成功',
      });

    } catch (error) {
      ctx.auditLog('在线支付', `${error}`, 'error');
      return ctx.helper.renderFail(ctx, {
        message: '服务器伐开心',
        data: null,
      });
    }
  }
  // 培训团购
  async trainGroupPay() {
    const { ctx } = this;
    try {
      const data = ctx.request.body;
      if (!data.description) {
        throw '支付调起失败，缺少商品描述';
      }
      if (!data.out_trade_no) {
        throw '支付调起失败，缺少商品描述';
      }
      if (!data.amount.total) {
        throw '支付调起失败，缺少商品金额';
      }
      if (!data.type) {
        throw '支付调起失败，缺少支付方式';
      }
      if (!data.quota) {
        throw '支付调起失败，缺少团购人数';
      }
      const res = await ctx.service.trainPay.createGroupOrder(data);
      ctx.helper.renderSuccess(ctx, {
        data: res,
        message: '支付调起成功',
      });

    } catch (error) {
      ctx.auditLog('在线支付', `${error}`, 'error');
      return ctx.helper.renderFail(ctx, {
        message: '服务器伐开心',
        data: null,
      });
    }
  }
}
module.exports = TrainPayController;
