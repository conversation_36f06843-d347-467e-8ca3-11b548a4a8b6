const Controller = require('egg').Controller;
const jwt = require('jsonwebtoken');
const _ = require('lodash');
const validator = require('validator');
const { validatorUtil, authToken, siteFunc } = require('@utils');
const { userRule } = require('@validate');

class SuperUserController extends Controller {

  // 用户名登陆
  async loginAction() {

    const {
      ctx,
      app,
      config,
    } = this;
    try {

      const fields = ctx.request.body || {};

      const formObj = {
        userName: fields.userName.trim(),
      };

      ctx.validate(userRule.login(ctx), Object.assign({}, formObj, {
        password: fields.password,
      }));
      const user = await ctx.service.superUser.item(ctx, {
        query: formObj,
        files: '_id password enable userName logo name cname',
      });

      if (!_.isEmpty(user)) {
        const userPsd = user.password;

        const hashPassword = ctx.helper.hashSha256(fields.password, config.salt_sha2_key);
        if (userPsd !== hashPassword) {
          ctx.helper.renderFail(ctx, {
            message: ctx.__('validate_login_notSuccess_1'),
          });
          return;
        }

        if (!user.enable) {
          ctx.helper.renderFail(ctx, {
            message: ctx.__('validate_user_forbiden'),
          });
          return;
        }

        const userToken = jwt.sign({
          _id: user._id,
        }, app.config.encrypt_key, {
          expiresIn: '30day',
        });

        ctx.cookies.set('admin_' + app.config.auth_govcookie_name, userToken, {
          path: '/',
          maxAge: app.config.adminUserMaxAge,
          signed: true,
          httpOnly: false,
        }); // cookie 有效期30天

        // 记录登录日志
        const clientIp = ctx.header['x-forwarded-for'] || ctx.header['x-real-ip'] || ctx.request.ip;
        const loginLog = {
          type: 'login',
          logs: user.userName + ' login，ip:' + clientIp,
        };

        if (!_.isEmpty(ctx.service.systemOptionLog)) {
          await ctx.service.systemOptionLog.create(loginLog);
        }

        ctx.auditLog('登陆操作', `未知用户正在通过用户名 ${formObj.userName} 执行登录。`, 'info');
        const userInfo = JSON.parse(JSON.stringify(user));
        delete userInfo.password;
        // if (user.logo) user.logo = 'http://' + ctx.request.header.host + user.logo;
        ctx.helper.renderSuccess(ctx, {
          message: ctx.__('validate_user_loginOk'),
          data: {
            userInfo,
            userToken: userToken || '',
          },
        });
      } else {
        ctx.helper.renderFail(ctx, {
          message: ctx.__('validate_login_notSuccess_1'),
        });
      }

    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });

    }
  }
  // 手机验证码登陆
  async smLoginAction(ctx) {
    try {
      const { app, ctx } = this;
      const fields = ctx.request.body || {};
      const messageCode = fields.messageCode.trim(),
        phoneNum = fields.phoneNum.trim(),
        countryCode = fields.countryCode,
        cacheKey = '_sendMessage_login_';
      let errMsg = '';
      if (!phoneNum || !validatorUtil.checkPhoneNum((phoneNum).toString())) {
        errMsg = ctx.__('validate_inputCorrect', [ ctx.__('label_user_phoneNum') ]);
      }

      if (!countryCode) {
        errMsg = ctx.__('validate_selectNull', [ ctx.__('label_user_countryCode') ]);
      }

      const params = countryCode + phoneNum;
      const currentCode = ctx.helper.getCache(app.config.session_secret + cacheKey + params);

      if (!messageCode || !validator.isNumeric((messageCode).toString()) || (messageCode).length !== 6) {
        errMsg = ctx.__('validate_inputCorrect', [ ctx.__('label_user_imageCode') ]);
      }

      if (!currentCode || !validator.isNumeric((currentCode).toString()) || (currentCode).length !== 6) {
        errMsg = '对不起！验证码错误！请重新发送验证码！';
      }

      if (currentCode !== messageCode) {
        errMsg = '对不起！验证码错误！看看是不是验证码写错了？';
      }

      if (errMsg) {
        // 依照需求决定是否使用严格的校验方式 现方式允许用户手机号或验证码写错后不用重新发送验证码
        // ctx.helper.clearCache(params, cacheKey);
        ctx.helper.renderFail(ctx, {
          message: errMsg,
        });
        return;
      }

      const queryUserObj = {
        $or: [{
          phoneNum,
        }, {
          phoneNum: '0' + phoneNum,
        }],
        countryCode,
      };
      const userCount = await ctx.service.superUser.count(queryUserObj);
      if (userCount > 0) {
        const user = await ctx.service.superUser.item(ctx, {
          query: queryUserObj,
          files: '_id enable userName name logo cname',
        });
        if (!user.enable) {
          ctx.helper.clearCache(params, cacheKey);
          ctx.helper.renderFail(ctx, {
            message: ctx.__('validate_user_forbiden'),
          });
          return;
        }

        const userToken = jwt.sign({
          _id: user._id,
        }, app.config.encrypt_key, {
          expiresIn: '30day',
        });

        ctx.cookies.set('admin_' + app.config.auth_govcookie_name, userToken, {
          path: '/',
          maxAge: app.config.adminUserMaxAge,
          signed: true,
          httpOnly: false,
        }); // cookie 有效期30天

        // 重置验证码
        ctx.helper.clearCache(params, cacheKey);
        ctx.auditLog('登陆操作', `未知用户正在通过手机号 ${phoneNum} 执行登录。`, 'info');
        const userInfo = JSON.parse(JSON.stringify(user));
        delete userInfo.password;
        // if (user.logo) user.logo = 'http://' + ctx.request.header.host + user.logo;
        ctx.helper.renderSuccess(ctx, {
          message: ctx.__('validate_user_loginOk'),
          data: {
            userInfo,
            userToken: userToken || '',
          },
        });
      } else {
        ctx.helper.renderFail(ctx, {
          message: '用户不存在',
        });
      }
    } catch (err) {
      // console.log(err)
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  }
  // 发送验证码
  async sendVerificationCode(ctx) {
    try {
      console.log('发送验证码');
      const { config, ctx, app } = this;
      const fields = ctx.query || {};
      const phoneNum = fields.phoneNum.trim();
      console.log(phoneNum);
      const countryCode = fields.countryCode;
      const messageType = fields.messageType;

      let cacheKey = '';
      let errMsg = '';

      if (!phoneNum || !validator.isNumeric(phoneNum.toString())) {
        errMsg = ctx.__('validate_inputCorrect', [ ctx.__('label_user_phoneNum') ]);
      }

      if (!countryCode) {
        errMsg = ctx.__('validate_selectNull', [ ctx.__('label_user_countryCode') ]);
      }

      if (!messageType) {
        errMsg = ctx.__('validate_error_params');
      }

      switch (messageType) {
        case '0': {
          cacheKey = '_sendMessage_reg_';
          break;
        }
        case '1': { // 登录
          cacheKey = '_sendMessage_login_';
          break;
        }
        case '7': { // 修改手机号
          // 校验现在账号的手机号和需要发送短信的手机号是否为同一个
          const _id = fields._id || (ctx.session.superUser ? ctx.session.superUser._id : (await authToken.checkToken(ctx.cookies.get('admin_' + app.config.auth_govcookie_name), app.config.encrypt_key))._id, ctx);
          const user = await ctx.service.superUser.item(ctx, {
            query: { _id },
            files: 'phoneNum',
          });
          if (user.phoneNum === phoneNum) {
            ctx.helper.renderFail(ctx, {
              message: '对不起！此为原手机号',
            });
            return;
          }
          cacheKey = '_sendMessage_update_';
          break;
        }
        default: {
          errMsg = ctx.__('validate_error_params');
          break;
        }
      }

      if (errMsg) {
        ctx.helper.renderFail(ctx, {
          message: errMsg,
        });
        return;
      }

      // 生成短信验证码
      const currentStr = siteFunc.randomString(6, '123456789');

      const endStr = countryCode + phoneNum;
      const currentKey = config.session_secret + cacheKey + endStr;
      console.log(currentStr, '---currentKey---', currentKey);
      ctx.helper.setCache(currentKey, currentStr, 1000 * 60 * 15); // 验证码缓存15分钟
      // 发送短消息
      await ctx.curl(`${this.config.iServiceHost}/api/sendSMS`, {
        method: 'POST',
        dataType: 'json', // 返回的数据类型
        data: {
          templateCodeName: 'verificationCode',
          TemplateParam: JSON.stringify({ code: currentStr.toString() }),
          PhoneNumbers: phoneNum,
        },
      });
      console.log(currentStr);

      ctx.helper.renderSuccess(ctx, {
        message: ctx.__('restful_api_response_success', [ ctx.__('user_action_tips_sendMessage') ]),
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  }

}

module.exports = SuperUserController;
