const Controller = require('egg').Controller;
const { authToken } = require('@utils');
const jwt = require('jsonwebtoken');
const _ = require('lodash');
const validator = require('validator');
const { siteFunc, validatorUtil } = require('@utils');
// const path = require('path');
// const {
//   _item,
// } = require(path.join(process.cwd(), 'app/service/general'));

const {
  userRule,
} = require('@validate');

class AllUserController extends Controller {

  async sendVerificationCode(ctx) {
    try {
      console.log('发送验证码');
      const { config, ctx, app } = this;
      const fields = ctx.query || {};
      const phoneNum = fields.phoneNum.trim();
      console.log(phoneNum);
      const countryCode = fields.countryCode;
      const messageType = fields.messageType;

      let cacheKey = '';
      let errMsg = '';

      if (!phoneNum || !validator.isNumeric(phoneNum.toString())) {
        errMsg = ctx.__('validate_inputCorrect', [ ctx.__('label_user_phoneNum') ]);
      }

      if (!countryCode) {
        errMsg = ctx.__('validate_selectNull', [ ctx.__('label_user_countryCode') ]);
      }

      if (!messageType) {
        errMsg = ctx.__('validate_error_params');
      }

      switch (messageType) {
        case '0': {
          cacheKey = '_sendMessage_reg_';
          break;
        }
        case '1': { // 登录
          cacheKey = '_sendMessage_login_';
          break;
        }
        case '7': { // 修改手机号
          // 校验现在账号的手机号和需要发送短信的手机号是否为同一个
          const _id = fields._id || (ctx.session.user ? ctx.session.user._id : (await authToken.checkToken(ctx.cookies.get('admin_' + app.config.auth_toolscookie_name), app.config.encrypt_key))._id, ctx);
          const user = await ctx.service.user.item(ctx, {
            query: { _id },
            files: this.getAuthUserFields('login'),
          });
          if (user.phoneNum === phoneNum) {
            ctx.helper.renderFail(ctx, {
              message: '对不起！此为原手机号',
            });
            return;
          }
          cacheKey = '_sendMessage_update_';
          break;
        }
        default: {
          errMsg = ctx.__('validate_error_params');
          break;
        }
      }

      if (errMsg) {
        ctx.helper.renderFail(ctx, {
          message: errMsg,
        });
        return;
      }

      // 生成短信验证码
      const currentStr = siteFunc.randomString(6, '123456789');

      const endStr = countryCode + phoneNum;
      const currentKey = config.session_secret + cacheKey + endStr;
      console.log(currentStr, '---currentKey---', currentKey);
      ctx.helper.setCache(currentKey, currentStr, 1000 * 60 * 15); // 验证码缓存15分钟
      // 发送短消息
      await ctx.curl(`${this.config.iServiceHost}/api/sendSMS`, {
        method: 'POST',
        dataType: 'json', // 返回的数据类型
        data: {
          templateCodeName: 'verificationCode',
          TemplateParam: JSON.stringify({ code: currentStr.toString() }),
          PhoneNumbers: phoneNum,
        },
      });
      console.log(currentStr);

      ctx.helper.renderSuccess(ctx, {
        message: ctx.__('restful_api_response_success', [ ctx.__('user_action_tips_sendMessage') ]),
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  }

  async loginAction() {
    console.log('7987987987987987987');

    const {
      ctx,
      app,
      config,
    } = this;
    try {

      const fields = ctx.request.body || {};
      // const systemConfigs = await ctx.service.systemConfig.find({
      //   isPaging: '0',
      // });
      // const {
      //   showImgCode,
      // } = systemConfigs[0];

      // let errMsg = '';
      // if (showImgCode && (!fields.imageCode || fields.imageCode !== ctx.session.imageCode)) {
      //   errMsg = ctx.__('validate_inputCorrect', [ ctx.__('label_user_imageCode') ]);
      // }

      // if (errMsg) {
      //   ctx.helper.renderFail(ctx, {
      //     message: errMsg,
      //   });
      //   return;
      // }

      const formObj = {
        userName: fields.userName.trim(),
      };

      ctx.validate(userRule.login(ctx), Object.assign({}, formObj, {
        password: fields.password,
      }));
      const toolsTargetUser = await ctx.service.allUser.getToolsTargetUser(ctx.request.body);
      const user = toolsTargetUser.user;

      if (!_.isEmpty(user)) {
        const userPsd = user.password;
        const hashPassword = ctx.helper.hashSha256(fields.password, config.salt_sha2_key);
        if (userPsd !== hashPassword) {
          ctx.helper.renderFail(ctx, {
            message: '密码输入错误',
          });
          return;
        }

        if (!user.enable) {
          ctx.helper.renderFail(ctx, {
            message: ctx.__('validate_user_forbiden'),
          });
          return;
        }

        const userToken = jwt.sign({
          _id: user._id,
        }, app.config.encrypt_key, {
          expiresIn: '30day',
        });

        ctx.cookies.set('admin_' + app.config.auth_toolscookie_name, userToken, {
          path: '/',
          maxAge: app.config.adminUserMaxAge,
          signed: false,
          httpOnly: false,
        }); // cookie 有效期30天
        // console.log('获取cookie', ctx.cookies, ctx.cookies.get('admin_' + app.config.auth_toolscookie_name, {
        //   path: '/',
        //   maxAge: app.config.adminUserMaxAge,
        //   signed: false,
        //   httpOnly: false,
        // }));
        // 记录登录日志
        const clientIp = ctx.header['x-forwarded-for'] || ctx.header['x-real-ip'] || ctx.request.ip;
        const loginLog = {
          type: 'login',
          logs: user.userName + ' login，ip:' + clientIp,
        };

        if (!_.isEmpty(ctx.service.systemOptionLog)) {
          await ctx.service.systemOptionLog.create(loginLog);
        }

        ctx.auditLog('登陆操作', `未知用户正在通过用户名 ${formObj.userName} 执行登录。`, 'info');
        if (user.logo) user.logo = 'http://' + ctx.request.header.host + user.logo;
        delete user.password;
        ctx.body = {
          status: 200,
          data: user,
          userToken: userToken || '',
          message: ctx.__('validate_user_loginOk'),
        };
      } else {
        ctx.helper.renderFail(ctx, {
          message: fields.userName.trim() + '的账号未注册',
        });
      }

    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });

    }
    console.log('564564564564564');
  }


  async smLoginAction(ctx) {
    console.log('78788787');
    try {
      const { app, ctx } = this;
      const fields = ctx.request.body || {};
      const messageCode = fields.messageCode.trim(),
        phoneNum = fields.phoneNum.trim(),
        countryCode = fields.countryCode,
        cacheKey = '_sendMessage_login_';
      let errMsg = '';
      if (!phoneNum || !validatorUtil.checkPhoneNum((phoneNum).toString())) {
        errMsg = ctx.__('validate_inputCorrect', [ ctx.__('label_user_phoneNum') ]);
      }

      if (!countryCode) {
        errMsg = ctx.__('validate_selectNull', [ ctx.__('label_user_countryCode') ]);
      }

      const params = countryCode + phoneNum;
      const currentCode = ctx.helper.getCache(app.config.session_secret + cacheKey + params);

      if (!messageCode || !validator.isNumeric((messageCode).toString()) || (messageCode).length !== 6) {
        errMsg = ctx.__('validate_inputCorrect', [ ctx.__('label_user_imageCode') ]);
      }

      if (!currentCode || !validator.isNumeric((currentCode).toString()) || (currentCode).length !== 6) {
        errMsg = '对不起！验证码错误！请重新发送验证码！';
      }

      if (currentCode !== messageCode) {
        console.log(currentCode, messageCode);
        errMsg = '对不起！验证码错误！看看是不是验证码写错了？';
      }

      if (errMsg) {
        // 依照需求决定是否使用严格的校验方式 现方式允许用户手机号或验证码写错后不用重新发送验证码
        // ctx.helper.clearCache(params, cacheKey);
        ctx.helper.renderFail(ctx, {
          message: errMsg,
        });
        return;
      }

      const toolsTargetUser = await ctx.service.allUser.getToolsTargetUser(ctx.request.body);
      const user = toolsTargetUser.user;
      if (user) {
        // const user = await ctx.service.user.item(ctx, {
        //   query: queryUserObj,
        //   files: this.getAuthUserFields('login'),
        // });
        console.log('用户信息', user.enable);

        if (!user.enable) {
          ctx.helper.clearCache(params, cacheKey);
          ctx.helper.renderFail(ctx, {
            message: ctx.__('validate_user_forbiden'),
          });
          return;
        }

        const userToken = jwt.sign({
          _id: user._id,
        }, app.config.encrypt_key, {
          expiresIn: '30day',
        });

        console.log('app.config.auth_toolscookie_name');
        ctx.cookies.set('admin_' + app.config.auth_toolscookie_name, userToken, {
          path: '/',
          maxAge: app.config.adminUserMaxAge,
          signed: true,
          httpOnly: false,
        }); // cookie 有效期30天

        console.log('获取cookie', ctx.cookies.get('admin_' + app.config.auth_toolscookie_name, userToken, {
          path: '/',
          maxAge: app.config.adminUserMaxAge,
          signed: true,
          httpOnly: false,
        }));
        // 重置验证码

        ctx.helper.clearCache(params, cacheKey);
        ctx.auditLog('登陆操作', `用户正在通过手机号 ${phoneNum} 执行登录。`, 'info');
        if (user.logo) user.logo = 'http://' + ctx.request.header.host + user.logo;
        ctx.helper.renderSuccess(ctx, {
          data: {
            user,
            userToken: userToken || '',
          },
          message: ctx.__('validate_user_loginOk'),
        });
      } else {
        const userObj = {
          userName: phoneNum,
          countryCode,
          phoneNum,
        };
        const user = await this.ctx.model.User.create(userObj);
        const userToken = jwt.sign({
          _id: user._id,
        }, app.config.encrypt_key, {
          expiresIn: '30day',
        });

        ctx.cookies.set('admin_' + app.config.auth_toolscookie_name, userToken, {
          path: '/',
          maxAge: app.config.adminUserMaxAge,
          signed: true,
          httpOnly: true,
        }); // cookie 有效期30天

        // 重置验证码
        ctx.helper.clearCache(params, cacheKey);
        ctx.auditLog('注册操作', `用户通过手机号 ${userObj.phoneNum} 注册成功并登录。`, 'info');
        // 返回数据
        if (user.logo) user.logo = 'http://' + ctx.request.header.host + user.logo;
        ctx.helper.renderSuccess(ctx, {
          data: {
            user,
            userToken: userToken || '',
          },
          message: ctx.__('validate_user_regOk'),
        });
      }
      ctx.session = {
        toolsAPP: {
          // model,
          phoneNum,
        },
      };
      console.log(ctx.session.toolsAPP, ctx.session.toolsAPP.model, '8888888888');
      const selectModel = this.ctx.session.model;

      const selectPhoneNum = this.ctx.session.phoneNum;
      // this.updatePwd(selectModel, selectPhoneNum); // 将selectModel传入updatePwd方法
      console.log(selectPhoneNum, selectModel, 'chasadohewoioiewhoiewgoiewgoiewn');
    } catch (err) {
      console.log(err);
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  }

  async updatePwd() {
    const { ctx, app } = this;
    console.log('jinlaijinlai', ctx.session);
    try {
      // 通过调用service的方法来获取数据
      const toolsUser = this.ctx.session.toolsUser;
      const user = await ctx.service.updatePwd.update(toolsUser.model, toolsUser._id, ctx.request.body.password, ctx.request.body.newPwd);
      console.log(user, 'user');
      if (user) {
        const userToken = jwt.sign({
          _id: user._id,
        }, app.config.encrypt_key, {
          expiresIn: '30day',
        });
        ctx.cookies.set('admin_' + app.config.auth_toolscookie_name, userToken, {
          path: '/',
          maxAge: app.config.adminUserMaxAge,
          signed: true,
          httpOnly: true,
        }); // cookie 有效期30天
        ctx.helper.renderSuccess(ctx, {
          data: {
            user,
            userToken: userToken || '',
          },
          message: ctx.__('validate_user_loginOk'),
        });
      } else {
        ctx.helper.renderFail(ctx, {
          message: ctx.__('validate_user_loginFail'),
        });
      }


    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });

    }
  }
  // 登录公共方法
  async loginActionCommon(unionIdObj, userRawData, session_key = '') { // alipayUnionId
    const { ctx, app } = this;
    const userCount = Object.values(unionIdObj)[0] ? await ctx.service.user.count(unionIdObj) : [];
    if (userCount > 0) {
      const user = await ctx.service.user.item(ctx, {
        query: unionIdObj,
        files: this.getAuthUserFields('login'),
      });
      if (!user.enable) {
        ctx.helper.renderFail(ctx, {
          message: ctx.__('validate_user_forbiden'),
        });
        return;
      }
      const userToken = jwt.sign({
        _id: user._id,
      }, app.config.encrypt_key, {
        expiresIn: '30day',
      });
      ctx.cookies.set('admin_' + app.config.auth_toolscookie_name, userToken, {
        path: '/',
        maxAge: app.config.adminUserMaxAge,
        signed: true,
        httpOnly: true,
      }); // cookie 有效期30天
      // ctx.auditLog('登陆操作', `未知用户正在通过手机号 ${phoneNum} 执行登录。`);
      ctx.body = {
        data: user,
        userToken,
        status: 200,
        session_key: session_key || '',
        message: ctx.__('validate_user_loginOk'),
      };
    } else {
      const user = await ctx.service.user.create(userRawData);
      const userToken = jwt.sign({
        _id: user._id,
      }, app.config.encrypt_key, {
        expiresIn: '30day',
      });

      ctx.cookies.set('admin_' + app.config.auth_toolscookie_name, userToken, {
        path: '/',
        maxAge: app.config.adminUserMaxAge,
        signed: true,
        httpOnly: true,
      }); // cookie 有效期30天
      ctx.body = {
        status: 200,
        data: user,
        userToken: userToken || '',
        session_key: session_key || '',
        message: ctx.__('validate_user_regOk'),
      };
    }
  }

  getAuthUserFields(type = '') {
    let fieldStr = 'id enable _id email userName logo phoneNum name companyId company idNo companyStatus employeeId';
    if (type === 'login') {
      fieldStr = 'employeeId enable password _id email userName logo phoneNum name companyId company idNo companyStatus employeeId';
    } else if (type === 'base') {
      fieldStr = 'id userName name password category group logo date enable state phoneNum idNo companyStatus employeeId company countryCode email watchers followers comments idNo favorites favoriteCommunityContent despises comments profession experience industry introduction birth gender';
    } else if (type === 'session') {
      fieldStr = 'id userName name password category group logo date enable state phoneNum companyStatus company employeeId countryCode watchers followers praiseContents praiseMessages praiseCommunityContent watchSpecials watchCommunity watchTags favorites favoriteCommunityContent despises despiseMessage despiseCommunityContent idNo position gender vip email comments';
    }
    return fieldStr;
  }

  async loginVerification(ctx) {
    const user = await ctx.model[ctx.session.toolsUser.model].findOne({ state: '1', _id: ctx.session.toolsUser._id });
    // console.log(1111, user, ctx.session.toolsUser._id)
    // 字段格式化
    const formatData = {
      _id: user._id,
      name: user.name,
      userName: user.userName,
      phoneNum: user.phoneNum,
      birth: user.birth,
      gender: user.gender,
      logo: user.logo,
      idNo: user.idNo,
    };
    if (user && user.state === '1') {
      ctx.helper.renderSuccess(ctx, {
        data: formatData,
        message: '校验成功',
      });
    } else {
      ctx.helper.renderFail(ctx, {
        message: '校验失败',
      });
    }
  }

  // app检查更新
  async checkForUpdate(ctx) {
    const params = ctx.request.body;
    // console.log('进来了吗', params);
    const doc = await ctx.model.AppManage.findOne({ platform: params.platform, status: true, appName: params.appName });
    // console.log(4444444, doc);
    if (doc) {
      const res = this.compareVersion(doc.version, params.version);
      if (res === 1) {
        const data = {
          // apk_path: ctx.app.config.origin + '/static' + ctx.app.config.upload_app_http_path + '/' + doc.annex_app[0].staticSrc,
          apk_path: 'http://192.168.0.192:7009/static' + ctx.app.config.upload_app_http_path + '/' + doc.annex_app[0].staticSrc,
          log: doc.log,
          importantUpdate: doc.importantUpdate,
          version: doc.version,
        };
        // 需要升级
        ctx.helper.renderSuccess(ctx, {
          data: {
            data,
            update: true,
            success: true,
          },
          message: '检查更新成功',
        });
      } else {
        ctx.helper.renderSuccess(ctx, {
          data: {
            update: false,
            success: true,
          },
          message: '检查更新成功',
        });
      }
    } else {
      ctx.helper.renderSuccess(ctx, {
        data: {
          update: false,
          success: true,
        },
        message: '检查更新成功',
      });
    }
  }
  // 比较版本号 version1: 数据库最新版本，version2：用户使用版本
  compareVersion(version1, version2) {
    version1 = version1.split('.');
    version2 = version2.split('.');
    const n = Math.max(version1.length, version2.length);
    for (let i = 0; i < n; i++) {
      const code1 = (version1[i] === undefined) ? 0 : parseInt(version1[i]);
      const code2 = (version2[i] === undefined) ? 0 : parseInt(version2[i]);
      if (code1 > code2) {
        return 1;
      } else if (code1 < code2) {
        return -1;
      }
    }
    return 0;
  }
}

module.exports = AllUserController;
