const Controller = require('egg').Controller;

class PpeVideoController extends Controller {
  // 获取视频播放地址
  async getPPEVideo() {
    try {
      const {
        ctx,
      } = this;
      const {
        VideoId,
        // courseID,
        // chapterID,
      } = ctx.query;
      const video = await ctx.model.PPEvideo.find({});
      const dataArr = [];
      for (let i = 0; i < video.length; i++) {
        const ele = video[i];
        const response = await ctx.helper.request_alivod('GetPlayInfo', {
          VideoId: ele.VideoId,
          Formats: 'mp4',
          AuthTimeout: 10800, // 3小时过期，我不信一个视频还能超过三小时ε=(´ο｀*)))唉，懒得写刷新接口了
          OutputType: 'cdn',
        }, {});
        response.PlayInfoList.name = ele.name;
        response.PlayInfoList.harmFactors = ele.harmFactors;
        response.PlayInfoList.type = ele.type;
        response.PlayInfoList.modelNumber = ele.modelNumber;
        dataArr.push(response.PlayInfoList);
      }
      const videoProgress = 0;
      try {
        await ctx.model.PPEvideo.updateOne({
          VideoId,
        }, {
          $inc: {
            times: 1,
          },
        });
      } catch (error) {
        ctx.logger.error(error);
        console.error(error);
      }
      ctx.helper.renderSuccess(ctx, {
        data: {
          response: dataArr,
          videoProgress,
          message: 'OK',
        },
      });
    } catch (error) {
      this.ctx.logger.error(error);
      console.error(error);
      this.ctx.helper.renderFail(this.ctx, {
        message: error,
      });
    }
  }


}
module.exports = PpeVideoController;
