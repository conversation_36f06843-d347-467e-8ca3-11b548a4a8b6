// 法律法规
const Controller = require('egg').Controller;

class LearningController extends Controller {
  async list(ctx) {
    const data = await ctx.service.learning.list(ctx.request.body);
    if (data) {
      ctx.helper.renderSuccess(ctx, { data, message: '数据获取成功' });
    } else {
      ctx.helper.renderFail(ctx, { data, message: '数据获取失败' });
    }

  }
  // 获取文档类别id
  async categories(ctx) {
    ctx.helper.renderSuccess(ctx, {
      data: this.app.config.categories,
      message: '数据获取成功',
    });
  }

  // 首页中全局搜索
  async globalSearch(ctx) {
    const data = await ctx.service.learning.globalSearch(ctx.request.body);
    ctx.helper.renderSuccess(ctx, { data, message: '数据获取成功' });
  }
  // 获取公开课列表
  async publicCoursesList(ctx) {
    const params = ctx.request.body;
    if (params.classificationId) { // 根据课程类型获取培训
      const data = await ctx.service.adminTraining.getList2(params);
      ctx.helper.renderSuccess(ctx, { data, message: '数据获取成功' });
      return;
    }
    const data = await ctx.service.adminTraining.getList(params);
    ctx.helper.renderSuccess(ctx, { data, message: '数据获取成功' });
  }


}
module.exports = LearningController;
