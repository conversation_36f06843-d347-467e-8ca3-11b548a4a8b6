const Controller = require('egg').Controller;
/**
 * @Controller PayController-支付
 */
class PayController extends Controller {
  /**
   * @Router POST /app/pay/nativePay
   * @summary 扫码支付--返回二维码链接
   * @Description 请求参数 object 微信 参数介绍 请看文档https://pay.weixin.qq.com/wiki/doc/apiv3/apis/chapter3_4_1.shtml
   * @Request body payRequest *body
   * @response 200 baseResponse 创建成功
   */
  /**
 */
  async nativePay() {
    const { ctx } = this;
    try {
      const data = ctx.request.body;
      if (!data.description) {
        throw '支付调起失败，缺少商品描述';
      }
      if (!data.out_trade_no) {
        throw '支付调起失败，缺少商品描述';
      }
      if (!data.amount.total) {
        throw '支付调起失败，缺少商品金额';
      }
      if (!data.type) {
        throw '支付调起失败，缺少支付方式';
      }
      // 微信支付
      if (data.type === 'wxpay') {
        const res = await ctx.service.pay.wxNativePay(data);
        if (res && res.status === 200) {
          ctx.helper.renderSuccess(ctx, {
            data: {
              code_url: res.code_url,
              time_expire: res.time_expire,
            },
            message: '支付调起成功',
          });
        } else {
          throw res;
        }
      } else if (data.type === 'alipay') {
        const res = await ctx.service.pay.aliNativePay(data);
        if (typeof res === 'object' && res.alipay_trade_precreate_response) {
          ctx.helper.renderSuccess(ctx, {
            data: {
              code_url: res.alipay_trade_precreate_response.qr_code,
              time_expire: res.time_expire,
            },
            message: '支付调起成功',
          });
        } else {
          throw res;
        }
      }
    } catch (error) {
      ctx.auditLog('在线支付', error.message, 'error');
      return ctx.helper.renderFail(ctx, {
        message: '系统错误',
        data: error.message,
      });
    }
  }
  // 微信小程序
  async wxSpPay() {
    const { ctx } = this;
    try {
      const data = ctx.request.body;
      if (!data.description) {
        throw '支付调起失败，缺少商品描述';
      }
      if (!data.out_trade_no) {
        throw '支付调起失败，缺少商品描述';
      }
      if (!data.amount.total) {
        throw '支付调起失败，缺少商品金额';
      }
      if (!data.type) {
        throw '支付调起失败，缺少支付方式';
      }
      // 微信支付
      const res = await ctx.service.pay.wxSpPay(data);
      if (res.status === 200) {
        ctx.helper.renderSuccess(ctx, {
          data: res,
          message: '支付调起成功',
        });
      } else {
        throw res.message;
      }
    } catch (error) {
      ctx.auditLog('在线支付', `${error}`, 'error');
      return ctx.helper.renderFail(ctx, {
        message: '服务器伐开心',
        data: null,
      });
    }
  }
  // 微信退款
  async wxRefunds() {
    const { ctx } = this;
    try {
      const data = ctx.request.body;
      const res = await ctx.service.pay.wxRefunds(data);
      ctx.helper.renderSuccess(ctx, {
        data: res,
        message: '微信退款调起成功',
      });
    } catch (error) {
      ctx.auditLog('在线支付微信退款', `${error}`, 'error');
      return ctx.helper.renderFail(ctx, {
        message: '服务器伐开心',
        data: null,
      });
    }
  }
  // 微信关闭订单
  async wxCloseOrder() {
    const { ctx } = this;
    try {
      const data = ctx.request.body;
      const res = await ctx.service.pay.wxCloseOrder(data);
      ctx.helper.renderSuccess(ctx, {
        data: res,
        message: ' 微信关闭订单调起成功',
      });
    } catch (error) {
      ctx.auditLog('在线支付微信关闭订单', `${error}`, 'error');
      return ctx.helper.renderFail(ctx, {
        message: '服务器伐开心',
        data: null,
      });
    }
  }
  // 支付宝 支付回调异步，多用于支付成功的回调
  async alipayNotify() {
    const { ctx } = this;
    try {
      const data = ctx.request.body;
      console.log('进来了校验', data);
      const result = await ctx.service.pay.checkAliSign(data);
      if (result) {
        console.log('订单成功支付！！！请做处理', result);
        await ctx.service.pay.checkAliStatus(data);
        return 'success';
      }
      throw `签名校验失败,${JSON.stringify(data)}`;

    } catch (error) {
      ctx.auditLog('在线支付', `${error}`, 'error');
      return 'fail';
    }

  }
  // 支付宝 查询订单信息
  async alipayCheck() {
    const { ctx } = this;
    try {
      const data = ctx.request.body;
      const res = await ctx.service.pay.alipayCheck(data);
      const desc = {
        WAIT_BUYER_PAY: '交易创建，等待买家付款',
        TRADE_CLOSED: '未付款交易超时关闭，或支付完成后全额退款',
        TRADE_SUCCESS: '交易支付成功',
        TRADE_FINISHED: '交易结束，不可退款',
      };
      if (res.code === '10000') {
        const { trade_status: trade_state, buyer_logon_id: appid, total_amount: amount } = res;
        ctx.helper.renderSuccess(ctx, {
          data: { trade_state, trade_state_desc: desc[trade_state], appid, amount },
          message: '支付宝查询订单成功',
        });
      } else {
        throw res.sub_msg;
      }
    } catch (error) {
      ctx.auditLog('支付宝查询订单信息', `${error} 。`, 'error');
      return ctx.helper.renderFail(ctx, {
        message: '服务器伐开心',
        data: null,
      });
    }
  }
  // 支付宝退款
  async alipayRefunds() {
    const { ctx } = this;
    try {
      const data = ctx.request.body;
      console.log('支付宝退款', data);
      const res = await ctx.service.pay.alipayRefunds(data);
      ctx.helper.renderSuccess(ctx, {
        data: res,
        message: '支付宝退款调起成功',
      });
    } catch (error) {
      ctx.auditLog('在线支付支付宝退款', `${error}`, 'error');
      return ctx.helper.renderFail(ctx, {
        message: '服务器伐开心',
        data: null,
      });
    }
  }
  // 支付宝 关闭订单
  async alipayCloseOrder() {
    const { ctx } = this;
    try {
      const data = ctx.request.body;
      const res = await ctx.service.pay.alipayCloseOrder(data);
      ctx.helper.renderSuccess(ctx, {
        data: res,
        message: ' 支付宝关闭订单调起成功',
      });
    } catch (error) {
      ctx.auditLog('在线支付支付宝关闭订单', `${error}`, 'error');
      return ctx.helper.renderFail(ctx, {
        message: '服务器伐开心',
        data: null,
      });
    }
  }
  // 微信回调
  async wxNotify() {
    const { ctx } = this;
    try {
      const data = ctx.request.body;
      console.log(11111, data);
      const res = await ctx.service.pay.checkWxSign(data.resource);
      await ctx.service.pay.checkWxStatus(res);
      // 还差一句回调通知接受成功
      ctx.helper.renderSuccess(ctx, {
        data: {
          code: 'SUCCESS',
          message: '成功',
        },
        message: 'SUCCESS',
      });
    } catch (error) {
      console.log(error);
      return ctx.helper.renderFail(ctx, {
        message: '服务器伐开心',
        data: {
          code: 'FAIL',
          message: '失败',
        },
      });
    }

  }
  async wxRefundsNotify() {
    const { ctx } = this;
    try {
      const data = ctx.request.body;
      console.log(11111, data);
      const res = await ctx.service.pay.checkWxSign(data.resource);
      await ctx.service.pay.checkWxRefundsStatus(res);
      // 还差一句回调通知接受成功
      ctx.helper.renderSuccess(ctx, {
        data: {
          code: 'SUCCESS',
          message: '成功',
        },
        message: 'SUCCESS',
      });
    } catch (error) {
      console.log(error);
      return ctx.helper.renderFail(ctx, {
        message: '服务器伐开心',
        data: {
          code: 'FAIL',
          message: '失败',
        },
      });
    }
  }
  // 微信查询订单状态
  async wxpayCheck() {
    const { ctx } = this;
    try {
      const data = ctx.request.body;
      const res = await ctx.service.pay.wxpayCheck(data);
      if (res.status !== 200) {
        return ctx.helper.renderFail(ctx, {
          message: res.message,
          status: res.status,
          data: null,
        });
      }
      console.log('微信查询订单结果', res);
      const { trade_state, trade_state_desc, appid, amount } = res;
      ctx.helper.renderSuccess(ctx, {
        data: { trade_state, trade_state_desc, appid, amount },
        message: '微信查询订单成功',
      });
    } catch (error) {
      console.log(error);
      return ctx.helper.renderFail(ctx, {
        message: '服务器伐开心',
        data: null,
      });
    }
  }
  // 查询订单状态混合
  async orderCheck() {
    const { ctx } = this;
    try {
      const data = ctx.request.body;
      if (!data.out_trade_no || !data.type) {
        throw '缺少订单号或支付类型';
      }
      if ([ 'wxpay', 'alipay' ].indexOf(data.type) === -1) {
        throw '支付类型错误';
      }
      if (data.type === 'wxpay') {
        const res = await ctx.service.pay.wxpayCheck(data);
        if (res.status !== 200) {
          return ctx.helper.renderFail(ctx, {
            message: res.message,
            status: res.status,
            data: null,
          });
        }
        console.log('微信查询订单结果', res);
        const { trade_state, trade_state_desc, appid, amount } = res;
        ctx.helper.renderSuccess(ctx, {
          data: { trade_state, trade_state_desc, appid, amount },
          message: '微信查询订单成功',
        });
      } else if (data.type === 'alipay') {
        const res = await ctx.service.pay.alipayCheck(data);
        const desc = {
          WAIT_BUYER_PAY: '交易创建，等待买家付款',
          TRADE_CLOSED: '未付款交易超时关闭，或支付完成后全额退款',
          TRADE_SUCCESS: '交易支付成功',
          TRADE_FINISHED: '交易结束，不可退款',
        };
        // console.log('res1111', res);
        if (res.code === '10000') {
          const { trade_status: trade_state, buyer_logon_id: appid, total_amount } = res;
          ctx.helper.renderSuccess(ctx, {
            data: { trade_state, trade_state_desc: desc[trade_state], appid, amount: { total: total_amount, payer_currency: 'CNY' } },
            message: '支付宝查询订单成功',
          });
        } else if (res.code === '40004') {
          ctx.helper.renderSuccess(ctx, {
            data: { trade_state_desc: '请扫码支付' },
            message: '支付宝查询订单成功',
          });
        } else {
          throw res.sub_msg;
        }
      }
    } catch (error) {
      console.log(error);
      return ctx.helper.renderFail(ctx, {
        message: '服务器伐开心',
        data: null,
      });
    }
  }
  // 发起退款 混合
  async refunds() {
    const { ctx } = this;
    try {
      const data = ctx.request.body;
      console.log('进来了混合退款');
      if (!data.out_trade_no || !data.reason || !data.amount.total || !data.amount.refund || !data.type) {
        throw '退款订单，退款理由，订单总金额，订单退款金额条件不全';
      }
      let res = null;
      if (data.type === 'wxpay') {
        res = await ctx.service.pay.wxRefunds(data);
      } else if (data.type === 'alipay') {
        res = await ctx.service.pay.alipayRefunds(data);
      }
      ctx.helper.renderSuccess(ctx, {
        data: res,
        message: '退款调起成功',
      });
    } catch (error) {
      ctx.auditLog('在线支付退款', `${error}`, 'error');
      return ctx.helper.renderFail(ctx, {
        message: '服务器伐开心',
        data: null,
      });
    }
  }
}
module.exports = PayController;
