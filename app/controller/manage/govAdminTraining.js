// 管理员培训
const AdminTrainingController = {

  // 以下是监管端的接口
  // 获取统计信息
  async getSatisticByYear(ctx) {
    const { app } = this;
    try {
      let {
        searchYear,
        span,
      } = ctx.query;
      if (!span) span = 1;
      const superID = ctx.session.superUser._id;
      const query = {
        superID,
      };
      const certificateQuery = {
        superID,
      };

      if (searchYear) {
        // 保险处理
        searchYear = new Date(searchYear);
        searchYear.setDate(1);
        searchYear.setMonth(0);

        const nextSearchYear = new Date(searchYear);
        nextSearchYear.setDate(1);
        nextSearchYear.setMonth(0);
        nextSearchYear.setYear(searchYear.getFullYear() + span);

        query.completeTime = {
          $lt: nextSearchYear, // 比下一年元旦早
          $gte: searchYear, // 比搜索年元旦晚
        };

        certificateQuery.createdAt = {
          $lt: nextSearchYear, // 比下一年元旦早
          $gte: searchYear, // 比搜索年元旦晚
        };
      }
      const list = await ctx.model.AdminTraining.find(query);
      const certificate = await ctx.model.Certificate.countDocuments(certificateQuery);
      // console.log(list)
      const satisticData = {
        planCount: list.length,
        enterpriseCount: 0,
        participantsCount: 0,
        completedEnterprisesCount: 0,
        certificate,
      };
      const tempEnterprises = [];
      const incompleteEnterprise = [];
      // 遍历今年所有的培训
      for (let i = 0; i < list.length; i++) {
        const plan = list[i];
        const differenceSet = plan.EnterpriseID.filter(function(v) {
          return !plan.completedEnterprise.includes(v);
        });
        // 计算参与企业数避免重复
        const planEnterpriseLen = plan.EnterpriseID.length;
        for (let j = 0; j < planEnterpriseLen; j++) {
          const enterprise = plan.EnterpriseID[j];
          if (!tempEnterprises.includes(enterprise)) {
            tempEnterprises.push(enterprise);
          }
        }
        // 计算为完成企业数，比计算完成企业容易，只要一个未完成就算未完成
        // 若一个企业参与多个培训，需都完成才算完成
        const differenceSetLen = differenceSet.length;
        for (let j = 0; j < differenceSetLen; j++) {
          const enterprise = differenceSet[j];
          if (!incompleteEnterprise.includes(enterprise)) {
            incompleteEnterprise.push(enterprise);
          }
        }
        // 计算参与人次，可以重复
        const persons = await ctx.model.PersonalTraining.countDocuments({
          adminTrainingId: plan._id,
        });
        // console.log(persons)
        satisticData.participantsCount += persons;
        // for (let j = 0; j < persons.length; j++) {
        //   if (persons[j].completeState) satisticData.certificate++;
        // }
      }
      satisticData.enterpriseCount = tempEnterprises.length;
      satisticData.completedEnterprisesCount = tempEnterprises.length - incompleteEnterprise.length;

      const regAdd = ctx.session.superUser.regAdd;
      const regaddQuery = {};
      if (regAdd[0] !== app.config.China.name) {
        regaddQuery['workAddress.districts'] = {
          $all: regAdd,
        };
      }
      const enterpriseCount = await ctx.model.Adminorg.countDocuments(regaddQuery);

      satisticData.allEnterpriseCount = enterpriseCount;
      ctx.helper.renderSuccess(ctx, {
        data: {
          message: 'OK',
          satisticData,
        },
      });
    } catch (error) {
      // ctx.logger.error(error);
      console.error(error);
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  },

  // 获取最近好多年的覆盖率
  async getCoverageLastManyYears(ctx) {
    const { app } = this;
    try {
      let {
        span,
      } = ctx.query;
      if (!span) span = 5;

      const toYear = new Date();
      toYear.setDate(1);
      toYear.setMonth(0);

      const upperBoundYear = new Date(toYear);
      const lowerBoundYear = new Date(toYear);
      const result = [];
      for (let index = 0; index < span; index++) {
        lowerBoundYear.setYear(toYear.getFullYear() - index);
        upperBoundYear.setYear(toYear.getFullYear() - index + 1);
        const query = {
          completeTime: {
            $lt: upperBoundYear, // 比下一年元旦早
            $gte: lowerBoundYear, // 比搜索年元旦晚
          },
          superID: ctx.session.superUser._id,
        };
        const list = await ctx.model.AdminTraining.find(query);
        // console.log(11111111111,lowerBoundYear, upperBoundYear, list)
        const satisticData = {
          year: lowerBoundYear.getFullYear(),
          planCount: list.length,
          enterpriseCount: 0,
          completedEnterprisesCount: 0,
        };
        const tempEnterprises = [];
        const incompleteEnterprise = [];
        // 遍历今年所有的培训
        for (let i = 0; i < list.length; i++) {
          const plan = list[i];
          const differenceSet = plan.EnterpriseID.filter(function(v) {
            return !plan.completedEnterprise.includes(v);
          });
          // 计算参与企业数避免重复
          for (let j = 0; j < plan.EnterpriseID.length; j++) {
            const enterprise = plan.EnterpriseID[j];
            if (!tempEnterprises.includes(enterprise)) {
              tempEnterprises.push(enterprise);
            }
          }
          // 计算为完成企业数，比计算完成企业容易，只要一个未完成就算未完成
          // 若一个企业参与多个培训，需都完成才算完成
          for (let j = 0; j < differenceSet.length; j++) {
            const enterprise = differenceSet[j];
            if (!incompleteEnterprise.includes(enterprise)) {
              incompleteEnterprise.push(enterprise);
            }
          }
        }
        satisticData.enterpriseCount = tempEnterprises.length;
        satisticData.completedEnterprisesCount = tempEnterprises.length - incompleteEnterprise.length;
        result.push(satisticData);
      }
      const regAdd = ctx.session.superUser.regAdd;
      const regaddQuery = {};
      if (regAdd[0] !== app.config.China.name) {
        regaddQuery['workAddress.districts'] = {
          $all: regAdd,
        };
      }
      // const aaa = await ctx.model.Adminorg.find(regaddQuery);
      // console.log('aaa', aaa, regAdd)
      const enterpriseCount = await ctx.model.Adminorg.countDocuments(regaddQuery);
      ctx.helper.renderSuccess(ctx, {
        data: {
          message: 'OK',
          result,
          enterpriseCount,
        },
      });
    } catch (error) {
      ctx.logger.error(error);
      console.error(error);
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  },
  // 获取培训列表
  async getPlanList(ctx) {
    try {
      const {
        searchKey,
        searchPlanStatus,
        current,
        pageSize,
      } = ctx.query;
      let {
        selectYear,
      } = ctx.query;
      const query = {
        superID: ctx.session.superUser._id,
      };
      if (searchKey) {
        query.name = {
          $regex: searchKey,
        };
      }
      if (searchPlanStatus) {
        console.log(3333333, searchPlanStatus);
        query.completeTime = {
          [searchPlanStatus]: new Date(),
        };
      }
      if (selectYear) {
        selectYear = new Date(selectYear);
        selectYear.setDate(1);
        selectYear.setMonth(0);

        const nextSearchYear = new Date(selectYear);
        nextSearchYear.setDate(1);
        nextSearchYear.setMonth(0);
        nextSearchYear.setYear(selectYear.getFullYear() + 1);

        query.completeTime = {
          $lt: nextSearchYear, // 比下一年元旦早
          $gte: selectYear, // 比搜索年元旦晚
        };
      }

      const {
        list,
        count,
      } = await ctx.service.adminTraining.getPlanList(query, current, pageSize);
      ctx.helper.renderSuccess(ctx, {
        data: {
          list,
          count,
        },
        status: 200,
      });
    } catch (error) {
      ctx.logger.error(error);
      console.error(error);
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  },
};

module.exports = AdminTrainingController;
