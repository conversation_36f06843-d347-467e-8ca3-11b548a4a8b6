const Controller = require('egg').Controller;
const shortid = require('shortid');
const Segment = require('segment');

const segment = new Segment();
segment.useDefault(); // 载入默认词典

class CoursesController extends Controller {

  async getListByName() {
    const {
      ctx,
    } = this;
    try {
      const current = ctx.query.current || 1;
      const searchkey = ctx.query.searchkey || '';
      const query = {};
      if (searchkey.length) {
        query.$or = [{
          name: {
            $regex: searchkey,
          },
        }];
      }
      const {
        list,
        count,
      } = await ctx.service.courses.getListByName(query, current);
      ctx.helper.renderSuccess(ctx, {
        data: {
          list,
          count,
          message: 'OK',
        },
      });
    } catch (error) {
      ctx.logger.error(error);
      console.error(error);
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  // 获取观看量前五的课程，也可以改成推荐课程
  async getHotCourses() {
    const {
      ctx,
    } = this;
    try {
      const {
        list,
      } = await ctx.service.courses.getHotCourses();
      ctx.helper.renderSuccess(ctx, {
        data: {
          list,
          message: 'OK',
        },
      });
    } catch (error) {
      ctx.logger.error(error);
      console.error(error);
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  async getCourseByClass() {
    const {
      ctx,
    } = this;
    try {
      const current = ctx.query.current || 1;
      const classID = ctx.query.classID || '';
      const query = {
        allowToOpen: true,
      };
      // console.log(classID);
      if (classID.length) {
        query.classification = {
          $all: [ classID ],
        };
      }
      const {
        list,
        count,
      } = await ctx.service.courses.getListByClass(query, current);
      ctx.helper.renderSuccess(ctx, {
        data: {
          list,
          count,
          message: 'OK',
        },
      });

    } catch (error) {
      ctx.logger.error(error);
      console.error(error);
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  async getClassification() {
    const {
      ctx,
    } = this;
    try {
      const {
        level,
        parentID,
      } = ctx.query;
      // console.log(777777777777, ctx.query, level, parentID);
      const list = await ctx.service.courses.getClassification(level, parentID);
      ctx.helper.renderSuccess(ctx, {
        data: {
          message: 'OK',
          list,
        },
      });
    } catch (error) {
      ctx.logger.error(error);
      console.error(error);
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  // 获取视频播放地址
  async getVideoUrl() {
    try {
      const {
        ctx,
      } = this;
      const {
        VideoId,
        // courseID,
        // chapterID,
      } = ctx.query;
      const response = await ctx.helper.request_alivod('GetPlayInfo', {
        VideoId,
        Formats: 'mp4',
        AuthTimeout: 10800, // 3小时过期，我不信一个视频还能超过三小时ε=(´ο｀*)))唉，懒得写刷新接口了
        OutputType: 'cdn',
        // Definition:  // 清晰度
        // Formats: 'm3u8' // ts流
      }, {});
      const videoProgress = 0;
      try {
        await ctx.model.VideoInfos.updateOne({
          VideoId,
        }, {
          $inc: {
            times: 1,
          },
        });
      } catch (error) {
        ctx.logger.error(error);
        console.error(error);
      }
      ctx.helper.renderSuccess(ctx, {
        data: {
          response: response.PlayInfoList,
          videoProgress,
          message: 'OK',
        },
      });
    } catch (error) {
      this.ctx.logger.error(error);
      console.error(error);
      this.ctx.helper.renderFail(this.ctx, {
        message: error,
      });
    }
  }
  // 更新视频播放地址，暂未使用
  async refreshVideoUrl() {
    console.log(123);
  }

  async getCourseOne() {
    const {
      ctx,
    } = this;
    try {
      const {
        _id,
        personalTrainingId,
      } = ctx.query;
      // debugger;
      if (personalTrainingId) {
        // const userID = ctx.session.user._id;
        const personalTraining = await ctx.service.courses.getCourseProgress(ctx, {
          query: {
            _id: personalTrainingId,
          },
        });
        if (!personalTraining) {
          console.error('没有相关培训记录');
          ctx.helper.renderFail(ctx, {
            message: '没有相关培训记录',
          });
          return;
        }
        // 找到课程
        let progress = {};
        let progressMark = false;
        for (let index = 0; index < personalTraining.courses.length; index++) {
          if (personalTraining.courses[index].coursesId === _id) {
            progress = personalTraining.courses[index];
            break;
          }
        }
        if (!Object.keys(progress).length) {
          progress.coursePlayProgress = [];
          personalTraining.courses.push(progress);
          progressMark = true;
        }

        const course = await ctx.service.courses.getCourseOne(_id);
        const contentList = [];
        course.sort.sort(function(a, b) {
          return a.sequence - b.sequence;
        });

        let lastProgress = 0;
        let videoProgress = 0;
        let duration = 0;

        for (let index = 0; index < course.sort.length; index++) {
          // debugger
          const element = course[course.sort[index].contentType].find(function(item) {
            return item._id === course.sort[index].ID;
          });
          let courseProgress = progress.coursePlayProgress.find(function(item) {
            return item.chapterID === course.sort[index]._id;
          });
          if (!courseProgress) {
            // 这里需要拿到对应的课时数
            const coursesInfo = await ctx.model.Courses.aggregate([{
              $match: {
                _id,
              },
            }, {
              $unwind: '$sort',
            }, {
              $match: {
                'sort._id': course.sort[index]._id,
              },
            }, {
              $lookup: {
                from: 'videoInfos',
                localField: 'sort.ID',
                foreignField: '_id',
                as: 'video',
              },
            }, {
              $unwind: '$video',
            }]);
            progressMark = true;
            courseProgress = {
              _id: shortid.generate(),
              chapterID: course.sort[index]._id,
              videoProgress: 0,
              totalTime: 0,
              completeState: false,
              classHours: coursesInfo[0].video.classHours,
            };
            progress.coursePlayProgress.push(courseProgress);
          }
          if (progress.chapterPosition === course.sort[index]._id) lastProgress = index;
          let Video,
            video;
          switch (course.sort[index].contentType) {
            // 拿视频VideoId存着
            case 'videoInfos':
              Video = await this.ctx.helper.request_alivod('GetVideoInfo', {
                VideoId: element.VideoId,
              }, {});
              video = {
                completeState: courseProgress.completeState,
                contentType: course.sort[index].contentType,
                _id: course.sort[index]._id,
                ID: course.sort[index].ID,
                VideoId: element.VideoId,
                title: Video.Video.Title,
                poster: Video.Video.CoverURL,
                Description: Video.Video.Description,
                viewnumber: element.times,
                author: element.author,
              };
              // 拿视频，第一次看，progress是空对象，就把第一个东西给他， 或者不是空得，那就给他对应东西
              if ((!index && !progress.chapterPosition) || progress.chapterPosition === course.sort[index]._id) {
                const response = await this.ctx.helper.request_alivod('GetPlayInfo', {
                  VideoId: element.VideoId,
                  Formats: 'mp4',
                }, {});
                duration = response.PlayInfoList.PlayInfo[0].Duration;
                video.src = response.PlayInfoList.PlayInfo[0].PlayURL;
                video.Size = response.PlayInfoList.PlayInfo[0].Size;
                const tempProgress = progress.coursePlayProgress.find(function(item) {
                  return item.chapterID === progress.chapterPosition;
                });
                if (tempProgress) videoProgress = tempProgress.videoProgress;
                try {
                  await ctx.model.VideoInfos.updateOne({
                    VideoId: element.VideoId,
                  }, {
                    $inc: {
                      times: 1,
                    },
                  });
                } catch (error) {
                  ctx.logger.error(error);
                  console.error(error);
                }
              }
              contentList.push(video);
              break;
            case 'documents':
              contentList.push({
                contentType: course.sort[index].contentType,
                _id: course.sort[index]._id,
                ID: course.sort[index].ID,
                htmlContent: element.htmlContent,
                title: element.name,
                cover: element.cover,
                Description: element.Description,
              });
              break;
            default:
              break;
          }
        }
        if (progressMark) {
          await ctx.service.courses.updatePersonalTraining({
            _id: personalTrainingId,
          }, {
            $set: {
              courses: personalTraining.courses,
            },
          });
        }
        ctx.helper.renderSuccess(ctx, {
          data: {
            like: progress.like ? progress.like : false,
            videoProgress,
            lastProgress,
            contentList,
            course,
            message: 'OK',
            duration,
            personalTraining,
          },
        });

        // 浏览量加一， 加try,万一出错不影响前面的返回速度。
        try {
          await ctx.service.courses.updateCourse(_id, {
            $inc: {
              views: 1,
            },
          });
        } catch (error) {
          ctx.logger.error(error);
          console.error(error);
        }
        return;
      }
      await this.getCourseOneWithOutPerson(ctx, _id);

      //
    } catch (error) {
      ctx.logger.error(error);
      console.error(error);
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  async getCourseOneWithOutPerson(ctx, courseID) {
    const course = await ctx.service.courses.getCourseOne(courseID);
    course.sort.sort(function(a, b) {
      return a.sequence - b.sequence;
    });
    const contentList = [];
    for (let index = 0; index < course.sort.length; index++) {
      // debugger
      const element = course[course.sort[index].contentType].find(function(item) {
        return item._id === course.sort[index].ID;
      });
      let Video,
        video;
      switch (course.sort[index].contentType) {
        // 拿视频VideoId存着
        case 'videoInfos':
          Video = await this.ctx.helper.request_alivod('GetVideoInfo', {
            VideoId: element.VideoId,
          }, {});
          video = {
            completeState: false,
            contentType: course.sort[index].contentType,
            _id: course.sort[index]._id,
            ID: course.sort[index].ID,
            VideoId: element.VideoId,
            title: Video.Video.Title,
            poster: Video.Video.CoverURL,
            Description: Video.Video.Description,
            viewnumber: element.times,
            author: element.author,
          };
          contentList.push(video);
          break;
        case 'documents':
          contentList.push({
            contentType: course.sort[index].contentType,
            _id: course.sort[index]._id,
            ID: course.sort[index].ID,
            htmlContent: element.htmlContent,
            title: element.name,
            cover: element.cover,
            Description: element.Description,
          });
          break;
        default:
          break;
      }
    }
    ctx.helper.renderSuccess(ctx, {
      data: {
        lastProgress: 0,
        contentList,
        course,
        message: 'OK',
      },
    });
  }

  // 更新课程或者视频的进度
  async updateCourseProgress() {
    const {
      ctx,
    } = this;
    try {
      const {
        courseID,
        lastChapterID,
        personalTrainingId,
        chapterPosition, // 培训端的
        newFun,
      } = ctx.request.body;
      const videoProgress = +ctx.request.body.videoProgress;
      const duration = ctx.request.body.duration ? +ctx.request.body.duration : 0;
      const personalTraining = await ctx.model.PersonalTraining.findOne({ _id: personalTrainingId }, { courses: 1, completeState: 1, adminTrainingId: 1, trainingType: 1 });
      // await this.updateCompletedEnterprise_new(ctx, personalTrainingId)
      if (!personalTraining) {
        ctx.helper.renderFail(ctx, {
          message: '找不到personalTraining',
        });
        return;
      }
      if (!personalTraining.courses) personalTraining.courses = [];
      // 找到课程
      let progress = {};
      let nowIndex = null;// 课程所在index
      for (let index = 0; index < personalTraining.courses.length; index++) {
        if (personalTraining.courses[index].coursesId === courseID) {
          progress = personalTraining.courses[index];
          nowIndex = index;
          break;
        }
      }
      if (newFun) { // 新培训端过来的!! xxn add
        progress.chapterPosition = chapterPosition;
        const coursePlayProgress = progress.coursePlayProgress;
        // 寻找当前视频--如果没找到，那就创建一条
        let findFlag = false;
        for (let index = 0; index < coursePlayProgress.length; index++) {
          if (coursePlayProgress[index].chapterID === chapterPosition) {
            findFlag = true;
            const resultVideo = coursePlayProgress[index];
            const temp = videoProgress - resultVideo.videoProgress;
            if (temp > 0) { // 如果当前进度大于存储进度，更新进度
              resultVideo.totalTime += temp;
              resultVideo.videoProgress = videoProgress; // 视频观看进度
            }
            if (resultVideo.completeState) { resultVideo.videoProgress = videoProgress; }
            if (!resultVideo.duration) {
              const sxcourseInfo = await ctx.model.Courses.find({ _id: courseID, 'sort._id': chapterPosition }, { 'sort.ID.$': 1 });
              const sxvideo = await ctx.model.VideoInfos.findOne({ _id: sxcourseInfo[0].sort[0].ID });
              const response = await this.ctx.helper.request_alivod('GetPlayInfo', {
                VideoId: sxvideo.VideoId,
                Formats: 'mp4',
              }, {});
              resultVideo.duration = response.PlayInfoList.PlayInfo[0].Duration;
            }
            // 判断 当前视频的completeState
            if (!resultVideo.completeState && (Math.abs(videoProgress - resultVideo.duration) < 60)) {
              const difTime = resultVideo.totalTime - resultVideo.duration;
              if (difTime < 80 || difTime >= 0) resultVideo.completeState = true;
            }
            break;
          }
        }
        if (!findFlag) { // 如果没找到，
          // 根据课程id 和现在所看id 去找 classHours
          const xcourseInfo = await ctx.model.Courses.find({ _id: courseID, 'sort._id': chapterPosition }, { 'sort.ID.$': 1 });
          const xvideo = await ctx.model.VideoInfos.findOne({ _id: xcourseInfo[0].sort[0].ID });
          const response = await this.ctx.helper.request_alivod('GetPlayInfo', {
            VideoId: xvideo.VideoId,
            Formats: 'mp4',
          }, {});
          const nowVideoduration = response.PlayInfoList.PlayInfo[0].Duration;
          const newCourseData = {
            _id: shortid.generate(),
            chapterID: chapterPosition,
            classHours: xvideo.classHours || 1,
            videoProgress: videoProgress || 0,
            totalTime: videoProgress || 0,
            duration: nowVideoduration,
            completeState: (Math.abs(videoProgress - xvideo.duration) < 60),
          };
          personalTraining.courses[nowIndex].coursePlayProgress.push(newCourseData);
        }
        progress.completeState = coursePlayProgress.every(ele => ele.completeState); // 判断整个课程完成了没有
        // 判断整个培训完成了没有
        let completeState = personalTraining.completeState;
        if (!completeState) {
          if (personalTraining.trainingType === 2 && personalTraining.adminTrainingId) {
            const adminTraining = await ctx.model.AdminTraining.findOne({ _id: personalTraining.adminTrainingId }, { needExam: 1 });
            if (adminTraining.needExam) {
              completeState = false;
            } else {
              completeState = personalTraining.courses.every(ele => ele.completeState);
            }
          } else {
            completeState = personalTraining.courses.every(ele => ele.completeState);
          }
        }

        // 更新数据
        // console.log(999999999, completeState, personalTraining.courses, personalTraining.courses[0].coursePlayProgress);
        const updateData = {
          completeState,
          courses: personalTraining.courses,
        };
        const res = await ctx.service.courses.updatePersonalTraining({
          _id: personalTrainingId,
        }, {
          $set: updateData,
        });
        if (res.nModified === 1) {
          ctx.helper.renderSuccess(ctx, {
            data: updateData,
            message: '进度更新成功',
          });
        } else {
          ctx.helper.renderFail(ctx, {
            message: '进度更新失败',
            data: res,
          });
        }

        return;
      }
      // 么有对应课程的进度，加进去
      if (!Object.keys(progress).length) {
        // 这里需要拿到对应的课时数
        const coursesInfo = await ctx.model.Courses.aggregate([{
          $match: {
            _id: courseID,
          },
        }, {
          $unwind: '$sort',
        }, {
          $match: {
            'sort._id': lastChapterID,
          },
        }, {
          $lookup: {
            from: 'videoInfos',
            localField: 'sort.ID',
            foreignField: '_id',
            as: 'video',
          },
        }, {
          $unwind: '$video',
        }]);
        progress = {
          _id: shortid.generate(),
          coursesId: courseID,
          chapterPosition: lastChapterID,
          coursePlayProgress: [{
            _id: shortid.generate(),
            chapterID: lastChapterID,
            videoProgress: videoProgress || 0,
            totalTime: videoProgress || 0,
            duration,
            classHours: coursesInfo[0].video.classHours,
          }],
        };
        personalTraining.courses.push(progress);
      }
      // 课程章节ID记录
      if (progress.chapterPosition !== lastChapterID) progress.chapterPosition = lastChapterID;
      let flag = false;
      // console.log('11111111111111111111111111111111', progress)
      for (let index = 0; index < progress.coursePlayProgress.length; index++) {
        const element = progress.coursePlayProgress[index];
        if (element.chapterID === lastChapterID) {
          const temp = videoProgress - element.videoProgress;
          if (videoProgress) element.videoProgress = videoProgress;
          // console.log(temp, element.videoProgress, element.totalTime)
          if (temp > 5 && temp < 50) {
            // console.log(1324323421)
            element.totalTime += temp;
          }
          flag = true;
          if (element.duration !== duration) element.duration = duration;
          break;
        }
      }
      if (!flag) {
        // 这里需要拿到对应的课时数
        const coursesInfo = await ctx.model.Courses.aggregate([{
          $match: {
            _id: courseID,
          },
        }, {
          $unwind: '$sort',
        }, {
          $match: {
            'sort._id': lastChapterID,
          },
        }, {
          $lookup: {
            from: 'videoInfos',
            localField: 'sort.ID',
            foreignField: '_id',
            as: 'video',
          },
        }, {
          $unwind: '$video',
        }]);
        progress.coursePlayProgress.push({
          _id: shortid.generate(),
          chapterID: lastChapterID,
          videoProgress: videoProgress || 0,
          totalTime: videoProgress || 0,
          duration,
          classHours: coursesInfo[0].video.classHours,
        });
      }
      // console.log(personalTraining.courses[0].coursePlayProgress[0])
      await ctx.service.courses.updatePersonalTraining({
        _id: personalTrainingId,
      }, {
        $set: {
          courses: personalTraining.courses,
        },
      });
      ctx.helper.renderSuccess(ctx, {
        data: {
          message: 'OK',
        },
      });
    } catch (error) {
      ctx.logger.error(error);
      console.error(error);
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  // 给课程点个赞 1
  async likeCourse() {
    const {
      ctx,
    } = this;
    try {
      const {
        courseID,
        personalTrainingId,
      } = ctx.request.body;
      let like = ctx.request.body.like;
      if (typeof like === 'string') like = like === 'true';
      if (personalTrainingId) { // 已加入我的培训计划
        const personalTraining = await ctx.model.PersonalTraining.findOne({ $or: [
          { _id: personalTrainingId },
          { adminTrainingId: personalTrainingId, userId: ctx.session.user._id, status: true },
        ] }, { courses: 1 });
        if (personalTraining && personalTraining.courses) {
          for (let index = 0; index < personalTraining.courses.length; index++) {
            if (personalTraining.courses[index].coursesId === courseID) {
              personalTraining.courses[index].like = like;
              break;
            }
          }
          await ctx.service.courses.updatePersonalTraining({
            _id: personalTraining._id,
          }, {
            $set: {
              courses: personalTraining.courses,
            },
          });
        }
      }
      ctx.helper.renderSuccess(ctx, {
        data: {
          message: 'OK',
        },
      });

      // 点赞量， 不影响前端，报错也没啥事。
      try {
        await ctx.service.courses.updateCourse(courseID, {
          $inc: {
            likes: like ? 1 : -1,
          },
        });
      } catch (error) {
        ctx.logger.error(error);
        console.error(error);
      }
      return;
    } catch (error) {
      ctx.logger.error(error);
      console.error(error);
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  // 搜索课程，目前只根据名字。
  async searchCourse() {
    const {
      ctx,
    } = this;
    try {
      const {
        searchKey,
      } = ctx.query;
      // 根据课程名搜索。确保搜索精确，先分词
      const participle = segment.doSegment(searchKey, {
        stripPunctuation: true,
        simple: true,
      });
      // 按分词数组中长短排序
      participle.sort(function(a, b) {
        return b.length - a.length;
      });
      // console.log(participle);
      const result = []; // 结果数组，默认搜搜10个
      for (let index = 0; index < participle.length; index++) {
        const element = participle[index];
        const resultFullName = await ctx.service.courses.searchCourses({
          name: {
            $regex: element,
          },
        });
        // console.log(resultFullName);
        if (resultFullName && resultFullName.length) {
          result.push(...resultFullName);
        }
        if (result.length >= 9) break;
      }
      ctx.helper.renderSuccess(ctx, {
        data: {
          result,
          message: 'OK',
        },
      });

    } catch (error) {
      ctx.logger.error(error);
      console.error(error);
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  // 添加评论，评论是针对课程的，弹幕是针对视频的。1
  async creatComment() {
    const {
      ctx,
    } = this;
    try {
      const {
        content,
        courseID,
      } = ctx.request.body;
      const userID = ctx.session.user._id;
      const backData = await ctx.service.courses.createComment({
        content,
        courseID,
        userID,
      });
      // console.log(backData)
      ctx.helper.renderSuccess(ctx, {
        data: {
          backData,
          message: 'OK',
        },
      });
      try {
        await ctx.service.courses.updateCourse(courseID, {
          $inc: {
            commentLength: 1,
          },
        });
      } catch (error) {
        ctx.logger.error(error);
        console.error(error);
      }
    } catch (error) {
      ctx.logger.error(error);
      console.error(error);
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }
  // 获取评论。1
  async getComments() {
    const {
      ctx,
    } = this;
    try {
      const courseID = ctx.query.courseID;
      const current = ctx.query.current ? +ctx.query.current : 1;
      const resData = await ctx.service.courses.getComments(courseID, current);
      ctx.helper.renderSuccess(ctx, {
        data: {
          ...resData,
          message: 'OK',
        },
      });
    } catch (error) {
      ctx.logger.error(error);
      console.error(error);
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }
  // 添加回复
  async createReply() {
    const {
      ctx,
    } = this;
    try {
      const {
        content,
        courseID,
        _id,
      } = ctx.request.body;
      const userID = ctx.session.user._id;
      const backData = await ctx.service.courses.createComment({
        content,
        courseID,
        userID,
        level: 2,
      });
      await ctx.service.courses.updateComment(_id, {
        $push: {
          replys: backData.id,
        },
      });
      ctx.helper.renderSuccess(ctx, {
        data: {
          // backData,
          message: 'OK',
        },
      });
    } catch (error) {
      ctx.logger.error(error);
      console.error(error);
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  // 给评论点赞 1
  async likeComment() {
    const {
      ctx,
    } = this;
    try {
      const {
        _id, unlike,
      } = ctx.request.body;
      if (unlike) {
        await ctx.service.courses.updateComment(_id, {
          $inc: {
            unlikes: 1,
          },
        });
      } else {
        await ctx.service.courses.updateComment(_id, {
          $inc: {
            likes: 1,
          },
        });
      }

      ctx.helper.renderSuccess(ctx, {
        data: {
          // backData,
          message: 'OK',
        },
      });
    } catch (error) {
      ctx.logger.error(error);
      console.error(error);
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  async updateCompleteState() {
    try {
      const {
        ctx,
      } = this;
      const {
        personalTrainingID,
      } = ctx.query;
      const personalTraining = await ctx.model.PersonalTraining.findOne({
        _id: personalTrainingID,
      });
      if (!personalTraining) {
        ctx.helper.renderFail(ctx, {
          message: '没有对应记录',
        });
        return;
      }
      if (personalTraining.completeState) { // 培训完了，不用更新了
        ctx.helper.renderSuccess(ctx, {
          data: {
            info: '培训完成',
            message: 'OK',
          },
        });
        return;
      }
      let allCoursesMark = true;
      for (let i = 0; i < personalTraining.courses.length; i++) {
        // 遍历课程所有的记录
        const courseProgress = personalTraining.courses[i];
        // console.log(courseProgress)
        if (!courseProgress.completeState) {
          let mark = false; // 标记是否所有章节看完，有未完成的就变为true
          for (let j = 0; j < courseProgress.coursePlayProgress.length; j++) {
            const coursePlayProgress = courseProgress.coursePlayProgress[j];
            if (!coursePlayProgress.completeState) {
              const temp = Number(coursePlayProgress.totalTime) / Number(coursePlayProgress.duration);
              if (temp > 0.75) coursePlayProgress.completeState = true;
              else mark = true; // 没完成，变true
            }
          }
          // 所有都完成，mark还是false
          if (!mark && courseProgress.coursePlayProgress.length) {
            courseProgress.completeState = true;
            // 增加一条记录到企业端的培训记录表
            ctx.service.personalTraining.addPropagate(personalTrainingID);
          } else {
            // 这个课程没完成，就说明整个培训也没完成
            allCoursesMark = false;
          }
        }
      }

      let needExam = true;
      // 所有课程都完成了，看看需不需要考试
      if (allCoursesMark) {
        let modelName = '';
        let idField = '';
        if (personalTraining.trainingType === 1 || personalTraining.trainingType === 2) {
          modelName = 'AdminTraining';
          idField = 'adminTrainingId';
        } else if (personalTraining.trainingType === 3) {
          modelName = 'EmployeesTrainingPlan';
          idField = 'employeesTrainingPlanId';
        }
        if (modelName) {
          const training = await ctx.model[modelName].findOne({
            _id: personalTraining[idField],
          }, {
            needExam: 1,
          });
          needExam = training.needExam;
          if (!training.needExam) {
            personalTraining.completeState = true;
          }
        }
      }
      await ctx.service.courses.updatePersonalTraining({
        _id: personalTrainingID,
      }, {
        $set: {
          courses: personalTraining.courses,
          completeState: personalTraining.completeState,
        },
      });

      try {
        if (!needExam) {
          // await this.updateCompletedEnterprise_new(ctx, personalTrainingID);
        }
      } catch (error) {
        this.ctx.logger.error(error);
        console.error(error);
        throw error;
      }

      ctx.helper.renderSuccess(ctx, {
        data: {
          message: 'OK',
        },
      });
    } catch (error) {
      this.ctx.logger.error(error);
      console.error(error);
      this.ctx.helper.renderFail(this.ctx, {
        message: error,
      });
    }
  }


  async getPauseTime(ctx) {
    try {
      const query = ctx.query,
        _id = query._id || '';
      // 查进度
      if (_id === '') {
        ctx.helper.renderFail(ctx);
        return;
      }
      const adminTraining = await ctx.service.courses.item(ctx, {
        query: {
          _id,
        },
        files: 'breakpoint breakpointInterval breakpointRange',
      });

      ctx.helper.renderSuccess(ctx, {
        data: adminTraining,
      });
    } catch (error) {
      ctx.auditLog('管理员培训课程视频个人获取间隔时间错误', `${error.stack} 。`, 'error');
      ctx.helper.renderFail(ctx);
    }
  }


  // 修改后的updateCompletedEnterprise，判断企业是否完成某个培训
  async updateCompletedEnterprise_new(ctx, personalTrainingId) {
    const personalTrainingInfo = await ctx.service.personalTraining.findOne({
      _id: personalTrainingId,
    });
    if (personalTrainingInfo.trainingType !== 1 || !personalTrainingInfo.completeState) return;
    // console.log(personalTrainingInfo);
    // const EnterpriseID = ctx.session.adminUserInfo.EnterpriseID;
    // 查询这个人所涉及的所有企业
    const train = await ctx.model.AdminTraining.findOne({
      _id: personalTrainingInfo.adminTrainingId,
    }, {
      EnterpriseID: 1,
    });
    const companys = await ctx.model.Adminorg.find({
      adminArray: {
        $in: [ personalTrainingInfo.adminUserId ],
      },
      _id: {
        $in: train.EnterpriseID,
      },
    }, {
      adminArray: 1,
      _id: 1,
    });
    if (!companys || !companys.length) {
      ctx.logger.error(new Error('adminuserid查询无企业' + personalTrainingInfo.adminUserId));
      return;
    }
    for (let i = 0; i < companys.length; i++) {
      const company = companys[i];
      if (!company.adminArray || !company.adminArray.length) continue;
      // 查询这个企业所有没作废的完成的培训记录
      const personTrains = await ctx.model.PersonalTraining.find({
        adminUserId: {
          $in: company.adminArray,
        },
        adminTrainingId: personalTrainingInfo.adminTrainingId,
        completeState: true,
        status: true,
      }, {
        EnterpriseID: 1,
        completeState: 1,
        userId: 1,
        adminTrainingId: 1,
      });
      if (!personTrains || !personTrains.length) continue;

      // 查询这个企业所有培训的管理员负责人
      const roles = await ctx.model.Roles.findOne({
        EnterpriseID: company._id,
      });
      const userIDSet = new Set(); // 去重
      for (let i = 0; i < roles.formData.length; i++) {
        const formData = roles.formData[i];
        for (let k = 0; k < formData.userId.length; k++) {
          const employeeId = formData.userId[k].slice(-1)[0];
          const user = await ctx.model.Employee.findOne({
            _id: employeeId,
          }, {
            _id: 1,
            userId: 1,
          });
          userIDSet.add(user.userId);
        }
      }

      // 比对培训记录和管理员负责人的userId
      let allCompleteMark = true;
      for (const userId of userIDSet) {
        let hasId = false;
        for (let j = 0; j < personTrains.length; j++) {
          if (personTrains[j].userId === userId) {
            hasId = true;
            if (!personTrains[j].completeState) {
              allCompleteMark = false;
              break;
            }
          }
        }
        if (!allCompleteMark || !hasId) {
          allCompleteMark = false;
          break;
        }
      }

      if (allCompleteMark) {
        // 完成了
        console.log('完成了');
        await ctx.model.AdminTraining.updateOne({
          _id: personalTrainingInfo.adminTrainingId,
        }, {
          $addToSet: {
            completedEnterprise: company._id,
          },
        });
      } else {
        // 没完成，给他删了
        console.log('没完成，给他删了');
        await ctx.model.AdminTraining.updateOne({
          _id: personalTrainingInfo.adminTrainingId,
        }, {
          $pull: {
            completedEnterprise: company._id,
          },
        }, {
          multi: true,
        });
      }
    }

    return;
  }
}
module.exports = CoursesController;
