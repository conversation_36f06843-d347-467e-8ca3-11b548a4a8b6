const Controller = require('egg').Controller;
const path = require('path');

class FileController extends Controller {
  /**
   * 测试文件上传 (pipe)
   * POST /manage/file/upload
   * Content-Type: multipart/form-data
   * Body: file (文件)
   */
  async upload() {
    const { ctx } = this;
    try {
      // 获取上传的文件流
      const stream = await ctx.getFileStream();
      if (!stream) {
        ctx.body = {
          success: false,
          message: '没有文件上传',
        };
        return;
      }
      console.log(' ctx.session', ctx.session);
      // 获取参数
      const enterpriseId = ctx.session.user.EnterpriseID;

      // 构造目标路径
      const fileName = stream.filename;
      const target = path.join(
        ctx.app.config.upload_path,
        enterpriseId,
        fileName
      );

      // 调用 helper.pipe 方法
      const result = await ctx.helper.pipe({
        readableStream: stream,
        target,
      });

      ctx.body = {
        success: result.status === 200,
        message: result.status === 200 ? '上传成功' : '上传失败',
        data: {
          fileName,
          originalName: stream.filename,
          target,
          url: result.url || '',
          type: result.type,
          enterpriseId,
        },
        result,
      };
    } catch (error) {
      ctx.logger.error('文件上传错误:', error);
      ctx.body = {
        success: false,
        message: '上传失败: ' + error.message,
        error: error.toString(),
      };
    }
  }

  /**
   * 测试文件删除 (deleteObject)
   * DELETE /manage/file/delete
   * Body: { "fileName": "filename.jpg" }
   */
  async delete() {
    const { ctx } = this;
    try {
      const { fileName } = ctx.request.body;

      if (!fileName) {
        ctx.body = {
          success: false,
          message: '文件不能为空',
        };
        return;
      }
      const enterpriseId = ctx.session.user.EnterpriseID;
      const deletePath = path.join(
        ctx.app.config.upload_path,
        enterpriseId,
        fileName
      );
      // 调用 helper.deleteObject 方法
      const result = await ctx.helper.deleteObject(deletePath);

      ctx.body = {
        success: result.status === 200,
        message: result.status === 200 ? '删除成功' : '删除失败',
        data: {
          filePath: deletePath,
          type: result.type,
        },
        result,
      };
    } catch (error) {
      ctx.logger.error('文件删除错误:', error);
      ctx.body = {
        success: false,
        message: '删除失败: ' + error.message,
        error: error.toString(),
      };
    }
  }

  /**
   * 测试文件下载 (fGetObject)
   * POST /manage/file/download
   * Body: { "fileName": "filename.jpg" }
   */
  async download() {
    const { ctx } = this;
    try {
      const { fileName } = ctx.request.body;

      if (!fileName) {
        ctx.body = {
          success: false,
          message: '文件不能为空',
        };
        return;
      }
      const enterpriseId = ctx.session.user.EnterpriseID;
      // 存在本机上的文件路径
      const objectPath = path.join(
        ctx.app.config.upload_path,
        enterpriseId,
        fileName
      );
      const objectPathSplit = objectPath.replace(/\\/gi, '/').split('/public/');
      // 调用 helper.fGetObject 方法
      const result = await ctx.helper.fGetObject({
        objectPath: objectPathSplit[objectPathSplit.length - 1],
        filePath: objectPath,
      });

      ctx.body = {
        success: result.status === 200,
        message: result.status === 200 ? '下载成功' : '下载失败',
        data: {
          objectPath,
          localPath: objectPath,
          type: result.type,
        },
        result,
      };
    } catch (error) {
      ctx.logger.error('文件下载错误:', error);
      ctx.body = {
        success: false,
        message: '下载失败: ' + error.message,
        error: error.toString(),
      };
    }
  }

  /**
   * 测试路径拼接 (concatenatePath)
   * POST /manage/file/getUrl
   * Body: { "fileName": "filename.jpg" }
   */
  async getUrl() {
    const { ctx } = this;
    try {
      const { fileName } = ctx.request.body;
      const enterpriseId = ctx.session.user.EnterpriseID;
      const filePath = path.join(
        ctx.app.config.upload_http_path,
        enterpriseId,
        fileName
      );
      console.log('filePath', ctx.app.config.upload_http_path);
      if (!filePath) {
        ctx.body = {
          success: false,
          message: '文件路径不能为空',
        };
        return;
      }

      // 调用 helper.concatenatePath 方法
      const url = await ctx.helper.concatenatePath({
        path: filePath,
      });

      ctx.body = {
        success: !!url,
        message: url ? '获取URL成功' : '获取URL失败',
        data: {
          filePath,
          url,
        },
      };
    } catch (error) {
      ctx.logger.error('获取文件URL错误:', error);
      ctx.body = {
        success: false,
        message: '获取URL失败: ' + error.message,
        error: error.toString(),
      };
    }
  }
}

module.exports = FileController;
