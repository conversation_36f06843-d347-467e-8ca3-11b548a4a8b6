const Controller = require('egg').Controller;
const validator = require('validator');
const sha1 = require('sha1');
const moment = require('moment');
const pump = require('pump');
const path = require('path');
const fs = require('fs');
// const awaitWriteStream = require('await-stream-ready').write;
const jwt = require('jsonwebtoken');
const {
  siteFunc,
  tools,
} = require('@utils');
const addSignatureToPDF = require('../../utils/pdfSign.js');
const _ = require('lodash');
// 管道读入一个虫洞。
const sendToWormhole = require('stream-wormhole');

// const moment = require('moment');


class AdminController extends Controller {
  async updateInfo() {
    const { ctx, app } = this;
    let { password, phoneNum, countryCode, messageCode } = ctx.request.body;
    let errMsg = '';
    const _id = ctx.session.user._id;

    if (password) {
      password = password.trim();
      if (password.indexOf('$') === -1) {
        await ctx.service.user.update(ctx, _id, { password });
        await ctx.model.AdminUser.updateOne(
          // 同步修改adminUser的密码
          { userId: _id },
          { password }
        );
        ctx.helper.renderSuccess(ctx, {
          message: `修改 ${ctx.__('restful_api_response_success', [
            ctx.__('lc_password'),
          ])}`,
        });
      } else {
        ctx.helper.renderFail(ctx, {
          message: ctx.__('validate_error_params'),
        });
      }
    } else if (phoneNum && messageCode && countryCode) {
      phoneNum = phoneNum.trim();
      messageCode = messageCode.trim();
      countryCode = countryCode.trim();
      const cacheKey = '_sendMessage_update_';
      if (!phoneNum || !validator.isNumeric(phoneNum.toString())) {
        errMsg = ctx.__('validate_inputCorrect', [
          ctx.__('label_user_phoneNum'),
        ]);
      }
      if (!countryCode) {
        errMsg = ctx.__('validate_selectNull', [
          ctx.__('label_user_countryCode'),
        ]);
      }
      if (
        !messageCode ||
        !validator.isNumeric(messageCode.toString()) ||
        messageCode.length !== 6
      ) {
        errMsg = ctx.__('validate_inputCorrect', [
          ctx.__('label_user_imageCode'),
        ]);
      }
      const queryUserObj = {
        $or: [
          {
            phoneNum,
          },
          {
            phoneNum: '0' + phoneNum,
          },
        ],
        countryCode,
      };
      const userCount = await ctx.service.user.count(queryUserObj);
      if (userCount > 0) {
        errMsg = '该手机号已被使用！';
      }
      const params = countryCode + phoneNum;
      const currentCode = ctx.helper.getCache(
        app.config.session_secret + cacheKey + params
      );
      if (
        !currentCode ||
        !validator.isNumeric(currentCode.toString()) ||
        currentCode.length !== 6
      ) {
        errMsg = '对不起！手机号错误！';
      }
      if (errMsg) {
        ctx.helper.renderFail(ctx, {
          message: errMsg,
        });
        return;
      }

      const userDetail = await ctx.model.User.findOne({ _id }, { phoneNum: 1 });
      const errRes = await tools.validPhoneAndIDNum(ctx, 'user', {
        _id,
        phoneNum,
        oldPhoneNum: userDetail.phoneNum,
      });
      if (errRes) {
        ctx.helper.renderFail(ctx, {
          message: errRes,
        });
        return;
      }

      if (currentCode !== messageCode) {
        // 依照需求决定是否使用严格的校验方式 现方式允许用户手机号或验证码写错后不用重新发送验证码
        // ctx.helper.clearCache(params, cacheKey);
        ctx.helper.renderFail(ctx, {
          message: '对不起！验证码错误！看看是不是验证码写错了？',
        });
        return;
      }
      const updateRes = await ctx.service.user.update(ctx, _id, {
        userName: phoneNum,
        phoneNum,
      });

      if (updateRes && updateRes._id) {
        // 同步修改
        await tools.updateData(ctx, 'user', {
          _id,
          type: '2',
        });
      }

      ctx.helper.renderSuccess(ctx, {
        status: 200,
        message: `修改 ${ctx.__('restful_api_response_success', [
          ctx.__('label_user_phoneNum'),
        ])}`,
      });
    } else {
      ctx.helper.renderFail(ctx, {
        message: ctx.__('validate_error_params'),
      });
    }
  }

  async logOutAction() {
    const { ctx, app } = this;
    ctx.auditLog('退出登录', '当前用户进行了退出登录操作。', 'info');
    ctx.session = null;
    // 获取当前token
    const token =
      ctx.cookies.get('admin_' + app.config.auth_toolscookie_name) ||
      ctx.get('Authorization');
    // 如果存在token,加入黑名单
    if (token) {
      await ctx.helper.setCache(
        `token_blacklist_${token}`,
        true,
        app.config.jwtUserExpiresIn * 1000
      );
    }

    ctx.cookies.set('admin_' + app.config.auth_cookie_name, null);
    ctx.helper.renderSuccess(ctx, { message: '退出登录成功' });
  }

  getAuthUserFields(type = '') {
    let fieldStr =
      'id enable _id  aboutTransfer email userName logo phoneNum name companyId employeeId company idNo nickName wx alipay companyStatus companyName';
    if (type === 'login') {
      fieldStr =
        'enable aboutTransfer _id email userName logo phoneNum name companyId employeeId company idNo nickName wx alipay companyStatus title nickName role forTrainrole introduction companyName passwordEncryptionAlgorithm';
    } else if (type === 'base') {
      fieldStr =
        'wx aboutTransfer alipay id userName name password category group logo date enable employeeId state phoneNum company countryCode email watchers followers comments IDcard favorites favoriteCommunityContent despises comments profession experience industry introduction birth gender companyStatus';
    } else if (type === 'session') {
      fieldStr =
        'wx aboutTransfer alipay id userName name password category group logo date enable employeeId state phoneNum company countryCode watchers followers praiseContents praiseMessages praiseCommunityContent watchSpecials watchCommunity watchTags favorites favoriteCommunityContent despises despiseMessage despiseCommunityContent IDcard position gender vip email comments companyStatus';
    }
    return fieldStr;
  }
  // 用于前端判断用户是否已登录
  async loginVerification() {
    const { ctx, config } = this;
    try {
      console.log(ctx.session, 'ctx.session-------------');
      const _id = ctx.session.user._id;
      const options = {
        returnOptions: {
          phoneNum: {
            returnPlaintext: true, // 返回明文密码
          },
          idNo: {
            returnPlaintext: true, // 返回明文密码
          },
        },
      };
      let user = await ctx.service.user.item(ctx, {
        query: { _id },
        populate: 'none',
        files: this.getAuthUserFields('login'),
      }, options);
      user = _.assign({}, user);
      user = user._doc;
      console.log(
        user.name || user.userName,
        '========刷新页面展示的用户信息==============='
      );

      if (user) {
        if (user.logo && !user.wx.unionId && !user.alipay.unionId) {
          if (config.storageType.includes('oss')) {
            const logo = user.logo.split('/')[5];
            if (logo && logo.includes('.')) {
              user.logo = await ctx.helper.concatenatePath({
                path: `${this.app.config.upload_http_path}/${user.companyId[0]}/${logo}`,
              });
            }
          } else {
            user.logo = 'http://' + ctx.request.header.host + user.logo;
          }
        }
        // console.log(1111111, user);
        // user.idNo
        const Infodata =
          (await ctx.model.Employee.findOne({ IDNum: user.idNo })) || {};
        // console.log(Infodata, 'Infodata00000000000000000');
        // 判断我的角色身份，对应的是roles表中的alisa
        user.myRoles = [];
        if (
          user.employeeId &&
          user.companyStatus === 2 &&
          user.companyId &&
          user.companyId.length > 0
        ) {
          const EnterpriseID = user.companyId[user.companyId.length - 1];
          const companyDetail = await ctx.model.Adminorg.findOne(
            { _id: EnterpriseID },
            { cname: 1 }
          );
          if (companyDetail && user.company !== companyDetail.cname) {
            user.company = companyDetail.cname || '';
            await ctx.model.User.updateOne(
              { _id: user._id },
              { company: user.company }
            );
          }
          const roles = await ctx.model.Roles.findOne({ EnterpriseID });
          if (roles) {
            roles.formData.forEach(ele => {
              ele.userId.forEach(ele2 => {
                if (ele2.includes(user.employeeId)) {
                  user.myRoles.push(ele.alisa);
                }
              });
            });
            console.log('当前用户的角色身份：', user.myRoles);
          }
        }
        // 判断是否是企业管理员
        let adminUserInfo;
        const adminUsers = await ctx.model.AdminUser.find(
          {
            $or: [{ userId: user._id }, { phoneNum: user.phoneNum }],
            enable: true,
            state: '1',
          },
          { id: 1, userId: 1, employees: 1 }
        );
        if (
          adminUsers.length === 1 &&
          adminUsers[0].employees.includes(user.employeeId)
        ) {
          adminUserInfo = adminUsers[0];
        }
        if (adminUsers.length > 1) {
          ctx.auditLog(
            'adminUser账户重复',
            `user用户手机号${user.phoneNum}或者_id为${user._id}绑定的adminUser有多个`,
            'error'
          );
        }
        if (adminUserInfo && !adminUserInfo.userId) {
          // 给adminUser绑定一个userId
          await ctx.model.AdminUser.updateOne(
            { _id: adminUserInfo._id },
            { userId: user._id }
          );
        }
        user.adminUserId = adminUserInfo ? adminUserInfo._id : null;

        // user.isAdminUser = !!adminUserInfo;
        user.oApi_version = this.app.config.version;
        user.workType = Infodata.station;
        user.gender = Infodata.gender;
        // if (!user.gender) {
        //   // 根据18位身份证计算性别
        //   if (user.idNo && user.idNo.length === 18) {
        //     // 身份证第17位置奇数为男偶数为女 '0'男'1'女 
        //     user.gender = user.idNo.slice(16, 17) % 2 === 0 ? '1' : '0';
        //   }
        // }
        // 根据18位身份证计算年龄，如果没有则用Infodata
        if (user.idNo && user.idNo.length === 18) {
          user.age = moment().diff(moment(user.idNo.slice(6, 14), 'YYYYMMDD'), 'years');
        } else {
          user.age = Infodata.age;
        }
        // 根据入职时间workStart计算工龄，如果没有入职时间则用workYears
        if (Infodata.workStart) {
          // 满了一年的，以年为单位，未满一年的，以月为单位
          user.workYears =
            moment().diff(moment(Infodata.workStart), 'years') > 0
              ? moment().diff(moment(Infodata.workStart), 'years') + '年'
              : moment().diff(moment(Infodata.workStart), 'months') > 0
                ? moment().diff(moment(Infodata.workStart), 'months') + '月'
                : moment().diff(moment(Infodata.workStart), 'days') + '天';
        } else {
          user.workYears = Infodata.workYears || '';
        }
        // console.log(user, 'lihj--------------------');
        const branch = ctx.app.config.branch;
        user.branch = branch;
        await ctx.helper.renderSuccess(ctx, {
          data: user,
          // modelPath: `${this.app.config.static.prefix}/${this.app.config.upload_http_path}/${user.companyId[user.companyId.length - 1]}`,
          message: '校验成功',
        });
      } else {
        await ctx.helper.renderFail(ctx, {
          message: '校验失败',
        });
      }
    } catch (error) {
      ctx.helper.renderFail(error);
    }
  }

  // 根据用户手机号码获取可选企业(针对已经注册或者登陆的用户)
  async getCompanysByPhoneNum() {
    const { ctx } = this;
    const phoneNum = ctx.query.phoneNum;
    const companys = await ctx.service.user.findCompanyByPhoneNum(phoneNum);
    if (companys && companys.length) {
      const res = companys.filter(item => item.EnterpriseID);
      ctx.helper.renderSuccess(ctx, {
        data: res,
        message: '企业匹配成功',
      });
    } else {
      ctx.helper.renderFail(ctx, {
        message: '没有匹配到任何企业',
      });
    }
  }

  // 绑定企业
  async boundEnterprise() {
    const { ctx } = this;
    const { name, companyId, idNo, company, employeeId, phoneNum } =
      ctx.request.body;
    const _id = ctx.session.user._id || ctx.request.body.userId;
    if (!companyId || !_id) {
      ctx.helper.renderFail(ctx, {
        status: 400,
        message: 'companyId或者userId没有收到',
      });
      return;
    }
    let resEmployeeId = employeeId;
    if (!employeeId) {
      const findEmployees = await ctx.service.user.findEmployee({
        EnterpriseID: companyId,
        name,
        $or: [{ IDNum: idNo }, { phoneNum }],
      });
      if (findEmployees.length === 1) resEmployeeId = findEmployees[0]._id;
    }
    // 更新数据
    const res = await ctx.service.user.updateCompany(_id, {
      name,
      idNo,
      company: company || '',
      companyStatus: resEmployeeId ? 2 : 1,
      employeeId: resEmployeeId,
      companyId: [ companyId ],
    });
    if (res) {
      // 同步修改
      await tools.updateData(ctx, 'user', {
        _id,
        type: '2',
      });

      ctx.auditLog('绑定企业操作', `用户${_id}绑定企业成功`, 'info');
      ctx.helper.renderSuccess(ctx, {
        message: '绑定企业成功',
      });
    } else {
      ctx.auditLog('绑定企业操作', `用户${_id}绑定企业失败`, 'error');
      ctx.helper.renderFail(ctx, {
        message: '绑定企业失败',
      });
    }
  }
  async oldBoundEnterprise() {
    const { ctx } = this;
    const { name, companyId, idNo, company, employeeId } = ctx.request.body;
    const _id = ctx.session.user._id || ctx.request.body.userId;
    if (!companyId || !_id) {
      ctx.helper.renderFail(ctx, {
        status: 400,
        message: 'companyId或者userId没有收到',
      });
      return;
    }
    // 1. 判断该身份证号码是否已注册
    const userCount = await ctx.model.User.findOne({
      idNo,
    });
    if (userCount && userCount.idNo !== idNo) {
      ctx.auditLog(
        '绑定身份证号码失败',
        `${name}设置的身份证号${idNo}已${userCount.name}被注册`,
        'info'
      );
      ctx.helper.renderFail(ctx, {
        status: 400,
        message: `该身份证号已${userCount.name}被注册`,
      });
      return;
    }
    // 2. 判断该用户是否已经存在employee中
    let companyStatus = 1; // 假设是不存在于employee总那状态就是已绑定待审核状态
    const employee = await ctx.service.employee.find({ IDNum: idNo });
    console.log(
      `11111111111查询到用户${userCount.name}存在于employee: `,
      employee
    );
    if (employee || employeeId) companyStatus = 2;
    // 3.判断这个这个企业id是否已经存在于用户的companyId数组中，存在的话要先删除掉
    const companyIdArr = userCount ? userCount.companyId : [];
    if (companyIdArr.includes(companyId)) {
      companyIdArr.splice(companyIdArr.indexOf(companyId), 1);
    }
    companyIdArr.push(companyId);
    // 4. 更新数据
    const res = await ctx.service.user.updateCompany(_id, {
      name,
      idNo,
      company: company || '',
      companyStatus,
      employeeId: employee ? employee._id : employeeId,
      companyId: companyIdArr,
      // $push: {
      //   companyId,
      // },
    });
    if (res) {
      ctx.auditLog('绑定企业操作', `用户${_id}绑定企业成功`, 'info');
      ctx.helper.renderSuccess(ctx, {
        message: '绑定企业成功',
      });
    } else {
      ctx.auditLog('绑定企业操作', `用户${_id}绑定企业失败`, 'info');
      ctx.helper.renderFail(ctx, {
        message: '绑定企业失败',
      });
    }
  }
  // 解绑当前企业
  async unbindEnterprise() {
    const { ctx } = this;
    const _id = ctx.session.user._id;
    await ctx.service.user
      .updateCompany(_id, {
        company: '',
        companyStatus: 0,
        employeeId: '',
      })
      .then(res => {
        ctx.auditLog('用户解除绑定企业', `用户${_id}解除绑定企业成功`, 'info');
        console.log('解除绑定企业后：', res);
        ctx.helper.renderSuccess(ctx, {
          message: '解绑成功',
        });
      })
      .catch(err => {
        console.log('解除绑定企业: ', err);
        ctx.helper.renderFail(ctx, {
          message: '解绑失败',
        });
      });
  }

  // 获取员工体检提醒
  async getphysicalExaminationNotice() {
    let harmFactors = '';
    let stations = '';
    let checkNotice = {};
    const params = this.ctx.request.body;
    // 获取员工信息
    const employeeInfo = await this.ctx.model.Employee.findOne({
      EnterpriseID: params.EnterpriseID,
      IDNum: params.idNo,
    });
    // 获取该员工最近一次体检
    const lastCheck = (
      await this.ctx.model.Suspect.find({
        EnterpriseID: params.EnterpriseID,
        employeeId: employeeInfo._id,
        checkDate: { $exists: true },
      }).sort({ checkDate: -1 })
    )[0];
    stations = await this.findEmployeeStation({
      EnterpriseID: params.EnterpriseID,
      employeeId: employeeInfo._id,
    });
    for (let j = 0; j < stations.length; j++) {
      harmFactors =
        (stations[j].harmFactors || '') +
        (stations[j].customizeHarm ? '、' + stations[j].customizeHarm : '');
    }
    if (harmFactors && !lastCheck && employeeInfo.status === 0) {
      checkNotice = { show: true, type: '离岗', harmFactors }; // 离岗
      this.ctx.body = { status: 200, data: checkNotice };
      return;
    }

    if (harmFactors && !lastCheck) {
      // 新上岗
      checkNotice = { show: true, type: '新上岗', harmFactors };
    } else if (lastCheck) {
      // 获取最近一次离岗
      const shouldCheck2 = (
        await this.ctx.model.EmployeeStatusChange.aggregate([
          {
            $match: {
              employee: employeeInfo._id,
            },
          },
          { $unwind: '$statusChanges' },
          {
            $match: {
              'statusChanges.EnterpriseID': params.EnterpriseID,
              'statusChanges.changType': 1,
            },
          },
          { $sort: { 'statusChanges.timestamp': -1 } }, // 按时间排序
        ])
      )[0];
      // 获取最近一次转岗
      const lastChangeStation = (
        await this.ctx.model.EmployeeStatusChange.aggregate([
          {
            $match: {
              employee: employeeInfo._id,
            },
          },
          { $unwind: '$statusChanges' },
          {
            $match: {
              'statusChanges.stationsTo.0': { $exists: true },
              'statusChanges.stationFrom': { $exists: true },
              'statusChanges.EnterpriseID': params.EnterpriseID,
              'statusChanges.changType': 2,
            },
          },
          { $sort: { 'statusChanges.timestamp': -1 } }, // 按时间排序
        ])
      )[0];
      // 离岗并且离岗前所在岗位存在危害因素并且离岗前3个月内未作体检(目前不在该企业)
      if (
        employeeInfo.EnterpriseID !== params.EnterpriseID &&
        shouldCheck2 &&
        harmFactors &&
        new Date(lastCheck.checkDate).getTime() <
        new Date(
          new Date().setMonth(
            new Date(shouldCheck2.statusChanges.timestamp).getMonth() - 3
          )
        ).getTime()
      ) {
        checkNotice = {
          show: true,
          type: '离岗',
          lastCheckTime: moment(new Date(lastCheck.checkDate)).format(
            'YYYY年MM月DD日'
          ),
          harmFactors,
        };
      } else {
        // // 转岗
        // if (lastChangeStation.statusChanges.stationsTo[0]) {
        //   lastChangeStation.statusChanges.stationsTo[0] = await this.getStationInfo(params.EnterpriseID, employeeInfo._id, lastChangeStation.statusChanges.stationsTo[0]) || '';
        // }
        // if (lastChangeStation.statusChanges.stationFrom) {
        //   lastChangeStation.statusChanges.stationFrom = await this.getStationInfo(params.EnterpriseID, employeeInfo._id, lastChangeStation.statusChanges.stationsTo[0]) || '';
        // }
        // 转岗前三个月以上未作体检
        console.log('上一次体检', lastCheck.checkDate);
        if (
          lastChangeStation &&
          employeeInfo.status === 1 &&
          new Date(lastCheck.checkDate).getTime() <
          new Date(
            new Date().setMonth(
              new Date(lastChangeStation.statusChanges.timestamp).getMonth() -
              3
            )
          ).getTime()
        ) {
          checkNotice = {
            show: true,
            type: '转岗',
            lastCheckTime: moment(new Date(lastCheck.checkDate)).format(
              'YYYY年MM月DD日'
            ),
          };
        } else if (
          employeeInfo.status === 1 &&
          new Date(lastCheck.checkDate).getTime() >
          new Date(
            new Date().setMonth(new Date().getMonth() - 12)
          ).getTime() &&
          new Date(lastCheck.checkDate).getTime() <
          new Date(new Date().setMonth(new Date().getMonth() - 10)).getTime()
        ) {
          checkNotice = {
            show: true,
            type: '将到期',
            harmFactors,
            lastCheckTime: moment(new Date(lastCheck.checkDate)).format(
              'YYYY年MM月DD日'
            ),
          };
        } else if (
          employeeInfo.status === 1 &&
          new Date(lastCheck.checkDate).getTime() >
          new Date(
            new Date().setMonth(new Date().getMonth() - 24)
          ).getTime() &&
          new Date(lastCheck.checkDate).getTime() <
          new Date(new Date().setMonth(new Date().getMonth() - 12)).getTime()
        ) {
          checkNotice = {
            show: false,
            type: '已过期',
            lastCheckTime: moment(new Date(lastCheck.checkDate)).format(
              'YYYY年MM月DD日'
            ),
          };
        } else if (
          employeeInfo.status === 1 &&
          new Date(lastCheck.checkDate).getTime() >
          new Date(
            new Date().setMonth(new Date().getMonth() - 12)
          ).getTime() &&
          new Date(lastCheck.checkDate).getTime() < new Date().getTime()
        ) {
          checkNotice = {
            show: false,
            type: '已体检',
            lastCheckTime: moment(new Date(lastCheck.checkDate)).format(
              'YYYY年MM月DD日'
            ),
          };
        }
      }
    }
    console.log('走到这里了????', checkNotice);
    this.ctx.body = { status: 200, data: checkNotice };
  }

  async confirmPhysicalExamination() {
    const { ctx, app } = this;
    let params = {};
    try {
      if (JSON.stringify(ctx.request.body) === '{}') {
        params = await this.parseData(ctx, app);
      } else {
        params = ctx.request.body;
      }

      ctx.service.user.confirmPhysicalExamination(params);

      ctx.body = { status: 200, message: '体检确认成功' };
    } catch (error) {
      console.log(error);
      ctx.body = { status: 500, message: error };
    }
  }

  // 获取员工当前岗位
  // 获取员工岗位
  async findEmployeeStation(params) {
    try {
      const res = await this.ctx.model.MillConstruction.aggregate([
        {
          $match: {
            EnterpriseID: params.EnterpriseID,
          },
        },
        {
          $unwind: '$children',
        },
        {
          $unwind: '$children.children',
        },
        {
          $match: {
            $or: [
              { 'children.children.employees': params.employeeId },
              { 'children.children.children.employees': params.employeeId },
            ],
          },
        },
      ]);
      const stations = [];
      res.forEach(item => {
        if (item.category === 'workspaces') {
          item.children.workspace = item.name;
          item.children.harmFactorsAndSort = item.children.harmFactors;
          item.children.harmFactors = item.children.harmFactors
            .map(item2 => item2[1])
            .join('、');
          stations.push(item.children);
        } else if (item.category === 'mill') {
          item.children.children.workspace = item.children.name;
          item.children.children.harmFactorsAndSort =
            item.children.children.harmFactors;
          item.children.children.harmFactors =
            item.children.children.harmFactors
              .map(item2 => item2[1])
              .join('、');
          stations.push(item.children.children);
        }
      });
      return stations;
    } catch (error) {
      console.log(error);
    }
  }
  // 查看体检信息
  async physicalExamination() {
    const { ctx } = this;
    const { idNo, EnterpriseID } = ctx.request.body;
    const employee = await ctx.service.employee.find({
      EnterpriseID,
      IDNum: idNo,
    });
    const condition = {
      employeeId: employee._id,
    };
    if (ctx.app.config.branch === 'by') {
      condition.checkDate = { $gte: new Date().setFullYear(new Date().getFullYear() - 1) };
    }
    const physical = await ctx.model.Suspect.find(condition).lean();
    const physicalBefore = await ctx.model.OccupationalHealthCheckList.find({
      employeeId: employee._id,
      status: '1',
      reviewStatus: '1',
    }).populate([
      {
        path: 'medicalExamInfo',
        select: 'harmFactors medicalExamType',
      },
    ]).lean();
    const isOss = ctx.app.config.storageType.includes('oss');
    for (const record of physicalBefore) {
      const medicalExamInfo = record.medicalExamInfo;
      if (record.guideForm.originName) {
        record.previewPic = isOss
          ? await ctx.helper.concatenatePath({
            path: `${this.app.config.upload_http_path}/${EnterpriseID}/${record.guideForm.staticName}`,
          })
          : `static${this.app.config.upload_http_path}/${EnterpriseID}/${record.guideForm.staticName}`;
      }
      if (medicalExamInfo.length) {
        const allHarmFactors = Array.from(
          new Set(
            medicalExamInfo.reduce((acc, info) => {
              if (info.harmFactors && Array.isArray(info.harmFactors)) {
                acc.push(...info.harmFactors);
              }
              return acc;
            }, [])
          )
        );
        const res = await this.ctx.model.PhysicalExamOrg.aggregate([
          {
            $match: {
              harmFactors: { $exists: true, $type: 'array' },
            },
          },
          {
            $addFields: {
              allMatched: {
                $setIsSubset: [
                  allHarmFactors,
                  {
                    $map: {
                      input: '$harmFactors',
                      as: 'factor',
                      in: { $arrayElemAt: [ '$$factor', 1 ] },
                    },
                  },
                ],
              },
            },
          },
          {
            $match: {
              allMatched: true,
            },
          },
          {
            $project: {
              allMatched: 0, // 可以选择不返回这个字段
            },
          },
        ]);
        const hospital = [];
        for (const hospitalData of res) {
          const orgId = hospitalData._id; // 假设 org_id 是医院的 ID
          const orgName = hospitalData.shortName || hospitalData.name;
          hospital.push({
            value: orgId,
            text: orgName,
            selected: false,
            disable: false,
          });
        }
        // 将医院信息添加到体检信息对象中
        record.hospitals = hospital;
      }
    }
    function orderSort(obj1, obj2) {
      const a = obj1.checkDate;
      const b = obj2.checkDate;
      if (a > b) {
        return -1;
      } else if (a < b) {
        return 1;
      }
      return 0;
    }
    physical.sort(orderSort);
    for (let i = 0; i < physical.length; i++) {
      if (physical[i].caseCard.staticName) {
        physical[i].caseCard.previewUrl = await ctx.helper.concatenatePath({
          path: `${this.app.config.upload_http_path}/${EnterpriseID}/${physical[i].caseCard.staticName}`,
        });
      }
      if (physical[i].checkType === '0') {
        physical[i].checkType = '上岗前';
      } else if (physical[i].checkType === '1') {
        physical[i].checkType = '在岗';
      } else if (physical[i].checkType === '2') {
        physical[i].checkType = '离岗';
      } else if (physical[i].checkType === '3') {
        physical[i].checkType = '复查';
      }
    }
    ctx.helper.renderSuccess(ctx, {
      data: {
        isOss,
        physical,
        physicalBefore,
        signatrue: employee.signatrue
          ? 'static' +
          this.app.config.upload_http_path +
          '/' +
          EnterpriseID +
          '/' +
          employee.signatrue
          : '',
      },
    });
  }

  // 确认体检医院
  async confirmSelectHospital() {
    const { ctx } = this;
    try {
      await ctx.service.occupationalHealthCheckList.confirmSelectHospital(
        ctx.request.body
      );
      ctx.helper.renderSuccess(ctx, {
        message: '确认成功',
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  // 上传引导单子
  async uploadGuideForm() {
    const { ctx, app } = this;
    const parts = ctx.multipart({ autoFields: true });
    let stream;
    let params = {};
    while ((stream = await parts()) != null) {
      if (!stream.filename) {
        // 注意如果没有传入直接返回
        return;
      }
      params = parts.field;
      const configFilePath = path.join(
        app.config.upload_path,
        params.EnterpriseID
      );
      const filename =
        Math.random().toString(36).substr(2) +
        new Date().getTime() +
        path.extname(stream.filename).toLocaleLowerCase();
      params.guideForm = {
        originName: filename,
        staticName: filename,
      };
      // 图片存放在静态资源public/img文件夹下
      if (!fs.existsSync(configFilePath)) {
        fs.mkdirSync(configFilePath);
      }
      const target = path.resolve(configFilePath, filename);
      // 生成一个文件写入 文件流
      // const writeStream = fs.createWriteStream(target);
      try {
        // 异步把文件流 写入
        // await pump(stream, writeStream); // 写入并销毁当前流
        await ctx.helper.pipe({
          readableStream: stream,
          target,
        });
        const res =
          await ctx.service.occupationalHealthCheckList.updateGuideForm(params);
        // res.previewPic =
        //   'static' +
        //   app.config.upload_http_path +
        //   '/' +
        //   params.EnterpriseID +
        //   '/' +
        //   filename;
        res.previewPic = await ctx.helper.concatenatePath({
          path: `${app.config.upload_http_path}/${params.EnterpriseID}/${filename}`,
        });
        const isOss = ctx.app.config.storageType.includes('oss');
        res.isOss = isOss;
        ctx.helper.renderSuccess(ctx, {
          data: res,
          message: '上传成功',
        });

        // await awaitWriteStream(stream.pipe(writeStream));
      } catch (error) {
        console.log(error);
        // 如果出现错误，关闭管道,防止浏览器响应卡死
        await sendToWormhole(stream);
        // writeStream.destroy();
        ctx.helper.renderFail(ctx, {
          message: error,
        });
      }
    }
  }

  // 删除引导单子
  async deleteGuideForm() {
    const { ctx, app } = this;
    try {
      const { _id, guideFormUrl, EnterpriseID } = ctx.request.body;
      // 删除文件
      const deletePath = path.join(
        app.config.upload_path,
        EnterpriseID,
        guideFormUrl
      );
      if (fs.existsSync(deletePath)) {
        await fs.unlinkSync(deletePath);
      }
      await ctx.service.occupationalHealthCheckList.deleteGuideForm({ _id });
      ctx.helper.renderSuccess(ctx, {
        message: '删除成功',
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  async listByPower() {
    const { ctx, service } = this;
    try {
      const payload = {
        isPaging: '0',
      };

      const manageCates = await service.userResource.find(payload, {
        files: 'api _id label enable routePath parentId type icon comments',
      });
      const adminPower = await ctx.helper.getAdminPower(ctx);
      const currentCates = await siteFunc.renderNoPowerMenus(
        manageCates,
        adminPower
      );
      ctx.helper.renderSuccess(ctx, {
        data: currentCates,
        message: '操作成功',
      });
    } catch (err) {
      console.error(err);
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  }

  // 获取可体检医院
  async findAllReservation(ctx) {
    try {
      const payload = ctx.query;
      const query = ctx.query.query ? JSON.parse(ctx.query.query) : {};
      if (query.level) payload.level = query.level;
      if (query.regAddr) payload.regAddr = query.regAddr;

      const data = await ctx.service.user.findAllService(payload);
      ctx.helper.renderSuccess(ctx, {
        data,
      });
    } catch (e) {
      ctx.helper.renderFail(ctx, {
        message: e,
      });
    }
  }

  // 获取所有指标
  async getAllIndicators() {
    const { ctx, app } = this;
    try {
      let res = await ctx.model.Indicator.find();
      // 如果数据库中不存在 指标数据，则初始化
      if (res.length === 0 && app.config.branch === 'wh') {
        const indicators = [
          { name: '身高', code: '2010200' },
          { name: '体重', code: '2010300' }, { name: '体重指数BMI', code: '2010600' }, { name: '高密度脂蛋白', code: '4071100' }, { name: '低密度脂蛋白', code: '4071200' }, { name: '丙氨酸氨基转移酶', code: '4040900' }, { name: 'γ-谷氨酰转肽酶', code: '4043400' }, { name: '总胆红素', code: '4042800' },
          { name: '尿蛋白', code: '4020500' }, { name: '尿隐血', code: '4021000' }, { name: '尿比重', code: '4321100' }, { name: 'FEV1', code: '3020200' }, { name: 'FVC', code: '3020100' }, { name: 'FEV1/FVC', code: '3020300' }, { name: '空腹血糖', code: '4070500' },
          { name: '白细胞计数', code: '4010100' },
        ];
        await ctx.model.Indicator.create(indicators);
        res = await ctx.model.Indicator.find();
      }
      console.log('🍊res', res);
      ctx.helper.renderSuccess(ctx, {
        data: res,
      });
    } catch (e) {
      ctx.helper.renderFail(ctx, {
        message: e,
      });
      ctx.auditLog('获取所有指标', e, 'error');
    }
  }

  // 获取个人历年体检指标趋势
  async indicatorsTrend(ctx) {
    try {
      const payload = ctx.query;
      const { itmcod } = payload;
      console.log('🍊payload', payload);
      const userInfo = await ctx.service.user.getUserInfo(ctx.session.user._id);
      const { age, name, gender, phoneNum, idNo, employeeId } = userInfo;
      const list = await ctx.service.user.indicatorsTrend({ employeeId: employeeId._id, itmcod });
      ctx.helper.renderSuccess(ctx, {
        data: {
          userInfo: { age, name, gender, phoneNum, idNo, marriage: employeeId.marriage },
          list,
        },
      });
    } catch (e) {
      ctx.helper.renderFail(ctx, {
        message: e,
      });
    }
  }

  // 预约方法
  async createReservation(ctx) {
    try {
      const fields = ctx.request.body || {};

      // 预约
      await ctx.service.user.createReservation(fields);
      ctx.helper.renderSuccess(ctx, {});
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  //  文件解析
  async parseData(ctx, app) {
    const parts = await ctx.multipart({ autoFields: true });
    const params = {};
    let stream;
    console.log('解析文件=====================');
    while ((stream = await parts()) != null) {
      if (!stream.filename) {
        // 注意如果没有传入直接返回
        return;
      }
      console.log(parts.field.EnterpriseID);
      for (const key in parts.field) {
        params[key] = parts.field[key];
      }
      const configFilePath = path.join(
        app.config.upload_path,
        params.EnterpriseID
      );
      const filename =
        Math.random().toString(36).substr(2) +
        new Date().getTime() +
        path.extname(stream.filename).toLocaleLowerCase();
      params.filename = filename;
      // 图片存放在静态资源public/img文件夹下
      tools.makeEnterpriseDir(configFilePath);
      const target = path.resolve(configFilePath, filename);
      // 生成一个文件写入 文件流
      const writeStream = fs.createWriteStream(target);
      // 体检签名公章图片路径
      const { branch = '' } = ctx.app.config;
      const signatruePath = process.cwd() + `/app/public/images/${branch}PhysicalSign.svg`;
      try {
        // 异步把文件流 写入
        await pump(stream, writeStream); // 写入并销毁当前流
        if (ctx.app.config.storageType.includes('oss')) {
          const pdfPathOss = path.join(
            app.config.upload_path,
            `/${params.EnterpriseID}`,
            `/${params.staticName}`
          );
          const pdfPathSplit = pdfPathOss
            .replace(/\\/gi, '/')
            .split('/public/');
          await ctx.helper.fGetObject({
            objectPath: pdfPathSplit[pdfPathSplit.length - 1],
            filePath: pdfPathOss,
          });
          await this.delay(1000);
          await addSignatureToPDF(
            pdfPathOss,
            target,
            0.7,
            0.1,
            pdfPathOss,
            signatruePath
          );
          // await awaitWriteStream(stream.pipe(writeStream));
          const readStream = fs.createReadStream(pdfPathOss);
          await ctx.helper.pipe({
            readableStream: readStream,
            target: pdfPathOss,
          });
          fs.unlinkSync(pdfPathOss);
        } else {
          await this.delay(1000);
          await addSignatureToPDF(
            app.config.enterpriseUpload_path +
            '/' +
            params.EnterpriseID +
            '/' +
            params.staticName,
            app.config.upload_path + '/' + params.EnterpriseID + '/' + filename,
            0.7,
            0.1,
            app.config.enterpriseUpload_path +
            '/' +
            params.EnterpriseID +
            '/' +
            params.staticName,
            signatruePath
          );
        }
      } catch (error) {
        console.log(error);
        // 如果出现错误，关闭管道,防止浏览器响应卡死
        await sendToWormhole(stream);
        writeStream.destroy();
        throw error;
      }
    }
    console.log('最后结果===', params);
    return params;
  }
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
  // user 上传更新头像
  async uploadLogo() {
    const { ctx, config } = this;
    try {
      const parts = ctx.multipart({ autoFields: true });
      // console.log(111, parts);
      const stream = await parts();
      const userId = ctx.session.user._id;
      const userDetail = await ctx.model.User.findOne(
        { _id: userId },
        { employeeId: 1, companyId: 1 }
      );
      if (!userDetail) {
        ctx.helper.renderFail(ctx, {
          message: 'userId找不到',
        });
      }
      const employeeId = userDetail.employeeId,
        companyId =
          userDetail.companyId && userDetail.companyId.length
            ? userDetail.companyId[userDetail.companyId.length - 1]
            : userId;
      if (stream && stream.filename) {
        const suffixArray = stream.filename.split('.');
        const suffix = suffixArray[suffixArray.length - 1];
        const fileName =
          Math.random().toString(36).substr(2) +
          new Date().getTime() +
          '.' +
          suffix;
        const savePath = path.join(config.upload_path, companyId);
        const target = path.resolve(savePath, fileName);
        tools.makeEnterpriseDir(savePath);
        try {
          // 异步把文件流 写入
          await ctx.helper.pipe({
            readableStream: stream,
            target,
          });
        } catch (error) {
          // 如果出现错误，关闭管道,防止浏览器响应卡死
          await sendToWormhole(stream);
          // writeStream.destroy();
          throw error;
        }
        // const writeStream = fs.createWriteStream(
        //   path.resolve(savePath, fileName)
        // );
        // await awaitWriteStream(stream.pipe(writeStream));
        // if (!Object.keys(parts.field).length) {
        //   await sendToWormhole(stream);
        // }
        // 更新user表
        // const logo = fileName;
        const logo = `/static${config.upload_http_path}/${companyId}/${fileName}`;
        await ctx.model.User.updateOne({ _id: userId }, { logo });
        // 同步修改证书内图片路径
        await ctx.model.Certificate.updateMany(
          { userId },
          { $set: { 'winner.headImg': logo } }
        );
        if (employeeId) {
          // 更新employees表
          await ctx.model.Employee.updateOne(
            { _id: employeeId },
            { headImg: fileName }
          );
        }
        ctx.helper.renderSuccess(ctx, {
          message: '文件上传成功',
          data: logo,
        });
      } else {
        ctx.helper.renderFail(ctx, {
          message: '未接收到文件',
        });
      }
    } catch (error) {
      ctx.logger.error(error);
      console.error(error);
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }
  // 更新用户身份证号码
  async updateUserInfo() {
    const { ctx } = this;
    const userId = ctx.session.user._id;
    const userDetail = await ctx.model.User.findOne(
      { _id: userId },
      { employeeId: 1, companyId: 1 }
    );
    if (!userDetail) {
      ctx.helper.renderFail(ctx, {
        message: 'userId找不到',
      });
    }
    const employeeId = userDetail.employeeId;
    const idNo = ctx.request.body.idNo ? ctx.request.body.idNo.trim() : '';
    const count = await ctx.model.User.count({ idNo });
    if (count) {
      return ctx.helper.renderFail(ctx, {
        message: '该身份证号已经被人使用',
        data: idNo,
      });
    }
    await ctx.model.User.updateOne({ _id: userId }, { idNo });
    await ctx.model.AdminUser.updateOne({ userId }, { IDcard: idNo });
    if (employeeId) {
      await ctx.model.Employee.updateOne({ _id: employeeId }, { IDNum: idNo });
    }
    ctx.helper.renderSuccess(ctx, {
      message: '身份证上传成功',
      data: idNo,
    });
  }

  // 绑定微信
  async bindWxInfo() {
    const { app, ctx } = this;
    let session_key = '';
    const fields = ctx.request.body || {};
    const WXBizDataCrypt = require('../../utils/WXBizDataCrypt.js');
    const appid = app.config.wxAutho.appid,
      secret = app.config.wxAutho.secret,
      userRawData = {
        wx: {
          nickName: fields.rawData.nickName,
          logo: fields.rawData.avatarUrl,
        },
        gender: fields.rawData.gender - 1,
        city: fields.rawData.city,
        province: fields.rawData.province,
        country: fields.rawData.country,
        countryCode: '86',
      };
    try {
      let unionId = '';
      let openId = '';
      if (fields.unionId) {
        console.log('参数===', fields);
        unionId = fields.unionId;
      } else {
        // 通过code调用腾讯接口获取openid）
        const res = await ctx.curl(
          'https://api.weixin.qq.com/sns/jscode2session',
          {
            data: {
              appid,
              secret,
              js_code: fields.info.code,
              grant_type: 'authorization_code',
            },
            dataType: 'json',
          }
        );
        session_key = res.data.session_key;
        if (res.status === 200 && res.data.unionid && res.data.openid) {
          // 可以直接通过jscode2session获取unionId
          unionId = res.data.unionid;
          openId = res.data.openid;
          userRawData.wx.unionId = unionId;
          userRawData.wx.openId = openId;
          const overres = await this.ctx.service.user.bindWxInfo(userRawData);
          ctx.helper.renderSuccess(ctx, {
            data: overres,
            message: '操作成功',
          });
        } else {
          // 对比签名
          const signature2 = sha1(fields.info.decryRawData + session_key);
          if (fields.info.signature !== signature2) {
            console.log('授权失败');
            ctx.helper.renderFail(ctx, {
              message: '授权失败',
            });
          } else {
            // 通过解密获取unoinid
            // 解密
            const pc = new WXBizDataCrypt(appid, session_key);
            const data = pc.decryptData(
              fields.info.encryptedData,
              fields.info.iv
            );
            unionId = data.unionId;
            userRawData.wx.unionId = unionId;
            userRawData.wx.openId = data.openId;
            const overres = await this.ctx.service.user.bindWxInfo(userRawData);
            ctx.helper.renderSuccess(ctx, {
              data: overres,
              message: '操作成功',
            });
          }
        }
      }
    } catch (err) {
      console.log(err);
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  }

  // 根据id查询员工信息以及危害因素等接害情况
  async findEmployeeById(ctx, app) {
    const params = ctx.request.body;
    const EnterpriseID = ctx.session.adminUserInfo
      ? ctx.session.adminUserInfo.EnterpriseID
      : '';
    params.EnterpriseID = EnterpriseID;
    try {
      const res = await ctx.service.mill.findEmployeeById(params);
      ctx.body = {
        code: 200,
        ...res,
        filePath: `${app.config.static.prefix}${app.config.upload_http_path}/${EnterpriseID}`,
      };
    } catch (error) {
      console.log(error);
      ctx.body = { code: 500, message: '服务器出错' };
    }
  }

  async getLastPhysicalDataAndDeteData() {
    try {
      const { ctx } = this;
      const userInfo = ctx.query;
      // console.log('劳动者个人信息', userInfo);
      ctx.auditLog('劳动者个人信息', `${userInfo}`, 'info');
      const res = await ctx.service.user.getLastPhysicalData(userInfo); // 体检数据
      const res2 = await ctx.service.user.getLastcheckResult(userInfo); // 检测数据
      ctx.helper.renderSuccess(ctx, {
        message: '获取体检检测数据',
        data: { res, res2 },
      });
    } catch (error) {
      this.ctx.auditLog('获取劳动者检测和体检数据', `${error}`, 'error');
    }

    // console.log('劳动者最新检测数据：', res2);
  }
  // 获取问卷列表
  async getQuestionnaireList() {
    const { ctx } = this;
    if (!ctx.session.user._id) {
      throw new Error('用户信息缺失');
    }
    const res = await this.ctx.model.User.findOne({
      _id: ctx.session.user._id,
    });
    if (!res) {
      throw new Error('不存在用户');
    }
    const EnterpriseID = res.companyId[res.companyId.length - 1];
    const org = await this.ctx.model.Adminorg.findOne({
      _id: EnterpriseID,
    });
    console.log('res', res);
    // 注册劳动者账号
    const info = {
      id: res._id,
      name: res.name,
      phone: res.phoneNum,
      gender: res.gender,
      auth_account: res.userName,
      org_id: EnterpriseID,
      org_name: org.cname,
      org_code: org.code,
      org_short_name: org.cname,
    };
    const ans = await ctx.curl(
      `${ctx.app.config.iService2Host}/wj`,
      {
        method: 'POST',
        dataType: 'json',
        data: info,
      }
    );
    console.log('ans', ans);
    const params = {
      dept_id: EnterpriseID,
      role_code: 'user',
      status: 1,
      user_id: res._id,
    };
    const { data } = await ctx.curl(
      `${ctx.app.config.iService2Host}/wj/questionnaires`,
      {
        method: 'POST',
        dataType: 'json',
        data: params,
      }
    );
    const token = jwt.sign({
      id: res._id,
    }, ctx.app.config.encrypt_key, {
      expiresIn: '30day',
    });
    console.log('data', data);
    ctx.helper.renderSuccess(ctx, {
      data: {
        list: data.data,
        token,
      },
    });
  }
}

module.exports = AdminController;
