const Controller = require('egg').Controller;
const moment = require('moment');
class QuestionnaireController extends Controller {
  // 获取个人问卷调查列表
  async getPersonalQuestionnaire() {
    const { ctx } = this;
    try {
      const _id = ctx.session.user._id;
      // 通过_id查询User表
      const res = await ctx.model.User.findOne(
        { _id },
        { companyId: 1, company: 1 }
      );
      const EnterpriseID = res.companyId[0];
      const cname = res.company;
      const list = await ctx.curl(
        `${this.config.iServiceHost}/questionnaire/getPersonalQuestionnaire`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          dataType: 'json', // 返回的数据类型
          data: JSON.stringify({ EnterpriseID, cname, questionAnswer: _id }),
        }
      );
      list.data.data = list.data.data.map(item => {
        return {
          ...item,
          createdAt: moment(item.createdAt).format('YYYY-MM-DD'),
        };
      });
      ctx.helper.renderSuccess(ctx, {
        data: list.data.data,
        status: list.status,
        message: '获取成功',
      });
    } catch (error) {
      ctx.auditLog('获取失败', `${error}`, 'error');
      return ctx.helper.renderFail(ctx, {
        message: '服务器伐开心',
        data: null,
      });
    }
  }

  // 获取个人指定问卷调查
  async getPersonalQuestionnaireById() {
    const { ctx } = this;
    try {
      const { questionnaireId, isFilled } = ctx.request.body;
      const _id = ctx.session.user._id;
      const list = await ctx.curl(
        `${this.config.iServiceHost}/questionnaire/getPersonalQuestionnaireById`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          dataType: 'json', // 返回的数据类型
          data: JSON.stringify({ questionnaireId, isFilled, questionAnswer: _id }),
        }
      );
      ctx.helper.renderSuccess(ctx, {
        data: list.data.data,
        status: list.status,
        message: '获取成功',
      });
    } catch (error) {
      ctx.auditLog('获取失败', `${error}`, 'error');
      return ctx.helper.renderFail(ctx, {
        message: '服务器伐开心',
        data: null,
      });
    }
  }

  // 提交个人问卷调查
  async submitSurvey() {
    const { ctx } = this;
    try {
      const data = ctx.request.body;
      const _id = ctx.session.user._id;
      data.questionAnswer = _id;
      const res = await ctx.model.User.findOne(
        { _id },
        { companyId: 1, company: 1 }
      );
      const companyId = res.companyId[0];
      data.companyId = companyId;
      const list = await ctx.curl(
        `${this.config.iServiceHost}/questionnaire/submitSurvey`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          dataType: 'json', // 返回的数据类型
          data,
        }
      );
      ctx.helper.renderSuccess(ctx, {
        data: list.data.data,
        status: list.status,
        message: '获取成功',
      });
    } catch (error) {
      ctx.auditLog('获取失败', `${error}`, 'error');
      return ctx.helper.renderFail(ctx, {
        message: '服务器伐开心',
        data: null,
      });
    }
  }

  // 扫码获取问卷
  async getQuestionnaireByCode() {
    const { ctx } = this;
    try {
      const { questionnaireId } = ctx.request.body;
      const _id = ctx.session.user._id;
      const list = await ctx.curl(
        `${this.config.iServiceHost}/questionnaire/getQuestionnaireByCode`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          dataType: 'json', // 返回的数据类型
          data: JSON.stringify({
            questionnaireId,
            questionAnswer: _id,
          }),
        }
      );
      ctx.helper.renderSuccess(ctx, {
        data: list.data.data,
        status: list.status,
        message: '获取成功',
      });
    } catch (error) {
      ctx.auditLog('获取失败', `${error}`, 'error');
      return ctx.helper.renderFail(ctx, {
        message: '服务器伐开心',
        data: null,
      });
    }
  }
}
module.exports = QuestionnaireController;
