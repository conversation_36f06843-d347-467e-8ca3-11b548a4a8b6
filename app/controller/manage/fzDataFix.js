'use strict';

const Controller = require('egg').Controller;

/**
 * @controller 福州数据处理管理
 */
class FzDataFixController extends Controller {
  /**
   * @summary 获取福州数据处理日志列表
   * @description 分页获取数据处理日志列表，支持多种过滤条件
   * @return {Promise<void>} 无返回值
   */
  async getLogs() {
    const { ctx, service } = this;
    try {
      const payload = ctx.query;
      const result = await service.fzDataFix.getLogs(payload);
      ctx.helper.renderSuccess(ctx, {
        data: result,
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err.message,
      });
    }
  }

  /**
   * @summary 获取福州数据处理统计信息
   * @description 获取日志统计数据，包括各种状态的任务数量
   * @return {Promise<void>} 无返回值
   */
  async getStats() {
    const { ctx, service } = this;
    try {
      const payload = ctx.query;
      const result = await service.fzDataFix.getStats(payload);
      ctx.helper.renderSuccess(ctx, {
        data: result,
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err.message,
      });
    }
  }

  /**
   * @summary 获取任务详情
   * @description 获取指定任务的详细信息，包括解密数据和处理结果
   * @return {Promise<void>} 无返回值
   */
  async getTaskDetails() {
    const { ctx, service } = this;
    try {
      const payload = ctx.query;
      const result = await service.fzDataFix.getTaskDetails(payload);
      ctx.helper.renderSuccess(ctx, {
        data: result,
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err.message,
      });
    }
  }

  /**
   * @summary 重试失败任务
   * @description 支持批量重试失败的任务或重试指定任务ID，支持按任务类型过滤
   * @return {Promise<void>} 无返回值
   */
  async retryFailedTasks() {
    const { ctx, service } = this;
    try {
      const payload = ctx.request.body;
      const result = await service.fzDataFix.retryFailedTasks(payload);
      ctx.helper.renderSuccess(ctx, {
        data: result,
      });

      // 记录审计日志
      const logMessage = payload.taskIds
        ? `重试指定任务: ${payload.taskIds.join(', ')}`
        : `批量重试失败任务, 类型: ${payload.taskType || '全部'}`;

      ctx.auditLog(
        '重试任务',
        logMessage,
        result.success ? 'info' : 'error'
      );
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err.message,
      });
    }
  }

  /**
   * @summary 企业数据清洗测试
   * @description 执行企业数据清洗测试
   * @return {Promise<void>} 无返回值
   */
  async testCompanyDataCleaning() {
    const { ctx, service } = this;
    try {
      const payload = ctx.request.body;
      const result = await service.fzDataFix.testDataCleaning(
        'company',
        payload
      );
      ctx.helper.renderSuccess(ctx, {
        data: result,
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err.message,
      });
    }
  }

  /**
   * @summary 体检数据清洗测试
   * @description 执行体检数据清洗测试
   * @return {Promise<void>} 无返回值
   */
  async testHealthCheckDataCleaning() {
    const { ctx, service } = this;
    try {
      const payload = ctx.request.body;
      const result = await service.fzDataFix.testDataCleaning(
        'healthCheck',
        payload
      );
      ctx.helper.renderSuccess(ctx, {
        data: result,
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err.message,
      });
    }
  }

  /**
   * @summary 诊断数据清洗测试
   * @description 执行诊断数据清洗测试
   * @return {Promise<void>} 无返回值
   */
  async testDiagnosisDataCleaning() {
    const { ctx, service } = this;
    try {
      const payload = ctx.request.body;
      const result = await service.fzDataFix.testDataCleaning(
        'diagnosis',
        payload
      );
      ctx.helper.renderSuccess(ctx, {
        data: result,
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err.message,
      });
    }
  }

  /**
   * @summary 获取文件列表摘要
   * @description 获取文件列表摘要信息，包括各类型文件数量
   * @return {Promise<void>} 无返回值
   */
  async getFiles() {
    const { ctx, service } = this;
    try {
      const result = await service.fzDataFix.getFiles();
      ctx.helper.renderSuccess(ctx, {
        data: result,
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err.message,
      });
    }
  }

  /**
   * @summary 获取文件详细信息
   * @description 获取分类文件的详细信息列表
   * @return {Promise<void>} 无返回值
   */
  async getFileDetails() {
    const { ctx, service } = this;
    try {
      const result = await service.fzDataFix.getFileDetails();
      ctx.helper.renderSuccess(ctx, {
        data: result,
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err.message,
      });
    }
  }

  /**
   * @summary 清理缓存
   * @description 清理系统缓存
   * @return {Promise<void>} 无返回值
   */
  async clearCache() {
    const { ctx, service } = this;
    try {
      const result = await service.fzDataFix.clearCache();
      ctx.helper.renderSuccess(ctx, {
        data: result,
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err.message,
      });
    }
  }

  /**
   * @summary 串行处理企业数据
   * @description 串行处理企业数据，逐个文件处理
   * @return {Promise<void>} 无返回值
   */
  async serialProcessCompany() {
    const { ctx, service } = this;
    try {
      const payload = ctx.request.body;
      // 如果传递了 dataType 参数，使用该参数，否则默认为 company
      const dataType = payload.dataType || 'company';
      const result = await service.fzDataFix.serialProcessData(
        dataType,
        payload
      );
      ctx.helper.renderSuccess(ctx, {
        data: result,
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err.message,
      });
    }
  }

  /**
   * @summary 串行处理体检数据
   * @description 串行处理体检数据，逐个文件处理
   * @return {Promise<void>} 无返回值
   */
  async serialProcessHealthCheck() {
    const { ctx, service } = this;
    try {
      const payload = ctx.request.body;
      const result = await service.fzDataFix.serialProcessData(
        'healthCheck',
        payload
      );
      ctx.helper.renderSuccess(ctx, {
        data: result,
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err.message,
      });
    }
  }

  /**
   * @summary 串行处理诊断数据
   * @description 串行处理诊断数据，逐个文件处理
   * @return {Promise<void>} 无返回值
   */
  async serialProcessDiagnosis() {
    const { ctx, service } = this;
    try {
      const payload = ctx.request.body;
      const result = await service.fzDataFix.serialProcessData(
        'diagnosis',
        payload
      );
      ctx.helper.renderSuccess(ctx, {
        data: result,
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err.message,
      });
    }
  }

  /**
   * @summary 处理单个任务
   * @description 立即处理指定的单个任务
   * @return {Promise<void>} 无返回值
   */
  async processSingleTask() {
    const { ctx, service } = this;
    try {
      const { taskId } = ctx.request.body;

      if (!taskId) {
        ctx.helper.renderFail(ctx, {
          message: '缺少taskId参数',
        });
        return;
      }

      const result = await service.fzDataFix.processSingleTask(taskId);
      ctx.helper.renderSuccess(ctx, {
        data: result,
      });

      ctx.auditLog(
        '处理单个任务',
        `任务ID: ${taskId}, 结果: ${result.message}`,
        result.success ? 'info' : 'error'
      );
    } catch (err) {
      ctx.logger.error('处理单个任务失败:', err);
      ctx.helper.renderFail(ctx, {
        message: err.message,
      });
    }
  }

  /**
   * @summary 继续处理中断的任务
   * @description 重新启动被中断或失败的Redis任务
   * @return {Promise<void>} 无返回值
   */
  async continueProcessing() {
    const { ctx, service } = this;
    try {
      const { taskId } = ctx.request.body;

      if (!taskId) {
        ctx.helper.renderFail(ctx, {
          message: '缺少taskId参数',
        });
        return;
      }

      // 检查任务是否存在于Redis中
      const taskData = await service.taskQueue.getTask(taskId);
      if (!taskData) {
        ctx.helper.renderFail(ctx, {
          message: '任务不存在或已过期',
        });
        return;
      }

      // 检查任务状态是否允许继续处理
      if (taskData.status === 'processing') {
        ctx.helper.renderFail(ctx, {
          message: '任务正在处理中，无需重新启动',
        });
        return;
      }

      if (taskData.status === 'success') {
        ctx.helper.renderFail(ctx, {
          message: '任务已成功完成，无需重新处理',
        });
        return;
      }

      // 重置任务状态并重新启动处理
      await service.taskQueue.updateTask(taskId, {
        status: 'pending',
        progress: 0,
        message: '任务已重新启动',
        restartTime: new Date().toISOString(),
      });

      // 根据任务类型重新启动串行处理
      const dataType = taskData.dataType || taskData.taskType;
      const payload = {
        filePath: taskData.filePath,
        index: taskData.index,
        maxTasks: taskData.maxTasks,
        continueFromTask: taskId, // 标记这是继续处理
      };

      const result = await service.fzDataFix.serialProcessData(
        dataType,
        payload
      );

      ctx.helper.renderSuccess(ctx, {
        data: result,
        message: '任务已重新启动处理',
      });

      ctx.auditLog(
        '继续处理任务',
        `任务ID: ${taskId}, 数据类型: ${dataType}`,
        'info'
      );
    } catch (err) {
      ctx.logger.error('继续处理任务失败:', err);
      ctx.helper.renderFail(ctx, {
        message: err.message,
      });
    }
  }

  /**
   * @summary SSE进度监控
   * @description 提供SSE连接监控任务处理进度
   * @return {Promise<void>} 无返回值
   */
  async sseProgress() {
    const { ctx } = this;
    try {
      const { taskId } = ctx.query;

      // 设置SSE响应头
      ctx.set({
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        Connection: 'keep-alive',
        'Access-Control-Allow-Origin': '*',
      });

      // 发送连接成功消息
      ctx.body =
        'data: ' +
        JSON.stringify({
          type: 'connected',
          message: '连接成功',
          taskId,
        }) +
        '\n\n';

      // 这里可以根据taskId监控具体任务进度
      // 简化版本暂时只返回连接成功
    } catch (err) {
      ctx.status = 500;
      ctx.body =
        'data: ' +
        JSON.stringify({
          type: 'error',
          message: err.message,
        }) +
        '\n\n';
    }
  }

  /**
   * @summary 基于任务ID的SSE进度监控 - 简化版本
   * @description 提供基于特定任务ID的实时进度监控，使用数据库轮询
   * @return {Promise<void>} 无返回值
   */
  async sseProgressByTaskId() {
    const { ctx } = this;

    try {
      const taskId = ctx.params.taskId;
      if (!taskId) {
        ctx.status = 400;
        ctx.body = { success: false, message: '缺少taskId参数' };
        return;
      }

      // 设置SSE响应头
      ctx.set({
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        Connection: 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Cache-Control',
      });

      // 发送连接成功消息
      ctx.res.write(
        'data: {"type": "connected", "message": "SSE连接已建立"}\n\n'
      );

      let isConnected = true;
      let lastStatus = null;
      let checkCount = 0;
      const maxChecks = 300; // 最多检查5分钟（每秒检查一次）

      // 定期检查任务状态
      const checkProgress = async () => {
        try {
          if (!isConnected || ctx.res.finished || checkCount >= maxChecks) {
            return;
          }

          checkCount++;

          // 首先尝试从Redis获取任务状态（用于串行处理等批量任务）
          let progressData = null;
          let taskType = null;

          try {
            ctx.logger.info(`SSE监控开始查询任务: ${taskId}`);

            // 检查taskQueue服务是否可用
            if (
              ctx.service.taskQueue &&
              typeof ctx.service.taskQueue.getTask === 'function'
            ) {
              ctx.logger.info('taskQueue服务可用，开始查询Redis任务');

              const redisTask = await ctx.service.taskQueue.getTask(taskId);
              ctx.logger.info(`Redis查询结果:`, {
                taskId,
                found: !!redisTask,
                taskData: redisTask ? Object.keys(redisTask) : null,
              });

              if (redisTask) {
                taskType =
                  redisTask.taskType || this._extractTaskTypeFromId(taskId);
                ctx.logger.info(`任务类型识别: ${taskType}`);

                const realTimeStats = await this._getRealTimeProgress(taskType);
                ctx.logger.info(`实时统计结果:`, realTimeStats);

                progressData = {
                  status: redisTask.status || 'pending',
                  progress: realTimeStats.progress,
                  message:
                    redisTask.message ||
                    this._getStatusMessage(redisTask.status),
                  processed: realTimeStats.processed,
                  total: realTimeStats.total,
                  failed: realTimeStats.failed,
                  duration: parseInt(redisTask.duration) || 0,
                  taskId,
                };

                ctx.logger.info(`Redis任务进度数据构建完成:`, progressData);
              } else {
                ctx.logger.warn(`Redis中未找到任务: ${taskId}`);
              }
            } else {
              ctx.logger.warn('taskQueue服务不可用，跳过Redis查询', {
                hasService: !!ctx.service.taskQueue,
                hasMethod: ctx.service.taskQueue
                  ? typeof ctx.service.taskQueue.getTask
                  : 'no service',
              });
            }
          } catch (redisError) {
            ctx.logger.error(`从Redis获取任务失败: ${taskId}`, redisError);
          }

          // 如果Redis中没有，尝试从数据库获取
          if (!progressData) {
            ctx.logger.info(`Redis中未找到任务，尝试数据库查询: ${taskId}`);

            const dbTask = await ctx.model.FzDataProcessLog.findOne({ taskId });
            ctx.logger.info(`数据库查询结果:`, {
              taskId,
              found: !!dbTask,
              status: dbTask ? dbTask.processingInfo?.status : null,
            });

            if (dbTask) {
              const currentStatus = dbTask.processingInfo.status;
              taskType = dbTask.taskType || this._extractTaskTypeFromId(taskId);
              ctx.logger.info(`数据库任务类型识别: ${taskType}`);

              const realTimeStats = await this._getRealTimeProgress(taskType);
              ctx.logger.info(`数据库任务实时统计:`, realTimeStats);

              progressData = {
                status: currentStatus,
                progress: realTimeStats.progress,
                message:
                  dbTask.result?.message ||
                  this._getStatusMessage(currentStatus),
                processed: realTimeStats.processed,
                total: realTimeStats.total,
                failed: realTimeStats.failed,
                duration: dbTask.processingInfo?.duration || 0,
                taskId,
              };

              ctx.logger.info(`数据库任务进度数据构建完成:`, progressData);
            } else {
              ctx.logger.warn(`数据库中也未找到任务: ${taskId}`);
            }
          }

          // 如果都没有找到任务
          if (!progressData) {
            ctx.res.write(
              'data: {"status": "error", "message": "任务不存在"}\n\n'
            );
            ctx.res.end();
            return;
          }

          // 只有状态变化时才发送更新
          if (progressData.status !== lastStatus) {
            ctx.res.write(`data: ${JSON.stringify(progressData)}\n\n`);
            lastStatus = progressData.status;
          }

          // 如果任务完成或失败，2秒后关闭连接
          if (
            progressData.status === 'success' ||
            progressData.status === 'failed'
          ) {
            setTimeout(() => {
              if (!ctx.res.finished) {
                ctx.res.end();
              }
            }, 2000);
            return;
          }

          // 继续检查
          setTimeout(checkProgress, 1000);
        } catch (error) {
          ctx.logger.error(`检查任务进度失败: ${taskId}`, error);
          ctx.res.write(
            `data: {"status": "error", "message": "检查进度失败: ${error.message}"}\n\n`
          );
          ctx.res.end();
        }
      };

      // 处理客户端断开连接
      const cleanup = () => {
        isConnected = false;
        ctx.logger.info(`SSE连接断开: ${taskId}`);
      };

      ctx.req.on('close', cleanup);
      ctx.req.on('error', cleanup);

      // 开始检查进度
      checkProgress();
    } catch (error) {
      ctx.logger.error('SSE进度监控失败', error);
      if (!ctx.res.finished) {
        ctx.res.write(
          `data: {"status": "error", "message": "SSE进度监控失败: ${error.message}"}\n\n`
        );
        ctx.res.end();
      }
    }
  }

  /**
   * 计算数据库任务进度百分比
   * @param {Object} task - 数据库任务对象
   * @return {Number} 进度百分比
   */
  _calculateProgressFromDB(task) {
    const status = task.processingInfo?.status;

    switch (status) {
      case 'pending':
        return 0;
      case 'processing': {
        // 根据处理记录数计算进度
        const total = task.dataStats?.totalRecords || 0;
        const processed = task.dataStats?.processedRecords || 0;
        if (total > 0) {
          return Math.min(Math.floor((processed / total) * 100), 99);
        }
        return 50; // 处理中但无具体进度时显示50%
      }
      case 'success':
        return 100;
      case 'failed':
        return 0;
      default:
        return 0;
    }
  }

  /**
   * 获取状态对应的消息
   * @param {String} status - 任务状态
   * @return {String} 状态消息
   */
  _getStatusMessage(status) {
    const messages = {
      pending: '等待处理',
      processing: '正在处理中...',
      success: '处理完成',
      failed: '处理失败',
      skipped: '已跳过',
      retrying: '重试中',
    };

    return messages[status] || '未知状态';
  }

  /**
   * 从任务ID中提取任务类型
   * @param {String} taskId - 任务ID
   * @return {String} 任务类型
   */
  _extractTaskTypeFromId(taskId) {
    if (taskId.includes('company') || taskId.includes('crpt')) {
      return 'company';
    } else if (taskId.includes('healthCheck') || taskId.includes('bhk')) {
      return 'healthCheck';
    } else if (taskId.includes('diagnosis') || taskId.includes('occdiscase')) {
      return 'diagnosis';
    }
    return 'unknown';
  }

  /**
   * 获取实时进度统计
   * @param {String} taskType - 任务类型
   * @return {Promise<Object>} 进度统计
   */
  async _getRealTimeProgress(taskType) {
    const { ctx } = this;

    try {
      // 构建查询条件
      const query = {};
      if (taskType && taskType !== 'unknown') {
        query.taskType = taskType;
      }

      // 聚合查询获取统计信息
      const stats = await ctx.model.FzDataProcessLog.aggregate([
        { $match: query },
        {
          $group: {
            _id: null,
            total: { $sum: 1 },
            pending: {
              $sum: {
                $cond: [{ $eq: ['$processingInfo.status', 'pending'] }, 1, 0],
              },
            },
            processing: {
              $sum: {
                $cond: [
                  { $eq: ['$processingInfo.status', 'processing'] },
                  1,
                  0,
                ],
              },
            },
            success: {
              $sum: {
                $cond: [{ $eq: ['$processingInfo.status', 'success'] }, 1, 0],
              },
            },
            failed: {
              $sum: {
                $cond: [{ $eq: ['$processingInfo.status', 'failed'] }, 1, 0],
              },
            },
            skipped: {
              $sum: {
                $cond: [{ $eq: ['$processingInfo.status', 'skipped'] }, 1, 0],
              },
            },
            totalProcessedRecords: { $sum: '$dataStats.processedRecords' },
            totalFailedRecords: { $sum: '$dataStats.failedRecords' },
          },
        },
      ]);

      const result = stats[0] || {
        total: 0,
        pending: 0,
        processing: 0,
        success: 0,
        failed: 0,
        skipped: 0,
        totalProcessedRecords: 0,
        totalFailedRecords: 0,
      };

      // 计算进度百分比
      const processed = result.success + result.failed + result.skipped;
      const progress =
        result.total > 0 ? Math.floor((processed / result.total) * 100) : 0;

      return {
        total: result.total,
        processed: processed,
        pending: result.pending,
        processing: result.processing,
        success: result.success,
        failed: result.failed,
        skipped: result.skipped,
        progress: Math.min(progress, 100),
        processedRecords: result.totalProcessedRecords || 0,
        failedRecords: result.totalFailedRecords || 0,
      };
    } catch (error) {
      ctx.logger.error('获取实时进度统计失败', error);
      return {
        total: 0,
        processed: 0,
        pending: 0,
        processing: 0,
        success: 0,
        failed: 0,
        skipped: 0,
        progress: 0,
        processedRecords: 0,
        failedRecords: 0,
      };
    }
  }

  /**
   * @summary 获取队列统计信息
   * @description 获取任务队列的统计信息
   * @return {Promise<void>} 无返回值
   */
  async getQueueStats() {
    const { ctx, service } = this;
    try {
      const stats = await service.taskProcessor.getProcessingStats();
      const taskStats = await service.taskQueue.getTaskStats();

      ctx.helper.renderSuccess(ctx, {
        data: {
          ...stats,
          taskStats,
        },
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err.message,
      });
    }
  }

  /**
   * @summary 启动任务处理器
   * @description 启动指定类型的任务处理器
   * @return {Promise<void>} 无返回值
   */
  async startTaskProcessor() {
    const { ctx, service } = this;
    try {
      const { taskType } = ctx.request.body;

      if (!taskType) {
        ctx.helper.renderFail(ctx, {
          message: '缺少taskType参数',
        });
        return;
      }

      await service.taskProcessor.startProcessor(taskType);

      ctx.helper.renderSuccess(ctx, {
        data: { message: `任务处理器已启动: ${taskType}` },
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err.message,
      });
    }
  }

  /**
   * @summary 停止任务处理器
   * @description 停止任务处理器
   * @return {Promise<void>} 无返回值
   */
  async stopTaskProcessor() {
    const { ctx, service } = this;
    try {
      await service.taskProcessor.stopProcessor();

      ctx.helper.renderSuccess(ctx, {
        data: { message: '任务处理器已停止' },
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err.message,
      });
    }
  }

  /**
   * @summary 重试任务
   * @description 重试指定的失败任务
   * @return {Promise<void>} 无返回值
   */
  async retryTask() {
    const { ctx, service } = this;
    try {
      const taskId = ctx.params.taskId;

      if (!taskId) {
        ctx.helper.renderFail(ctx, {
          message: '缺少taskId参数',
        });
        return;
      }

      await service.taskQueue.retryTask(taskId);

      ctx.helper.renderSuccess(ctx, {
        data: { message: `任务已重试: ${taskId}` },
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err.message,
      });
    }
  }

  /**
   * @summary 取消任务
   * @description 取消指定的任务
   * @return {Promise<void>} 无返回值
   */
  async cancelTask() {
    const { ctx, service } = this;
    try {
      const taskId = ctx.params.taskId;

      if (!taskId) {
        ctx.helper.renderFail(ctx, {
          message: '缺少taskId参数',
        });
        return;
      }

      await service.taskQueue.cancelTask(taskId);

      ctx.helper.renderSuccess(ctx, {
        data: { message: `任务已取消: ${taskId}` },
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err.message,
      });
    }
  }

  /**
   * @summary 获取Redis任务列表
   * @description 获取Redis中的任务信息，支持分页和过滤
   * @return {Promise<void>} 无返回值
   */
  async getRedisTaskList() {
    const { ctx } = this;
    try {
      const {
        page = 1,
        pageSize = 20,
        taskType,
        status,
        pattern,
      } = ctx.request.query;

      const result = await ctx.service.fzDataFix.getRedisTaskList({
        page: parseInt(page),
        pageSize: parseInt(pageSize),
        taskType,
        status,
        pattern,
      });

      ctx.helper.renderSuccess(ctx, {
        data: result,
        message: 'Redis任务列表获取成功',
      });
    } catch (err) {
      ctx.logger.error('获取Redis任务列表失败:', err);
      ctx.helper.renderFail(ctx, {
        message: err.message,
      });
    }
  }

  /**
   * @summary 获取Redis队列统计
   * @description 获取Redis中各个队列的统计信息
   * @return {Promise<void>} 无返回值
   */
  async getRedisQueueStats() {
    const { ctx } = this;
    try {
      const result = await ctx.service.fzDataFix.getRedisQueueStats();

      ctx.helper.renderSuccess(ctx, {
        data: result,
        message: 'Redis队列统计获取成功',
      });
    } catch (err) {
      ctx.logger.error('获取Redis队列统计失败:', err);
      ctx.helper.renderFail(ctx, {
        message: err.message,
      });
    }
  }

  /**
   * @summary 清理Redis任务
   * @description 清理Redis中的过期或指定状态的任务
   * @return {Promise<void>} 无返回值
   */
  async cleanupRedisTasks() {
    const { ctx } = this;
    try {
      const { taskType, status, olderThan, dryRun = true } = ctx.request.body;

      const result = await ctx.service.fzDataFix.cleanupRedisTasks({
        taskType,
        status,
        olderThan,
        dryRun: dryRun === 'true' || dryRun === true,
      });

      ctx.helper.renderSuccess(ctx, {
        data: result,
        message: dryRun ? 'Redis任务清理预览完成' : 'Redis任务清理完成',
      });

      ctx.auditLog(
        '清理Redis任务',
        `类型: ${taskType || '全部'}, 状态: ${status || '全部'}, 清理数量: ${
          result.cleanedCount
        }`,
        'info'
      );
    } catch (err) {
      ctx.logger.error('清理Redis任务失败:', err);
      ctx.helper.renderFail(ctx, {
        message: err.message,
      });
    }
  }

  /**
   * @summary 获取解密文件列表
   * @description 根据任务类型获取可解密的ZIP文件列表
   * @return {Promise<void>} 无返回值
   */
  async getDecryptFiles() {
    const { ctx, service } = this;
    try {
      const { taskType } = ctx.query;

      if (!taskType) {
        ctx.helper.renderFail(ctx, {
          message: '缺少taskType参数',
        });
        return;
      }

      const result = await service.fzDataFix.getDecryptFiles(taskType);
      ctx.helper.renderSuccess(ctx, {
        data: result,
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err.message,
      });
    }
  }

  /**
   * @summary 手动执行从API获取所有ZIP数据
   * @description 手动触发从福州API获取所有区域的ZIP数据，包括企业信息、体检数据和诊断信息
   * @return {Promise<void>} 无返回值
   */
  async manualGetAllZipFromApi() {
    const { ctx, service } = this;
    try {
      ctx.logger.info('手动执行从API获取所有ZIP数据开始');

      // 调用服务层的 getALLZipFromApi 方法
      await service.fzDataFix.getALLZipFromApi();

      ctx.helper.renderSuccess(ctx, {
        message: '手动执行从API获取所有ZIP数据完成',
        data: {
          executedAt: new Date().toISOString(),
          status: 'completed',
        },
      });

      ctx.auditLog(
        '手动执行从API获取ZIP数据',
        '手动触发福州数据同步任务完成',
        'info'
      );
    } catch (err) {
      ctx.logger.error('手动执行从API获取所有ZIP数据失败:', err);
      ctx.helper.renderFail(ctx, {
        message: `执行失败: ${err.message}`,
      });

      ctx.auditLog(
        '手动执行从API获取ZIP数据',
        `执行失败: ${err.message}`,
        'error'
      );
    }
  }

  /**
   * @summary 解密ZIP包
   * @description 解密指定的ZIP包并返回数据内容，不保存到数据库
   * @return {Promise<void>} 无返回值
   */
  async decryptZipFile() {
    const { ctx, service } = this;
    try {
      const { taskType, fileName } = ctx.request.body;

      if (!taskType || !fileName) {
        ctx.helper.renderFail(ctx, {
          message: '缺少taskType或fileName参数',
        });
        return;
      }

      const result = await service.fzDataFix.decryptZipFile(taskType, fileName);
      ctx.helper.renderSuccess(ctx, {
        data: result,
      });

      ctx.auditLog(
        '解密ZIP包',
        `任务类型: ${taskType}, 文件名: ${fileName}, 记录数: ${result.totalRecords}`,
        'info'
      );
    } catch (err) {
      ctx.logger.error('解密ZIP包失败:', err);
      ctx.helper.renderFail(ctx, {
        message: err.message,
      });
    }
  }

  /**
   * @summary 根据任务ID解密ZIP包
   * @description 根据任务ID获取文件路径并解密ZIP包，返回数据内容，不保存到数据库
   * @return {Promise<void>} 无返回值
   */
  async decryptTaskZip() {
    const { ctx, service } = this;
    try {
      const { taskId } = ctx.request.body;

      if (!taskId) {
        ctx.helper.renderFail(ctx, {
          message: '缺少taskId参数',
        });
        return;
      }

      const result = await service.fzDataFix.decryptTaskZip(taskId);
      ctx.helper.renderSuccess(ctx, {
        data: result,
      });

      ctx.auditLog(
        '解密任务ZIP包',
        `任务ID: ${taskId}, 记录数: ${result.totalRecords}`,
        'info'
      );
    } catch (err) {
      ctx.logger.error('解密任务ZIP包失败:', err);
      ctx.helper.renderFail(ctx, {
        message: err.message,
      });
    }
  }
  /**
   * 获取福州数据处理日志列表
   */
  async getFzDataProcessLogs() {
    const { ctx } = this;
    const {
      pageSize = 20,
      pageNum = 1,
      taskType = '',
      status = '',
      dateRange = '',
    } = ctx.query;

    try {
      // 构建查询条件
      const query = {};
      if (taskType) {
        query.taskType = taskType;
      }
      if (status) {
        query['processingInfo.status'] = status;
      }
      if (dateRange) {
        const dates = dateRange.split(',');
        if (dates.length === 2) {
          query.createdAt = {
            $gte: new Date(dates[0]),
            $lte: new Date(dates[1]),
          };
        }
      }

      // 分页查询
      const skip = (pageNum - 1) * pageSize;
      const logs = await ctx.model.FzDataProcessLog.find(query)
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(parseInt(pageSize))
        .exec();

      const total = await ctx.model.FzDataProcessLog.countDocuments(query);

      // 格式化数据
      const formattedLogs = logs.map((log) => ({
        id: log._id,
        taskId: log.taskId,
        taskType: log.taskType,
        fileName: log.dataSource.fileName,
        fileType: log.dataSource.fileType,
        status: log.processingInfo.status,
        startTime: log.processingInfo.startTime,
        endTime: log.processingInfo.endTime,
        duration: log.processingInfo.duration,
        retryCount: log.processingInfo.retryCount,
        totalRecords: log.dataStats.totalRecords,
        processedRecords: log.dataStats.processedRecords,
        failedRecords: log.dataStats.failedRecords,
        successRate: log.successRate,
        isRetryable: log.isRetryable,
        message: log.result.message,
        createdAt: log.createdAt,
        updatedAt: log.updatedAt,
      }));

      ctx.helper.renderSuccess(ctx, {
        data: {
          list: formattedLogs,
          total,
          pageNum: parseInt(pageNum),
          pageSize: parseInt(pageSize),
        },
        message: '获取日志列表成功',
      });
    } catch (error) {
      ctx.logger.error('获取福州数据处理日志失败:', error);
      ctx.helper.renderFail(ctx, {
        message: `获取日志失败: ${error.message}`,
      });
    }
  }

  /**
   * 获取福州数据处理统计信息
   */
  async getFzDataProcessStats() {
    const { ctx } = this;
    const { taskType = '', dateRange = '' } = ctx.query;

    try {
      const options = {};
      if (taskType) {
        options.taskType = taskType;
      }
      if (dateRange) {
        const dates = dateRange.split(',');
        if (dates.length === 2) {
          options.dateRange = {
            start: new Date(dates[0]),
            end: new Date(dates[1]),
          };
        }
      }

      const result = await ctx.service.fzDataFix.getProcessLogStats(options);

      if (result.success) {
        ctx.helper.renderSuccess(ctx, {
          data: result.data,
        });
      } else {
        ctx.helper.renderFail(ctx, {
          message: result.message,
        });
      }
    } catch (error) {
      ctx.logger.error('获取福州数据处理统计失败:', error);
      ctx.helper.renderFail(ctx, {
        message: `获取统计信息失败: ${error.message}`,
      });
    }
  }

  /**
   * 处理所有数据类型（企业、体检、诊断）
   */
  async processAllDataTypes() {
    const { ctx } = this;
    const {
      useParallel = true,
      concurrency = 3,
      batchSize = 10,
      maxFiles = 0,
      skipOnError = true,
    } = ctx.request.body || {};

    try {
      // 同时处理三种类型的数据
      const results = await Promise.allSettled([
        // 企业数据
        ctx.service.fzDataFix.processAllCompanyData({
          useParallel,
          concurrency,
          batchSize,
          maxFiles,
          skipOnError,
        }),
        // 体检数据（如果有对应的批量处理方法）
        // ctx.service.fzDataFix.processAllHealthCheckData({...}),
        // 诊断数据（如果有对应的批量处理方法）
        // ctx.service.fzDataFix.processAllDiagnosisData({...}),
      ]);

      const processResults = {
        company:
          results[0].status === 'fulfilled'
            ? results[0].value
            : { success: false, error: results[0].reason },
        // healthCheck: results[1].status === 'fulfilled' ? results[1].value : { success: false, error: results[1].reason },
        // diagnosis: results[2].status === 'fulfilled' ? results[2].value : { success: false, error: results[2].reason },
      };

      ctx.helper.renderSuccess(ctx, {
        message: '批量数据处理完成',
        data: processResults,
      });
    } catch (error) {
      ctx.logger.error('批量数据处理异常:', error);
      ctx.helper.renderFail(ctx, {
        message: `批量处理失败: ${error.message}`,
      });
    }
  }

  /**
   * 获取福州数据文件列表
   */
  async getFzDataFiles() {
    const { ctx } = this;
    const { forceRefresh = false } = ctx.query;

    try {
      const result = await ctx.service.fzDataFix.getFzDataFilesWithCache(
        forceRefresh === 'true'
      );

      ctx.helper.renderSuccess(ctx, {
        message: '获取文件列表成功',
        data: result,
      });
    } catch (error) {
      ctx.logger.error('获取福州数据文件列表异常:', error);
      ctx.helper.renderFail(ctx, {
        message: `获取文件列表失败: ${error.message}`,
      });
    }
  }

  /**
   * 清理福州数据缓存
   */
  async clearFzDataCache() {
    const { ctx } = this;

    try {
      const result = await ctx.service.fzDataFix.clearFzDataCache();

      if (result.success) {
        ctx.helper.renderSuccess(ctx, {
          message: result.message,
        });
      } else {
        ctx.helper.renderFail(ctx, {
          message: result.message,
        });
      }
    } catch (error) {
      ctx.logger.error('清理福州数据缓存异常:', error);
      ctx.helper.renderFail(ctx, {
        message: `清理缓存失败: ${error.message}`,
      });
    }
  }

  /**
   * 获取文件列表详情
   */
  async getFileListDetails() {
    const { ctx } = this;

    try {
      const result = await ctx.service.fzDataFix.getFzDataFilesWithCache(false);

      if (result) {
        // 格式化文件列表，添加更多信息
        const formatFileList = (files, type) => {
          return files.map((file, index) => ({
            index,
            fileName: file,
            fileType: type,
            fullPath: `/app/public/fzData/${file}`,
            size: '未知', // 可以后续添加文件大小获取
            lastModified: '未知', // 可以后续添加修改时间
          }));
        };

        const detailedResult = {
          summary: {
            totalFiles:
              result.FromcrptDownLoad.length +
              result.FrombhkDataDownLoad.length +
              result.FromoccdiscaseDownLoad.length,
            companyFiles: result.FromcrptDownLoad.length,
            healthCheckFiles: result.FrombhkDataDownLoad.length,
            diagnosisFiles: result.FromoccdiscaseDownLoad.length,
          },
          files: {
            company: formatFileList(
              result.FromcrptDownLoad,
              'FromcrptDownLoad'
            ),
            healthCheck: formatFileList(
              result.FrombhkDataDownLoad,
              'FrombhkDataDownLoad'
            ),
            diagnosis: formatFileList(
              result.FromoccdiscaseDownLoad,
              'FromoccdiscaseDownLoad'
            ),
          },
          cached: result.cached || false,
          lastUpdate: result.lastUpdate || new Date().toISOString(),
        };

        ctx.helper.renderSuccess(ctx, {
          data: detailedResult,
          message: '获取文件列表详情成功',
        });
      } else {
        ctx.helper.renderFail(ctx, {
          message: '获取文件列表失败',
        });
      }
    } catch (error) {
      ctx.logger.error('获取文件列表详情异常:', error);
      ctx.helper.renderFail(ctx, {
        message: `获取文件列表详情失败: ${error.message}`,
      });
    }
  }

  /**
   * 批量处理体检数据
   */
  async processAllHealthCheckData() {
    const { ctx } = this;
    try {
      ctx.logger.info('开始批量处理体检数据');

      const {
        maxFiles = 0,
        concurrency = 3,
        batchSize = 10,
        useParallel = true,
        skipOnError = true,
      } = ctx.request.body;

      const options = {
        maxFiles: parseInt(maxFiles) || 0,
        concurrency: parseInt(concurrency) || 3,
        batchSize: parseInt(batchSize) || 10,
        useParallel: useParallel !== false,
        skipOnError: skipOnError !== false,
      };

      const result = await ctx.service.fzDataFix.processAllHealthCheckData(
        options
      );

      ctx.body = {
        code: result.success ? 200 : 500,
        message: result.message,
        data: result,
      };

      ctx.auditLog(
        '批量处理体检数据',
        `总文件: ${result.totalFiles}, 成功: ${result.processedFiles}, 失败: ${result.failedFiles}`,
        result.success ? 'info' : 'error'
      );
    } catch (error) {
      ctx.logger.error('批量处理体检数据异常:', error);
      ctx.auditLog('批量处理体检数据', `异常: ${error.message}`, 'error');

      ctx.body = {
        code: 500,
        message: `批量处理体检数据失败: ${error.message}`,
        data: null,
      };
    }
  }

  /**
   * 批量处理诊断数据
   */
  async processAllDiagnosisData() {
    const { ctx } = this;
    try {
      ctx.logger.info('开始批量处理诊断数据');

      const {
        maxFiles = 0,
        concurrency = 3,
        batchSize = 10,
        useParallel = true,
        skipOnError = true,
      } = ctx.request.body;

      const options = {
        maxFiles: parseInt(maxFiles) || 0,
        concurrency: parseInt(concurrency) || 3,
        batchSize: parseInt(batchSize) || 10,
        useParallel: useParallel !== false,
        skipOnError: skipOnError !== false,
      };

      const result = await ctx.service.fzDataFix.processAllDiagnosisData(
        options
      );

      ctx.body = {
        code: result.success ? 200 : 500,
        message: result.message,
        data: result,
      };

      ctx.auditLog(
        '批量处理诊断数据',
        `总文件: ${result.totalFiles}, 成功: ${result.processedFiles}, 失败: ${result.failedFiles}`,
        result.success ? 'info' : 'error'
      );
    } catch (error) {
      ctx.logger.error('批量处理诊断数据异常:', error);
      ctx.auditLog('批量处理诊断数据', `异常: ${error.message}`, 'error');

      ctx.body = {
        code: 500,
        message: `批量处理诊断数据失败: ${error.message}`,
        data: null,
      };
    }
  }

  /**
   * 创建处理任务
   */
  async createTasks() {
    const { ctx } = this;
    try {
      const {
        taskType = 'all',
        clearExisting = false,
        skipExisting = true,
        maxFiles = 0,
        recentOnly = false,
        recentDays = 2,
      } = ctx.request.body;

      const result = await ctx.service.fzDataFix.createTasksFromFiles({
        taskType,
        clearExisting,
        skipExisting,
        maxFiles,
        recentOnly,
        recentDays,
      });

      ctx.body = {
        code: result.success ? 200 : 500,
        message: result.message,
        data: result,
      };

      ctx.auditLog(
        '创建处理任务',
        `类型: ${taskType}, 清理现有: ${clearExisting}, 跳过已存在: ${skipExisting}, 结果: ${result.message}`,
        result.success ? 'info' : 'error'
      );
    } catch (error) {
      ctx.logger.error('创建任务接口异常:', error);
      ctx.helper.renderFail(ctx, {
        message: `创建任务失败: ${error.message}`,
      });
    }
  }

  /**
   * 获取任务队列状态
   */
  async getQueueStatus() {
    const { ctx } = this;
    try {
      const { taskType } = ctx.query;

      const result = await ctx.service.fzDataFix.getTaskQueueStatus({
        taskType,
      });

      ctx.body = {
        code: result.success ? 200 : 500,
        message: result.message || 'success',
        data: result.data || result,
      };
    } catch (error) {
      ctx.logger.error('获取队列状态异常:', error);
      ctx.helper.renderFail(ctx, {
        message: `获取队列状态失败: ${error.message}`,
      });
    }
  }

  /**
   * 启动任务队列处理
   */
  async startTaskQueue() {
    const { ctx } = this;
    try {
      const {
        taskType = null,
        batchSize = 20, // 增加默认批次大小
        concurrency = 6, // 增加默认并发数
        skipOnError = true,
      } = ctx.request.body;

      // 生成处理器ID
      const processId = require('shortid').generate();

      // 异步启动处理，不等待完成
      setImmediate(async () => {
        try {
          await ctx.service.fzDataFix.processTaskQueue({
            taskType,
            batchSize: parseInt(batchSize) || 20,
            concurrency: parseInt(concurrency) || 6,
            skipOnError: skipOnError !== false,
            processId,
          });
        } catch (error) {
          ctx.logger.error(`任务队列处理异常 [${processId}]:`, error);
        }
      });

      ctx.body = {
        code: 200,
        message: '任务队列处理已启动',
        data: {
          processId,
          options: { taskType, batchSize, concurrency, skipOnError },
        },
      };

      ctx.auditLog(
        '启动任务队列',
        `处理器: ${processId}, 类型: ${taskType || 'all'}`,
        'info'
      );
    } catch (error) {
      ctx.logger.error('启动任务队列异常:', error);
      ctx.helper.renderFail(ctx, {
        message: `启动任务队列失败: ${error.message}`,
      });
    }
  }

  /**
   * 控制处理器
   */
  async controlProcessor() {
    const { ctx } = this;
    try {
      const { processId, action } = ctx.request.body;

      if (!processId || !action) {
        ctx.helper.renderFail(ctx, {
          message: '缺少必要参数: processId 和 action',
        });
        return;
      }

      const result = await ctx.service.fzDataFix.controlProcessor(
        processId,
        action
      );

      ctx.body = {
        code: result.success ? 200 : 500,
        message: result.message,
        data: result,
      };

      ctx.auditLog(
        '控制处理器',
        `处理器: ${processId}, 操作: ${action}, 结果: ${result.message}`,
        result.success ? 'info' : 'warn'
      );
    } catch (error) {
      ctx.logger.error('控制处理器异常:', error);
      ctx.helper.renderFail(ctx, {
        message: `控制处理器失败: ${error.message}`,
      });
    }
  }

  /**
   * 清理重复的任务记录
   */
  async cleanupDuplicateTasks() {
    const { ctx } = this;
    const { taskType = '', dryRun = 'true' } = ctx.request.body || {};

    try {
      const options = {};
      if (taskType) {
        options.taskType = taskType;
      }
      options.dryRun = dryRun === 'true';

      const result = await ctx.service.fzDataFix.cleanupDuplicateTasks(options);

      if (result.success) {
        ctx.helper.renderSuccess(ctx, {
          message: result.message,
          data: result,
        });
      } else {
        ctx.helper.renderFail(ctx, {
          message: result.message,
          data: result,
        });
      }
    } catch (error) {
      ctx.logger.error('清理重复任务失败:', error);
      ctx.helper.renderFail(ctx, {
        message: `清理重复任务失败: ${error.message}`,
      });
    }
  }

  /**
   * 获取实时处理状态
   */
  async getProcessingStatus() {
    const { ctx } = this;
    try {
      const { processId } = ctx.query;

      let status = {
        processing: false,
        message: '没有活跃的处理进程',
      };

      if (processId) {
        // 获取特定处理器状态
        const processStatus = await ctx.helper.getRedis(
          `fz_process_${processId}`
        );
        if (processStatus) {
          status = JSON.parse(processStatus);
          status.processing = status.status === 'running';
        }
      }

      // 获取动态进程管理器状态
      if (global.dynamicProcessManager) {
        status.dynamicProcessManager = global.dynamicProcessManager.getStatus();
      }

      // 获取任务队列概况
      const queueSummary = await ctx.service.fzDataFix.getTaskQueueStatus();
      status.queueSummary = queueSummary.data;

      ctx.body = {
        code: 200,
        data: status,
      };
    } catch (error) {
      ctx.logger.error('获取处理状态异常:', error);
      ctx.helper.renderFail(ctx, {
        message: `获取处理状态失败: ${error.message}`,
      });
    }
  }

  /**
   * 测试动态进程
   */
  async testDynamicProcess() {
    const { ctx } = this;
    try {
      const DynamicProcessManager = require('../utils/dynamicProcessManager');

      console.log('\n=== 测试动态进程管理器 ===');
      console.log(`当前主进程PID: ${process.pid}`);

      // 创建测试文件列表（模拟文件路径）
      const testFiles = [
        '/fake/path/test1.zip',
        '/fake/path/test2.zip',
        '/fake/path/test3.zip',
      ];

      console.log(`测试文件列表: ${testFiles.length} 个文件`);

      const processManager = new DynamicProcessManager({
        maxProcesses: 2,
        taskTimeout: 30000, // 30秒超时
      });

      console.log('动态进程管理器已创建');

      try {
        const result = await processManager.processFiles(testFiles, {
          taskType: 'company',
          batchSize: 2,
          maxConcurrentProcesses: 2,
        });

        console.log('动态进程处理结果:', result);

        ctx.body = {
          code: 200,
          message: '动态进程测试完成',
          data: {
            success: result.success,
            message: result.message,
            totalFiles: result.totalFiles,
            processedFiles: result.processedFiles,
            failedFiles: result.failedFiles,
            skippedFiles: result.skippedFiles,
            processStats: result.processStats,
          },
        };
      } catch (processError) {
        console.error('动态进程处理失败:', processError);
        throw processError;
      } finally {
        // 清理进程管理器
        await processManager.cleanup();
        console.log('动态进程管理器已清理');
      }
    } catch (error) {
      console.error('测试动态进程失败:', error);
      ctx.body = {
        code: 500,
        message: `测试失败: ${error.message}`,
        error: error.stack,
      };
    }
  }
}

module.exports = FzDataFixController;
