
const Controller = require('egg').Controller;
class CommentController extends Controller {
  // 评价打分
  async scoring() {
    const { ctx } = this;
    const { _id, scoring } = ctx.request.body;
    const res = await ctx.service.complaints.scoring(_id, scoring);
    if (res.ok === 1) {
      ctx.helper.renderSuccess(ctx, { data: res, message: '数据更新成功' });
    } else {
      ctx.helper.renderFail(ctx, { data: res, message: '数据更新失败' });
    }
  }
  // 根据用户id获取所有相关数据
  async get() {
    const { ctx } = this;
    const userId = ctx.query.userId || ctx.session.user._id;
    const status = ctx.query.status;
    const res = await ctx.service.complaints.findByUserId(userId, status);
    res && ctx.helper.renderSuccess(ctx, { data: res, message: '数据获取成功' });
  }
  // 补充提交资料和信息
  async update() {
    const { ctx } = this;
    const { _id, status, comments } = ctx.request.body;
    const addStatus = ctx.request.body.addStatus || 0;
    const res = await ctx.service.complaints.update(_id, status, comments, addStatus);
    if (res.ok === 1) {
      ctx.helper.renderSuccess(ctx, { data: res, message: '数据更新成功' });
    } else {
      ctx.helper.renderFail(ctx, { data: res, message: '数据更新失败' });
    }
  }
  // 撤销/删除
  async delete() {
    const { ctx } = this;
    const _id = ctx.query._id;
    const res = await ctx.service.complaints.delete(_id);
    if (res.ok === 1) {
      ctx.helper.renderSuccess(ctx, { data: res, message: '数据更新成功' });
    } else {
      ctx.helper.renderFail(ctx, { data: res, message: '数据更新失败' });
    }
  }
  // 获取某条的详情
  async getDetail() {
    const { ctx, app } = this;
    const _id = ctx.query._id;
    const res = await ctx.service.complaints.findById(_id);
    if (res) {
      const result = JSON.parse(JSON.stringify(res));
      const uploadPath = `http://${ctx.request.host}/static${app.config.upload_http_path}/complaints_files/${ctx.session.user._id}/`;
      result.files = [];
      res.comments.forEach(ele => {
        ele.files.forEach(file => {
          result.files.push(uploadPath + file);
        });
      });
      ctx.helper.renderSuccess(ctx, { data: result, message: '数据获取成功' });
    } else {
      ctx.helper.renderFail(ctx, { message: '数据获取失败' });
    }
  }
  // 添加一条投诉
  async add() {
    const { ctx } = this;
    const data = ctx.request.body;
    const res = await ctx.service.complaints.create(data);
    if (res) {
      const msgToCompany = await ctx.service.complaints.msgToCompany(data.companyId, data.comments.msg);
      if (msgToCompany) {
        ctx.helper.renderSuccess(ctx, { data: res, message: '数据提交成功' });
      } else {
        console.log('投诉举报-企业提醒失败');
      }
    } else {
      ctx.helper.renderFail(ctx, { message: '数据提交失败' });
    }
  }

  // 根据企业id获取企业所在地的监管单位
  async getSupervision() {
    const { ctx } = this;
    const companyId = ctx.query.companyId;
    const supervision = await ctx.service.complaints.getSupervisionByCompanyId(companyId);
    if (companyId) {
      ctx.helper.renderSuccess(ctx, { data: supervision, message: '数据获取成功' });
    } else {
      ctx.helper.renderFail(ctx, { message: '数据获取失败' });
    }
  }

}
module.exports = CommentController;
