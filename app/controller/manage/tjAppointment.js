const Controller = require('egg').Controller;
class tjAppointmentController extends Controller {
  // 获取当前员工体检计划
  async getTjPlan() {
    const { ctx } = this;
    try {
      const payload = ctx.query;
      let res = null;
      res = await ctx.service.tjAppointment.getTjPlan(payload);
      ctx.helper.renderSuccess(ctx, {
        data: res,
        message: '查询成功',
      });
    } catch (error) {
      return ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  // 搜索员工
  async getTjEmployee() {
    const { ctx } = this;
    try {
      const params = ctx.request.body;
      const res = await ctx.service.tjAppointment.getTjEmployee(params);
      ctx.helper.renderSuccess(ctx, {
        data: res,
        message: '查询成功',
      });
    } catch (error) {
      return ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  // 获取当前员工已预约体检
  async getTjAppointment() {
    const { ctx } = this;
    try {
      const params = ctx.query;
      let res = null;
      if (ctx.app.config.branch === 'wh') {
        res = await ctx.service.tjAppointmentWh.getTjAppointment(params);
      } else {
        res = await ctx.service.tjAppointment.getTjAppointment(params);
      }
      ctx.helper.renderSuccess(ctx, {
        data: res,
        message: '查询成功',
      });
    } catch (error) {
      return ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }
  async createTjAppointment() {
    const { ctx } = this;
    try {
      const params = ctx.request.body;
      let res = null;
      if (ctx.app.config.branch === 'wh') {
        res = await ctx.service.tjAppointmentWh.createTjAppointment(params);
      } else {
        res = await ctx.service.tjAppointment.createTjAppointment(params);
      }
      ctx.helper.renderSuccess(ctx, {
        data: res,
        message: '创建成功',
      });
    } catch (error) {
      console.log(error);
      return ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }
  async cancelTjAppointment() {
    const { ctx } = this;
    try {
      const params = ctx.request.body;
      let res = null;
      if (ctx.app.config.branch === 'wh') {
        res = await ctx.service.tjAppointmentWh.cancelTjAppointment(params);
      } else {
        res = await ctx.service.tjAppointment.cancelTjAppointment(params);
      }
      ctx.helper.renderSuccess(ctx, {
        data: res,
        message: '取消成功',
      });
    } catch (error) {
      console.log(error);
      return ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }
  async updateTjAppointment() {
    const { ctx } = this;
    try {
      const params = ctx.request.body;
      let res = null;
      if (ctx.app.config.branch === 'wh') {
        res = await ctx.service.tjAppointmentWh.updateTjAppointment(params);
      } else {
        res = await ctx.service.tjAppointment.updateTjAppointment(params);
      }
      ctx.helper.renderSuccess(ctx, {
        data: res,
        message: '修改成功',
      });
    } catch (error) {
      console.log(error);
      return ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }
  // 获取必选检查项目列表
  async getRequiredCheckItemList() {
    const { ctx } = this;
    try {
      const params = ctx.request.query;
      const res = await ctx.service.tjAppointment.getRequiredCheckItemList(params);
      ctx.helper.renderSuccess(ctx, {
        data: res,
        message: '查询成功',
      });
    } catch (error) {
      console.log(error);
      return ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }
  // 获取职业病检测（危害因素）项目列表
  async getOccupationalHealth() {
    const { ctx } = this;
    try {
      const params = ctx.request.query;
      // params.harmFactors = [ '噪声', '粉尘', '苯', '铅' ]; // 测试数据
      const res = await ctx.service.tjAppointment.getOccupationalHealth(params);
      ctx.helper.renderSuccess(ctx, {
        data: res,
        message: '查询成功',
      });
    } catch (error) {
      console.log(error);
      return ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }
  // 获取选件检查项目列表
  async getOptionalCheckItemList() {
    const { ctx } = this;
    try {
      const params = ctx.request.query;
      const res = await ctx.service.tjAppointment.getOptionalCheckItemList(params);
      ctx.helper.renderSuccess(ctx, {
        data: res,
        message: '查询成功',
      });
    } catch (error) {
      console.log(error);
      return ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  // 获取体检计划剩余人数
  async getTjPlanCount() {
    const { ctx } = this;
    try {
      const params = ctx.request.query;
      const res = await ctx.service.tjAppointment.getTjPlanCount(params);
      ctx.helper.renderSuccess(ctx, {
        data: res,
        message: '查询成功',
      });
    } catch (error) {
      console.log(error);
      return ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  // 获取问卷答卷地址
  async getQuestionnaireUrl() {
    const { ctx } = this;
    try {
      const params = ctx.request.query;
      const res = await ctx.service.tjAppointment.getQuestionnaireUrl(params);
      ctx.helper.renderSuccess(ctx, {
        data: res,
        message: '查询答卷地址成功',
      });
    } catch (error) {
      console.log(error);
      return ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }
  // 获取当前分支
  getBranch(ctx) {
    ctx.helper.renderSuccess(ctx, {
      data: ctx.app.config.branch || '',
    });

  }
}

module.exports = tjAppointmentController;
