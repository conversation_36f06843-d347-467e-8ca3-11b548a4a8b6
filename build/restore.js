require('shelljs/global');
const muri = require('muri');
const exec = require('child_process').exec;
const settings = require('../config/config.local');

const mongoUri = settings({
  baseDir: '.',
}).mongoose.client.url;

console.log('初始化...');

const parsedUri = muri(mongoUri);
const parameters = [];

if (parsedUri.hosts && parsedUri.hosts.length) {
  const host = parsedUri.hosts[0];
  parameters.push(`-h ${host.host}`, `--port ${host.port}`);
}

if (parsedUri.auth) {
  parameters.push(`-u "${parsedUri.auth.user}"`, `-p "${parsedUri.auth.pass}"`);
}

if (parsedUri.db) {
  parameters.push(`-d "${parsedUri.db}"`);
}

parameters.push(`--drop ./databak/${parsedUri.db}`);

const authSource = parsedUri.options.authSource || '';
if (authSource) {
  parameters.push(`--authenticationDatabase ${authSource}`);
}

const cmd = `mongorestore ${parameters.join(' ')}`;
console.log(cmd);
console.log(`数据库${parsedUri.db}初始化完成！开始导入...`);
exec(cmd).stdout;
