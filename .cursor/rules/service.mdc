---
description: Service 层主要负责实现业务逻辑，数据库操作，以及外部服务调用。
globs: **/app/service/*.js
alwaysApply: false
---
# Service 层规范与最佳实践

这些是基于 Egg.js 和 DoraCMS 的服务层文件规范。Service 层主要负责实现业务逻辑，数据库操作，以及外部服务调用。

## 基本规范

### 命名规范
- 服务类必须继承 `egg.Service`
- 服务文件必须以 `Service` 结尾
- 类名采用 PascalCase 命名，如 `AdminUserService`
- 方法名采用 camelCase 命名，如 `createUser`

### 目录结构
**/app/
├── service/
│ ├── **.js

## 数据库操作规范

### 1. 数据库操作必须使用 ctx.service.db
```javascript
// 查询操作
const result = await ctx.service.db.find('User', query, projection, options);

// 查询单条数据
const doc = await ctx.service.db.findOne('User', query, projection, options);

// 创建操作
const doc = await ctx.service.db.create('User', data, supplementaryNotes, options);

// 批量创建
const docs = await ctx.service.db.insertMany('User', dataArray, supplementaryNotes);

// 更新单条数据
const updated = await ctx.service.db.updateOne(
  'User',
  { _id: id },           // 查询条件
  { $set: updateData },  // 更新数据
  options,               // 选项
  supplementaryNotes     // 补充说明
);

// 更新多条数据
const updated = await ctx.service.db.updateMany(
  'User',
  filter,               // 查询条件
  { $set: updateData }, // 更新数据
  options,              // 选项
  supplementaryNotes    // 补充说明
);

// 查找并更新
const doc = await ctx.service.db.findOneAndUpdate(
  'User',
  filter,              // 查询条件
  update,              // 更新数据
  options,             // 选项
  supplementaryNotes   // 补充说明
);

// 删除单条数据
const result = await ctx.service.db.deleteOne(
  'User', 
  filter,              // 查询条件
  supplementaryNotes,  // 补充说明
  options              // 选项
);

// 删除多条数据
const result = await ctx.service.db.deleteMany(
  'User',
  filter,             // 查询条件
  supplementaryNotes  // 补充说明
);

// 聚合查询
const result = await ctx.service.db.aggregate('User', pipeline, options);

// 计数查询
const count = await ctx.service.db.count('User', query, options);
```

### 2. 注意事项
1. Model 名称必须首字母大写
2. supplementaryNotes 用于记录操作日志的补充说明
3. 所有数据库操作都会自动记录操作日志
4. 支持事务操作，会自动处理事务
5. 自动处理权限校验，通过 options.authCheck 控制

### 3. 选项说明
```javascript
const options = {
  authCheck: true,      // 是否进行权限校验，默认为 true
  count: false,         // 是否返回计数，用于 find 方法
  lean: true,           // 是否返回普通 JavaScript 对象
  sort: { _id: -1 },    // 排序选项
  limit: 10,            // 限制返回数量
  skip: 0,              // 跳过数量
  select: '-password'   // 选择返回字段
};
```

## 缓存处理规范

### 1. Redis 缓存操作
```javascript
// 获取缓存
const data = await ctx.helper.getRedis(key);

// 设置缓存
await ctx.helper.setRedis(key, value, ttl);

// 删除缓存
await ctx.helper.delRedis(key);
```

### 2. 内存缓存
```javascript
// 获取缓存
const data = await ctx.helper.getCache(key);

// 设置缓存
ctx.helper.setCache(key, value, expireTime);

// 删除缓存
ctx.helper.delCache(key);
```

## 业务处理规范

### 1. 数据验证
```javascript
// 业务规则验证
if (!isValid(data)) {
  throw new Error('数据验证失败');
}

// 唯一性检查
const exists = await ctx.model.User.findOne({
  email: data.email
});
if (exists) {
  throw new Error('记录已存在');
}
```

### 2. 敏感数据处理
```javascript
// 密码加密
data.password = await ctx.helper.hashSm3(password);

// 数据脱敏
data.phone = ctx.helper.maskPhone(phone);
```

## 错误处理规范

```javascript
try {
  // 业务逻辑处理
  const result = await this.doSomething();
  return result;
} catch (err) {
  // 记录错误日志
  ctx.logger.error('操作失败:', err);
  // 抛出业务友好的错误
  throw new Error('操作失败，请稍后重试');
}
```

## 代码组织规范

### 1. 服务方法结构
```javascript
async methodName(params) {
  const { ctx } = this;
  try {
    // 1. 参数验证
    this.validateParams(params);
    
    // 2. 业务处理
    const result = await this.processLogic(params);
    
    // 3. 返回结果
    return result;
  } catch (err) {
    // 4. 错误处理
    ctx.logger.error(err);
    throw err;
  }
}
```

### 2. 注释规范
```javascript
/**
 * @service 用户服务
 */
class UserService extends Service {
  /**
   * @summary 创建用户
   * @description 创建新用户并分配角色
   * @param {Object} userData - 用户数据
   * @return {Promise<Object>} 创建的用户对象
   */
  async create(userData) {
    // 实现代码
  }
}
```

## 禁止事项

1. 禁止在 Service 层直接处理 HTTP 请求上下文
2. 禁止在 Service 层直接操作 response
3. 禁止将未经处理的错误直接抛出给控制器
4. 禁止在 Service 层处理视图相关逻辑
5. 禁止在 Service 层直接处理用户会话状态
6. 禁止在 Service 层硬编码配置信息
7. 禁止省略错误处理和日志记录

## 安全实践

1. 所有敏感数据必须加密存储
2. 查询操作必须考虑注入风险
3. 批量操作必须考虑性能影响
4. 必须处理并发访问问题
5. 使用 `ctx.helper.hashSm3` 或 `ctx.helper.hashSha256` 进行数据加密

## 日志记录

1. 使用 `ctx.logger` 记录关键操作日志
2. 错误日志必须包含详细信息
3. 敏感操作必须记录操作详情

```javascript
// 日志记录示例
ctx.logger.info('操作信息', { data });
ctx.logger.error('错误信息', err);
```

## 测试要求

1. 每个服务必须有对应的测试用例
2. 测试用例必须覆盖成功和失败场景
3. 必须模拟各种异常情况
4. 测试数据必须使用 mock 数据，不能依赖实际数据库
