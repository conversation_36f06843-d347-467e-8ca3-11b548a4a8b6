---
description: 
globs: 
alwaysApply: false
---
 # Egg.js 定时任务规范

## 基本结构
Egg.js 定时任务文件应遵循以下结构：

```javascript
module.exports = () => {
  return {
    schedule: {
      // 定时任务配置
      interval: '数值', // 执行间隔时间，单位毫秒，也可使用 cron 表达式
      disable: false,   // 是否禁用此任务
      immediate: false, // 项目启动时是否立即执行一次
      type: 'worker',   // 执行类型: worker(随机 worker 执行) 或 all(所有 worker 都执行)
    },
    async task(ctx) {
      // 定时执行的任务逻辑
    },
  };
};
```

## 任务配置参数

### schedule 参数
- `interval`: 数值(毫秒)或 cron 表达式，定义任务执行间隔
- `cron`: cron 表达式，与 interval 二选一
- `disable`: 布尔值，是否禁用此定时任务
- `immediate`: 布尔值，应用启动时是否立即执行一次
- `type`: 字符串，可选值 'worker' 或 'all'
  - `worker`: 随机选择一个 worker 执行任务
  - `all`: 所有 worker 都执行任务

### task 函数
- 必须是异步函数，接收 ctx 参数(应用上下文)
- 通过 ctx 可访问应用的所有资源，包括:
  - `ctx.model`: 数据模型
  - `ctx.service`: 服务层
  - `ctx.app`: 应用实例
  - `ctx.logger`: 日志工具

## 最佳实践

### 性能优化
- 任务执行时间不应过长，避免阻塞进程
- 处理大量数据时，考虑分批处理或使用流式处理
- 适当设置 interval 值，避免任务执行过于频繁

### 错误处理
- 在任务中实现完善的错误捕获和处理机制
- 使用 try/catch 包裹关键代码，避免任务因异常而中断
- 记录任务执行情况，特别是错误信息

```javascript
async task(ctx) {
  try {
    // 任务逻辑
  } catch (error) {
    // 错误处理
    ctx.logger.error(`定时任务执行失败: ${error.message}`, error);
  }
}
```

### 数据清理
- 长期运行的定时任务应考虑数据清理机制
- 处理完成后及时清理临时数据
- 避免内存泄漏问题

### 日志记录
- 使用 `ctx.logger` 记录关键操作和异常
- 区分不同级别的日志：debug, info, warn, error
- 避免过多的日志输出，影响性能

### 命名规范
- 文件名应使用小驼峰命名法，如 `dataSync.js`
- 文件名应清晰表明任务功能
- 避免使用过于笼统的名称，如 `task.js`

## 示例

```javascript
// app/schedule/dataSync.js
module.exports = () => {
  return {
    schedule: {
      interval: '10m', // 每10分钟执行一次
      type: 'worker',
      immediate: false,
    },
    async task(ctx) {
      const { logger, service } = ctx;
      try {
        logger.info('开始同步数据...');
        const result = await service.data.syncExternalData();
        logger.info(`数据同步完成，处理了 ${result.count} 条记录`);
      } catch (error) {
        logger.error(`数据同步失败: ${error.message}`, error);
        // 记录审计日志
        ctx.auditLog('数据同步失败', { error: error.message }, 'error');
      }
    },
  };
};
``` 