---
description: 这些是基于 Egg.js 和 Mongoose 5.13.x 的数据模型定义规范。所有的数据模型都应该遵循这些规范来确保一致性和可维护性。
globs: app/model/**/*.js,lib/plugins/*/app/model/**/*.js
alwaysApply: false
---
 # Mongoose Model 定义规范

这些是基于 Egg.js 和 Mongoose 5.13.x 的数据模型定义规范。所有的数据模型都应该遵循这些规范来确保一致性和可维护性。

## 基本规范

### 1. 文件命名
- 模型文件必须放在 `app/model` 目录下
- 文件名使用 camelCase 命名法
- 文件名应该是模型名称的单数形式

### 2. 模型定义基本结构
```javascript
/**
 * @file 模型名称
 * @description 模型描述
 * <AUTHOR>
 * @createDate 创建日期
 */
module.exports = app => {
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;
  const shortid = require('shortid');
  const ctx = app.createAnonymousContext();
  
  const ModelSchema = new Schema({
    // 字段定义
  }, {
    timestamps: true,        // 自动管理 createdAt 和 updatedAt
    versionKey: false,       // 不需要 __v 字段
    collection: 'collectionName', // 指定集合名称
    minimize: false,         // 保存空对象
    strict: true            // 严格模式，不允许定义外的字段
  });
  
  // 索引定义
  ModelSchema.index({ field: 1 });
  
  // 插件配置
  ModelSchema.plugin(pluginName, options);
  
  return mongoose.model('ModelName', ModelSchema);
};
```

## 字段定义规范

### 1. 必需字段
```javascript
{
  _id: {
    type: String,
    default: shortid.generate
  },
  state: {
    type: String,
    default: '1',  // 1正常，0删除
    enum: ['0', '1']
  },
  date: {
    type: Date,
    default: Date.now,
    get: v => moment(v).format('YYYY-MM-DD HH:mm:ss')
  },
  enable: {
    type: Boolean,
    default: true,
    required: true
  }
}
```

### 2. 字段类型定义
```javascript
{
  // 字符串类型
  name: {
    type: String,
    trim: true,           // 自动去除空格
    lowercase: true,      // 转小写
    maxlength: 100,      // 最大长度
    minlength: 2         // 最小长度
  },
  
  // 带默认值和验证的字符串
  status: {
    type: String,
    default: 'pending',
    enum: {
      values: ['pending', 'active', 'disabled'],
      message: '{VALUE} 不是有效的状态'
    }
  },
  
  // 数字类型
  age: {
    type: Number,
    min: [0, '年龄不能小于0'],
    max: [150, '年龄不能大于150'],
    required: [true, '年龄不能为空']
  },
  
  // 日期类型
  createdAt: {
    type: Date,
    default: Date.now,
    immutable: true  // 不可修改
  },
  
  // 数组类型
  tags: [{
    type: String,
    enum: ['work', 'life', 'study']
  }],
  
  // 嵌套文档
  address: {
    street: {
      type: String,
      required: true
    },
    city: {
      type: String,
      required: true
    },
    country: {
      type: String,
      default: 'China'
    },
    _id: false  // 禁用子文档的_id
  },
  
  // 带验证的字段
  email: {
    type: String,
    required: true,
    unique: true,
    validate: {
      validator: function(v) {
        return /\S+@\S+\.\S+/.test(v);
      },
      message: '邮箱格式不正确'
    }
  },
  
  // 混合类型
  mixed: Schema.Types.Mixed,
  
  // ObjectId 引用
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  }
}
```

### 3. 实例方法定义
```javascript
ModelSchema.methods.customMethod = function() {
  // this 指向文档实例
};
```

### 4. 静态方法定义
```javascript
ModelSchema.statics.findByName = function(name) {
  return this.find({ name: new RegExp(name, 'i') });
};
```

### 5. 查询助手
```javascript
ModelSchema.query.byName = function(name) {
  return this.where({ name: new RegExp(name, 'i') });
};
```

### 6. 虚拟字段
```javascript
ModelSchema.virtual('fullName')
  .get(function() {
    return this.firstName + ' ' + this.lastName;
  })
  .set(function(v) {
    this.firstName = v.substr(0, v.indexOf(' '));
    this.lastName = v.substr(v.indexOf(' ') + 1);
  });
```

### 7. 中间件定义
```javascript
// 文档中间件
ModelSchema.pre('save', function(next) {
  // do something
  next();
});

ModelSchema.post('save', function(doc) {
  // do something
});

// 查询中间件
ModelSchema.pre('find', function() {
  this.where({ state: '1' });
});
```

## 索引定义规范

### 1. 复合索引选项
```javascript
ModelSchema.index(
  { field1: 1, field2: -1 },
  {
    unique: true,
    sparse: true,
    background: true,
    expireAfterSeconds: 3600,
    partialFilterExpression: { field: { $exists: true }}
  }
);
```

## 数据验证规范

### 1. 自定义验证器
```javascript
{
  phone: {
    type: String,
    validate: {
      validator: function(v) {
        return /^1[3-9]\d{9}$/.test(v);
      },
      message: '手机号格式不正确'
    }
  }
}
```

### 2. 异步验证器
```javascript
{
  email: {
    type: String,
    validate: {
      validator: function(v) {
        return new Promise(resolve => {
          resolve(/\S+@\S+\.\S+/.test(v));
        });
      },
      message: '邮箱格式不正确'
    }
  }
}
```

## 性能优化规范

1. 查询优化
   ```javascript
   // 使用 lean()
   await Model.find().lean();
   
   // 使用 select 选择需要的字段
   await Model.find().select('name age -_id');
   
   // 使用 limit 限制返回数量
   await Model.find().limit(10);
   ```

2. 索引优化
   - 为经常查询的字段创建索引
   - 为排序字段创建索引
   - 为复合查询创建复合索引
   - 避免在大字段上建立索引

3. 文档结构优化
   - 避免过深的文档嵌套
   - 合理使用子文档
   - 适当反范式化以提高查询性能

## 禁止事项

1. 禁止直接在模型中写业务逻辑
2. 禁止定义未使用的字段和索引
3. 禁止使用已废弃的 Mongoose 特性
4. 禁止在模型中直接操作其他模型
5. 禁止省略字段说明注释
6. 禁止使用 Object 和 Array 类型，应该使用 Schema.Types.Mixed 和 []
7. 禁止在生产环境使用 autoIndex
8. 禁止使用 collection.ensureIndex()

## 最佳实践

1. 始终定义 Schema 选项
   ```javascript
   const schema = new Schema({...}, {
     timestamps: true,
     versionKey: false,
     strict: true
   });
   ```

2. 使用 TypeScript 类型定义
   ```javascript
   interface IUser {
     name: string;
     email: string;
     createdAt: Date;
   }
   
   const UserSchema = new Schema<IUser>({...});
   ```

3. 合理使用中间件
   - 数据验证和转换
   - 业务规则验证
   - 关联数据处理
   - 日志记录

4. 错误处理
   ```javascript
   schema.post('save', function(error, doc, next) {
     if (error.name === 'MongoError' && error.code === 11000) {
       next(new Error('重复的值'));
     } else {
       next(error);
     }
   });
   ``` 