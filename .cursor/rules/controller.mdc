---
description: Egg.js 控制器，处理 HTTP 请求，使用 ctx 对象
globs: **/app/controller/**/*.js,
alwaysApply: false
---
# Controller 层规范与最佳实践

这些是基于 Egg.js 和 DoraCMS 的控制器文件规范。控制器负责处理 HTTP 请求，使用 `ctx` 对象进行请求和响应操作。

## 基本规范
只允许使用get和post方式

### 命名规范
- 控制器类必须继承 `egg.Controller`
- 控制器文件必须以 `Controller` 结尾
- 类名采用 PascalCase 命名，如 `AdminResourceController`
- 方法名采用 camelCase 命名，如 `listByPower`

### 目录结构
**/app/
├── controller/
│ ├── manage/ # 管理后台控制器需要权限校验 
│ │ ├── **.js
│ │ └── ***.js
│ ├── api/ # API 接口控制器 无需权限校验 

没有特殊说明默认只创建manage接口
## 响应处理规范

### 1. 统一响应格式
必须使用 `ctx.helper` 提供的响应方法：

```javascript
// 成功响应
ctx.helper.renderSuccess(ctx, {
  data: result,           // 返回的数据
  message: '操作成功'     // 可选的成功消息
});

// 失败响应
ctx.helper.renderFail(ctx, {
  message: err.message,   // 错误信息
  data: {},              // 可选的额外数据
}, status);              // 状态码，默认500

// 自定义状态响应
ctx.helper.renderCustom(ctx, {
  status: 200,           // 自定义状态码
  data: result,          // 返回数据
  message: ''           // 消息
});
```

### 2. 错误处理规范
- 所有控制器方法必须使用 try-catch 包裹
- 统一使用 helper 方法处理错误响应

```javascript
async listByPower() {
  const { ctx, service } = this;
  try {
    // 业务逻辑处理
    const result = await service.xxx.find();
    ctx.helper.renderSuccess(ctx, {
      data: result
    });
  } catch (err) {
    ctx.helper.renderFail(ctx, {
      message: err.message
    });
  }
}
```

## 业务处理规范

### 1. 参数获取与校验
```javascript
const { ctx } = this;
// 获取查询参数
const payload = ctx.query;
// 获取请求体参数
const postData = ctx.request.body;
// 参数校验
ctx.validate(ctx.rule.createUser, postData);
```

### 2. 权限处理

### 3. 缓存处理
```javascript
// 获取缓存
const cacheData = await ctx.helper.getCache(cacheKey);
// 设置缓存
ctx.helper.setCache(cacheKey, value, expireTime);
// Redis缓存操作
await ctx.helper.getRedis(key);
await ctx.helper.setRedis(key, value, ttl);
```

## 代码组织规范

### 1. 控制器方法结构
```javascript
async methodName() {
  const { ctx, service } = this;
  try {
    // 1. 参数获取和校验
    const payload = ctx.request.body;
    
    // 2. 权限校验（如需要）
    const adminPower = await ctx.helper.getAdminPower(ctx);
    
    // 3. 业务处理（调用 service）
    const result = await service.xxx.method(payload);
    
    // 4. 响应处理
    ctx.helper.renderSuccess(ctx, {
      data: result
    });
  } catch (err) {
    // 5. 错误处理
    ctx.helper.renderFail(ctx, {
      message: err.message
    });
  }
}
```

### 2. 注释规范
```javascript
/**
 * @controller 资源管理
 */
class AdminResourceController extends Controller {
  /**
   * @summary 获取权限下的资源列表
   * @description 根据当前用户权限获取可访问的资源列表
   * @return {Promise<void>} 无返回值
   */
  async listByPower() {
    // 实现代码
  }
}
```

## 禁止事项

1. 禁止在控制器中直接操作数据库，必须通过 service 层
2. 禁止在控制器中写复杂业务逻辑，应当封装到 service 中
3. 禁止直接返回原始错误信息，应当统一格式化
4. 禁止省略错误处理
5. 禁止使用 `ctx.body` 直接返回数据，必须使用 helper 方法
6. 禁止在控制器中定义常量或配置信息
7. 禁止在控制器中处理文件上传等复杂操作，应封装到 service
8. 禁止使用路径传参的方式，统一使用参数传承那

## 安全实践

1. 所有用户输入必须进行验证和转义
2. 敏感信息（如密码）必须通过 helper 提供的加密方法处理
3. 权限验证必须在操作之前进行
4. 使用 `ctx.helper.hashSm3` 或 `ctx.helper.hashSha256` 进行数据加密
5. 文件操作必须验证文件类型和大小

## 日志记录

1. 使用 `ctx.auditLog` 记录关键操作日志
2. 错误日志必须包含详细信息
3. 敏感操作必须记录操作人和时间

```javascript
// 日志记录示例
ctx.auditLog('操作说明', '详细信息', 'info|error');
```

## 测试要求

1. 每个控制器必须有对应的测试用例
2. 测试用例必须覆盖成功和失败场景
3. 必须模拟各种异常情况
4. 测试数据必须使用 mock 数据，不能依赖实际数据库