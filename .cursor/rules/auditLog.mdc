---
description: 这些是基于 Egg.js 的日志记录规范。使用 `ctx.auditLog` 方法记录系统操作日志，支持不同级别的日志记录，并自动包含用户和环境信息。
globs: 
alwaysApply: false
---
 # 日志记录规范与最佳实践

这些是基于 Egg.js 的日志记录规范。使用 `ctx.auditLog` 方法记录系统操作日志，支持不同级别的日志记录，并自动包含用户和环境信息。

## 基本用法

```javascript
ctx.auditLog(title, message, level);
```

### 参数说明
- `title`: 日志标题，字符串类型，默认值 "警告：未输入任何记录！"
- `message`: 日志详细信息，字符串类型，默认值 "警告：未输入任何记录！"
- `level`: 日志级别，可选值 'debug'|'info'|'warn'|'error'，默认值 'debug'

## 日志级别使用规范

### 1. Debug 级别
```javascript
// 用于开发调试信息
ctx.auditLog('用户登录', '用户开始登录流程', 'debug');
```

### 2. Info 级别
```javascript
// 用于记录正常操作信息
ctx.auditLog('创建用户', '成功创建新用户：张三', 'info');
```

### 3. Warn 级别
```javascript
// 用于记录警告信息
ctx.auditLog('参数验证', '用户提交的参数不完整', 'warn');
```

### 4. Error 级别
```javascript
// 用于记录错误信息
ctx.auditLog('数据库操作', '创建用户记录失败', 'error');
```

## 日志内容规范

### 1. 自动记录信息
每条日志都会自动包含以下信息：
```javascript
{
  title: '操作标题',
  userID: '当前用户ID',
  EnterpriseID: '企业ID',
  message: '详细信息',
  userAgent: {
    IP: '用户IP地址',
    AGENT: '用户浏览器信息'
  }
}
```

### 2. 标题命名规范
- 使用简短的动词+名词组合
- 清晰表达操作类型
- 保持一致的命名风格

示例：
```javascript
// 推荐的标题格式
ctx.auditLog('用户登录', message);
ctx.auditLog('创建订单', message);
ctx.auditLog('删除记录', message);
ctx.auditLog('更新配置', message);
```

### 3. 消息内容规范
- 包含操作的具体信息
- 记录关键参数和结果
- 使用清晰的语言描述

示例：
```javascript
ctx.auditLog(
  '创建用户',
  `用户名: ${username}, 角色: ${role}, 结果: 创建成功`,
  'info'
);
```

## 使用场景

### 1. 用户操作记录
```javascript
// 用户登录
ctx.auditLog('用户登录', `用户 ${username} 登录系统`, 'info');

// 用户注销
ctx.auditLog('用户注销', `用户 ${username} 退出系统`, 'info');
```

### 2. 数据操作记录
```javascript
// 创建记录
ctx.auditLog('创建数据', `在 ${collection} 中创建新记录`, 'info');

// 更新记录
ctx.auditLog('更新数据', `更新 ${collection} 中的记录 ${id}`, 'info');

// 删除记录
ctx.auditLog('删除数据', `从 ${collection} 中删除记录 ${id}`, 'info');
```

### 3. 系统异常记录
```javascript
// 系统错误
ctx.auditLog('系统异常', error.message, 'error');

// 业务警告
ctx.auditLog('业务警告', '数据重复提交', 'warn');
```

## 最佳实践

1. 合理使用日志级别
   - debug: 开发调试信息
   - info: 正常操作信息
   - warn: 警告信息
   - error: 错误信息

2. 日志内容要求
   - 记录必要的上下文信息
   - 避免记录敏感信息
   - 保持信息的完整性

3. 错误日志处理
   - error 级别日志会自动处理换行符
   - 自动格式化为标准错误信息

4. 异常处理
```javascript
try {
  // 业务逻辑
} catch (err) {
  ctx.auditLog('操作失败', err.message, 'error');
  throw err;
}
```

## 禁止事项

1. 禁止记录敏感信息（如密码、token）
2. 禁止在日志中包含用户隐私数据
3. 禁止记录过大的数据结构
4. 禁止在高频循环中使用日志
5. 禁止在日志中包含未经处理的原始错误堆栈

## 日志查看

1. 日志存储位置
   - 开发环境：项目根目录 logs 文件夹
   - 生产环境：配置的日志路径

2. 日志文件分类
   - common-error.log：错误日志
   - egg-web.log：应用日志
   - egg-schedule.log：定时任务日志