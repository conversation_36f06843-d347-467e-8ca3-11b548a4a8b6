# 项目背景
- 数据库：egg-mongoose@3.3.1 (mongoose@5.13.22 创建mongoose的模型时候_id使用 uuid)
- 语言：JavaScript、CommonJS
- 框架：eggjs 2.x doracms

# 目录
项目目录：
 ├── .vscode(代码片段，根据关键字可以快速地生成代码)
 ├── public(静态资源文件，如js、css或者上传的文件)
 ├── app(后端项目主目录)
 │   └── controller(api接口)
 │       └── api/ # API 接口控制器 无需权限校验
 │       └── manage/ # 管理后台控制器需要权限校验
 │   └── service(服务，写业务逻辑)
 │   └── bootstrap(启动文件)
 │   └── extend(扩展库)
 │   └── middleware(中间件)
 │   └── schedule(定时任务)
 │   └── model(mongoose模型)
 │   └── utils(工具函数)
 │   └── validate(参数校验)
 │   └── view(项目前端的首页，可自定义 不同分支有不同的首页)
 │   └── router.js(路由)
 ├── config(配置文件)
 ├── lib(eggjs 的后端插件 此项目内相当于子模块)
 ├── src(前端项目主目录)
 ├── package.json(依赖管理，项目信息)
 ├── bootstrap.js(生产环境启动入口文件，可借助pm2等工具多进程启动)
 └── app.js(项目入口)

 lib的egg子模块后端目录
 ├── lib/plugins/*
 │   └── egg-jk-插件名(插件名小驼峰规范egg-jk-插件名，插件名与前端项目名一致)
 │   │    └── app(插件的app目录)
 │   │    │   └── controller(api接口)
 │   │    │       └──   api(开放接口)
 │   │    │       └──   manage(需要鉴权)
 │   │    │   └──   bootstrap(启动文件)
 │   │    │   └── extend(扩展库)
 │   │    │   └── middleware(中间件)
 │   │    │   └── model(mongoose模型)
 │   │    │   └── service(服务，写业务逻辑)
 │   │    │   └── utils(工具函数)
 │   │    │   └── validate(参数校验)
 │   │    └── config(插件的配置文件)
 │   │    └── app.js(插件入口)
 │   │    └── package.json(依赖管理，项目信息)

 # 其它
- 始终使用中文回复，包括代码注释等
- 创建模块代码需要读取.cursor/rules的lib.mdc、controller.mdc、service.mdc、db.mdc，backstage.mdb,其它rules根据需要进行参考
