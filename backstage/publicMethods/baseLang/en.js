export default {
  main: {
    name: 'name',
    myMessage: 'Notifiction',
    settings: 'Setting',
    logOut: 'Log Out',
    lastLoginTime: 'Last Login Time',
    lastLoginIp: 'Last Login IP',
    myPower: 'My Right',
    seeDetails: 'Check The Details',
    adminUserTotalNum: 'AdminUsers',
    regUserTotalNum: 'Registers',
    contentsTotalNum: 'Contents',
    messagesTotalNum: 'Comments',
    shortcutOption: 'Short Cuts',
    addAdminUser: 'Add  New  Admin',
    addContents: 'Add New Contents',
    sourceManage: 'Resource  Management',
    systemConfigs: 'System systemConfigs',
    databak: 'Data BackUp',
    nearMessages: 'Recent Comments',
    messageIn: 'At',
    messageSaid: 'Said',
    messageReply: 'Reply',
    noMessages: 'No Data, Right Now!',
    nearNewUsers: 'New Rigistered Users',
    confirmBtnText: 'Yes',
    cancelBtnText: 'Cancel',
    reSetBtnText: 'Reset',
    scr_modal_title: 'Hints',
    scr_modal_del_succes_info: 'Delete Succeeded!',
    scr_modal_del_error_info: 'Cancelled Delete',
    scr_modal_del_faild_info: 'Delete failed!',
    form_btnText_update: 'Update',
    form_btnText_save: 'Save',
    radioOn: 'Yes',
    radioOff: 'No',
    updateSuccess: 'Update Succeeded',
    addSuccess: 'Add Succeeded',
    dataTableOptions: 'Operate',
    del_notSelectDel: 'Please select the data to delete!',
    del_notice: 'Do you want delete the records?',
    just_del_notice: "You'll delete this records forever, continue?",
    install_notice: 'Are you sure you want to install the plug-in?',
    uninstall_notice: 'Are you sure you want to uninstall the plug-in?',
    update_notice: 'Are you sure you want to update the plug-in?',
    comments_label: 'Note',
    sort_label: 'Sort',
    ask_select_label: 'Please Choose',
    target_Item: 'Specify The Target',
    confirm_logout: 'Are you usre you want to exit?',
    login_timeout: 'Your login has time out',
    server_error_notice: 'Server connection exception, please try again later.',
    re_login: 'Re-Login',
    addNew: 'Add',
    modify: 'Edit',
    del: 'Delete',
    gender: 'Gender',
    gender_0: 'Male',
    gender_1: 'Female',
    marriage: 'Marriage',
    marriage_0: 'Unmarried',
    marriage_1: 'Married',
    back: 'Return',
    post: 'Publish',
    nopage: "The page you're trying visit not exist or you don't have the right",
    close_modal: 'Close',
    askForReInputContent: 'Found that you have unsaved documents, do you load them?',
    cancelReInputContent: 'Load has been cancelled and data has been cleared.',
    noModifyPasswordTips: "Leave it blank if you don't change your password",
  },
  validate: {
    inputNull: 'Please Type{label}',
    inputCorrect: 'Please Entery the Right{label}',
    selectNull: 'Please Choose{label}',
    rangelength: 'Length between {min} to {max} ',
    ranglengthandnormal: '{min} to {max} character, only letter, Number and underline available!',
    maxlength: 'The max character {max} you can Entery',
    passwordnotmatching: 'The password and the confirmation you typed do not match',
    limitNotSelectImg: "You haven't selected a picture yet",
    limitUploadImgCount: 'Only {count} photos can be uploaded',
    limitUploadImgType: 'Only JPG,PNG,JPEG,BMP,JFIF format pics are available!',
    limitUploadImgSize: "The pics size can't exceed{size}MB!",
    limitUploadFileType: 'The format of the uploaded file is incorrect',
    limitUploadFileSize: 'Upload file size cannot exceed {size} MB',
    error_params: 'You have error in filling in parameters, please re operate.',
  },
  // LangEnd
};
