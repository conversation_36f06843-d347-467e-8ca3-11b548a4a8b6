{"name": "oapi", "version": "1.0.467", "description": "Restful Open API", "private": true, "egg": {"declarations": true}, "dependencies": {"@alicloud/facebody20191230": "^4.0.8", "@alicloud/pop-core": "^1.7.10", "@koa/multer": "^3.0.2", "@signpdf/placeholder-pdf-lib": "3.2.0", "@signpdf/signer-p12": "3.2.0", "@signpdf/signpdf": "3.2.0", "@wecom/crypto": "^1.0.1", "aedes": "^0.46.1", "ali-oss": "^6.17.1", "alipay-sdk": "^3.1.5", "amqplib": "^0.10.3", "angular-expressions": "^1.4.3", "archiver": "^5.3.1", "await-stream-ready": "^1.0.1", "axios": "^1.7.9", "basic-auth": "^2.0.1", "combine-word": "^1.1.5", "crypto-js": "4.2.0", "dingtalk-encrypt": "^2.1.1", "docxtemplater": "^3.17.6", "docxtemplater-image-module-free-norepeat": "^1.0.8", "egg": "^3.12.0", "egg-cors": "^3.0.1", "egg-dora-validate": "^1.0.1", "egg-mongoose": "^3.3.1", "egg-redis": "^2.6.0", "egg-scripts": "^2.17.0", "egg-swagger-doc": "^2.3.2", "egg-view-nunjucks": "^2.2.0", "fast-xml-parser": "^4.4.0", "fontkit": "^2.0.2", "global-agent": "^3.0.0", "http-proxy": "^1.18.1", "id-validator-modify": "^1.0.3", "image-size": "^1.0.1", "js-base64": "^3.7.7", "js-cache": "^1.0.3", "jsonwebtoken": "^9.0.0", "koa-compress": "^5.0.0", "koa-xml-body": "^3.0.0", "lodash": "^4.17.15", "mammoth": "^1.4.11", "minio": "^7.1.3", "mkdirp": "^1.0.4", "module-alias": "^2.2.1", "moment": "^2.24.0", "mongodb": "^5.1.0", "mqtt": "^4.2.8", "multer": "^1.4.5-lts.1", "muri": "^1.3.0", "nanoid": "^5.1.0", "net": "^1.0.2", "node-schedule": "^2.1.1", "node-xlsx": "^0.23.0", "pdf-lib": "^1.17.1", "pdfjs-dist": "^3.7.107", "pizzip": "^3.1.4", "pump": "^3.0.0", "qiniu": "^7.2.2", "qrcode": "^1.5.3", "segment": "^0.1.3", "sha1": "^1.1.1", "sharp": "^0.33.5", "shelljs": "^0.8.4", "shortid": "^2.2.14", "sm-crypto": "^0.3.13", "stream-wormhole": "^1.1.0", "svg-captcha": "^1.4.0", "uuid": "^9.0.0", "validate": "^5.2.0", "validator": "^13.1.0", "wechatpay-node-v3": "^2.1.0", "xss": "^1.0.6"}, "devDependencies": {"autod": "^3.0.1", "autod-egg": "^1.1.0", "cross-env": "^7.0.3", "egg-bin": "^6.4.1", "egg-ci": "^2.1.0", "egg-mock": "^5.10.0", "eslint": "^8.0.0", "eslint-config-egg": "^12.1.0"}, "resolutions": {"axios": "^1.7.9", "braces": "^3.0.3", "cookie": "^1.0.2", "cross-spawn": "^7.0.6", "jsrsasign": "^11.1.0", "koa": "^2.15.4", "micromatch": "^4.0.8", "mongoose": "^5.13.20", "undici": "^5.26.3", "xlsx": "https://cdn.sheetjs.com/xlsx-0.20.3/xlsx-0.20.3.tgz", "follow-redirects": "^1.15.9", "semver": "7.5.2", "ws": "^8.18.0"}, "overrides": {"axios": "^1.7.9", "braces": "^3.0.3", "cookie": "^1.0.2", "cross-spawn": "^7.0.6", "koa": "^2.15.4", "micromatch": "^4.0.8", "undici": "^5.26.3", "mongoose": "^5.13.20", "xlsx": "https://cdn.sheetjs.com/xlsx-0.20.3/xlsx-0.20.3.tgz", "follow-redirects": "^1.15.9", "semver": "^7.7.1", "ws": "^8.18.0", "egg-security": "^3.7.0"}, "engines": {"node": ">=14.0.0"}, "scripts": {"start": "cross-env NODE_ENV=production EGG_SERVER_ENV=prod ENABLE_NODE_LOG=YES NODE_LOG_DIR=/opt/log/oapi/ egg-scripts start --daemon --workers=2 --title=oapi --stdout=/opt/log/oapi/master-stdout.log --stderr=/opt/log/oapi/master-stderr.log", "zaw": "cross-env NODE_ENV=production EGG_SERVER_ENV=zaw ENABLE_NODE_LOG=YES NODE_LOG_DIR=/opt/log/zaw-oapi/ egg-scripts start --title=zaw-oapi --env=zaw --stdout=/opt/log/zaw-oapi/master-stdout.log --stderr=/opt/log/zaw-oapi/master-stderr.log", "sit": "cross-env NODE_ENV=production EGG_SERVER_ENV=sit ENABLE_NODE_LOG=YES NODE_LOG_DIR=/opt/log/oapi/ egg-scripts start --daemon --workers=2 --title=oapi --stdout=/opt/log/oapi/master-stdout.log --stderr=/opt/log/oapi/master-stderr.log", "sit2": "cross-env NODE_ENV=production EGG_SERVER_ENV=sit2 ENABLE_NODE_LOG=YES NODE_LOG_DIR=/opt/log/oapi2/ egg-scripts start --daemon --workers=2 --title=oapi2 --stdout=/opt/log/oapi2/master-stdout.log --stderr=/opt/log/oapi2/master-stderr.log", "k8s": "egg-scripts start --stikcy --title=$(echo $EGG_SERVER_ENV)-oapi --workers=2", "stop": "egg-scripts stop --title=oapi", "stop2": "egg-scripts stop --title=oapi2", "stop-zaw": "egg-scripts stop --title=zaw-oapi", "dev": "cross-env NODE_ENV=development && egg-bin dev --port=7009", "debug": "egg-bin debug", "build": "npm run lint", "test": "npm run lint -- --fix && npm run test-local && npm run cov", "test-local": "cross-env NODE_ENV=test && cross-env EGG_SERVER_ENV=unittest && egg-bin test --timeout 5400", "cov": "cross-env NODE_ENV=test && cross-env EGG_SERVER_ENV=unittest && egg-bin cov", "lint": "eslint . --fix", "ci": "npm run lint -- --fix && npm run cov", "autod": "autod", "init": "node ./install/index.js", "clusterStart": "cross-env NODE_ENV=production && pm2 start server.js --name oapi", "clusterStop": "pm2 stop server.js --name oapi"}, "ci": {"version": "10"}, "repository": {"type": "git", "url": ""}, "author": "", "license": "MIT", "_moduleAliases": {"@root": ".", "@validate": "app/validate", "@utils": "app/utils"}}